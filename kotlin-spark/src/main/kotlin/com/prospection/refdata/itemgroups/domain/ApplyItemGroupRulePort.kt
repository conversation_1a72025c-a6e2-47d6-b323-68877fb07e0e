package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row

interface ApplyItemGroupRulePort {
    fun getItemsToItemsGroupsWithoutSourceAttributeColumns(
        items: Dataset<Row>,
        itemGroups: List<ItemGroup>,
        codingSystemToClassifications: List<CodingSystemToClassification>
    ): Dataset<Row>

    fun getItemsToItemGroupWithSourceAttributeColumns(
        items: Dataset<Row>,
        itemGroup: ItemGroup,
        codingSystemToClassifications: List<CodingSystemToClassification>
    ): Dataset<Row>

    fun getItemGroupPreview(
        items: Dataset<Row>,
        itemGroup: ItemGroup,
        codingSystemToClassifications: List<CodingSystemToClassification>
    ): Dataset<Row>

    fun getItemsWithItemGroupsOnly(items: Dataset<Row>, itemGroups: List<ItemGroup>): Dataset<Row>

    fun getCustomGroupsBasedOnCondition(items: Dataset<Row>, customGroupCondition: CustomGroupCondition): Dataset<Row>
}