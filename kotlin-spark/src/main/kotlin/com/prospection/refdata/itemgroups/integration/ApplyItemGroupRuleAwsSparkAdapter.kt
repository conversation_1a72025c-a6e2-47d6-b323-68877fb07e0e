package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.consts.SourceAttribute.CODE_COLUMN_NAME
import com.prospection.refdata.common.consts.SourceAttribute.PREFIX_SOURCE_ATTRIBUTE
import com.prospection.refdata.common.integration.ApplyRuleHelper
import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.CustomGroupCondition
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions
import org.apache.spark.sql.functions.array
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.functions.explode
import org.apache.spark.sql.functions.`when`
import org.jetbrains.kotlinx.spark.api.lit
import org.springframework.stereotype.Component
import scala.jdk.CollectionConverters

@Component
class ApplyItemGroupRuleAwsSparkAdapter : ApplyItemGroupRulePort {

    override fun getItemsToItemsGroupsWithoutSourceAttributeColumns(
        items: Dataset<Row>,
        itemGroups: List<ItemGroup>,
        codingSystemToClassifications: List<CodingSystemToClassification>
    ): Dataset<Row> {
        val itemsToItemGroups = getItemsAppliedItemGroupsWithItemGroupName(items, itemGroups)

        return explodeByCodingSystemToClassification(codingSystemToClassifications, itemsToItemGroups)
            .select(
                col(CODE_COLUMN_NAME).`as`(ItemGroupExportColumns.ITEM_CODE.columnName),
                col(ItemGroupExportColumns.ITEM_GROUP_CODE.columnName),
                col(ItemGroupExportColumns.ITEM_GROUP_NAME.columnName),
                col(ItemGroupExportColumns.CLASSIFICATION.columnName)
            )
            .distinct() // because there can be multiple rows with the same item codes like PBS
    }

    override fun getItemsToItemGroupWithSourceAttributeColumns(
        items: Dataset<Row>,
        itemGroup: ItemGroup,
        codingSystemToClassifications: List<CodingSystemToClassification>
    ): Dataset<Row> {
        val sourceColumns = items.columns()
            .filter { it.startsWith(PREFIX_SOURCE_ATTRIBUTE) }
            .map {
                col(it).`as`(it.replace(PREFIX_SOURCE_ATTRIBUTE, ""))
            }.toTypedArray()

        val itemsToItemGroups = getItemsAppliedItemGroupsWithItemGroupName(items, listOf(itemGroup))

        return explodeByCodingSystemToClassification(codingSystemToClassifications, itemsToItemGroups)
            .select(*sourceColumns, col(ItemGroupExportColumns.CLASSIFICATION.columnName))
            .distinct() // because there can be multiple rows with the same item codes like PBS
    }

    override fun getItemGroupPreview(
        items: Dataset<Row>,
        itemGroup: ItemGroup,
        codingSystemToClassifications: List<CodingSystemToClassification>
    ): Dataset<Row> {
        val filledMissingAttributesDs = fillMissingSourceAndEnrichedAttributes(items, listOf(itemGroup))
        val itemGroupRule = ApplyRuleHelper.translateFromRuleToSparkColumn(
            filledMissingAttributesDs,
            itemGroup.deserialisedRule
        )
        val previewItems = filledMissingAttributesDs.filter(itemGroupRule)

        return explodeByCodingSystemToClassification(codingSystemToClassifications, previewItems).distinct()
    }

    private fun applyItemGroupsToItems(items: Dataset<Row>, itemGroups: List<ItemGroup>): Dataset<Row> {
        val itemsWithAllSourceAttributesAndMissingEnrichedAttrs =
            fillMissingSourceAndEnrichedAttributes(items, itemGroups)

        val itemsEnrichedWithItemGroupsToMultipleItemGroupColumns =
            applyConditionsAndPopulateItemGroupColumns(itemGroups, itemsWithAllSourceAttributesAndMissingEnrichedAttrs)

        val allItemGroupBusinessKeys = itemGroups.map { it.businessKey }

        val itemsEnrichedWithItemGroupsToSingleItemGroupColumn =
            itemsEnrichedWithItemGroupsToMultipleItemGroupColumns.withColumn(
                ItemGroupExportColumns.ITEM_GROUP_CODE.columnName,
                array(*allItemGroupBusinessKeys.map {
                    itemsEnrichedWithItemGroupsToMultipleItemGroupColumns.col(
                        it
                    )
                }.toTypedArray())
            )
                .drop(*allItemGroupBusinessKeys.toTypedArray())

        return ScalaSparkItemsFunctions.filterOutNullFromArray(
            itemsEnrichedWithItemGroupsToSingleItemGroupColumn,
            ItemGroupExportColumns.ITEM_GROUP_CODE.columnName
        )
    }

    private fun fillMissingSourceAndEnrichedAttributes(
        items: Dataset<Row>,
        itemGroups: List<ItemGroup>
    ): Dataset<Row> {
        val nonExistingSourceAttributeColumns =
            ApplyRuleHelper.getNonExistingSourceAttributeColumns(items, itemGroups)

        val itemsWithAllSourceAttributeColumns =
            ApplyRuleHelper.populateNonExistingSourceAttributeColumns(items, nonExistingSourceAttributeColumns)

        return fillMissingEnrichedColumns(itemGroups, itemsWithAllSourceAttributeColumns)
    }

    override fun getItemsWithItemGroupsOnly(items: Dataset<Row>, itemGroups: List<ItemGroup>): Dataset<Row> {
        val sourceWithEnrichedAttributesCols = items.columns().map { col(it) }.toTypedArray()
        return applyItemGroupsToItems(items, itemGroups)
            .select(
                *sourceWithEnrichedAttributesCols,
                col(ItemGroupExportColumns.ITEM_GROUP_CODE.columnName),
            )
            .distinct() // because there can be multiple rows with the same item codes like PBS
    }

    override fun getCustomGroupsBasedOnCondition(
        items: Dataset<Row>,
        customGroupCondition: CustomGroupCondition
    ): Dataset<Row> {
        val groupCols = customGroupCondition.groupingAttributes.map { col(it) }.toTypedArray()

        return items
            .filter(ApplyRuleHelper.translateFromRuleToSparkColumn(items, customGroupCondition.deserialisedRule))
            .groupBy(*groupCols)
            .agg(functions.collect_list(col(CODE_COLUMN_NAME)).`as`("values"))
            .select(
                functions.concat_ws("_", *groupCols).`as`("group"),
                col("values"),
                lit("event.code").`as`("target"),
                lit(customGroupCondition.classification).`as`("classification"),
            )
    }

    private fun getItemsAppliedItemGroupsWithItemGroupName(
        items: Dataset<Row>,
        itemGroups: List<ItemGroup>
    ): Dataset<Row> {
        val itemGroupKeyToName = itemGroups.associateBy({ it.businessKey }, { it.name })
        val itemsWithItemGroupsOnly = applyItemGroupsToItems(items, itemGroups)
        val itemsWithNonItemGroups = ScalaSparkItemsFunctions.filterOutItemsWithoutItemGroups(
            itemsWithItemGroupsOnly,
            ItemGroupExportColumns.ITEM_GROUP_CODE.columnName
        )
        val explodedItems = ScalaSparkItemsFunctions.explodeDs(
            itemsWithNonItemGroups,
            ItemGroupExportColumns.ITEM_GROUP_CODE.columnName
        )

        return ScalaSparkItemsFunctions.addItemGroupName(
            explodedItems,
            ItemGroupExportColumns.ITEM_GROUP_CODE.columnName,
            ItemGroupExportColumns.ITEM_GROUP_NAME.columnName,
            CollectionConverters.MapHasAsScala(itemGroupKeyToName).asScala()
        )
    }

    private fun fillMissingEnrichedColumns(itemGroups: List<ItemGroup>, ds: Dataset<Row>): Dataset<Row> {
        val existingColumnsNames = ds.columns().toList()
        val existingColumns = existingColumnsNames.map { col(it) }.toTypedArray()

        val missingEnrichedColumns = itemGroups.map {
            it.deserialisedRule.getEnrichedAttributes()
        }
            .flatten()
            .distinct()
            .filter { !existingColumnsNames.contains(it) }
            .map { lit(array()).name(it) }
            .toTypedArray()

        return ds.select(
            *existingColumns,
            *missingEnrichedColumns,
        )
    }

    private fun applyConditionsAndPopulateItemGroupColumns(
        itemGroups: List<ItemGroup>,
        dataset: Dataset<Row>
    ): Dataset<Row> {
        val existingColumns = dataset.columns().toList().map { col(it) }.toTypedArray()

        val enrichedColumns = itemGroups.map {
            `when`(
                ApplyRuleHelper.translateFromRuleToSparkColumn(dataset, it.deserialisedRule),
                it.businessKey
            )
                .otherwise(null)
                .`as`(it.businessKey)
        }.toTypedArray()

        return dataset.select(
            *existingColumns,
            *enrichedColumns,
        )
    }

    private fun explodeByCodingSystemToClassification(
        codingSystemToClassifications: List<CodingSystemToClassification>,
        items: Dataset<Row>
    ): Dataset<Row> {
        return codingSystemToClassifications.map { codingSystemToClassification ->
            //Replace source_code by codingSystemColumnToExport
            fillCodeByCodingSystemColumnToExport(codingSystemToClassification.codingSystemColumnToExport, items)
                //Add classification column
                .withColumn(
                    ItemGroupExportColumns.CLASSIFICATION.columnName,
                    lit(codingSystemToClassification.classification)
                )
                .distinct()

        }.reduce { acc, dataset -> acc.unionAll(dataset) }
    }

    private fun fillCodeByCodingSystemColumnToExport(
        codingSystemColumnToExport: String,
        items: Dataset<Row>
    ): Dataset<Row> {
        return if (ScalaSparkItemsFunctions.isArrayTypeColumn(items, codingSystemColumnToExport)) {
            items.withColumn(CODE_COLUMN_NAME, explode(col(codingSystemColumnToExport)))
        } else {
            items.withColumn(CODE_COLUMN_NAME, col(codingSystemColumnToExport))
        }
    }
}