package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import com.prospection.refdata.itemgroups.domain.ItemGroupsSparkPort
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import com.prospection.refdata.items.domain.ItemsSparkPort
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.springframework.stereotype.Component
import scala.jdk.CollectionConverters.MapHasAsJava

@Component
class ItemGroupsSparkImpl(
    private val itemPort: ItemsSparkPort,
    private val applyItemGroupRulePort: ApplyItemGroupRulePort,
) : ItemGroupsSparkPort {
    companion object {
        private const val MAX_ITEM_GROUP_PREVIEW = 100
    }

    override fun getItemGroupPreview(
        itemGroup: ItemGroup,
        codingSystemToClassifications: Map<String, List<CodingSystemToClassification>>,
        publishedItemsVersion: String,
    ): List<ItemGroupPreviewResult> {
        return codingSystemToClassifications.mapNotNull { (codingSystem, codingSystemToClassifications) ->
            val itemsToItemGroups = itemPort.getPublishedItems(codingSystem, publishedItemsVersion)?.let {
                applyItemGroupRulePort.getItemGroupPreview(it, itemGroup, codingSystemToClassifications)
                    .drop(CODING_SYSTEM_ATTRIBUTE_NAME)
            }

            toItemGroupPreview(itemsToItemGroups, codingSystem)
        }.toList()
    }

    private fun toItemGroupPreview(itemsToItemGroups: Dataset<Row>?, codingSystem: String): ItemGroupPreviewResult? {
        if (itemsToItemGroups == null) {
            return null
        }
        itemsToItemGroups.persist()
        return try {
            val totalRowCount = itemsToItemGroups.count()
            if (totalRowCount > 0L) {
                ItemGroupPreviewResult(
                    codingSystem,
                    totalRowCount,
                    itemsToItemGroups.columns()
                        .toList(),
                    ScalaSparkItemsFunctions.toArrayMap(itemsToItemGroups.limit(MAX_ITEM_GROUP_PREVIEW))
                        .toList()
                        .map { MapHasAsJava(it).asJava() }
                )
            } else {
                null
            }
        } finally {
            itemsToItemGroups.unpersist()
        }
    }
}