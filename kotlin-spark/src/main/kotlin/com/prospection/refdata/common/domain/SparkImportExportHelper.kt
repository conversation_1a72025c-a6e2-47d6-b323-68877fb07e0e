package com.prospection.refdata.common.domain

import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Encoder
import org.apache.spark.sql.Row

interface SparkImportExportHelper {
    fun readCsv(path: String): Dataset<Row>
    fun readParquet(path: String): Dataset<Row>?
    fun writeCsv(path: String, ds: Dataset<Row>)
    fun writeParquet(path: String, ds: Dataset<Row>)
    fun <T> writeParquet(path: String, objectsToWrite: List<T>, encoder: Encoder<T>)
    fun writeExcel(ds: Dataset<Row>, path: String, sheetName: String, sheetIndex: Int)
}