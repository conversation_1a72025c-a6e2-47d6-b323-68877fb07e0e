package com.prospection.refdata.common.integration

import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import com.prospection.refdata.items.ScalaSparkItemsFunctions.getArrayFieldValuationFunction
import com.prospection.refdata.items.ScalaSparkItemsFunctions.nullToEmpty
import com.prospection.refdata.items.ScalaSparkItemsFunctions.nullToEmptyArray
import com.prospection.refdata.items.ScalaSparkItemsFunctions.toLowerCase
import com.prospection.refdata.items.ScalaSparkItemsFunctions.toLowercaseArrayString
import com.prospection.refdata.rules.domain.Rule
import com.prospection.refdata.rules.domain.Type
import org.apache.spark.sql.Column
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions
import org.jetbrains.kotlinx.spark.api.not

object ApplyRuleHelper {
    fun getNonExistingSourceAttributeColumns(
        rawItems: Dataset<Row>,
        enrichmentRules: List<HasRule>
    ): Set<String> {
        val columnNames = rawItems.columns()

        val allSourceAttributeNames = enrichmentRules.flatMap { it.deserialisedRule.getAllRules() }
            .filter { it.isLeafRule() }
            .map { it.field!! }
            .filter { Type.getTypeByAttributeName(it) == Type.SOURCE_ATTRIBUTE }
            .distinct()

        return allSourceAttributeNames.filter {
            !columnNames.contains(it)
        }
            .toSet()
    }

    /**
     * Spark throws an AnalysisException if column('columnName') is called and columnName doesn't exist in the dataset.
     * For example, a column 'source_indication' doesn't exist in PBS raw items but if a rule is trying to evaluate,
     * for example column("source_indication") == "MM", our desired result is FALSE (or not matching),q
     * but this will throw an error instead of evaluated as "false"
     *
     * So here we populate any source attributes existing in enrichment rules to avoid the exception
     */
    fun populateNonExistingSourceAttributeColumns(
        rawItems: Dataset<Row>,
        nonExistingSourceAttributeColumns: Set<String>
    ): Dataset<Row> {
        val cols = createColumns(rawItems, nonExistingSourceAttributeColumns)

        return rawItems.select(rawItems.col("*"), *cols)
    }

    fun translateFromRuleToSparkColumn(dataset: Dataset<Row>, rule: Rule): Column {
        return if (rule.isLeafRule()) {
            val operator = rule.operator

            val columnName = escapeAsSparkColumnNameIfEnrichedAttribute(rule.field!!)

            val column = functions.col(columnName)
            val valueToMatch = rule.value!!.lowercase() // to perform case-free evaluation

            if (ScalaSparkItemsFunctions.isArrayTypeColumn(dataset, columnName)) {
                val columnWithoutNull = nullToEmptyArray(column)
                getArrayFieldValuationFunction(toLowercaseArrayString(columnWithoutNull), valueToMatch, operator!!)
            } else {
                // Spark does not support null comparison. So we have to replace null to empty string before evaluation.
                val columnWithoutNull = nullToEmpty(column)
                getStringFieldEvaluation(toLowerCase(columnWithoutNull), valueToMatch, operator!!)
            }
        } else {
            val applyCombinator = getSparkCombinator(rule.combinator!!)

            rule.rules!!
                .map { translateFromRuleToSparkColumn(dataset, it) }
                .reduce { col1, col2 -> applyCombinator(col1, col2) }
        }
    }

    fun escapeAsSparkColumnName(name: String) = name.lowercase().replace(" ", "_")

    private fun escapeAsSparkColumnNameIfEnrichedAttribute(field: String): String =
        if (Type.getTypeByAttributeName(field) == Type.ENRICHED_ATTRIBUTE) {
            escapeAsSparkColumnName(field)
        } else {
            field
        }

    private fun getSparkCombinator(combinator: String): (Column, Column) -> Column {
        return when (combinator) {
            "and" -> { c1, c2 -> c1.and(c2) }
            "or" -> { c1, c2 -> c1.or(c2) }
            else -> throw RuntimeException("Unsupported rule combinator $combinator found")
        }
    }

    private fun getStringFieldEvaluation(col: Column, value: String, operator: String): Column {
        return when (operator) {
            "=" -> col.equalTo(value)
            "!=" -> !col.equalTo(value)
            "contains" -> col.contains(value)
            "doesNotContain" -> !col.contains(value)
            "beginsWith" -> col.startsWith(value)
            "doesNotBeginWith" -> !col.startsWith(value)
            else -> throw RuntimeException("Unsupported rule operator $operator found")
        }
    }

    private fun createColumns(dataset: Dataset<Row>, colNames: Set<String>): Array<Column> {
        if (colNames.isEmpty()) {
            return emptyArray()
        }

        return colNames.map {
            if (ScalaSparkItemsFunctions.isArrayTypeColumn(dataset, it)) {
                functions.lit(emptyArray<String>()).cast("array<string>").`as`(it)
            } else {
                functions.lit("").`as`(it)
            }
        }.toTypedArray()
    }
}