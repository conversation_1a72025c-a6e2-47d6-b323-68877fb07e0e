package com.prospection.refdata.common.integration

import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Encoder
import org.apache.spark.sql.Row
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.SparkSession
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request

@Component
class S3SparkImportExportHelper(
    @Value("\${application.amazon.s3Bucket}")
    private val bucket: String,
    private val s3Client: S3Client,
    val spark: SparkSession
): SparkImportExportHelper {

    override fun readCsv(path: String): Dataset<Row> {
        return spark.read().option("header", "true")
            .option("quote", "\"")
            .option("escape", "\"") // TODO: this will be different by classification. Following RFC for now
            .csv(s3aPath(path))
    }

    /**
     * if count is 1, then only the path exists without any files under it
     * if count is 0, then the path doesn't even exist
     */
    override fun readParquet(path: String): Dataset<Row>? {
        val response = s3Client.listObjectsV2(
            ListObjectsV2Request.builder().bucket(bucket).prefix(path).build()
        )
        if (response.keyCount() < 2) {
            return null
        }

        return spark.read()
            .parquet(s3aPath(path))
    }

    override fun writeParquet(path: String, ds: Dataset<Row>) {
        ds.write()
            .mode(SaveMode.Overwrite)
            .parquet(s3aPath(path))
    }

    override fun <T> writeParquet(path: String, objectsToWrite: List<T>, encoder: Encoder<T>) {
        val ds: Dataset<T> = spark.createDataset(objectsToWrite, encoder)

        ds.write()
            .mode(SaveMode.Overwrite)
            .parquet(s3aPath(path))
    }

    override fun writeCsv(path: String, ds: Dataset<Row>) {
        ScalaSparkItemsFunctions.joinAllArrayColumns(ds)
            .coalesce(1)
            .write()
            .option("header", "true")
            .option("escape", "\"")
            .mode(SaveMode.Overwrite)
            .csv(s3aPath(path))
    }

    override fun writeExcel(ds: Dataset<Row>, path: String, sheetName: String, sheetIndex: Int) {
        ScalaSparkItemsFunctions.joinAllArrayColumns(ds)
            .write()
            .format("com.crealytics.spark.excel")
            .option("header", true)
            .option("dataAddress", "'${sheetName}'!A1")
            .mode(if (sheetIndex == 0) SaveMode.Overwrite else SaveMode.Append)
            .save(s3aPath(path))
    }

    private fun s3aPath(path: String) = "s3a://$bucket/$path"
}