package com.prospection.refdata.items.integration

import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.items.domain.ItemsSparkPort
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.springframework.stereotype.Component

@Component
class ItemsSparkAdapter(private val importExportHelper: SparkImportExportHelper): ItemsSparkPort {

    override fun getPublishedItems(codingSystem: String, version: String): Dataset<Row>? {
        return importExportHelper.readParquet(
            "${Items.Published.ENRICHED_ITEMS_PARQUET}/${codingSystem}/${
                createVersionPath(
                    version
                )
            }"
        )
    }
}