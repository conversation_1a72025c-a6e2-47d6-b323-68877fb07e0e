package com.prospection.refdata.items.integration

import com.prospection.refdata.common.domain.SparkImportExportHelper
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

internal class ItemsSparkAdapterTest {
    private val mockImportExportHelper: SparkImportExportHelper = mock()

    private val itemsSparkAdapter = ItemsSparkAdapter(mockImportExportHelper)

    @AfterEach
    fun tearDown() {
        reset(mockImportExportHelper)
    }

    @Test
    fun getPublishedItems() {
        // given
        val dataset: Dataset<Row> = mock()
        whenever(mockImportExportHelper.readParquet(any())).thenReturn(dataset)

        // test
        val result = itemsSparkAdapter.getPublishedItems("testCodingSystem", "testVersion")

        // verify
        assertThat(result).isSameAs(dataset)

        val pathCaptor = argumentCaptor<String>()
        verify(mockImportExportHelper, times(1)).readParquet(pathCaptor.capture())
        assertThat(pathCaptor.firstValue).isEqualTo("items/published/enriched-items-parquet/testCodingSystem/version=testVersion")
    }
}