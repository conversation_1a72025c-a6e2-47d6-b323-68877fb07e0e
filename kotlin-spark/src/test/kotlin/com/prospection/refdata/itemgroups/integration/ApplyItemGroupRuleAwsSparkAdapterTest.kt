package com.prospection.refdata.itemgroups.integration

import TestSpark.spark
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.rules.domain.Rule
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.extractor.Extractors
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import java.time.LocalDateTime

internal class ApplyItemGroupRuleAwsSparkAdapterTest {
    private val adapter = ApplyItemGroupRuleAwsSparkAdapter()

    data class TestRow(
        val source_code: String,
        val source_name: String?,
        val brand_type: List<String>,
        val coding_system: String
    )

    @Test
    fun `Should append item group by source attribute`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "Item 1", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            )
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "Item 1", emptyList(), "DRG Dispensing")
                )
            ), itemGroups, listOf(createCodingSystemToClassification("DRG Dispensing"))
        ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(1)
            .first()
            .hasToString("[item_1,group_1,Group 1,DRG Dispensing]")
    }

    @Test
    fun `Should append item group by enriched attribute`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "brand_type", value = "MM", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            )
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "", listOf("MM"), "DRG Dispensing")
                )
            ), itemGroups, listOf(createCodingSystemToClassification("DRG Dispensing"))
        ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(1)
            .first()
            .hasToString("[item_1,group_1,Group 1,DRG Dispensing]")
    }

    @Test
    fun `Should append item group by classification`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "coding_system", value = "DRG Dispensing", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            )
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "", emptyList(), "DRG Dispensing")
                )
            ), itemGroups, listOf(
                createCodingSystemToClassification(
                    classification = "DRG Dispensing Classification1",
                    codingSystem = "DRG Dispensing"
                ),
                createCodingSystemToClassification(
                    classification = "DRG Dispensing Classification2",
                    codingSystem = "DRG Dispensing"
                )
            )
        ).collectAsList()

        assertThat(itemsToItemGroups).extracting(Extractors.toStringMethod())
            .containsExactly(
                "[item_1,group_1,Group 1,DRG Dispensing Classification1]",
                "[item_1,group_1,Group 1,DRG Dispensing Classification2]",
            )
    }

    @Test
    fun `Should apply rules to multiple item rows and filter out items without any item group`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "brand_type", value = "MM", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            )
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "Item 1", listOf("ADHD", "MM"), "DRG Dispensing"),
                    // item_2 is filtered out because it doesn't match with any rule condition
                    TestRow("item_2", "Item 2", listOf("ADHD", "PsO"), "DRG Dispensing"),
                    TestRow("item_3", "Item 3", listOf("MM", "PsO"), "DRG Dispensing"),
                )
            ), itemGroups, listOf(createCodingSystemToClassification("DRG Dispensing"))
        ).collectAsList()

        assertThat(itemsToItemGroups).extracting(Extractors.toStringMethod())
            .containsExactly(
                "[item_1,group_1,Group 1,DRG Dispensing]",
                "[item_3,group_1,Group 1,DRG Dispensing]"
            )
    }

    @Test
    fun `Should explode to multiple rows if an item belongs to multiple item groups`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "Item 1", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            ),
            ItemGroup(
                businessKey = "group_2",
                name = "Group 2",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "Item 2", operator = "!="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            )
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "Item 1", emptyList(), "DRG Dispensing"),
                )
            ), itemGroups, listOf(createCodingSystemToClassification("DRG Dispensing"))
        ).collectAsList()

        assertThat(itemsToItemGroups).extracting(Extractors.toStringMethod())
            .containsAll(
                listOf(
                    "[item_1,group_1,Group 1,DRG Dispensing]",
                    "[item_1,group_2,Group 2,DRG Dispensing]"
                )
            )
    }

    @Test
    fun `Should populate distinct item to item group mappings`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "Item 1", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            )
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "Item 1", emptyList(), "DRG Dispensing"),
                    TestRow("item_1", "Item 1", emptyList(), "DRG Dispensing"),
                    TestRow("item_2", "Item 1", emptyList(), "DRG Dispensing"),
                )
            ), itemGroups, listOf(createCodingSystemToClassification("DRG Dispensing"))
        ).collectAsList()

        assertThat(itemsToItemGroups).extracting(Extractors.toStringMethod())
            .containsExactly(
                "[item_1,group_1,Group 1,DRG Dispensing]",
                "[item_2,group_1,Group 1,DRG Dispensing]"
            )
    }

    @Test
    fun `Should not throw an error if a non-existing source attribute column is in a rule condition`() {
        val itemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_not_existing_name", value = "whatever", operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "thomas"
            ),
        )

        val itemsToItemGroups = adapter.getItemsToItemsGroupsWithoutSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "Item 1", emptyList(), "DRG Dispensing"),
                )
            ), itemGroups, listOf(createCodingSystemToClassification("DRG Dispensing"))
        ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(0)
    }


    @Test
    fun `should get items to item groups with source attribute columns`() {
        val itemGroup = ItemGroup(
            businessKey = "group_1",
            name = "Group 1",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "PBS Item", operator = "="),
                    ),
                    combinator = "and",
                )
            ),
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester"
        )

        val itemsToItemGroups = adapter.getItemsToItemGroupWithSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "source_attribute_1", listOf("brand_1", "brand_2"), "PBS Item"),
                    TestRow("item_2", "source_attribute_2", emptyList(), "PBS Item"),
                    TestRow("item_1", "source_attribute_1", emptyList(), "PBS Item")
                )
            ),
            itemGroup, listOf(
                createCodingSystemToClassification(
                    codingSystem = "PBS Item",
                    classification = "PBS Item Classification"
                ),
                createCodingSystemToClassification(
                    codingSystem = "PBS Item",
                    classification = "PBS Drug Classification",
                    codingSystemColumnToExport = "brand_type"
                ),
            )
        ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(4)
            .extracting(Extractors.toStringMethod())
            .containsExactly(
                "[item_1,source_attribute_1,PBS Item Classification]",
                "[item_2,source_attribute_2,PBS Item Classification]",
                "[brand_1,source_attribute_1,PBS Drug Classification]",
                "[brand_2,source_attribute_1,PBS Drug Classification]",
            )
    }

    @Test
    fun `should ignore missing enriched columns`() {
        val itemGroup = ItemGroup(
            businessKey = "group_1",
            name = "Group 1",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "PBS Item", operator = "="),
                        Rule(field = "not_existing_enriched_attribute", value = "Random", operator = "="),
                    ),
                    combinator = "and",
                )
            ),
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester"
        )

        val itemsToItemGroups = adapter.getItemsToItemGroupWithSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "source_attribute_1", emptyList(), "PBS Item"),
                    TestRow("item_2", "source_attribute_2", emptyList(), "PBS Item"),
                    TestRow("item_1", "source_attribute_1", emptyList(), "PBS Item"),
                )
            ),
            itemGroup,
            listOf(createCodingSystemToClassification("PBS Item"))
        ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(0)
    }

    @Test
    fun `should evaluate item group rule to get items to item groups with source name attribute is null value`() {
        val itemGroup = ItemGroup(
            businessKey = "group_1",
            name = "Group 1",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "PBS Item", operator = "="),
                        Rule(field = "source_name", value = "source_attribute_1", operator = "doesNotContain")
                    ),
                    combinator = "and",
                )
            ),
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester"
        )

        val itemsToItemGroups = adapter.getItemsToItemGroupWithSourceAttributeColumns(
            createDataFrame(
                listOf(
                    TestRow("item_1", "source_attribute_1", emptyList(), "PBS Item"),
                    TestRow("item_2", null, emptyList(), "PBS Item"),
                )
            ),
            itemGroup,
            listOf(
                createCodingSystemToClassification(
                    codingSystem = "PBS Item",
                    classification = "PBS Item Classification"
                )
            )
        ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(1)
            .extracting(Extractors.toStringMethod())
            .containsExactly(
                "[item_2,null,PBS Item Classification]"
            )
    }

    @Test
    fun `apply item group rule should return items have brand type contains selected and not contains not selected value`() {
        val itemGroup = ItemGroup(
            businessKey = "group_1",
            name = "Group 1",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "PBS Drug", operator = "="),
                        Rule(field = "brand_type", value = "selected", operator = "="),
                        Rule(field = "brand_type", value = "not selected", operator = "!=")
                    ),
                    combinator = "and",
                )
            ),
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester"
        )

        val items = createDataFrame(
            listOf(
                TestRow("item_1", "source_attribute_1", listOf("selected"), "PBS Drug"),
                TestRow("item_2", "source_attribute_2", listOf("selected", "not selected"), "PBS Drug"),
                TestRow("item_3", "source_attribute_3", listOf("brand type A"), "PBS Drug"),
                TestRow("item_4", "source_attribute_4", listOf("brand type B", "selected"), "PBS Drug"),
            )
        )

        val itemsToItemGroups =
            adapter.getItemsToItemGroupWithSourceAttributeColumns(
                items,
                itemGroup,
                listOf(createCodingSystemToClassification("PBS Item"))
            ).collectAsList()

        assertThat(itemsToItemGroups).hasSize(2)

        assertThat(itemsToItemGroups[0].getAs<String>("code")).isEqualTo("item_1")
        assertThat(itemsToItemGroups[0].getAs<String>("name")).isEqualTo("source_attribute_1")

        assertThat(itemsToItemGroups[1].getAs<String>("code")).isEqualTo("item_4")
        assertThat(itemsToItemGroups[1].getAs<String>("name")).isEqualTo("source_attribute_4")
    }

    @Test
    fun `should filter out items matches item group rule when previewing item group`() {
        val itemGroup = ItemGroup(
            businessKey = "group_1",
            name = "Group 1",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "PBS Drug", operator = "="),
                        Rule(field = "source_name", value = "source_name_1", operator = "="),
                    ),
                    combinator = "and",
                )
            ),
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester"
        )
        val items = createDataFrame(
            listOf(
                TestRow("item_1", "source_name_1", listOf("brand_1"), "PBS Drug"),
                TestRow("item_2", "source_name_2", listOf("brand_3"), "PBS Drug"),
                TestRow("item_3", "source_name_1", emptyList(), "PBS Drug"),
            )
        )

        val codingSystemToClassifications = listOf(
            createCodingSystemToClassification(codingSystem = "PBS Drug", classification = "PBS Classification 1"),
            createCodingSystemToClassification(
                codingSystem = "PBS Drug",
                classification = "PBS Classification 2",
                codingSystemColumnToExport = "brand_type"
            )
        )

        val itemsToItemGroups =
            adapter.getItemGroupPreview(items, itemGroup, codingSystemToClassifications).sort("source_code")
                .collectAsList()

        assertThat(itemsToItemGroups).hasSize(3)

        assertThat(itemsToItemGroups[0].getAs<String>("source_code")).isEqualTo("brand_1")
        assertThat(itemsToItemGroups[0].getAs<String>("source_name")).isEqualTo("source_name_1")
        assertThat(itemsToItemGroups[0].getAs<String>("classification")).isEqualTo("PBS Classification 2")

        assertThat(itemsToItemGroups[1].getAs<String>("source_code")).isEqualTo("item_1")
        assertThat(itemsToItemGroups[1].getAs<String>("source_name")).isEqualTo("source_name_1")
        assertThat(itemsToItemGroups[1].getAs<String>("classification")).isEqualTo("PBS Classification 1")

        assertThat(itemsToItemGroups[2].getAs<String>("source_code")).isEqualTo("item_3")
        assertThat(itemsToItemGroups[2].getAs<String>("source_name")).isEqualTo("source_name_1")
        assertThat(itemsToItemGroups[2].getAs<String>("classification")).isEqualTo("PBS Classification 1")
    }

    @Test
    fun `should not throw exception when previewing item group of a coding system does not have enriched attribute`() {
        val itemGroup = ItemGroup(
            businessKey = "group_1",
            name = "Group 1",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "PBS Drug", operator = "="),
                        Rule(field = "not_existing_enriched_attribute", value = "Random", operator = "=")
                    ),
                    combinator = "and"
                )
            ),
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester"
        )
        val items = createDataFrame(
            listOf(
                TestRow("item_1", "source_name_1", listOf("brand_1"), "PBS Drug"),
                TestRow("item_3", "source_name_1", emptyList(), "PBS Drug"),
            )
        )

        val codingSystemToClassifications = listOf(
            createCodingSystemToClassification(codingSystem = "PBS Drug", classification = "PBS Classification 1"),
            createCodingSystemToClassification(
                codingSystem = "PBS Drug",
                classification = "PBS Classification 2",
                codingSystemColumnToExport = "brand_type"
            )
        )

        assertDoesNotThrow { adapter.getItemGroupPreview(items, itemGroup, codingSystemToClassifications) }
    }

    private fun createDataFrame(rows: List<TestRow>): Dataset<Row> {
        return spark.createDataFrame(rows, TestRow::class.java)
    }

    private fun createCodingSystemToClassification(
        codingSystem: String,
        classification: String = codingSystem,
        codingSystemColumnToExport: String = "source_code"
    ): CodingSystemToClassification {
        return CodingSystemToClassification(
            codingSystem = codingSystem,
            classification = classification,
            codingSystemColumnToExport = codingSystemColumnToExport
        )
    }

}