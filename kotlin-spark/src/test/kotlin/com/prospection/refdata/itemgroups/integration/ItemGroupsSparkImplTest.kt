package com.prospection.refdata.itemgroups.integration

import TestSpark.spark
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.items.domain.ItemsSparkPort
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

internal class ItemGroupsSparkImplTest {
    private val mockItemsSparkPort: ItemsSparkPort = mock()
    private val mockApplyItemGroupRulePort: ApplyItemGroupRulePort = mock()

    private val itemGroupsSparkImpl = ItemGroupsSparkImpl(mockItemsSparkPort, mockApplyItemGroupRulePort)

    @AfterEach
    fun cleanUp() {
        reset(mockItemsSparkPort, mockApplyItemGroupRulePort)
    }

    @Test
    fun `preview item group should return two coding systems with valid columns and rows`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        val codingSystemC = "C"
        val codingSystemD = "D"
        val codingSystemToClassifications = mapOf(
            codingSystemA to listOf(createCodingSystemToClassification(codingSystemA)),
            codingSystemB to listOf(createCodingSystemToClassification(codingSystemB)),
            codingSystemC to listOf(createCodingSystemToClassification(codingSystemC)),
            codingSystemD to listOf(createCodingSystemToClassification(codingSystemD))
        )

        val publishVersion = "2021-12-20"
        val publishedItemsA = spark.emptyDataFrame().alias(codingSystemA)
        doReturn(publishedItemsA).whenever(mockItemsSparkPort).getPublishedItems(eq(codingSystemA), eq(publishVersion))
        val publishedItemsB = spark.emptyDataFrame().alias(codingSystemB)
        doReturn(publishedItemsB).whenever(mockItemsSparkPort).getPublishedItems(eq(codingSystemB), eq(publishVersion))
        val publishedItemsC = spark.emptyDataFrame().alias(codingSystemC)
        doReturn(publishedItemsC).whenever(mockItemsSparkPort).getPublishedItems(eq(codingSystemC), eq(publishVersion))
        doReturn(null).whenever(mockItemsSparkPort).getPublishedItems(eq(codingSystemD), eq(publishVersion))

        val itemsOfCodingSystemA = createItemData((1..20).map { ItemRow("$it", "item_group_$it", codingSystemA) }, ItemRow::class.java)
        val itemsOfCodingSystemB = createItemData((1..80).map { ItemRow2("$it", "item_group_$it", "dose_$it", codingSystemB) }, ItemRow2::class.java)
        val itemsOfCodingSystemC = spark.emptyDataFrame()

        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""")
        doReturn(itemsOfCodingSystemA).whenever(mockApplyItemGroupRulePort)
            .getItemGroupPreview(eq(publishedItemsA), eq(itemGroup), eq(codingSystemToClassifications[codingSystemA]!!))
        doReturn(itemsOfCodingSystemB).whenever(mockApplyItemGroupRulePort)
            .getItemGroupPreview(eq(publishedItemsB), eq(itemGroup), eq(codingSystemToClassifications[codingSystemB]!!))
        doReturn(itemsOfCodingSystemC).whenever(mockApplyItemGroupRulePort)
            .getItemGroupPreview(eq(publishedItemsC), eq(itemGroup), eq(codingSystemToClassifications[codingSystemC]!!))

        val result = itemGroupsSparkImpl.getItemGroupPreview(itemGroup, codingSystemToClassifications, publishVersion)

        assertEquals(2, result.size)

        val itemGroupPreviewCodingSystemA = result[0]
        assertEquals(codingSystemA, itemGroupPreviewCodingSystemA.codingSystem)
        assertEquals(20, itemGroupPreviewCodingSystemA.totalRowCount)
        assertEquals(setOf("source_code", "source_name"), itemGroupPreviewCodingSystemA.columns.toSet())
        assertEquals(20, itemGroupPreviewCodingSystemA.rows.size)
        assertEquals(
            mapOf("source_code" to "1", "source_name" to "item_group_1"),
            itemGroupPreviewCodingSystemA.rows.first()
        )

        val itemGroupPreviewCodingSystemB = result[1]
        assertEquals(codingSystemB, itemGroupPreviewCodingSystemB.codingSystem)
        assertEquals(setOf("source_code", "source_name", "source_dose"), itemGroupPreviewCodingSystemB.columns.toSet())
        assertEquals(80, itemGroupPreviewCodingSystemB.totalRowCount)
        assertEquals(80, itemGroupPreviewCodingSystemB.rows.size)
        assertEquals(
            mapOf("source_code" to "1", "source_name" to "item_group_1", "source_dose" to "dose_1"),
            itemGroupPreviewCodingSystemB.rows.first()
        )

        verify(mockItemsSparkPort, times(4)).getPublishedItems(any(), any())
        verify(mockApplyItemGroupRulePort, times(3)).getItemGroupPreview(any(), any(), any())
    }

    @Test
    fun `should return maximum of 100 items per coding system matches item group rule when previewing item group`() {
        val codingSystemA = "A"
        val codingSystemToClassifications = mapOf(
            codingSystemA to listOf(createCodingSystemToClassification(codingSystemA))
        )

        val publishVersion = "2021-12-20"
        val publishedItemsA = spark.emptyDataFrame().alias(codingSystemA)
        doReturn(publishedItemsA).whenever(mockItemsSparkPort).getPublishedItems(eq(codingSystemA), eq(publishVersion))

        val itemsOfCodingSystemA = createItemData((1..120).map { ItemRow("$it", "item_group_$it", codingSystemA) }, ItemRow::class.java)

        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""")
        doReturn(itemsOfCodingSystemA).whenever(mockApplyItemGroupRulePort)
            .getItemGroupPreview(eq(publishedItemsA), eq(itemGroup), eq(codingSystemToClassifications[codingSystemA]!!))

        val result = itemGroupsSparkImpl.getItemGroupPreview(itemGroup, codingSystemToClassifications, publishVersion)

        assertEquals(1, result.size)

        val itemGroupPreviewCodingSystemA = result[0]
        assertEquals(codingSystemA, itemGroupPreviewCodingSystemA.codingSystem)
        assertEquals(120, itemGroupPreviewCodingSystemA.totalRowCount)
        assertEquals(setOf("source_code", "source_name"), itemGroupPreviewCodingSystemA.columns.toSet())
        assertEquals(100, itemGroupPreviewCodingSystemA.rows.size)
        assertEquals(
            mapOf("source_code" to "1", "source_name" to "item_group_1"),
            itemGroupPreviewCodingSystemA.rows.first()
        )

        verify(mockItemsSparkPort, times(1)).getPublishedItems(any(), any())
        verify(mockApplyItemGroupRulePort, times(1)).getItemGroupPreview(any(), any(), any())
    }

    data class ItemRow(val source_code: String, val source_name: String, val coding_system: String)
    data class ItemRow2(val source_code: String, val source_name: String, val source_dose: String, val coding_system: String)

    private fun <T> createItemData(rows: List<T>, clazz: Class<T>): Dataset<Row> {
        return spark.createDataFrame(rows, clazz)
    }

    private fun createCodingSystemToClassification(
        codingSystemName: String,
        classification: String = codingSystemName,
        codingSystemColumnToExport: String = "source_code"
    ): CodingSystemToClassification {
        return CodingSystemToClassification(
            codingSystem = codingSystemName,
            classification = classification,
            codingSystemColumnToExport = codingSystemColumnToExport
        )
    }

}