package com.prospection.refdata.common

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.Row
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.AfterAll
import java.io.File

/**
 * Test to verify that the ArtifactManager workaround for Spark 4.0 bug (SPARK-52396) works correctly.
 * 
 * This test specifically checks that Spark can initialize and perform operations that trigger
 * ArtifactManager without encountering permission issues in Docker environments.
 */
class ArtifactManagerWorkaroundTest {

    companion object {
        private lateinit var spark: SparkSession

        @BeforeAll
        @JvmStatic
        fun setUp() {
            // Apply the workaround before initializing Spark
            applyArtifactManagerWorkaround()
            
            // Initialize Spark with minimal configuration
            spark = SparkSession.builder()
                .appName("ArtifactManager Workaround Test")
                .master("local[1]")
                .config("spark.sql.shuffle.partitions", "1")
                .config("spark.default.parallelism", "1")
                .config("spark.sql.adaptive.enabled", "false")
                .config("spark.shuffle.compress", "false")
                .config("spark.broadcast.compress", "false")
                .config("spark.ui.enabled", "false")
                .config("spark.local.dir", "/tmp/spark-test")
                .config("spark.sql.warehouse.dir", "/tmp/spark-test/warehouse")
                .config("spark.sql.artifact.dir", "/tmp/spark-artifacts")
                .config("spark.sql.artifact.root.dir", "/tmp/spark-artifacts")
                .getOrCreate()
            
            spark.sparkContext().setLogLevel("WARN")
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            if (::spark.isInitialized) {
                spark.stop()
            }
        }

        /**
         * WORKAROUND for Spark 4.0 ArtifactManager bug (SPARK-52396)
         * Fixed in Spark 4.0.1: https://github.com/apache/spark/pull/51083
         */
        private fun applyArtifactManagerWorkaround() {
            println("Test: Applying ArtifactManager workaround for Spark 4.0 bug (SPARK-52396)")
            
            // Set system properties to influence temp directory creation
            System.setProperty("java.io.tmpdir", "/tmp")
            System.setProperty("user.dir", "/tmp")
            
            // Create artifacts directory in temp location to ensure it exists
            val artifactsDir = File("/tmp/spark-artifacts")
            artifactsDir.mkdirs()
            artifactsDir.setWritable(true, false)
            artifactsDir.setReadable(true, false)
            artifactsDir.setExecutable(true, false)
            
            println("Test: ArtifactManager workaround applied")
        }
    }

    @Test
    fun `test spark session initialization with artifact manager workaround`() {
        // This test verifies that Spark can initialize without ArtifactManager permission issues
        assert(spark.sparkContext().isLocal)
        println("✓ Spark session initialized successfully")
    }

    @Test
    fun `test dataframe operations that may trigger artifact manager`() {
        // Create a simple DataFrame and perform operations that might trigger ArtifactManager
        val df = spark.range(10).toDF("id")
        
        // Persist the DataFrame (this can trigger ArtifactManager in some scenarios)
        df.persist()
        
        try {
            val count = df.count()
            assert(count == 10L)
            println("✓ DataFrame operations completed successfully, count: $count")
        } finally {
            df.unpersist()
        }
    }

    @Test
    fun `test sql operations that may trigger artifact manager`() {
        // Create a simple SQL operation
        val result = spark.sql("SELECT 1 + 1 as result").count()
        assert(result == 1L)
        println("✓ SQL operations completed successfully, result count: $result")
    }

    @Test
    fun `test artifact directory exists and is writable`() {
        val artifactsDir = File("/tmp/spark-artifacts")
        assert(artifactsDir.exists()) { "Artifacts directory should exist" }
        assert(artifactsDir.isDirectory) { "Artifacts path should be a directory" }
        assert(artifactsDir.canWrite()) { "Artifacts directory should be writable" }
        assert(artifactsDir.canRead()) { "Artifacts directory should be readable" }
        println("✓ Artifacts directory is properly configured")
    }
}
