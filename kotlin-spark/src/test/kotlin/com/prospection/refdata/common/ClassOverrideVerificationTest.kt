package com.prospection.refdata.common

import org.apache.spark.sql.SparkSession
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.AfterAll
import java.io.File

/**
 * Critical test to verify whether our ArtifactManager class override is actually being used
 * by Spark internals, not just by our application code.
 * 
 * This addresses the concern that while our application code might load our custom
 * ArtifactManager, other classes within the Spark JAR itself might still reference
 * the original buggy version.
 */
class ClassOverrideVerificationTest {

    companion object {
        @BeforeAll
        @JvmStatic
        fun setUp() {
            // Clean up any existing temp directories to ensure fresh test
            val tempDirs = listOf("/tmp/artifacts", "/tmp/spark-artifacts")
            tempDirs.forEach { path ->
                val dir = File(path)
                if (dir.exists()) {
                    dir.deleteRecursively()
                }
            }
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            // Clean up after test
        }
    }

    @Test
    fun `test that spark internals use our fixed artifact manager`() {
        println("=== Testing Class Override Verification ===")
        
        // This test will fail with the original ArtifactManager bug if our override isn't working
        // The key is that we're NOT setting any system properties or workarounds
        // We're relying ONLY on the class override
        
        try {
            println("Creating Spark session without any workarounds...")
            
            val spark = SparkSession.builder()
                .appName("Class Override Verification Test")
                .master("local[1]")
                .config("spark.sql.shuffle.partitions", "1")
                .config("spark.default.parallelism", "1")
                .config("spark.sql.adaptive.enabled", "false")
                .config("spark.shuffle.compress", "false")
                .config("spark.broadcast.compress", "false")
                .config("spark.ui.enabled", "false")
                .config("spark.local.dir", "/tmp/spark-test")
                .config("spark.sql.warehouse.dir", "/tmp/spark-test/warehouse")
                .getOrCreate()
            
            spark.sparkContext().setLogLevel("WARN")
            
            println("✓ Spark session created successfully!")
            
            // Force operations that would trigger ArtifactManager
            println("Testing DataFrame operations...")
            val df = spark.range(10)
            val count = df.count()
            println("✓ DataFrame count: $count")
            
            // Test SQL operations
            println("Testing SQL operations...")
            val sqlCount = spark.sql("SELECT 1 + 1 as result").count()
            println("✓ SQL result count: $sqlCount")
            
            // Test operations that might trigger artifact management
            println("Testing operations that trigger artifact management...")
            df.persist()
            val persistedCount = df.count()
            df.unpersist()
            println("✓ Persisted DataFrame count: $persistedCount")
            
            spark.stop()
            println("✓ All operations completed successfully!")
            println("✓ Our class override is working - no ArtifactManager permission errors!")
            
        } catch (e: Exception) {
            if (e.message?.contains("Failed to create a temp directory") == true ||
                e.message?.contains("artifacts") == true) {
                throw AssertionError(
                    "CRITICAL: Our class override is NOT working! " +
                    "Spark internals are still using the original buggy ArtifactManager. " +
                    "Error: ${e.message}", e
                )
            } else {
                // Some other error - might be unrelated to ArtifactManager
                println("Unexpected error (might be unrelated to ArtifactManager): ${e.message}")
                throw e
            }
        }
    }

    @Test
    fun `test artifact directory location without spark session`() {
        println("=== Testing ArtifactManager Companion Object Directly ===")
        
        try {
            // Access the ArtifactManager companion object directly
            val artifactManagerClass = Class.forName("org.apache.spark.sql.artifact.ArtifactManager\$")
            val companionObject = artifactManagerClass.getField("MODULE\$").get(null)
            
            // This should trigger the lazy initialization of artifactRootDirectory
            val artifactRootDirectoryMethod = artifactManagerClass.getDeclaredMethod("artifactRootDirectory")
            artifactRootDirectoryMethod.isAccessible = true
            
            println("Calling artifactRootDirectory method...")
            val artifactRootDirectory = artifactRootDirectoryMethod.invoke(companionObject) as java.nio.file.Path
            
            val artifactRootPath = artifactRootDirectory.toString()
            println("Artifact root directory: $artifactRootPath")
            
            // Verify it's in the system temp directory (our fix)
            if (artifactRootPath.startsWith("/tmp")) {
                println("✓ SUCCESS: ArtifactManager is using system temp directory")
                println("✓ Our class override is working at the companion object level")
            } else {
                throw AssertionError(
                    "FAILURE: ArtifactManager is NOT using system temp directory. " +
                    "Path: $artifactRootPath. This suggests our class override is not working."
                )
            }
            
            // Verify the directory exists and is writable
            val artifactDir = artifactRootDirectory.toFile()
            if (!artifactDir.exists()) {
                throw AssertionError("Artifact directory was not created: $artifactRootPath")
            }
            
            if (!artifactDir.canWrite()) {
                throw AssertionError("Artifact directory is not writable: $artifactRootPath")
            }
            
            println("✓ Artifact directory exists and is writable")
            
        } catch (e: Exception) {
            if (e.message?.contains("Failed to create a temp directory") == true) {
                throw AssertionError(
                    "CRITICAL: Original ArtifactManager bug is still present! " +
                    "Our class override is not working. Error: ${e.message}", e
                )
            } else if (e is AssertionError) {
                throw e
            } else {
                throw AssertionError("Unexpected error accessing ArtifactManager: ${e.message}", e)
            }
        }
    }

    @Test
    fun `test class loading source verification`() {
        println("=== Testing Class Loading Source ===")
        
        val artifactManagerClass = Class.forName("org.apache.spark.sql.artifact.ArtifactManager")
        val codeSource = artifactManagerClass.protectionDomain?.codeSource?.location
        
        println("ArtifactManager class loaded from: $codeSource")
        
        val codeSourcePath = codeSource?.path ?: ""
        val isFromOurProject = codeSourcePath.contains("kotlin-spark") || 
                              codeSourcePath.contains("build/classes") ||
                              codeSourcePath.contains("build\\classes")
        
        println("Is ArtifactManager loaded from our project? $isFromOurProject")
        
        if (isFromOurProject) {
            println("✓ SUCCESS: ArtifactManager is loaded from our project")
            println("✓ Class override is working at the classloader level")
        } else {
            println("⚠ WARNING: ArtifactManager appears to be loaded from Spark JAR")
            println("This suggests our class override might not be working as expected")
            println("However, the functionality test is more important than the source location")
        }
    }
}
