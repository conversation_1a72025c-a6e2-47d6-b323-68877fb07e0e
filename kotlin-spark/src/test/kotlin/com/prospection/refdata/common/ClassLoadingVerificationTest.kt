package com.prospection.refdata.common

import org.apache.spark.sql.SparkSession
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.AfterAll
import java.io.File
import java.lang.reflect.Method

/**
 * Test to verify that our custom ArtifactManager class is actually being used
 * by all parts of Spark, including internal Spark classes.
 * 
 * This addresses the concern that while our application code might load our custom
 * ArtifactManager, other classes within the Spark JAR itself might still reference
 * the original buggy version.
 */
class ClassLoadingVerificationTest {

    companion object {
        private lateinit var spark: SparkSession

        @BeforeAll
        @JvmStatic
        fun setUp() {
            // Initialize Spark with minimal configuration
            spark = SparkSession.builder()
                .appName("Class Loading Verification Test")
                .master("local[1]")
                .config("spark.sql.shuffle.partitions", "1")
                .config("spark.default.parallelism", "1")
                .config("spark.sql.adaptive.enabled", "false")
                .config("spark.shuffle.compress", "false")
                .config("spark.broadcast.compress", "false")
                .config("spark.ui.enabled", "false")
                .config("spark.local.dir", "/tmp/spark-test")
                .config("spark.sql.warehouse.dir", "/tmp/spark-test/warehouse")
                .getOrCreate()
            
            spark.sparkContext().setLogLevel("WARN")
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            if (::spark.isInitialized) {
                spark.stop()
            }
        }
    }

    @Test
    fun `test artifact manager class source location`() {
        // Verify which JAR/location the ArtifactManager class is loaded from
        val artifactManagerClass = Class.forName("org.apache.spark.sql.artifact.ArtifactManager")
        val codeSource = artifactManagerClass.protectionDomain?.codeSource?.location
        
        println("ArtifactManager class loaded from: $codeSource")
        
        // Check if it's loaded from our project (not from a Spark JAR)
        val codeSourcePath = codeSource?.path ?: ""
        val isFromOurProject = codeSourcePath.contains("kotlin-spark") || 
                              codeSourcePath.contains("build/classes") ||
                              codeSourcePath.contains("build\\classes")
        
        println("Is ArtifactManager loaded from our project? $isFromOurProject")
        
        if (!isFromOurProject) {
            println("WARNING: ArtifactManager appears to be loaded from Spark JAR: $codeSourcePath")
            println("This suggests our class override might not be working as expected")
        }
    }

    @Test
    fun `test artifact manager companion object accessibility`() {
        // Test that we can access our fixed companion object
        try {
            val artifactManagerClass = Class.forName("org.apache.spark.sql.artifact.ArtifactManager\$")
            val companionObject = artifactManagerClass.getField("MODULE\$").get(null)
            
            // Try to access the artifactRootDirectory method
            val artifactRootDirectoryMethod = artifactManagerClass.getDeclaredMethod("artifactRootDirectory")
            artifactRootDirectoryMethod.isAccessible = true
            val artifactRootDirectory = artifactRootDirectoryMethod.invoke(companionObject) as java.nio.file.Path
            
            println("Successfully accessed artifactRootDirectory: $artifactRootDirectory")
            
            // Verify it's in the system temp directory (our fix)
            val artifactRootPath = artifactRootDirectory.toString()
            assert(artifactRootPath.startsWith("/tmp")) { 
                "ArtifactManager should use system temp directory, but got: $artifactRootPath" 
            }
            
            println("✓ ArtifactManager companion object is using our fixed implementation")
        } catch (e: Exception) {
            throw AssertionError("Failed to access ArtifactManager companion object: ${e.message}", e)
        }
    }

    @Test
    fun `test spark session state uses our artifact manager`() {
        // Test that SparkSession.sessionState uses our ArtifactManager
        val sessionState = spark.sessionState()
        val artifactManager = sessionState.artifactManager()
        
        // Use reflection to check the actual class
        val artifactManagerClass = artifactManager.javaClass
        println("SessionState.artifactManager class: ${artifactManagerClass.name}")
        println("SessionState.artifactManager loaded from: ${artifactManagerClass.protectionDomain?.codeSource?.location}")
        
        // Verify it's our implementation by checking if we can access our fixed methods
        try {
            // Access the artifactRootPath method which should use our fixed companion object
            val artifactRootPathMethod = artifactManagerClass.getDeclaredMethod("artifactRootPath")
            artifactRootPathMethod.isAccessible = true
            val artifactRootPath = artifactRootPathMethod.invoke(artifactManager) as java.nio.file.Path
            
            println("SessionState artifactManager.artifactRootPath: $artifactRootPath")
            
            // This should be in /tmp if our fix is working
            val pathString = artifactRootPath.toString()
            assert(pathString.startsWith("/tmp")) {
                "SessionState artifactManager should use system temp directory, but got: $pathString"
            }
            
            println("✓ SessionState is using our fixed ArtifactManager implementation")
        } catch (e: Exception) {
            throw AssertionError("Failed to verify SessionState ArtifactManager: ${e.message}", e)
        }
    }

    @Test
    fun `test class loader hierarchy and precedence`() {
        // Examine the class loader hierarchy to understand how classes are loaded
        val artifactManagerClass = Class.forName("org.apache.spark.sql.artifact.ArtifactManager")
        val classLoader = artifactManagerClass.classLoader
        
        println("ArtifactManager ClassLoader: ${classLoader.javaClass.name}")
        println("ArtifactManager ClassLoader: $classLoader")
        
        // Walk up the class loader hierarchy
        var currentLoader = classLoader
        var level = 0
        while (currentLoader != null && level < 10) {
            println("ClassLoader level $level: ${currentLoader.javaClass.name} - $currentLoader")
            currentLoader = currentLoader.parent
            level++
        }
        
        // Check if our classes directory is in the classpath
        val systemClassPath = System.getProperty("java.class.path")
        println("System classpath contains our build directory: ${systemClassPath.contains("kotlin-spark")}")
    }

    @Test
    fun `test artifact directory creation actually works`() {
        // This is the ultimate test - try to trigger the actual artifact directory creation
        // that was causing the original error
        
        try {
            // Force creation of artifact directories by accessing the companion object
            val artifactManagerClass = Class.forName("org.apache.spark.sql.artifact.ArtifactManager\$")
            val companionObject = artifactManagerClass.getField("MODULE\$").get(null)
            val artifactRootDirectoryMethod = artifactManagerClass.getDeclaredMethod("artifactRootDirectory")
            artifactRootDirectoryMethod.isAccessible = true
            
            // This call should trigger the lazy initialization of artifactRootDirectory
            val artifactRootDirectory = artifactRootDirectoryMethod.invoke(companionObject) as java.nio.file.Path
            
            // Verify the directory exists and is writable
            val artifactDir = artifactRootDirectory.toFile()
            assert(artifactDir.exists()) { "Artifact directory should exist: $artifactRootDirectory" }
            assert(artifactDir.isDirectory) { "Artifact path should be a directory: $artifactRootDirectory" }
            assert(artifactDir.canWrite()) { "Artifact directory should be writable: $artifactRootDirectory" }
            
            println("✓ Artifact directory creation successful: $artifactRootDirectory")
            println("✓ No permission errors occurred - our fix is working!")
            
        } catch (e: Exception) {
            if (e.message?.contains("Failed to create a temp directory") == true) {
                throw AssertionError("ORIGINAL BUG STILL PRESENT: Our class override is not working! Error: ${e.message}", e)
            } else {
                throw AssertionError("Unexpected error during artifact directory creation: ${e.message}", e)
            }
        }
    }

    @Test
    fun `test multiple spark sessions use our artifact manager`() {
        // Test that new Spark sessions also use our fixed ArtifactManager
        try {
            val newSession = spark.newSession()
            val newSessionState = newSession.sessionState()
            val newArtifactManager = newSessionState.artifactManager()
            
            // Check that the new session's artifact manager also uses our implementation
            val artifactManagerClass = newArtifactManager.javaClass
            println("New session ArtifactManager class: ${artifactManagerClass.name}")
            
            // Verify it uses our fixed implementation
            val artifactRootPathMethod = artifactManagerClass.getDeclaredMethod("artifactRootPath")
            artifactRootPathMethod.isAccessible = true
            val artifactRootPath = artifactRootPathMethod.invoke(newArtifactManager) as java.nio.file.Path
            
            val pathString = artifactRootPath.toString()
            assert(pathString.startsWith("/tmp")) {
                "New session ArtifactManager should use system temp directory, but got: $pathString"
            }
            
            newSession.stop()
            println("✓ New Spark sessions also use our fixed ArtifactManager implementation")
            
        } catch (e: Exception) {
            println("New session test failed (might be expected in some environments): ${e.message}")
            // Don't fail the test as new session creation might not be supported in all environments
        }
    }
}
