# Spark 4.0 ArtifactManager Fix

## Problem Description

Apache Spark 4.0 introduced a bug in the `ArtifactManager` class (SPARK-52396) where it attempts to create temporary directories in the current working directory instead of the system temp directory. This causes permission issues in Docker environments where the working directory is read-only.

### Error Symptoms
```
java.io.IOException: Failed to create a temp directory (under artifacts) after 10 attempts!
	at org.apache.spark.network.util.JavaUtils.createDirectory(JavaUtils.java:411)
	at org.apache.spark.util.SparkFileUtils.createDirectory(SparkFileUtils.scala:95)
	at org.apache.spark.util.SparkFileUtils.createDirectory$(SparkFileUtils.scala:94)
	at org.apache.spark.util.Utils$.createDirectory(Utils.scala:99)
	at org.apache.spark.util.Utils$.createTempDir(Utils.scala:249)
	at org.apache.spark.sql.artifact.ArtifactManager$.artifactRootDirectory$lzycompute(ArtifactManager.scala:468)
```

## Root Cause

The issue is in `ArtifactManager.scala` line 468:
```scala
Utils.createTempDir(ARTIFACT_DIRECTORY_PREFIX).toPath
```

This should be:
```scala
Utils.createTempDir(namePrefix = ARTIFACT_DIRECTORY_PREFIX).toPath
```

## Official Fix Status

- **Fixed in**: Apache Spark 4.0.1 and 4.1.0
- **GitHub PR**: https://github.com/apache/spark/pull/51083
- **Expected Release**: August/September 2025 for Spark 4.0.1

## Fix Implementation

We have implemented a **class override approach** that replaces the problematic `ArtifactManager` class with our own fixed version. This approach is elegant, transparent, and requires no changes to existing application code.

### Files Added

1. **kotlin-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala**
   - Complete replacement of the problematic Spark class
   - Contains the critical fix for SPARK-52396
   - Loaded via classpath precedence

2. **lambdas-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala**
   - Copy of the fixed class for Lambda deployments
   - Ensures consistent behavior across all environments

### How the Class Override Works

1. **Classpath Precedence**: When the JVM loads `org.apache.spark.sql.artifact.ArtifactManager`, it finds our version first in the classpath before the one in the Spark JAR

2. **Fixed Implementation**: Our version contains the critical fix:
   ```scala
   // Original buggy line:
   Utils.createTempDir(ARTIFACT_DIRECTORY_PREFIX).toPath

   // Fixed line in our version:
   Utils.createTempDir(namePrefix = ARTIFACT_DIRECTORY_PREFIX).toPath
   ```

3. **Complete Override**: The entire class is replaced, ensuring all methods work correctly together

4. **Transparent Operation**: No changes needed to existing application code - the fix is applied automatically

### Advantages of Class Override Approach

- **Direct Fix**: Fixes the exact problem at its source
- **No System Property Manipulation**: Cleaner than JVM property workarounds
- **Transparent**: No changes needed to existing application code
- **Minimal Impact**: Only affects the specific problematic class
- **Future-Proof**: Easy to remove when Spark 4.0.1 is released

## Verification

We have created a comprehensive test suite to verify the workaround:

**Test File**: `kotlin-spark/src/test/kotlin/com/prospection/refdata/common/ArtifactManagerWorkaroundTest.kt`

**Test Coverage**:
- Spark session initialization without permission errors
- DataFrame operations that may trigger ArtifactManager
- SQL operations that may trigger ArtifactManager
- Verification of artifacts directory configuration

**Test Results**: ✅ All tests pass successfully

## Usage Instructions

### For New Spark Applications

No changes are required! The fix is applied automatically through classpath precedence.

### For Existing Applications

The fix is already integrated and working:
- All Spark sessions automatically use the fixed ArtifactManager class
- No code changes required
- No configuration changes needed

The fix is completely transparent to your application code.

## Docker Configuration

Ensure your Dockerfile creates the necessary directories:

```dockerfile
# Create all necessary directories for Spark
RUN mkdir -p /tmp/spark-temp && \
    mkdir -p /tmp/spark-temp/warehouse && \
    mkdir -p /tmp/spark-temp/checkpoints && \
    mkdir -p /tmp/spark-temp/work && \
    mkdir -p /tmp/spark-temp/spark-artifacts && \
    # Set permissions
    chmod -R 777 /tmp/spark-temp
```

## Migration Path

### When Spark 4.0.1 is Released

1. Upgrade to Spark 4.0.1 or later
2. Remove our custom ArtifactManager.scala files:
   - `kotlin-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`
   - `lambdas-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`
3. Test thoroughly to ensure the official fix works

### Monitoring

- Monitor Spark release notes for 4.0.1 availability
- Test with 4.0.1 release candidates when available
- Keep our custom class files until official fix is verified

## Performance Impact

The class override has zero performance impact:
- No additional overhead during Spark initialization
- No runtime performance degradation
- Uses the exact same logic as the official fix

## Compatibility

- **Spark Version**: 4.0.0 (workaround required)
- **Java Version**: Compatible with all supported Java versions
- **Docker**: Works in all Docker environments
- **AWS Lambda**: Fully compatible with Lambda execution environment

## References

- **Spark JIRA**: [SPARK-52396](https://issues.apache.org/jira/browse/SPARK-52396)
- **GitHub PR**: https://github.com/apache/spark/pull/51083
- **Spark Documentation**: https://spark.apache.org/docs/latest/
