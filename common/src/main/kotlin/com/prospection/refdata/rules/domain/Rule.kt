package com.prospection.refdata.rules.domain

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.prospection.refdata.rules.domain.Type.CODING_SYSTEM
import com.prospection.refdata.rules.domain.Type.Companion.getTypeByAttributeName

/**
 * A rule can be either aggregation rule or leaf rule
 * An aggregation rule nests one or more leaf rules and has combinator/rules
 * A leaf rule contains actual evaluation logic and has field/value/operator
 *
 * Example JSON can be found on https://react-querybuilder.js.org/react-querybuilder/
 */
@JsonIgnoreProperties
data class Rule(
    @JsonProperty("field") val field: String? = null,
    @JsonProperty("value") val value: String? = null,
    @JsonProperty("operator") val operator: String? = null,
    @JsonProperty("combinator") val combinator: String? = null,
    @JsonProperty("rules") val rules: List<Rule>? = null
) {
    companion object {
        private fun getAllRules(rule: Rule): List<Rule> {
            val resultList = mutableListOf<Rule>()
            resultList.add(rule)

            if (!rule.isLeafRule()) {
                resultList.addAll(rule.rules!!.flatMap { getAllRules(it) })
            }

            return resultList
        }
    }

    fun isLeafRule(): Boolean {
        return field != null
    }

    @JsonIgnore
    fun getAllRules(): List<Rule> {
        return getAllRules(this)
    }


    @JsonIgnore
    fun canResolveToCodingSystem(codingSystem: String): Boolean {
        val resolvableCodingSystems = resolvableFieldsOfType(this, CODING_SYSTEM)
        if (resolvableCodingSystems.contains(codingSystem) || resolvableCodingSystems.isEmpty()) {
            return true
        }
        return false
    }

    /**
     * Determines which field type (e.g. classification) a rule can resolve to. If the rule can be applied to any field/type
     * then it will return an empty list.
     */
    @JsonIgnore
    fun resolvableFieldsOfType(rule: Rule, type: Type): Set<String> {
        var values: Set<String> = emptySet()
        if (rule.field != null && getTypeByAttributeName(rule.field) == type) {
            values = values + rule.value!!
        }
        val resolvableValues =
            if (!rule.isLeafRule()) rule.rules!!.map { resolvableFieldsOfType(it, type) } else emptySet()

        if (rule.combinator == "or" && resolvableValues.any { it.isEmpty() }) {
            return emptySet()
        }
        return values + resolvableValues.flatten().toSet()
    }


    @JsonIgnore
    fun getEnrichedAttributes(): Set<String> {
        return getAllRules()
            .filter { it.isLeafRule() && getTypeByAttributeName(it.field!!) == Type.ENRICHED_ATTRIBUTE }
            .mapNotNull { it.field }
            .toSet()

    }

}