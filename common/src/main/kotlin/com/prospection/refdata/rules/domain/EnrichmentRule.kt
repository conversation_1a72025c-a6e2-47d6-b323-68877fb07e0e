package com.prospection.refdata.rules.domain

import com.prospection.refdata.common.domain.HasRule
import java.time.LocalDateTime

data class EnrichmentRule(
    val id: String? = null,
    val enrichedAttributeValue: EnrichedAttributeValue,
    val goal: String? = null,
    var version: Int = 0,
    val lastModifiedBy: String? = null,
    val lastModifiedAt: LocalDateTime? = null,
    val deleted: Int = 0,
    override val rule: String,
) : HasRule {
    override val deserialisedRule: Rule = getDeserialisedRule(rule)

    fun isNewRule(): Boolean {
        return id == null
    }

    fun toSimpleName(): String {
        return "(${enrichedAttributeValue.enrichedAttribute.name}, ${enrichedAttributeValue.value})"
    }
}

