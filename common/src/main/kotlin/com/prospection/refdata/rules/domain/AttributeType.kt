package com.prospection.refdata.rules.domain

import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.common.consts.SourceAttribute.PREFIX_SOURCE_ATTRIBUTE


data class AttributeType(
    val type: Type,
    val attributes: List<Attribute>
)

data class Attribute(
    val name: String,
    val values: List<String>,

    // fields for source attribute only
    val hasAllCodingSystems: Boolean? = null,
    val codingSystems: List<String>? = null,
    val operators: List<String>? = null,
)

enum class Type {
    CODING_SYSTEM,
    SOURCE_ATTRIBUTE,
    ENRICHED_ATTRIBUTE;

    companion object {
        fun getTypeByAttributeName(attributeName: String): Type {
            return if (attributeName == CODING_SYSTEM_ATTRIBUTE_NAME) {
                CODING_SYSTEM
            } else if (attributeName.startsWith(PREFIX_SOURCE_ATTRIBUTE)) {
                SOURCE_ATTRIBUTE
            } else {
                ENRICHED_ATTRIBUTE
            }
        }
    }
}