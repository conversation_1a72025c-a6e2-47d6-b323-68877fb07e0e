package com.prospection.refdata.common.domain

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.Rule
import org.slf4j.LoggerFactory

interface HasRule {
    companion object {
        private val logger = LoggerFactory.getLogger(EnrichmentRule::class.java)
        val objectMapper: ObjectMapper = ObjectMapper()
            .registerKotlinModule()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    }

    val rule: String
    val deserialisedRule: Rule

    fun getDeserialisedRule(rule: String): Rule = try {
        objectMapper.readValue(rule, Rule::class.java)
    } catch (e: Exception) {
        logger.error("rule should be valid JSON string produced by our rule build UI (https://www.npmjs.com/package/react-querybuilder)")
        throw e
    }
}