package com.prospection.refdata.itemgroups.domain

import com.fasterxml.jackson.annotation.JsonIgnore
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.rules.domain.Rule
import java.time.LocalDateTime


data class ItemGroup(
    var id: String? = null,

    var businessKey: String? = null,

    var name: String,

    override var rule: String,

    var goal: String? = null,

    var deleted: Long = 0,

    var lastModifiedAt: LocalDateTime? = null,

    var lastModifiedBy: String? = null,

    var version: Long = 0,

    ) : HasRule {
    @JsonIgnore
    override val deserialisedRule: Rule = getDeserialisedRule(rule)
}