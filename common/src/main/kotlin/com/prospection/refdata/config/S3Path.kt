package com.prospection.refdata.config

object S3Path {
    const val ITEMS = "items"
    const val ITEM_GROUPS = "item-groups"
    const val RAW_UPLOAD_CSV = "raw-upload-csv"
    const val RAW_UPLOAD = "raw-upload"
    const val SNAPSHOTS = "snapshots"
    const val WAREHOUSE = "warehouse"

    object Items {
        private const val DRAFT = "$ITEMS/draft"
        private const val PUBLISHED = "$ITEMS/published"
        const val PREVIEW = "$ITEMS/preview"

        object Draft {
            const val ENRICHED_ITEMS_PARQUET = "$DRAFT/enriched-items-parquet"
            const val ENRICHED_ITEMS_CSV = "$DRAFT/enriched-items-csv"
            const val RAW_ITEMS = "$DRAFT/raw-items"
            const val CHANGE_SUMMARY = "$DRAFT/change-summary"
            const val DATA_UPDATE_REPORT = "$DRAFT/data-update-report"
        }

        object Published {
            const val RAW_ITEMS = "$PUBLISHED/raw-items"
            const val ENRICHED_ITEMS_PARQUET = "$PUBLISHED/enriched-items-parquet"
            const val ARCHIVE_ENRICHMENT_RULE = "$PUBLISHED/archive-enrichment-rule"
        }

        object Preview {
            const val TEMP = "$PREVIEW/temp"
        }
    }

    object ItemGroups {
        private const val DRAFT = "$ITEM_GROUPS/draft"
        private const val PUBLISHED = "$ITEM_GROUPS/published"
        private const val ARCHIVED = "$ITEM_GROUPS/archived"
        const val PREVIEW = "$ITEM_GROUPS/preview"

        object Draft {
            const val ITEM_TO_ITEM_GROUP = "$DRAFT/item-to-item-group"
            const val CHANGE_SUMMARY = "$DRAFT/change-summary"
        }

        object Published {
            const val ITEM_TO_ITEM_GROUP = "$PUBLISHED/item-to-item-group"
            const val ARCHIVE_ITEM_GROUP = "$PUBLISHED/archive-item-group"
            const val ITEM_GROUPS_TO_TOPICS = "$PUBLISHED/item-groups-to-topics"
        }

        object Archived {
            const val ITEM_TO_ITEM_GROUP = "$ARCHIVED/item-to-item-group"
        }

        object Preview {
            const val TEMP = "$PREVIEW/temp"
        }
    }

}