anchors:
  common_env_uat: &common_env_uat
    ENV_TEMPLATE: uat
    ENV_NAME: uat
    AWS_ACCOUNT_ID: ************
    AWS_ACCOUNT_NAME: pd2-dev

  common_env_prd: &common_env_prd
    ENV_TEMPLATE: prd
    ENV_NAME: prd
    AWS_ACCOUNT_ID: ************
    AWS_ACCOUNT_NAME: pd2-prod

steps:

  ##########################################
  ##########  CHECK RELEASE FLAG  ##########
  ##########################################

  - label: "Check deployment is on freeze"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "./master-pipeline/.buildkite/scripts/block_deploy.sh | buildkite-agent pipeline upload"

  #################################
  ##########  UAT PLAN   ##########
  #################################

  - wait

  - label: ":terraform: :large_red_square: Plan in UAT (${COUNTRY})"
    commands:
      - "make -s pull"
      - "make -s inf_plan"
      - "<NAME_EMAIL>:teamprospection/pd-checkov.git"
      - "make -s checkov_check"
    env:
      <<: *common_env_uat

  #################################
  ##########  UAT APPLY  ##########
  #################################

  - wait

  - block: ":red_button: Apply Hotfix Changes to UAT"
    branches: "hotfix/*"

  - label: ":terraform: :large_red_square: Apply to UAT (${COUNTRY})"
    commands:
      - "make -s pull"
      - "make -s inf_apply"
      - "make -s git_tag"
    env:
      <<: *common_env_uat

  #################################
  ##########  PROD PLAN  ##########
  #################################

  - wait

  - label: ":terraform: :large_red_square: Plan in PRD (${COUNTRY})"
    commands:
      - "make -s pull"
      - "make -s inf_plan"
      - "<NAME_EMAIL>:teamprospection/pd-checkov.git"
      - "make -s checkov_check"
    env:
      <<: *common_env_prd

  #################################
  ########## PROD APPLY  ##########
  #################################

  - wait

  - block: ":red_button: Apply Changes to Production"
    prompt: ":warning: Make sure you want to apply changes to Production? Continue to Proceed."

  - label: ":terraform: :large_red_square: Apply to PRD (${COUNTRY})"
    commands:
      - "make -s pull"
      - "make -s inf_apply"
    env:
      <<: *common_env_prd
