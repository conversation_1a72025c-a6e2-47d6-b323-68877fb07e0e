env:
  PROJECT_NAME: "pd-ref-data-service"
  IMAGE_REPO: "448934725283.dkr.ecr.ap-southeast-2.amazonaws.com"
  IMAGE: "${IMAGE_REPO}/${PROJECT_NAME}"
  TAG_VERSION: "${BUILDKITE_BUILD_NUMBER}"
  TAG_LATEST: "${BUILDKITE_BRANCH}_latest"
  TAG_BUILDER: "${BUILDKITE_BRANCH}_builder"
  DATADOG_AGENT_URL: "${DATADOG_AGENT_URL}"
  DOCKER_OPTS: "--net=host --userns=host"

# Define values to reuse in the pipeline
anchors:
  common_build: &common_build
    message: "${BUILDKITE_MESSAGE} (au)"
    commit: "${BUILDKITE_COMMIT}"
    branch: "${BUILDKITE_BRANCH}"
    env:
      BUILDKITE_PULL_REQUEST: "${BUILDKITE_PULL_REQUEST}"
      BUILDKITE_PULL_REQUEST_BASE_BRANCH: "${BUILDKITE_PULL_REQUEST_BASE_BRANCH}"
      BUILDKITE_PULL_REQUEST_REPO: "${BUILDKITE_PULL_REQUEST_REPO}"
      PROJECT_NAME: "${PROJECT_NAME}"
      IMAGE_REPO: "${IMAGE_REPO}"
      IMAGE: "${IMAGE}"
      TAG_VERSION: "${TAG_VERSION}"
      TAG_LATEST: "${TAG_LATEST}"
      TAG_BUILDER: "${TAG_BUILDER}"
      DOCKER_OPTS: "${DOCKER_OPTS}"
      COUNTRY: "au"

steps:

  #############################
  ##########  SETUP  ##########
  #############################

  - label: ":docker: :ecr: Setup Docker Registry"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "cd ./master-pipeline/.buildkite/scripts && make -s setup_registry"

  - label: ":docker: :ecr: Setup Docker Registry lambdas (kotlin/spark)"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "cd ./master-pipeline/.buildkite/scripts && make -s setup_registry"
    env:
      PROJECT_NAME: "${PROJECT_NAME}-lambdas-spark"

  - label: ":docker: :ecr: Setup Docker Registry lambdas (js)"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "cd ./master-pipeline/.buildkite/scripts && make -s setup_registry"
    env:
      PROJECT_NAME: "${PROJECT_NAME}-lambdas"

  - wait

  #############################
  ##########  BUILD  ##########
  #############################

  - label: ":docker: :gradle: Docker Build"
    commands:
      - "make -s pull"
      - "make -s build"
      - "make -s push"

  - wait

  #############################
  ##########  TEST   ##########
  #############################

  - label: ":junit: :jest: :sonarcloud: Unit Test"
    commands:
      - "make -s pull"
      - "make -s app_test BRANCH=${BUILDKITE_BRANCH} PROVIDER=${BUILDKITE_PIPELINE_PROVIDER} PULLREQUEST_KEY=${BUILDKITE_PULL_REQUEST} PULLREQUEST_BASE=${BUILDKITE_PULL_REQUEST_BASE_BRANCH}"
      - "make -s app_test_lambdas"
    plugins:
      - test-collector#v1.11.0:
          files:
            # Individual test result files from each module (standard JUnit XML format)
            - "*/build/test-results/test/TEST-*.xml"
            - "build/test-results/test/TEST-*.xml"
          format: "junit"
          api-token-env-name: "REF_DATA_BK_TEST_ANALYTICS_TOKEN"

  - wait: ~
    if: build.branch == "master"

  #############################
  ########## NONPROD ##########
  #############################

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-nonprod"
    label: ":buildkite: :pipeline: Deploy to Non-Production AU"
    async: true
    build:
      <<: *common_build

  ##################################
  ##########  UAT / PROD  ##########
  ##################################

  - wait

  - trigger: "${BUILDKITE_PIPELINE_SLUG}-deploy"
    label: ":buildkite: :pipeline: Deploy to UAT/Production AU"
    branches:
      - "master"
      - "hotfix/*"
    async: true
    build:
      <<: *common_build
