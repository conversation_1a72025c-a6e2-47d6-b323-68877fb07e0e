steps:
  #############################
  ######## select ENV #########
  #############################

  - label: "Select environment to deploy"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "./master-pipeline/.buildkite/scripts/env_input.sh | buildkite-agent pipeline upload"
    skip: "${MASTER_PIPELINE}"

  - wait

  #############################
  ##########  PLAN   ##########
  #############################

  - label: ":terraform: :large_blue_square: Plan in selected ENV (${COUNTRY})"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "./master-pipeline/.buildkite/scripts/run_plan.sh"

  #############################
  ##########  APPLY  ##########
  #############################

  - block: ":red_button: Apply Changes"

  - label: ":terraform: :large_blue_square: Apply to selected ENV (${COUNTRY})"
    commands:
      - "<NAME_EMAIL>:teamprospection/master-pipeline.git"
      - "source ./master-pipeline/.buildkite/scripts/export_env.sh"
      - "make -s pull"
      - "make -s inf_apply"