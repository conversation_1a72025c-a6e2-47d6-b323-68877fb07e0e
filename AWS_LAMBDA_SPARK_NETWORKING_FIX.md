# AWS Lambda Spark Networking Fix

## Problem Description

The AWS Lambda Spark function was failing with networking connectivity errors:

```
Job aborted due to stage failure: Task 3 in stage 1.0 failed 1 times, most recent failure: Lost task 3.0 in stage 1.0 (TID 4) (************** executor driver): org.apache.spark.executor.RemoteClassLoaderError: org.apache.spark.sql.catalyst.expressions.Object

Caused by: java.io.IOException: Failed to connect to /**************:45909
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: /**************:45909
Caused by: java.net.ConnectException: Connection refused
```

## Root Cause Analysis

1. **AWS Lambda Networking Constraints**: AWS Lambda has strict networking limitations that prevent arbitrary inter-process communication
2. **Spark Driver Binding**: The Spark driver was trying to bind to external IP addresses and random ports
3. **Missing Configuration**: Critical Lambda-specific networking settings were commented out or missing

## Solution Implemented

### 1. Fixed Spark Driver Binding

**File**: `lambdas-spark/src/main/kotlin/com/prospection/refdata/config/SparkConfiguration.kt`

**Key Changes**:
- ✅ Enabled `spark.driver.bindAddress = "127.0.0.1"` (was commented out)
- ✅ Added `spark.driver.host = "127.0.0.1"`
- ✅ Set `spark.executor.instances = "0"` (no separate executors in local mode)
- ✅ Disabled dynamic allocation: `spark.dynamicAllocation.enabled = "false"`
- ✅ Disabled shuffle service: `spark.shuffle.service.enabled = "false"`
- ✅ Set short timeouts for Lambda environment:
  - `spark.network.timeout = "10s"`
  - `spark.rpc.askTimeout = "10s"`
  - `spark.rpc.lookupTimeout = "10s"`
- ✅ Set dynamic port allocation:
  - `spark.driver.port = "0"`
  - `spark.blockManager.port = "0"`

### 2. Verified ArtifactManager Override

The custom ArtifactManager.scala files are already in place to fix the Spark 4.0 bug:
- ✅ `kotlin-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`
- ✅ `lambdas-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`

## Configuration Summary

The complete Lambda-optimized Spark configuration now includes:

```kotlin
val sparkConfig = SparkConfig.getCloudSparkConfig()
    .set("spark.master", "local[$numCores]")
    .set("spark.sql.shuffle.partitions", numCores.toString())
    .set("spark.default.parallelism", numCores.toString())
    .set("spark.sql.adaptive.enabled", "false")
    .set("spark.shuffle.compress", "false")
    .set("spark.broadcast.compress", "false")
    .set("spark.ui.enabled", "false")
    // AWS Lambda networking configuration - critical for Lambda environment
    .set("spark.driver.bindAddress", "127.0.0.1")
    .set("spark.driver.host", "127.0.0.1")
    .set("spark.executor.instances", "0") // No separate executors in local mode
    .set("spark.dynamicAllocation.enabled", "false")
    .set("spark.shuffle.service.enabled", "false")
    .set("spark.network.timeout", "10s") // Short timeout for Lambda
    .set("spark.rpc.askTimeout", "10s")
    .set("spark.rpc.lookupTimeout", "10s")
    // Prevent any external network communication attempts
    .set("spark.driver.port", "0") // Let system assign available port
    .set("spark.blockManager.port", "0")
    .set("spark.executor.heartbeatInterval", "60s") // Longer interval for Lambda
```

## Expected Results

With these fixes, the AWS Lambda Spark function should:

1. ✅ **No more networking errors**: All Spark communication stays on localhost
2. ✅ **Faster startup**: No time wasted trying to establish external connections
3. ✅ **Stable execution**: No random port conflicts or connection timeouts
4. ✅ **Lambda-optimized**: Configuration tuned for Lambda's execution environment

## Testing

To verify the fix works:

1. **Deploy the updated Lambda function**
2. **Monitor CloudWatch logs** for the absence of networking errors
3. **Test Spark operations** that previously failed
4. **Verify performance** - should see faster initialization

## Monitoring

Watch for these positive indicators in CloudWatch logs:
- ✅ No "Failed to connect" errors
- ✅ No "Connection refused" errors  
- ✅ Faster Spark session initialization
- ✅ Successful DataFrame operations

## Rollback Plan

If issues occur, the previous configuration can be restored by:
1. Commenting out the new networking settings
2. Reverting to the original commented `spark.driver.bindAddress` line
3. Redeploying the Lambda function

## References

- **AWS Lambda Networking**: https://docs.aws.amazon.com/lambda/latest/dg/configuration-network.html
- **Spark Configuration**: https://spark.apache.org/docs/latest/configuration.html
- **Spark 4.0 ArtifactManager Fix**: See `SPARK_4_ARTIFACT_MANAGER_WORKAROUND.md`
