-- Test script to verify revision_info_seq sequence exists and works
-- This can be run against a PostgreSQL database to test our fix

-- Check if the sequence exists
SELECT EXISTS (
    SELECT 1 
    FROM information_schema.sequences 
    WHERE sequence_name = 'revision_info_seq'
) AS sequence_exists;

-- Test getting next value from the sequence
SELECT nextval('revision_info_seq') AS next_value;

-- Check current value of the sequence
SELECT currval('revision_info_seq') AS current_value;

-- Check if revision_info table exists
SELECT EXISTS (
    SELECT 1 
    FROM information_schema.tables 
    WHERE table_name = 'revision_info'
) AS table_exists;

-- Show the structure of revision_info table
\d revision_info;
