root = true

[*]
charset = utf-8

end_of_line = lf
insert_final_newline = false
ij_formatter_off_tag = @formatter:off
ij_formatter_on_tag = @formatter:on
ij_formatter_tags_enabled = true
ij_smart_tabs = false
ij_wrap_on_typing = false

[{*.kt,*.scala}]
indent_size = 4
indent_style = space
tab_width = 4
ij_continuation_indent_size = 4
max_line_length = 120

[*.{kt,kts}]
# don't use wildcard for Kotlin imports
ij_kotlin_name_count_to_use_star_import = 999
ij_kotlin_name_count_to_use_star_import_for_members = 999

[*.yml]
indent_size = 2
tab_width = 2

[*.{ts,js}]
indent_size = 2
tab_width = 2