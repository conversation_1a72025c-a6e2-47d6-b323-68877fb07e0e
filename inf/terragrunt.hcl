locals {
  country_vars = read_terragrunt_config("../country.hcl")

  aws_account_id   = get_env("AWS_ACCOUNT_ID")
  aws_account_name = get_env("AWS_ACCOUNT_NAME")

  aws_role_name    = local.use_sso ? "" : get_env("AWS_ROLE_NAME", "terraform-role")
  aws_role_arn     = local.aws_role_name != "" ? "arn:aws:iam::${local.aws_account_id}:role/${local.aws_role_name}" : ""
  aws_region       = local.country_vars.locals.aws_region
  use_sso          = tobool(get_env("USE_SSO", "false"))
  sso_profile      = get_env("SSO_PROFILE", "platform-dev-admin")
  country_prefix   = local.country_vars.locals.country_prefix

  project_name            = get_env("PROJECT_NAME")
  env_name                = get_env("ENV_NAME")
  env_template            = get_env("ENV_TEMPLATE")
  relative_env_path       = "env/${local.country_prefix}/${local.env_name}"
  tag_version             = get_env("TAG_VERSION")
  service_image           = "${get_env("IMAGE")}:${get_env("TAG_VERSION")}"
  lambdas_spark_image_uri = "${get_env("IMAGE")}-lambdas-spark:${get_env("TAG_VERSION")}"
  lambdas_image_uri       = "${get_env("IMAGE")}-lambdas:${get_env("TAG_VERSION")}"

  state_bucket = "prospection-terraform-${local.aws_account_name}-${local.aws_region}"
  state_key    = "${local.project_name}/${local.relative_env_path}/terraform.tfstate"
  state_lock   = "terraform-locks"
}

generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"

  contents = <<EOF
provider "aws" {
  region              = "${local.aws_region}"
  allowed_account_ids = ["${local.aws_account_id}"]

  %{ if local.use_sso }
  profile = "${local.sso_profile}"
  %{ else }
  assume_role {
    role_arn     = "${local.aws_role_arn}"
  }
  %{ endif }
}
EOF
}

generate "platform" {
  path      = "platform.tf"
  if_exists = "overwrite_terragrunt"

  contents = <<EOF
data "terraform_remote_state" "platform" {
  backend = "s3"
  config = {
    bucket   = "${local.state_bucket}"
    key      = "pd-platform/${local.relative_env_path}/terraform.tfstate"
    region   = "${local.aws_region}"
    role_arn = "${local.use_sso}" ? null : "${local.aws_role_arn}"
    profile   = "${local.use_sso}" ? "${local.sso_profile}" : null
  }
}
EOF
}

generate "authorisation_service" {
  path      = "authorisation_service.tf"
  if_exists = "overwrite_terragrunt"

  contents = <<EOF
data "terraform_remote_state" "authorisation_service" {
  backend = "s3"
  config = {
    bucket   = "${local.state_bucket}"
    key      = "pd-authorisation-service/${local.relative_env_path}/terraform.tfstate"
    region   = "${local.aws_region}"
    role_arn = "${local.use_sso}" ? null : "${local.aws_role_arn}"
    profile   = "${local.use_sso}" ? "${local.sso_profile}" : null
  }
}
EOF
}

generate "feature_toggle_service" {
  path      = "feature_toggle_service.tf"
  if_exists = "overwrite_terragrunt"

  contents = <<EOF
data "terraform_remote_state" "feature_toggle_service" {
  count   = var.is_feature_toggle_service_available ? 1 : 0
  backend = "s3"
  config = {
    bucket   = "${local.state_bucket}"
    key      = "pd-feature-toggle-service/${local.relative_env_path}/terraform.tfstate"
    region   = "${local.aws_region}"
    role_arn = "${local.use_sso}" ? null : "${local.aws_role_arn}"
    profile   = "${local.use_sso}" ? "${local.sso_profile}" : null
  }
}
EOF
}

remote_state {
  backend = "s3"

  config = {
    encrypt        = true
    bucket         = local.state_bucket
    key            = local.state_key
    dynamodb_table = local.state_lock
    region         = local.aws_region
    role_arn       = local.use_sso ? null : local.aws_role_arn
    profile        = local.use_sso ? local.sso_profile : null
  }

  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

inputs = merge(
  local.country_vars.locals,
  {
    state_bucket            = local.state_bucket
    state_key               = local.state_key
    project_name            = local.project_name
    service_image           = local.service_image
    lambdas_spark_image_uri = local.lambdas_spark_image_uri
    lambda_image_uri        = local.lambdas_image_uri
    aws_account_id          = local.aws_account_id
    env_name                = local.env_name
    env_template            = local.env_template
    tag_version             = local.tag_version
  }
)
