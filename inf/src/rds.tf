module "service_rds" {
  source  = "terraform-aws-modules/rds-aurora/aws"
  version = "~> 2.0"

  name          = "${local.service_name}-aurora"
  database_name = replace(local.service_name, "-", "_")

  engine                     = "aurora-postgresql"
  engine_version             = "15.5"
  auto_minor_version_upgrade = false

  vpc_id  = data.terraform_remote_state.platform.outputs.vpc_id
  subnets = data.terraform_remote_state.platform.outputs.private_subnets

  allowed_cidr_blocks = [
    data.terraform_remote_state.platform.outputs.vpc_cidr_block,
    data.terraform_remote_state.platform.outputs.vpn_vpc_cidr_block
  ]

  replica_count = var.service_rds_replica_count
  instance_type = var.service_rds_instance_type

  storage_encrypted   = true
  apply_immediately   = true
  monitoring_interval = 10
  copy_tags_to_snapshot = true
  enabled_cloudwatch_logs_exports = ["postgresql"]
  deletion_protection = true


  db_parameter_group_name         = data.terraform_remote_state.platform.outputs.aurora_db_postgres15_parameter_group_name
  db_cluster_parameter_group_name = data.terraform_remote_state.platform.outputs.aurora_cluster_postgres15_parameter_group_name

  preferred_backup_window = ""

  iam_database_authentication_enabled = true

  ca_cert_identifier = "rds-ca-rsa2048-g1"

  tags = merge(
    local.common_tags,
    {AWSBackup="PlatformStateContinuous7DayRetention"}
  )
}
