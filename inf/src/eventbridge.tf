resource "aws_cloudwatch_event_rule" "scraper_pbs_items_rule" {
  name = "${local.service_name}-scraper-pbs-items-rule"
  description = "Runs periodically to scrape PBS items from the PBS website"
  schedule_expression = "rate(6 hours)"
}

resource "aws_cloudwatch_event_target" "scraper_pbs_items_event_target" {
  count = var.enable_pbs_ref_data_scraper ? 1 : 0
  rule = aws_cloudwatch_event_rule.scraper_pbs_items_rule.name
  target_id = "${local.service_name}-scraper-pbs-items-event-target"
  arn = aws_lambda_function.scraper_pbs_items.arn
}

resource "aws_cloudwatch_event_rule" "mdv_etl_trigger_rule" {
  name                = "${local.service_name}-mdv-etl-trigger-rule"
  description         = "Runs periodically to check if a new ETL of MDV Item can occur"
  schedule_expression = "rate(2 hours)"
}

resource "aws_cloudwatch_event_target" "mdv_etl_trigger_event_target" {
  rule      = aws_cloudwatch_event_rule.mdv_etl_trigger_rule.name
  target_id = "${local.service_name}-mdv-item-etl-trigger-event-target"
  arn       = aws_lambda_function.mdv_etl_trigger.arn
}

resource "aws_cloudwatch_event_rule" "pbs_authority_etl_trigger_rule" {
  name                = "${local.service_name}-pbs-authority-etl-trigger-rule"
  description         = "Runs periodically to check if a new ETL of PBS Authority can occur"
  schedule_expression = "rate(2 hours)"
}

resource "aws_cloudwatch_event_target" "pbs_authority_etl_trigger_event_target" {
  rule      = aws_cloudwatch_event_rule.pbs_authority_etl_trigger_rule.name
  target_id = "${local.service_name}-pbs-authority-etl-event-target"
  arn       = aws_lambda_function.pbs_authority_etl_trigger.arn
}

resource "aws_cloudwatch_event_rule" "fdb_drug_etl_trigger_rule" {
  name                = "${local.service_name}-fdb-drug-etl-trigger-rule"
  description         = "Runs periodically to check if a new ETL of FDB Drug can occur"
  schedule_expression = "rate(2 hours)"
}

resource "aws_cloudwatch_event_target" "fdb_drug_etl_trigger_event_target" {
  rule      = aws_cloudwatch_event_rule.fdb_drug_etl_trigger_rule.name
  target_id = "${local.service_name}-fdb-drug-etl-event-target"
  arn       = aws_lambda_function.fdb_drug_etl_trigger.arn
}

resource "aws_cloudwatch_event_rule" "icd10_etl_trigger_rule" {
  name                = "${local.service_name}-icd10-etl-trigger-rule"
  description         = "Runs periodically to check if a new ETL of ICD Diagnosis/ICD Procedure can occur"
  schedule_expression = "rate(2 hours)"
}

resource "aws_cloudwatch_event_target" "icd10_etl_trigger_event_target" {
  rule      = aws_cloudwatch_event_rule.icd10_etl_trigger_rule.name
  target_id = "${local.service_name}-icd10-etl-event-target"
  arn       = aws_lambda_function.icd10_etl_trigger.arn
}

resource "aws_cloudwatch_event_rule" "jmdc_etl_trigger_rule" {
  name                = "${local.service_name}-jmdc-etl-trigger-rule"
  description         = "Runs periodically to check if a new ETL of JMDC Diagnosis/JMDC Procedure/JMDC Drug/JMDC Material can occur"
  schedule_expression = "rate(2 hours)"
}

resource "aws_cloudwatch_event_target" "jmdc_etl_trigger_event_target" {
  rule      = aws_cloudwatch_event_rule.jmdc_etl_trigger_rule.name
  target_id = "${local.service_name}-jmdc-etl-event-target"
  arn       = aws_lambda_function.jmdc_etl_trigger.arn
}

resource "aws_cloudwatch_event_rule" "desc_etl_trigger_rule" {
  name                = "${local.service_name}-desc-etl-trigger-rule"
  description         = "Runs periodically to check if a new ETL of DESC Item can occur"
  schedule_expression = "rate(12 hours)"
}

resource "aws_cloudwatch_event_target" "desc_etl_trigger_event_target" {
  rule      = aws_cloudwatch_event_rule.desc_etl_trigger_rule.name
  target_id = "${local.service_name}-desc-etl-trigger-event-target"
  arn       = aws_lambda_function.desc_etl_trigger.arn
}