resource "aws_glue_crawler" "reference-data_crawler" {
  database_name = local.athena_database_name
  name          = "${local.service_resource_name}-crawler"
  role          = aws_iam_role.service_task_role.arn

  schema_change_policy {
    delete_behavior = "LOG"
  }

  configuration = jsonencode(
    {
      CrawlerOutput = {
        Partitions = { AddOrUpdateBehavior = "InheritFromTable" }
      }
      Version = 1
    }
  )

  s3_target {
    path = "s3://pd-au-${var.env_name}-common/external-reference-data/"
    exclusions = ["**_SUCCESS"]
  }
}