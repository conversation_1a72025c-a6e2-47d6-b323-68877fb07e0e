resource "aws_s3_bucket" "service_bucket" {
  bucket = local.service_resource_name
  tags = local.common_tags
}

resource "aws_s3_bucket_versioning" "service_bucket_versioning" {
  count = var.enable_s3_bucket_versioning ? 1 : 0
  bucket = aws_s3_bucket.service_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "service_bucket_lifecycle" {
  count = var.enable_s3_bucket_versioning ? 1 : 0
  bucket = aws_s3_bucket.service_bucket.id

  rule {
    id     = "expire-old-objects"
    status = "Enabled"

    filter {}

    abort_incomplete_multipart_upload {
      days_after_initiation = 7
    }

    expiration {
      expired_object_delete_marker = true
    }

    noncurrent_version_expiration {
      noncurrent_days = 7
    }
  }
}

resource "aws_s3_bucket_policy" "service_bucket_policy" {
  bucket = aws_s3_bucket.service_bucket.id
  policy = data.aws_iam_policy_document.service_bucket_policy_document.json
}

data "aws_iam_policy_document" "service_bucket_policy_document" {
  statement {
    actions = [
      "s3:List*",
      "s3:GetEncryptionConfiguration",
      "s3:GetBucketVersioning",
      "s3:GetBucketOwnershipControls",
      "s3:GetBucketLocation"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::699310932760:root"]
    }

    resources = [
      aws_s3_bucket.service_bucket.arn
    ]
  }

  statement {
    actions = [
      "s3:GetObjectTagging",
      "s3:GetObject"
    ]

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::699310932760:root"]
    }

    resources = [
      "${aws_s3_bucket.service_bucket.arn}/*"
    ]
  }
}

resource aws_s3_bucket_ownership_controls service_bucket_ownership_controls {
  bucket = aws_s3_bucket.service_bucket.id

  rule {
    object_ownership = "BucketOwnerEnforced"
  }
}

resource aws_s3_bucket_server_side_encryption_configuration service_bucket_encryption_configuration {
  bucket = local.service_resource_name

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "service_bucket_public_access_block" {
  bucket = aws_s3_bucket.service_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "service_s3_bucket_notification" {
  bucket      = aws_s3_bucket.service_bucket.id
  eventbridge = true
}
