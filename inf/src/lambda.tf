resource aws_lambda_function spark {
  function_name = "${local.service_name}-spark"
  role          = aws_iam_role.lambda_spark_execution_role.arn
  // browser timeout is already 60 seconds, no need more than that
  timeout       = 60

  image_uri     = var.lambdas_spark_image_uri
  package_type  = "Image"

  // this will give us 5 vCPU but CPU ceiling is actually 4 -> NUM_CORES = 4 bellow
  memory_size   = 8192

  description   = "Spark APIs to avoid running local Spark inside web app"

  environment {
    variables = {
      S3_BUCKET = aws_s3_bucket.service_bucket.bucket,
      NUM_CORES = 4
    }
  }

  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  ephemeral_storage {
    size = 2048
  }

  tags = local.common_tags
}

resource aws_lambda_function scraper_pbs_items {
  function_name = "${local.service_name}-scraper-pbs-items"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900 // giving the max value just in case file download takes longer

  image_uri    = var.lambdas_image_uri
  package_type = "Image"

  memory_size = 2048

  description = "Scrape PBS items that are refreshed on a monthly basis"
  dead_letter_config {
    target_arn = aws_sqs_queue.etl_lambda_functions_deadletter.arn
  }

  environment {
    variables = {
      OUTPUT_S3_BUCKET     = aws_s3_bucket.service_bucket.bucket
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.pbsDrugHandler"]
  }

  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource aws_lambda_function mdv_etl_trigger {
  function_name = "${local.service_name}-mdv-etl-trigger"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900

  image_uri    = var.lambdas_image_uri
  package_type = "Image"
  memory_size = 512
  reserved_concurrent_executions = 1

  description = "Trigger MDV ETL if new MDV files exist in the data lake"

  environment {
    variables = {
      DATA_LAKE_S3_BUCKET  = "prospection-data-lake-ap-northeast-1"
      OUTPUT_S3_BUCKET     = aws_s3_bucket.service_bucket.bucket
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.mdvHandler"]
  }


  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource aws_lambda_function pbs_authority_etl_trigger {
  function_name = "${local.service_name}-pbs-authority-etl-trigger"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900

  image_uri    = var.lambdas_image_uri
  package_type = "Image"
  memory_size = 512
  reserved_concurrent_executions = 1

  description = "Trigger PBS-10 ETL if new PBS-10 files exist in the data lake"

  environment {
    variables = {
      DATA_LAKE_S3_BUCKET  = var.data_lake_bucket
      OUTPUT_S3_BUCKET     = aws_s3_bucket.service_bucket.bucket
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.pbsAuthorityHandler"]
  }


  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource aws_lambda_function fdb_drug_etl_trigger {
  function_name = "${local.service_name}-fdb-drug-etl-trigger"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900

  image_uri    = var.lambdas_image_uri
  package_type = "Image"
  memory_size = 512
  reserved_concurrent_executions = 1

  description = "Trigger FDB Drug ETL if new FDB files exist in the data lake"

  environment {
    variables = {
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.fdbDrugHandler"]
  }


  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource aws_lambda_function icd10_etl_trigger {
  function_name = "${local.service_name}-icd10-etl-trigger"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900

  image_uri    = var.lambdas_image_uri
  package_type = "Image"
  memory_size = 512
  reserved_concurrent_executions = 1

  description = "Trigger ICD Diagnosis and ICD Procedure ETL if new CM/PCS files exist in the data lake"

  environment {
    variables = {
      OUTPUT_S3_BUCKET     = aws_s3_bucket.service_bucket.bucket
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.icd10Handler"]
  }

  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource aws_lambda_function jmdc_etl_trigger {
  function_name = "${local.service_name}-jmdc-etl-trigger"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900

  image_uri    = var.lambdas_image_uri
  package_type = "Image"
  memory_size = 512
  reserved_concurrent_executions = 1

  description = "Trigger JMDC Coding System ETL if a new file exist in the data lake"

  environment {
    variables = {
      DATA_LAKE_S3_BUCKET  = "prospection-data-lake-ap-northeast-1"
      OUTPUT_S3_BUCKET     = aws_s3_bucket.service_bucket.bucket
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.jmdcHandler"]
  }


  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource aws_lambda_function desc_etl_trigger {
  function_name = "${local.service_name}-desc-etl-trigger"
  role          = aws_iam_role.lambda_execution_role.arn
  timeout       = 900

  image_uri    = var.lambdas_image_uri
  package_type = "Image"
  memory_size = 512
  reserved_concurrent_executions = 1

  description = "Trigger DESC ETL if new DESC files exist in the data lake"

  environment {
    variables = {
      DATA_LAKE_S3_BUCKET  = "prospection-data-lake-ap-northeast-1"
      OUTPUT_S3_BUCKET     = aws_s3_bucket.service_bucket.bucket
      ETL_QUEUE_URL        = aws_sqs_queue.etl_queue.url
      REF_DATA_SERVICE_URL = "https://pd-ref-data-service.${local.resource_prefix}.prospection-internal.net"
    }
  }

  image_config {
    command = ["build/index.descHandler"]
  }


  vpc_config {
    subnet_ids         = [data.terraform_remote_state.platform.outputs.database_subnets[0]]
    security_group_ids = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
  }

  tags = local.common_tags
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_fdb_drug_lambda_permission" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.fdb_drug_etl_trigger.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.fdb_drug_etl_trigger_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_pbs_lambda_permission" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.scraper_pbs_items.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.scraper_pbs_items_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_mdv_lambda_permission" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.mdv_etl_trigger.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.mdv_etl_trigger_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_pbs_authority_lambda_permission" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.pbs_authority_etl_trigger.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.pbs_authority_etl_trigger_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_icd10_lambda_permission" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.icd10_etl_trigger.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.icd10_etl_trigger_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_jmdc_lambda_permission" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.jmdc_etl_trigger.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.jmdc_etl_trigger_rule.arn
}

resource "aws_lambda_permission" "allow_cloudwatch_to_call_desc_lambda_permission" {
  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.desc_etl_trigger.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.desc_etl_trigger_rule.arn
}