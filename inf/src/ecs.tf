locals {
  entrypoint_datadog_apm_enabled      = <<-EOT
  [
    "sh",
    "-c",
    "source ./set-datadog-config.sh; java -javaagent:dd-java-agent.jar -jar application.jar"
  ]
  EOT

  entrypoint_datadog_apm_disabled     = <<-EOT
  [
    "sh",
    "-c",
    "java -jar application.jar"
  ]
  EOT

  log_config_datadog_logging_enabled  = <<-EOT
  {
    "logDriver": "json-file"
  }
  EOT

  log_config_datadog_logging_disabled = <<-EOT
  {
    "logDriver": "awslogs",
    "options": {
      "awslogs-group": "${local.service_name}",
      "awslogs-region": "${var.aws_region}",
      "awslogs-stream-prefix": "app",
      "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"
    }
  }
  EOT

  ecs_secrets = flatten([
    {
      name      = "JWT_SECRET"
      valueFrom = data.terraform_remote_state.authorisation_service.outputs.jwt_secret_arn
    },
    var.is_feature_toggle_service_available ? [
      {
        name      = "FEATURE_TOGGLE_SERVICE_API_TOKEN"
        valueFrom = data.terraform_remote_state.feature_toggle_service[0].outputs.api_token_arn
      }
    ] : []
  ])
}

resource "aws_ecs_task_definition" "service_task_definition" {
  family = local.service_name

  task_role_arn      = aws_iam_role.service_task_role.arn
  execution_role_arn = data.terraform_remote_state.platform.outputs.ecs_task_execution_role_arn
  network_mode       = "awsvpc"

  cpu                      = var.service_cpu
  memory                   = var.service_memory
  requires_compatibilities = ["EC2"]

  container_definitions = templatefile("${path.module}/container-definitions.json.tpl", {
    name                                = local.service_name
    user                                = var.docker_user
    image                               = var.service_image
    aws_region                          = var.aws_region
    tag_version                         = var.tag_version
    env_name                            = var.env_name
    spring_profiles_active              = var.env_template
    service_rds_endpoint                = module.service_rds.this_rds_cluster_endpoint
    service_rds_database_name           = module.service_rds.this_rds_cluster_database_name
    entry_point                         = var.enable_datadog_apm ? local.entrypoint_datadog_apm_enabled : local.entrypoint_datadog_apm_disabled
    log_configuration                   = var.enable_datadog_logging ? local.log_config_datadog_logging_enabled : local.log_config_datadog_logging_disabled
    service_s3_bucket                   = local.service_resource_name
    etl_queue_url                       = aws_sqs_queue.etl_queue.url
    customer_service_url                = "https://pd-customer-service.${local.resource_prefix}.prospection-internal.net"
    dashx_service_url                   = "https://dashx.${local.resource_prefix}.prospection-internal.net"
    lambda_item_groups_spark            = aws_lambda_function.spark.function_name
    is_feature_toggle_service_available = var.is_feature_toggle_service_available
    feature_toggle_service_url          = "https://pd-feature-toggle-service.${local.resource_prefix}.prospection-internal.net"
    archive_unused_data_cron            = var.archive_unused_data_cron
    sns_topic_arn                       = aws_sns_topic.job_status_notification_topic.arn
    ecs_secrets                         = local.ecs_secrets
  })

  volume {
    name      = "tmp"
    docker_volume_configuration {
      scope         = "task"
      driver        = "local"
    }
  }

  tags = local.common_tags
}

resource "aws_ecs_service" "service" {
  name                              = local.service_name
  cluster                           = data.terraform_remote_state.platform.outputs.ecs_cluster_id
  task_definition                   = aws_ecs_task_definition.service_task_definition.arn
  desired_count                     = var.service_desired_count
  health_check_grace_period_seconds = 600
  wait_for_steady_state             = true

  capacity_provider_strategy {
    capacity_provider = data.terraform_remote_state.platform.outputs.ecs_capacity_provider
    weight            = 1
    base              = 0
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.service_target_group.arn
    container_name   = local.service_name
    container_port   = 8080
  }

  network_configuration {
    subnets          = data.terraform_remote_state.platform.outputs.private_subnets
    security_groups  = [data.terraform_remote_state.platform.outputs.vpc_internal_sg_id]
    assign_public_ip = false
  }

  service_registries {
    registry_arn   = aws_service_discovery_service.service_discovery_service.arn
    container_name = local.service_name
  }
}

resource "aws_service_discovery_service" "service_discovery_service" {
  name = var.project_name

  dns_config {
    namespace_id = data.terraform_remote_state.platform.outputs.local_dns_namespace_id

    dns_records {
      ttl  = 60
      type = "A"
    }
  }

  health_check_custom_config {
    failure_threshold = 1
  }
}
