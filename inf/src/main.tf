terraform {
  required_version = "= 1.9.8"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "4.59.0"
    }
  }
}

locals {
  resource_prefix       = "pd-${var.country_prefix}-${var.env_name}"
  service_name          = "${local.resource_prefix}-${var.project_name}"
  service_resource_name = "${local.resource_prefix}-ref-data-v2"
  athena_database_name  = "${local.resource_prefix}-ref-data-v2"

  common_tags = {
    Terraform              = "true"
    Terraform_State_Bucket = var.state_bucket
    Terraform_State_Key    = var.state_key
    Environment            = var.env_name
  }
}
