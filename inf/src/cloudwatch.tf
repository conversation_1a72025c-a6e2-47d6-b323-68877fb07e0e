resource "aws_cloudwatch_log_group" "service_log_group" {
  name = local.service_name

  tags = local.common_tags
}

resource "aws_cloudwatch_metric_alarm" "ecs_service_cpu_utilisation" {
  count               = var.enable_ecs_service_monitoring ? 1 : 0
  alarm_name          = "ecs-service-${local.service_name}-cpu-utilisation"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "90"
  datapoints_to_alarm = "3"
  alarm_actions       = [var.slack_channel_alerting_arn]
  ok_actions          = [var.slack_channel_alerting_arn]
  dimensions          = {
    ClusterName = data.terraform_remote_state.platform.outputs.ecs_cluster_name
    ServiceName = aws_ecs_service.service.name
  }
}

resource "aws_cloudwatch_metric_alarm" "ecs_service_memory_utilisation" {
  count               = var.enable_ecs_service_monitoring ? 1 : 0
  alarm_name          = "ecs-service-${local.service_name}-memory-utilisation"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "3"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "95"
  datapoints_to_alarm = "3"
  alarm_actions       = [var.slack_channel_alerting_arn]
  ok_actions          = [var.slack_channel_alerting_arn]
  dimensions          = {
    ClusterName = data.terraform_remote_state.platform.outputs.ecs_cluster_name
    ServiceName = aws_ecs_service.service.name
  }
}

resource "aws_cloudwatch_metric_alarm" "ecs_service_running_task_count" {
  count               = var.enable_ecs_service_monitoring ? 1 : 0
  alarm_name          = "ecs-service-${local.service_name}-running-task-count"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "60"
  # Using SampleCount to determine the number of running ECS Tasks
  # For further details: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/cloudwatch-metrics.html#cw_running_task_count
  statistic           = "SampleCount"
  threshold           = var.service_desired_count
  datapoints_to_alarm = "3"
  alarm_actions       = [var.slack_channel_alerting_arn]
  ok_actions          = [var.slack_channel_alerting_arn]
  dimensions          = {
    ClusterName = data.terraform_remote_state.platform.outputs.ecs_cluster_name
    ServiceName = aws_ecs_service.service.name
  }
}
