resource aws_sqs_queue etl_queue {
  name                        = "${local.service_name}-etl-queue.fifo"
  fifo_queue                  = true
  content_based_deduplication = true
  delay_seconds               = 0
  message_retention_seconds   = 1209600
  receive_wait_time_seconds   = 20
  visibility_timeout_seconds  = 1200 // assuming that an etl job takes longer than 20 minutes must've gone wrong
  sqs_managed_sse_enabled     = true

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.etl_queue_deadletter.arn
    maxReceiveCount     = 3
  })

  tags = local.common_tags
}

resource aws_sqs_queue etl_queue_deadletter {
  name                        = "${local.service_name}-etl-queue-deadletter.fifo"
  fifo_queue                  = true
  content_based_deduplication = true
  sqs_managed_sse_enabled     = true
  delay_seconds               = 0
  message_retention_seconds   = 1209600

  tags = local.common_tags
}

resource aws_sqs_queue etl_lambda_functions_deadletter {
  name                        = "${local.service_name}-etl-lambda-functions-deadletter"
  sqs_managed_sse_enabled     = true
  delay_seconds               = 0
  message_retention_seconds   = 1209600

  tags = local.common_tags
}

resource aws_sqs_queue_policy etl_queue_policy {
  queue_url = aws_sqs_queue.etl_queue.url
  policy    = data.aws_iam_policy_document.etl_queue_policy_document.json
}

data aws_iam_policy_document etl_queue_policy_document {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = [
        "events.amazonaws.com"
      ]
    }

    principals {
      type        = "AWS"
      identifiers = [
        aws_iam_role.lambda_execution_role.arn
      ]
    }

    actions = [
      "sqs:SendMessage"
    ]

    resources = [
      aws_sqs_queue.etl_queue.arn
    ]
  }

  statement {
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = [
        aws_iam_role.service_task_role.arn
      ]
    }

    actions = [
      "sqs:ReceiveMessage",
      "sqs:DeleteMessage"
    ]

    resources = [
      aws_sqs_queue.etl_queue.arn
    ]
  }
}