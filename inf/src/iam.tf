resource "aws_iam_role" "service_task_role" {
  name               = "${local.service_name}-task-role"
  path               = "/tasks/"
  assume_role_policy = data.aws_iam_policy_document.service_task_role_assume_role_policy_document.json

  tags = local.common_tags
}

data "aws_iam_policy_document" "service_task_role_assume_role_policy_document" {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com", "glue.amazonaws.com"]
    }
  }
}

resource "aws_iam_role_policy_attachment" "service_task_role_policy_attachment" {
  policy_arn = aws_iam_policy.service_task_role_policy.arn
  role       = aws_iam_role.service_task_role.id
}

resource "aws_iam_policy" "service_task_role_policy" {
  name   = "${local.service_name}-task-role-policy"
  policy = data.aws_iam_policy_document.service_task_role_policy_document.json
}

data "aws_iam_policy_document" "service_task_role_policy_document" {
  statement {
    effect = "Allow"

    actions = [
      "rds-db:connect"
    ]

    resources = [
      "arn:aws:rds-db:${var.aws_region}:${var.aws_account_id}:dbuser:${module.service_rds.this_rds_cluster_resource_id}/${module.service_rds.this_rds_cluster_database_name}"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "s3:*"
    ]

    resources = [
      aws_s3_bucket.service_bucket.arn,
      "${aws_s3_bucket.service_bucket.arn}/*"
    ]
  }

  statement {
    actions   = ["s3:ListBucket*", "s3:GetObject*"]
    resources = [
      "arn:aws:s3:::prospection-data-lake-ap-northeast-1/*",
      "arn:aws:s3:::prospection-data-lake-ap-northeast-1",
      "arn:aws:s3:::prospection-data-lake-us-west-2/us/nddf/fdb/*",
      "arn:aws:s3:::prospection-data-lake-us-west-2/us/cms/*",
      "arn:aws:s3:::prospection-data-lake-us-west-2",
      "arn:aws:s3:::${var.data_lake_bucket}/*",
      "arn:aws:s3:::${var.data_lake_bucket}"
    ]
  }

  statement {
    actions   = ["s3:List*", "s3:Put*", "s3:Get*", "s3:DeleteObject"]
    resources = [
      "arn:aws:s3:::pd-au-${var.env_name}-common/external-reference-data/*",
      "arn:aws:s3:::pd-au-${var.env_name}-common",
      "arn:aws:s3:::pd-jp-${var.env_name}-common/external-reference-data/*",
      "arn:aws:s3:::pd-jp-${var.env_name}-common",
      "arn:aws:s3:::pd-us-${var.env_name}-common/external-reference-data/*",
      "arn:aws:s3:::pd-us-${var.env_name}-common"
    ]
  }

  statement {
    actions = [
      "sqs:ReceiveMessage",
      "sqs:GetQueueUrl",
      "sqs:GetQueueAttributes",
      "sqs:DeleteMessage",
      "sqs:SendMessage"
    ]
    resources = [
      aws_sqs_queue.etl_queue.arn,
      aws_sqs_queue.etl_queue_deadletter.arn
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "lambda:InvokeFunction"
    ]

    resources = [
      "arn:aws:lambda:${var.aws_region}:${var.aws_account_id}:function:${aws_lambda_function.spark.function_name}"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "sns:Publish"
    ]

    resources = [
      aws_sns_topic.job_status_notification_topic.arn
    ]
  }
}

resource aws_iam_role lambda_spark_execution_role {
  name               = "${local.service_name}-lambda-spark-execution-role"
  path               = "/lambda/"
  assume_role_policy = data.aws_iam_policy_document.lambda_execution_role_assume_role_policy_document.json

  tags = local.common_tags
}

resource aws_iam_role_policy_attachment lambda_spark_execution_role_policy_attachment {
  policy_arn = aws_iam_policy.lambda_spark_execution_role_policy.arn
  role       = aws_iam_role.lambda_spark_execution_role.id
}

resource "aws_iam_role_policy_attachment" lambda_spark_vpc_access_execution_policy_attachment {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
  role       = aws_iam_role.lambda_spark_execution_role.id
}

resource aws_iam_policy lambda_spark_execution_role_policy {
  name   = "${local.service_name}-lambda-spark-execution-role-policy"
  policy = data.aws_iam_policy_document.lambda_spark_execution_role_policy_document.json
}

data aws_iam_policy_document lambda_spark_execution_role_policy_document {
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions   = ["s3:List*", "s3:Get*"]

    resources = [
      aws_s3_bucket.service_bucket.arn,
      "${aws_s3_bucket.service_bucket.arn}/*"
    ]
  }
}

resource aws_iam_role lambda_execution_role {
  name               = "${local.service_name}-lambda-execution-role"
  path               = "/lambda/"
  assume_role_policy = data.aws_iam_policy_document.lambda_execution_role_assume_role_policy_document.json

  tags = local.common_tags
}

resource aws_iam_role_policy_attachment lambda_execution_role_policy_attachment {
  policy_arn = aws_iam_policy.lambda_execution_role_policy.arn
  role       = aws_iam_role.lambda_execution_role.id
}

resource "aws_iam_role_policy_attachment" lambda_vpc_access_execution_policy_attachment {
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
  role       = aws_iam_role.lambda_execution_role.id
}

data aws_iam_policy_document lambda_execution_role_assume_role_policy_document {
  statement {
    actions = ["sts:AssumeRole"]

    principals {
      type        = "Service"
      identifiers = [
        "lambda.amazonaws.com"
      ]
    }
  }
}

resource aws_iam_policy lambda_execution_role_policy {
  name   = "${local.service_name}-lambda-execution-role-policy"
  policy = data.aws_iam_policy_document.lambda_execution_role_policy_document.json
}

data aws_iam_policy_document lambda_execution_role_policy_document {
  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]
    resources = ["*"]
  }

  statement {
    actions   = ["s3:PutObject"]
    resources = ["${aws_s3_bucket.service_bucket.arn}/*"]
  }

  statement {
    actions   = ["s3:ListBucket"]
    resources = [
      "arn:aws:s3:::prospection-data-lake-ap-northeast-1/*",
      "arn:aws:s3:::prospection-data-lake-ap-northeast-1",
      "arn:aws:s3:::${var.data_lake_bucket}/*",
      "arn:aws:s3:::${var.data_lake_bucket}",
      "arn:aws:s3:::prospection-data-lake-us-west-2/us/nddf/fdb/*",
      "arn:aws:s3:::prospection-data-lake-us-west-2/us/cms/*",
      "arn:aws:s3:::prospection-data-lake-us-west-2"
    ]
  }


  statement {
    actions   = ["sqs:SendMessage"]
    resources = [
      aws_sqs_queue.etl_queue.arn
    ]
  }

  statement {
    actions   = ["sqs:*"]
    resources = [
      aws_sqs_queue.etl_lambda_functions_deadletter.arn
    ]
  }
}

resource aws_iam_role_policy_attachment glue_execution_role_policy_attachment {
  policy_arn = aws_iam_policy.glue_execution_role_policy.arn
  role       = aws_iam_role.service_task_role.id
}

resource aws_iam_policy glue_execution_role_policy {
  name   = "${local.service_name}-glue-execution-role-policy"
  policy = data.aws_iam_policy_document.glue_execution_role_policy_document.json
}

data aws_iam_policy_document glue_execution_role_policy_document {
  statement {
    effect    = "Allow"
    actions   = [
      "glue:GetDatabase",
      "glue:StartCrawler",
      "glue:BatchGetPartition",
      "glue:GetTable",
      "glue:CreateTable",
      "glue:BatchCreatePartition",
      "glue:CreateDatabase",
      "glue:UpdateTable",
      "glue:UpdatePartition"
    ]
    resources = ["*"]
  }

  statement {
    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]
    resources = ["*"]
  }

  statement {
    actions   = ["s3:ListBucket*", "s3:GetObject*"]
    effect    = "Allow"
    resources = [
      "arn:aws:s3:::${local.service_resource_name}/*",
      "arn:aws:s3:::${local.service_resource_name}"
    ]
  }
}
