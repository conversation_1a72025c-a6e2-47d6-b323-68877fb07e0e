variable "state_bucket" {
  type = string
}

variable "state_key" {
  type = string
}

variable "aws_account_id" {
  type = string
}

variable "tag_version" {
  type = string
}

variable "aws_region" {
  type = string
}

variable "country_prefix" {
  type = string
}

variable "env_name" {
  type = string
}

variable "env_template" {
  type = string
}

variable "project_name" {
  type = string
}

variable "data_lake_bucket" {
  type = string
}

variable "service_image" {
  type = string
}

variable "docker_user" {
  type = string
  default = "65532:65532"
}

variable "service_cpu" {
  default = 1900
}

variable "service_memory" {
  default = 8000
}

variable "service_desired_count" {
  default = 1
}

variable "service_rds_replica_count" {
  default = 1
}

variable "service_rds_instance_type" {
  default = "db.t4g.medium"
}

variable "lambdas_image_uri" {
  type = string
}

variable "lambdas_spark_image_uri" {
  type = string
}

variable "slack_channel_alerting_arn" {
  default = ""
  type = string
}

// OPTIONS

variable "enable_ecs_service_monitoring" {
  type    = bool
  default = false
}

variable "enable_s3_bucket_versioning" {
  type    = bool
  default = false
}

variable "enable_datadog_apm" {
  type    = bool
  default = false
}

variable "enable_datadog_logging" {
  type    = bool
  default = false
}

variable "enable_pbs_ref_data_scraper" {
  type    = bool
  default = false
}

variable "is_feature_toggle_service_available" {
  type    = bool
  default = false
}

// sydney timezone
variable "archive_unused_data_cron" {
  type    = string
  default = "0 0 22 * * MON-FRI"
}
