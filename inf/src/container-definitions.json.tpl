[
    {
        "name": "${name}",
        "user": "${user}",
        "image": "${image}",
        "essential": true,
        "entryPoint": ${entry_point},
        "command": [],
        "environment": [
            {
                "name": "SPRING_PROFILES_ACTIVE",
                "value": "${spring_profiles_active}"
            },
            {
                "name": "SPRING_DATA_SOURCE_SERVER_NAME",
                "value": "${service_rds_endpoint}"
            },
            {
                "name": "SPRING_DATA_SOURCE_USER",
                "value": "${service_rds_database_name}"
            },
            {
                "name": "SPRING_DATA_SOURCE_DATABASE_NAME",
                "value": "${service_rds_database_name}"
            },
            {
                "name": "ENV_NAME",
                "value": "${env_name}"
            },
            {
                "name": "DD_ENV",
                "value": "${env_name}"
            },
            {
                "name": "DD_SERVICE",
                "value": "${name}"
            },
            {
                "name": "DD_VERSION",
                "value": "${tag_version}"
            },
            {
                "name": "DD_LOGS_INJECTION",
                "value": "true"
            },
            {
              "name": "S3_BUCKET",
              "value": "${service_s3_bucket}"
            },
            {
              "name": "ETL_QUEUE_URL",
              "value": "${etl_queue_url}"
            },
            {
              "name": "AWS_REGION",
              "value": "${aws_region}"
            },
            {
               "name": "CUSTOMER_SERVICE_URL",
               "value": "${customer_service_url}"
            },
            {
                "name": "DASHX_SERVICE_URL",
                "value": "${dashx_service_url}"
            },
            {
                "name": "LAMBDA_ITEM_GROUPS_SPARK",
                "value": "${lambda_item_groups_spark}"
            },
            {
                "name": "FEATURE_TOGGLE_SERVICE_URL",
                "value": "${feature_toggle_service_url}"
            },
            {
                "name": "FEATURE_TOGGLE_SERVICE_AVAILABLE",
                "value": "${is_feature_toggle_service_available}"
            },
            {
                "name": "ARCHIVE_UNUSED_DATA_CRON",
                "value": "${archive_unused_data_cron}"
            },
            {
                "name": "APPLICATION_NAME",
                "value": "${name}"
            },
            {
                "name": "SNS_TOPIC_ARN",
                "value": "${sns_topic_arn}"
            }
        ],
        "secrets": ${jsonencode(ecs_secrets)},
        "dockerLabels": {
            "com.datadoghq.tags.env": "${env_name}",
            "com.datadoghq.tags.service": "${name}",
            "com.datadoghq.tags.version": "${tag_version}"
        },
        "portMappings": [
            {
                "containerPort": 8080,
                "protocol": "tcp"
            }
        ],
        "readonlyRootFilesystem": true,
        "mountPoints": [
            {
              "sourceVolume": "tmp",
              "containerPath": "/tmp",
              "readOnly": false
            }
        ],
        "logConfiguration": ${log_configuration}
    }
]
