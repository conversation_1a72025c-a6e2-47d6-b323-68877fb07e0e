resource "aws_lb_target_group" "service_target_group" {
  port        = 80
  protocol    = "HTTP"
  target_type = "ip"
  vpc_id      = data.terraform_remote_state.platform.outputs.vpc_id

  health_check {
    path                = "/actuator/health/liveness"
    unhealthy_threshold = 5
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.common_tags, {
    Name = "${local.service_name}-tg"
  })
}

resource "aws_lb_listener_rule" "service_https_listener_rule" {
  listener_arn = data.terraform_remote_state.platform.outputs.internal_https_listener_arn

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.service_target_group.arn
  }

  condition {
    host_header {
      values = ["${var.project_name}.*"]
    }
  }
}
