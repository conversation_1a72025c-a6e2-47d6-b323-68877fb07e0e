module "monitoring" {
    source            = "git::*****************:teamprospection/prospection-terraform-modules.git//modules/terraform-prospection-monitoring"
    service_repo_name = "pd-ref-data-service"
    service_name      = "Ref Data Service"
    team_name         = "Platform"
    # without @ or #
    team_slack        = "platform-service-alerts-${var.env_name}"
    env               = var.env_name
    country           = var.country_prefix

    got_a_repeated_error = {
        enabled = true
        monitor_threshold = 5
    }

    first_error_occurrence = {
        enabled = true
        monitor_threshold = 1
    }
}
