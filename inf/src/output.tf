output "service_task_definition_arn" {
  value = aws_ecs_task_definition.service_task_definition.arn
}

output "service_task_definition_id" {
  value = aws_ecs_task_definition.service_task_definition.id
}

output "service_task_role_arn" {
  value = aws_iam_role.service_task_role.arn
}

output "service_task_role_id" {
  value = aws_iam_role.service_task_role.id
}

output "service_log_group_arn" {
  value = aws_cloudwatch_log_group.service_log_group.arn
}

output "service_log_group_id" {
  value = aws_cloudwatch_log_group.service_log_group.id
}

output "service_log_group_name" {
  value = aws_cloudwatch_log_group.service_log_group.name
}

output "service_rds_endpoint" {
  value = module.service_rds.this_rds_cluster_endpoint
}

output "service_rds_database_name" {
  value = module.service_rds.this_rds_cluster_database_name
}

output "service_rds_master_username" {
  value = module.service_rds.this_rds_cluster_master_username
}

output "service_rds_master_password" {
  value     = module.service_rds.this_rds_cluster_master_password
  sensitive = true
}

output "ref_data_bucket" {
  value = aws_s3_bucket.service_bucket.bucket
}

output "ref_data_bucket_arn" {
  value = aws_s3_bucket.service_bucket.arn
}
