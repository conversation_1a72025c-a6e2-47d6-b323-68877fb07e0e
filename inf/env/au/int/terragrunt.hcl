locals {
  data_lake_bucket = "prospection-datalake-inte"
  enable_pbs_ref_data_scraper = false

  is_feature_toggle_service_available = true

  enable_datadog_apm            = true
  enable_datadog_logging        = true

  archive_unused_data_cron      = "0 0 19 * * MON-FRI"
}

terraform {
  source = "../../..//src"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  data_lake_bucket = local.data_lake_bucket
  enable_pbs_ref_data_scraper = local.enable_pbs_ref_data_scraper

  is_feature_toggle_service_available = local.is_feature_toggle_service_available

  enable_datadog_apm            = local.enable_datadog_apm
  enable_datadog_logging        = local.enable_datadog_logging

  archive_unused_data_cron      = local.archive_unused_data_cron
}