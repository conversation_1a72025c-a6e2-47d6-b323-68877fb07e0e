locals {
  enable_ecs_service_monitoring = true
  slack_channel_alerting_arn    = "arn:aws:sns:ap-southeast-2:102782770593:aws-chatbot-infra-alerting-ap-southeast-2-non-prod"
  data_lake_bucket              = "prospection-datalake-uat"

  is_feature_toggle_service_available = true

  enable_datadog_apm            = true
  enable_datadog_logging        = true

  enable_pbs_ref_data_scraper   = false
  archive_unused_data_cron      = "0 0 19 * * MON-FRI"
}

terraform {
  source = "../../..//src"
}

include {
  path = find_in_parent_folders()
}

inputs = {
  enable_ecs_service_monitoring = local.enable_ecs_service_monitoring
  slack_channel_alerting_arn    = local.slack_channel_alerting_arn
  data_lake_bucket              = local.data_lake_bucket

  is_feature_toggle_service_available = local.is_feature_toggle_service_available

  enable_datadog_apm            = local.enable_datadog_apm
  enable_datadog_logging        = local.enable_datadog_logging

  enable_pbs_ref_data_scraper   = local.enable_pbs_ref_data_scraper
  archive_unused_data_cron      = local.archive_unused_data_cron
}
