### Infrastructure Stage ###
FROM alpine/terragrunt:1.9.8@sha256:448f2b2721e884535f02c7a2e66d0906502e11c928854c2ce54e84db6b30a1dc as inf

WORKDIR /inf

COPY ./inf /inf

# use buildkite instance credentials to fetch the bitbucket private key:
RUN apk add aws-cli
RUN aws s3 cp s3://prospection-buildkite-secrets-bucket/private_ssh_key_buildkite_readonly /root/private_ssh_key_buildkite_readonly
RUN chmod 600 /root/private_ssh_key_buildkite_readonly
ENV GIT_SSH_COMMAND "ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -i /root/private_ssh_key_buildkite_readonly"

CMD []

### Build Stage ###
FROM openjdk:17.0.2-jdk-slim as build

RUN apt-get update && apt-get install -y wget

WORKDIR /work
# Create scala-spark directory if not exists
WORKDIR /work/scala-spark
WORKDIR /work/common
WORKDIR /work/kotlin-spark
WORKDIR /work/lambdas-spark
WORKDIR /work

# Cache Gradle Wrapper
COPY ./gradle /work/gradle
COPY ./gradlew /work/gradlew
RUN ./gradlew -v

ARG NEXUS_USER
ARG NEXUS_PASSWORD

# Cache Gradle Dependencies
COPY build.gradle.kts /work/
COPY ./common/build.gradle.kts /work/common/
COPY ./scala-spark/build.gradle /work/scala-spark/
COPY ./kotlin-spark/build.gradle.kts /work/kotlin-spark/
COPY ./lambdas-spark/build.gradle.kts /work/lambdas-spark/
COPY settings.gradle.kts /work/
COPY gradle.properties /work/
RUN ./gradlew clean build :lambdas-spark:build \
    -x generateGitProperties \
    -x test \
    -x bootJar \
    -PnexusUser="$NEXUS_USER" \
    -PnexusPassword="$NEXUS_PASSWORD"

COPY . /work
RUN ./gradlew clean build compileTestKotlin \
    -x test \
    -PnexusUser="$NEXUS_USER" \
    -PnexusPassword="$NEXUS_PASSWORD" && \
    ./gradlew :lambdas-spark:shadowJar \
    -PnexusUser="$NEXUS_USER" \
    -PnexusPassword="$NEXUS_PASSWORD"

# Download DataDog Java agent
# If no DataDog Java Agent download URL is specified, download the latest version of the Agent
ARG DATADOG_AGENT_URL='https://dtdg.co/latest-java-tracer'
RUN wget -O dd-java-agent.jar "${DATADOG_AGENT_URL}"

CMD []

### Run Stage ###
FROM gcr.io/distroless/java17-debian12:debug as app

EXPOSE 8080/tcp

WORKDIR /app

COPY --from=build --chown=nonroot:nonroot /work/build/libs/*.jar application.jar
COPY --from=build --chown=nonroot:nonroot /work/set-datadog-config.sh set-datadog-config.sh
COPY --from=build --chown=nonroot:nonroot /work/dd-java-agent.jar dd-java-agent.jar
USER nonroot

# Default jvm will use 25% of container memory.
# we set it to 75% by default and leave the rest for system and datadog agent.
ENV JAVA_TOOL_OPTIONS="-XX:MaxRAMPercentage=75"

CMD ["application.jar"]

### Run Stage ###
FROM gcr.io/distroless/java17-debian12:debug as lambdas-spark

WORKDIR /app

COPY --from=build --chown=nonroot:nonroot /work/lambdas-spark/build/libs/lambdas-spark-0.0.1-SNAPSHOT-all.jar lambdas-spark.jar
USER nonroot

ENV JAVA_TOOL_OPTIONS="-XX:MaxRAMPercentage=70"

# Set runtime interface client as default command for the container runtime
ENTRYPOINT [ "/usr/bin/java", "-cp", "lambdas-spark.jar", "com.amazonaws.services.lambda.runtime.api.client.AWSLambda" ]
# Pass the name of the function handler as an argument to the runtime
CMD [ "com.prospection.refdata.itemgroups.lambda.ItemGroupsSparkHandler::handleRequest" ]