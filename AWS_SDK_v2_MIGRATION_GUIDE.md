# AWS SDK v1 to v2 Migration Guide

This document summarizes all the key changes required when upgrading your codebase from AWS SDK v1 to v2, with a focus on S3 usage and integration with Spring.

---

## 1. Dependency Updates
- **Remove** all AWS SDK v1 dependencies (e.g., `com.amazonaws:aws-java-sdk-s3`).
- **Add** AWS SDK v2 dependencies (e.g., `software.amazon.awssdk:s3`, `software.amazon.awssdk:auth`).

---

## 2. S3 Client Usage
- **v1:** `AmazonS3`
- **v2:** `S3Client`
  - Update all instantiations and injections to use `S3Client`.

---

## 3. S3 Object Content Access
- **v1:** `S3Object` (metadata + content stream via `.getObjectContent()`)
- **v2:** `S3Object` is metadata only (from list operations).
  - **To read/download content:**
    - Use `S3Client.getObject(GetObjectRequest)`
    - Returns `ResponseInputStream<GetObjectResponse>`
    - **Update all code** that previously used `S3Object` for content to use `ResponseInputStream<GetObjectResponse>`.

---

## 4. API Changes
- All request/response objects are now immutable builders (e.g., `GetObjectRequest.builder().key(...).build()`).
- Use `RequestBody.fromString()`, `RequestBody.fromBytes()`, etc. for uploads.
- Use `ListObjectsV2Request` for listing objects.

---

## 5. Presigned URLs
- **v1:** `GeneratePresignedUrlRequest` via `AmazonS3`
- **v2:** Use `S3Presigner` and `GetObjectPresignRequest`.
  - Define a Spring bean for `S3Presigner` (not provided by default in Spring Cloud AWS).

---

## 6. Spring Configuration
- **Beans:**
  - Update all beans and injections from v1 types to v2 types.
  - Provide custom beans for `S3Client` and `S3Presigner` in your configuration, using the appropriate credentials and endpoints for each environment/profile.

---

## 7. Interface & Type Changes
- **If you had:**
  - `ImportExportHelper<S3Object>`
- **Change to:**
  - `ImportExportHelper<ResponseInputStream<GetObjectResponse>>`
- **Update all usages:**
  - Constructor injections, mocks in tests, and all usages of the type parameter.
  - Update all lambdas and method signatures that previously expected `S3Object` to expect and use the input stream.

---

## 8. Code Patterns to Refactor
- Remove all usages of `.objectContent`, `.getObjectContent()`, or direct S3Object content access.
- Update all CSV/Excel/ZIP reading logic to use the input stream directly.
- Remove redundant second calls to `getObject` inside lambdas—the stream is provided by the new interface.

---

## 9. Testing
- Update all test mocks and usages to work with the new types (`ResponseInputStream<GetObjectResponse>`).

---

## 10. Configuration Properties
- Ensure your `application.yml`/`application-private.yml` have the correct keys for v2 (e.g., endpoint, credentials, region).

---

## Example: Old vs. New S3 File Read

**Old (v1):**
```kotlin
importExportHelper.readFile(path) { s3Object ->
    val stream = s3Object.objectContent
    // process stream
}
```

**New (v2):**
```kotlin
importExportHelper.readFile(path) { inputStream ->
    // process inputStream directly
}
```

---

## Additional Notes
- Spring Cloud AWS does not provide a default bean for `S3Presigner`; you must define it.
- Always use the builder pattern for requests and responses.

---

**This document serves as a reference for AWS SDK v2 migration and future onboarding.**
