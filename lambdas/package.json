{"engines": {"node": ">=20.16.0 <22"}, "scripts": {"build": "tsc && esbuild src/index.ts --bundle --platform=node --target=node20 --outdir=build", "lint": "prettier --write \"src/**/*.ts\"", "test": "tsc && jest"}, "devDependencies": {"@aws-sdk/types": "^3.55.0", "@babel/core": "^7.17.9", "@babel/plugin-transform-modules-commonjs": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@tsconfig/recommended": "^1.0.2", "@types/cheerio": "^0.22.31", "@types/jest": "^27.4.1", "@types/node-fetch": "^2.6.1", "@types/unzipper": "^0.10.5", "aws-sdk-client-mock": "^2.0.0", "aws-sdk-client-mock-jest": "^2.0.0", "babel-jest": "^28.1.3", "esbuild": "^0.14.36", "jest": "^28.1.3", "prettier": "^2.6.2", "typescript": "^4.6.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.75.0", "@aws-sdk/client-sqs": "^3.75.0", "@aws-sdk/lib-storage": "^3.87.0", "cheerio": "^1.0.0-rc.10", "dayjs": "^1.11.2", "node-fetch": "^3.2.3", "unzipper": "^0.10.11"}}