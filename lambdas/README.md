# Lambda functions in pd-ref-data-service

The purpose of this module is to build executable files to be run in AWS Lambda. Check [this wiki](https://prospection.atlassian.net/l/c/9wKH0Bx1) to see the role of the lambda and the detailed structure.

## Installation
Like always :)

```bash
npm ci
```

_Note that this project requires Node.js >= 20.16.0_

## Running lambda on your local
### Prerequisite
* SAM CLI - [Install the SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html) - for local testing. As of writing this doc, v1.46.0 was used.
* Docker - [Install Docker community edition](https://hub.docker.com/search/?type=edition&offering=community) - for local testing
* Local SQS and S3 must be running. They're managed in the root project. Run `make app_local_compose_up` at the root directory.
* Run the main reference data service in local profile.

And then run these commands in the current directory (./lambdas).
```bash
sam build
sam local invoke 

# or if you want to run a particular lambda function
sam build PbsItemsScraper
sam local invoke PbsItemsScraper # function name can be found in template.yaml 
```
Note that we're not using AWS SAM CLI for deployment and non-dev environments, instead, we use terraform.


## Testing
The below command will run the tests in src folder
```bash
npm run test
```

## Linting
The below command will automatically lint-fix your code.
```bash
npm run lint
```

## Explanation of Build & Deployment

```bash
npm run build
```

The above command will be run in a docker image (scripts are available in [Makefile](../Makefile) and
[Dockerfile](./Dockerfile)) when this module is deployed. The docker image will be deployed and run in AWS Lambda. The
command will build all the lambdas at once that are listed in [index.ts](./src/index.ts) (therefore, if you add a new
lambda function, please add it to the [index.ts](./src/index.ts) file). An output file `index.js` will be generated
under [build](./build) directory inside docker image (or on your local).

Then the docker image is pushed to our ECR. A Lambda function is deployed which specifies the docker image and container
override.
The container override specifies the CMD which the image should use to run. Each Lambda specifies a CMD which
points to its related handler in `build/index`.

Check Makefile, Dockerfile, and terraform scripts in this project for more details.

## Adding a New Lambda function
Ok you've written a new function with a handler that lives somewhere in `lambdas/src`. What's next?
### Lambda Code
Ensure your function gets picked up by our build process.
1. Add your new handler to `lambdas/src/index.ts`.
### Local SAM Setup
AWS SAM is used to invoke/test functions locally. SAM can figure out the Lambda configuration based off a 
CloudFormation template. However because we use Terraform, and it's not supported by SAM we must mirror our setup to aid local 
development. [Terraform support is a highly requested feature](https://github.com/aws/aws-sam-cli/issues/3154).

In `lambdas/template.yaml`:
1. Under the `Resources` property copy an existing function.
2. Change the name of the new function and update `Command: build/index.MyHandler` to point to your handler.
### Terraform Setup
In `inf/src/lambda.tf`:
1. Copy an existing function.
2. Update `image_config` to point to the handler of the new function.
3. Update the `function_name` and `description`.