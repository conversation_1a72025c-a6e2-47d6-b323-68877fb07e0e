import { DATE_FORMAT_YYYYMM, REGION_AP_NORTHEAST_1, SOURCE_BUCKET } from '../common/constants';
import { latestS3PartitionDate } from '../common/s3-functions';
import { TriggerEtlJob, triggerJobs } from '../common/trigger-job';
import { getLastDateOfMonth } from '../common/date-functions';

export const Source = {
  JMDC_DIAGNOSIS_CODING_SYSTEM: 'JMDC Diagnosis',
  JMDC_PROCEDURE_CODING_SYSTEM: 'JMDC Procedure',
  JMDC_DRUG_CODING_SYSTEM: 'JMDC Drug',
  JMDC_MATERIAL_CODING_SYSTEM: 'JMDC Material'
};

export const JMDC_PREFIX = 'jp/jmdc/';

const getVersionDateToCheck = async (partition: string) => {
  const year = partition.substring(0, 4);
  const month = partition.substring(4, 6);
  return getLastDateOfMonth(year, month);
};

export const JMDC_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.JMDC_DIAGNOSIS_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      prefix: JMDC_PREFIX,
      region: REGION_AP_NORTHEAST_1,
      partitionFormat: DATE_FORMAT_YYYYMM
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.JMDC_DIAGNOSIS_CODING_SYSTEM,
        fullPathPlaceholder: `${JMDC_PREFIX}{partition}`
      }
    ]
  },
  {
    codingSystem: Source.JMDC_PROCEDURE_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      prefix: JMDC_PREFIX,
      region: REGION_AP_NORTHEAST_1,
      partitionFormat: DATE_FORMAT_YYYYMM
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.JMDC_PROCEDURE_CODING_SYSTEM,
        fullPathPlaceholder: `${JMDC_PREFIX}{partition}`
      }
    ]
  },
  {
    codingSystem: Source.JMDC_DRUG_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      prefix: JMDC_PREFIX,
      region: REGION_AP_NORTHEAST_1,
      partitionFormat: DATE_FORMAT_YYYYMM
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.JMDC_DRUG_CODING_SYSTEM,
        fullPathPlaceholder: `${JMDC_PREFIX}{partition}`
      }
    ]
  },
  {
    codingSystem: Source.JMDC_MATERIAL_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      prefix: JMDC_PREFIX,
      region: REGION_AP_NORTHEAST_1,
      partitionFormat: DATE_FORMAT_YYYYMM
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.JMDC_MATERIAL_CODING_SYSTEM,
        fullPathPlaceholder: `${JMDC_PREFIX}{partition}`
      }
    ]
  }
];

export const jmdcHandler = async function (event, context) {
  return await triggerJobs(JMDC_TRIGGER_JOBS);
};
