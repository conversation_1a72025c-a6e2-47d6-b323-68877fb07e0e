import * as common from '../common';
import * as s3Functions from '../common/s3-functions';
import * as sqsFunctions from '../common/sqs-functions';
import { ETL_QUEUE_URL, SOURCE_BUCKET } from '../common/constants';
import { JMDC_PREFIX, jmdcHandler, Source } from './index';

jest.mock('../common');
jest.mock('../common/s3-functions');
jest.mock('../common/sqs-functions');

const mockFindLatestS3Partition = s3Functions.latestS3PartitionDate as jest.MockedFunction<
  typeof s3Functions.latestS3PartitionDate
>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

describe('JMDC ETL Trigger', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should not send message if etl not possible', async () => {
    mockFindLatestS3Partition.mockResolvedValue('202001');
    mockCheckEtlJobPossibility.mockResolvedValue(false);

    const result = await jmdcHandler(event, context);
    expect(result[Source.JMDC_DIAGNOSIS_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.JMDC_PROCEDURE_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.JMDC_DRUG_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.JMDC_MATERIAL_CODING_SYSTEM]).toBeFalsy();

    expect(mockSendEtlMessage).not.toBeCalled();
  });

  it('should send ETL SQS message if ETL is JMDC Procedure possible only', async () => {
    const lastPartition = '202002';
    const version = '20200229';
    const expectedVersion = '2020-02-29';
    mockFindLatestS3Partition.mockResolvedValue(lastPartition);
    mockCheckEtlJobPossibility.mockImplementation((parameters) =>
      Promise.resolve(Source.JMDC_PROCEDURE_CODING_SYSTEM === parameters.classification)
    );

    const result = await jmdcHandler(event, context);

    expect(result[Source.JMDC_DIAGNOSIS_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.JMDC_PROCEDURE_CODING_SYSTEM]).toBe(expectedVersion);
    expect(result[Source.JMDC_DRUG_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.JMDC_MATERIAL_CODING_SYSTEM]).toBeFalsy();

    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.JMDC_PROCEDURE_CODING_SYSTEM, lastPartition, version)
    );
  });

  it('should send ETL SQS message if ETL is possible for JMDC Diagnosis/JMDC Procedure/JMDC Drug/JMDC Material', async () => {
    const lastPartition = '202001';
    const version = '20200131';
    const expectedVersion = '2020-01-31';

    mockFindLatestS3Partition.mockResolvedValue(lastPartition);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    const result = await jmdcHandler(event, context);
    expect(result[Source.JMDC_DIAGNOSIS_CODING_SYSTEM]).toBe(expectedVersion);
    expect(result[Source.JMDC_PROCEDURE_CODING_SYSTEM]).toBe(expectedVersion);
    expect(result[Source.JMDC_DRUG_CODING_SYSTEM]).toBe(expectedVersion);
    expect(result[Source.JMDC_MATERIAL_CODING_SYSTEM]).toBe(expectedVersion);

    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.JMDC_DIAGNOSIS_CODING_SYSTEM, lastPartition, version)
    );
    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.JMDC_PROCEDURE_CODING_SYSTEM, lastPartition, version)
    );
    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.JMDC_DRUG_CODING_SYSTEM, lastPartition, version)
    );
    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.JMDC_MATERIAL_CODING_SYSTEM, lastPartition, version)
    );
  });
});

export const createEtlMessage = (codingSystem, lastPartition, version): any => {
  return {
    queueUrl: ETL_QUEUE_URL,
    paths: {
      [codingSystem]: {
        bucket: SOURCE_BUCKET,
        path: JMDC_PREFIX + lastPartition
      }
    },
    version: version,
    classification: codingSystem,
    messageGroupId: codingSystem.replace(/ /g, '_')
  };
};
