import { SOURCE_BUCKET, SOURCE_BUCKET_REGION } from '../common/constants';
import { findLatestS3Object } from '../common/s3-functions';
import { getDateFromFileNameWithFormatDDMMMYYYY } from '../common/date-functions';
import { TriggerEtlJob, triggerJobs } from '../common/trigger-job';

export const PBS_AUTHORITY_PATH_PREFIX = 'aus/dhs/pbs-10/raw/caveats/';
export const Source = {
  PBS_AUTHORITY_CODING_SYSTEM: 'PBS Authority'
};

const getVersionDateToCheck = async (partition: string) => {
  return getDateFromFileNameWithFormatDDMMMYYYY(partition);
};

export const PBS_AUTHORITY_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.PBS_AUTHORITY_CODING_SYSTEM,
    findLatestSource: findLatestS3Object,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      prefix: PBS_AUTHORITY_PATH_PREFIX,
      region: SOURCE_BUCKET_REGION
    },
    getVersionDateToCheck: getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.PBS_AUTHORITY_CODING_SYSTEM,
        fullPathPlaceholder: `${PBS_AUTHORITY_PATH_PREFIX}{partition}`
      }
    ]
  }
];
export const pbsAuthorityHandler = async function (event, context) {
  return await triggerJobs(PBS_AUTHORITY_TRIGGER_JOBS);
};
