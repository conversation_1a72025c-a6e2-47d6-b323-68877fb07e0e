import * as common from '../common';
import * as s3Functions from '../common/s3-functions';
import * as sqsFunctions from '../common/sqs-functions';
import { ETL_QUEUE_URL, SOURCE_BUCKET } from '../common/constants';
import { PBS_AUTHORITY_PATH_PREFIX, pbsAuthorityHandler, Source } from './index';

jest.mock('../common');
jest.mock('../common/s3-functions');
jest.mock('../common/sqs-functions');

const { checkEtlJobPossibility: actualCheckEtlJobPossibility } = jest.requireActual('../common');

const mockFindLatestFile = s3Functions.findLatestS3Object as jest.MockedFunction<typeof s3Functions.findLatestS3Object>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

describe('PBS Authority ETL Trigger', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should throw an error if latest file is not date format', () => {
    mockFindLatestFile.mockResolvedValue('abcde.xlsx');
    mockCheckEtlJobPossibility.mockImplementation(actualCheckEtlJobPossibility);
    expect(pbsAuthorityHandler(event, context)).rejects.toThrowError(TypeError);
  });

  it('should not send message if etl not possible', async () => {
    mockFindLatestFile.mockResolvedValue('file_01JAN2022.xlsx');
    mockCheckEtlJobPossibility.mockResolvedValue(false);
    const result = await pbsAuthorityHandler(event, context);
    expect(result[Source.PBS_AUTHORITY_CODING_SYSTEM]).toBeFalsy();
    expect(mockSendEtlMessage).not.toBeCalled();
  });

  it('should send ETL SQS message if ETL is possible', async () => {
    const latestDataset = 'file_07FEB2022.xlsx';
    mockFindLatestFile.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await pbsAuthorityHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.PBS_AUTHORITY_CODING_SYSTEM]: {
          bucket: SOURCE_BUCKET,
          path: `${PBS_AUTHORITY_PATH_PREFIX}${latestDataset}`
        }
      },
      version: '20220207',
      classification: Source.PBS_AUTHORITY_CODING_SYSTEM,
      messageGroupId: Source.PBS_AUTHORITY_CODING_SYSTEM.replace(/ /g, '_')
    });
  });
});
