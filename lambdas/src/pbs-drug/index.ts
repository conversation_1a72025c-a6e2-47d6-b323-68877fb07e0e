import fetch from 'node-fetch';
import cheerio from 'cheerio';
import dayjs from 'dayjs';
import { TriggerEtlJob, triggerJobs } from '../common/trigger-job';
import { DATE_FORMAT_YYYYMMDD, REFERENCE_DATA_BUCKET, REGION_AP_SOUTHEAST_2 } from '../common/constants';
import { unzipAndStoreFilesInS3 } from '../common/s3-functions';
export const PBS_DRUG_S3_PATH_PREFIX = 'original/PBS Drug/';
export const PBS_WEBSITE_DOMAIN = 'https://www.pbs.gov.au';
export const PBS_ITEMS_DOWNLOAD_PAGE_URL = `${PBS_WEBSITE_DOMAIN}/browse/downloads`;

export const Source = {
  PBS_DRUG_CODING_SYSTEM: 'PBS Drug'
};

const getVersionDateToCheck = async (date: string) => {
  return dayjs(date).toDate();
};

const getDownloadDate = async () => {
  const response = await fetch(PBS_ITEMS_DOWNLOAD_PAGE_URL);
  if (!response.ok) {
    throw Error('Can not fetch last updated date from PBS website');
  }
  const pageSelector = cheerio.load(await response.text());
  return await getPbsDrugDate(pageSelector);
};

const getDownloadUrl = async () => {
  const response = await fetch(PBS_ITEMS_DOWNLOAD_PAGE_URL);
  if (!response.ok) {
    throw Error('Can not fetch download URL from PBS website');
  }
  const pageSelector = cheerio.load(await response.text());
  return await getPbsDrugDownloadUrl(pageSelector);
};

const getPbsDrugDownloadUrl = async (pageSelector: cheerio.Root) => {
  const pbsTextFilesHrefVal = pageSelector(`a[href$="-v3extracts.zip"]`).attr('href');
  const regexToExtractDownloadUrn = /^\/\.\.(.+$)/;
  const pbsTextFileUrn = regexToExtractDownloadUrn.exec(pbsTextFilesHrefVal)[1];
  return `${PBS_WEBSITE_DOMAIN}${pbsTextFileUrn}`;
};

const getPbsDrugDate = async (pageSelector: cheerio.Root) => {
  const pbsTextFilesHrefVal = pageSelector(`a[href$="-v3extracts.zip"]`).attr('href');
  const regexToExtractDate = /^.*\/(.+)-v3extracts\.zip$/;
  return dayjs(regexToExtractDate.exec(pbsTextFilesHrefVal)[1]).format(DATE_FORMAT_YYYYMMDD);
};

export const downloadPbsDrugToS3 = async (versionDate: Date) => {
  const url = await getDownloadUrl();
  console.log(url);
  const s3Path = `${PBS_DRUG_S3_PATH_PREFIX}${dayjs(versionDate).format(DATE_FORMAT_YYYYMMDD)}`;
  await unzipAndStoreFilesInS3([url], REFERENCE_DATA_BUCKET, REGION_AP_SOUTHEAST_2, s3Path);
};

export const PBS_DRUG_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.PBS_DRUG_CODING_SYSTEM,
    findLatestSource: getDownloadDate,
    getVersionDateToCheck: getVersionDateToCheck,
    download: downloadPbsDrugToS3,
    etlFiles: [
      {
        bucket: REFERENCE_DATA_BUCKET,
        classification: Source.PBS_DRUG_CODING_SYSTEM,
        fullPathPlaceholder: `${PBS_DRUG_S3_PATH_PREFIX}{partition}`
      }
    ]
  }
];

export const pbsDrugHandler = async function (event, context) {
  return await triggerJobs(PBS_DRUG_TRIGGER_JOBS);
};
