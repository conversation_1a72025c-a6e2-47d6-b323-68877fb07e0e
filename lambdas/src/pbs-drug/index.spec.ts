import { Source, PBS_WEBSITE_DOMAIN, pbsDrug<PERSON><PERSON><PERSON>, PBS_DRUG_S3_PATH_PREFIX } from '.';

import fetch, { Response as FetchResponse } from 'node-fetch';
import { Readable } from 'stream';
import { DATA_LAKE_BUCKET_US_WEST_2, ETL_QUEUE_URL, REFERENCE_DATA_BUCKET, SOURCE_BUCKET } from '../common/constants';
import * as common from '../common';
import * as sqsFunctions from '../common/sqs-functions';
import { SendMessageCommand } from '@aws-sdk/client-sqs';
import { FDB_DATA_PREFIX } from '../fdb-drug';

jest.mock('node-fetch', () => jest.fn());
jest.mock('../common');
jest.mock('../common/sqs-functions');

const MockSendMessageCommand = SendMessageCommand as jest.MockedClass<typeof SendMessageCommand>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

const MOCK_PUBLISH_DATE = '2022-05-01';
const PBS_TEXT_FILES_URN = '/downloads/2022/05/2022-05-01-v3extracts.zip';
const MOCK_PBS_WEBSITE_HTML = `<div>
<a href="/..${PBS_TEXT_FILES_URN}">PBS Text files (ZIP 3MB)&nbsp;</a>
</div>`;

describe('PBS Drug Scraper Handler', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should throw error when fetching last updated date fail', async () => {
    mockFetch.mockReturnValueOnce(mockNfResponse({ ok: false }));
    await expect(pbsDrugHandler(event, context)).rejects.toThrowError(
      'Can not fetch last updated date from PBS website'
    );
  });

  it('should throw error when fetching download URL fail', async () => {
    mockCheckEtlJobPossibility.mockResolvedValue(true);
    mockFetch
      .mockReturnValueOnce(mockNfResponse({ bodyText: MOCK_PBS_WEBSITE_HTML }))
      .mockReturnValueOnce(mockNfResponse({ ok: false }));
    await expect(pbsDrugHandler(event, context)).rejects.toThrowError('Can not fetch download URL from PBS website');
  });

  it('should return null when ETL job is not possible', async () => {
    mockFetch
      .mockReturnValueOnce(mockNfResponse({ bodyText: MOCK_PBS_WEBSITE_HTML }))
      .mockReturnValueOnce(mockNfResponse({ ok: true }));
    mockCheckEtlJobPossibility.mockResolvedValue(false);

    const result = await expect(pbsDrugHandler(event, context));
    expect(result[Source.PBS_DRUG_CODING_SYSTEM]).toBeFalsy();
  });

  it('should throw error when fetch PBS file download fails', async () => {
    mockCheckEtlJobPossibility.mockResolvedValue(true);
    mockFetch
      .mockReturnValueOnce(mockNfResponse({ bodyText: MOCK_PBS_WEBSITE_HTML }))
      .mockReturnValueOnce(mockNfResponse({ bodyText: MOCK_PBS_WEBSITE_HTML }))
      .mockReturnValueOnce(mockNfResponse({ ok: false }))
      .mockReturnValueOnce(mockNfResponse({ ok: false }));
    await expect(pbsDrugHandler(event, context)).rejects.toThrowError('Fetch pbs files fail');
  });

  it('should return date when elt job is triggered', async () => {
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    mockFetch
      .mockReturnValueOnce(mockNfResponse({ bodyText: MOCK_PBS_WEBSITE_HTML }))
      .mockReturnValueOnce(mockNfResponse({ bodyText: MOCK_PBS_WEBSITE_HTML }))
      .mockReturnValueOnce(mockNfResponse({ body: mockReadableStream() }))
      .mockReturnValueOnce(mockNfResponse({ body: mockReadableStream() }));
    const results = await pbsDrugHandler(event, context);

    expect(mockFetch).toHaveBeenCalledWith(`${PBS_WEBSITE_DOMAIN}${PBS_TEXT_FILES_URN}`);

    const expectVersion = '20220501';
    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.PBS_DRUG_CODING_SYSTEM]: {
          path: `${PBS_DRUG_S3_PATH_PREFIX}${expectVersion}`,
          bucket: REFERENCE_DATA_BUCKET
        }
      },
      version: expectVersion,
      classification: Source.PBS_DRUG_CODING_SYSTEM,
      messageGroupId: Source.PBS_DRUG_CODING_SYSTEM.replace(/ /g, '_')
    });
    expect(results[Source.PBS_DRUG_CODING_SYSTEM]).toBe(MOCK_PUBLISH_DATE);
  });
});

const mockNfResponse = (
  response: Partial<FetchResponse> & { bodyText?: string; bodyJson?: any } = {}
): Promise<FetchResponse> => {
  response = {
    ok: true,
    ...response,
    json: () => Promise.resolve(response.bodyJson),
    text: () => Promise.resolve(response.bodyText)
  };
  return Promise.resolve(response as FetchResponse);
};

const mockReadableStream = () =>
  new Readable({
    read: function () {
      this.emit('close');
    }
  });
