import * as common from '../common';
import * as s3Functions from '../common/s3-functions';
import * as sqsFunctions from '../common/sqs-functions';
import dayjs from 'dayjs';
import { DATE_FORMAT_YYYYMMDD, ETL_QUEUE_URL, SOURCE_BUCKET } from '../common/constants';
import { mdvHandler, Source } from './index';

jest.mock('../common');
jest.mock('../common/s3-functions');
jest.mock('../common/sqs-functions');

const mockFindPartition = s3Functions.latestS3PartitionDate as jest.MockedFunction<
  typeof s3Functions.latestS3PartitionDate
>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

describe('MDV ETL Trigger', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should throw an error if partition is not date format', () => {
    mockFindPartition.mockResolvedValue('abcde');
    mockCheckEtlJobPossibility.mockResolvedValue(true);
    expect(mdvHandler(event, context)).rejects.toThrow('Found partition can not be converted to a date: abcde');
  });

  it('should not message if etl not possible', async () => {
    mockFindPartition.mockResolvedValue('2020-01-01');
    mockCheckEtlJobPossibility.mockResolvedValue(false);

    const result = await mdvHandler(event, context);
    expect(result[Source.MDV_DIAGNOSIS]).toBeFalsy();
    expect(result[Source.MDV_LAB_RESULT]).toBeFalsy();
    expect(result[Source.MDV_ITEM]).toBeFalsy();
  });

  it('should send MDV Diagnosis ETL SQS message if ETL is possible', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await mdvHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.MDV_DISEASE]: { path: 'jp/mdv/mdv/all/2020-01-01/raw/DPC/', bucket: SOURCE_BUCKET },
        [Source.MDV_HIA_DISEASE]: { path: 'jp/mdv/mdv/all/2023-11-01/raw/HIA/', bucket: SOURCE_BUCKET }
      },
      version: dayjs(new Date(latestDataset)).format(DATE_FORMAT_YYYYMMDD),
      classification: Source.MDV_DIAGNOSIS,
      messageGroupId: Source.MDV_DIAGNOSIS.replace(/ /g, '_')
    });
  });

  it('should send MDV Lab Result ETL SQS message if ETL is possible', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await mdvHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.MDV_LAB_RESULT]: { path: `jp/mdv/mdv/all/2020-01-01/raw/DPC/`, bucket: SOURCE_BUCKET }
      },
      version: dayjs(new Date(latestDataset)).format(DATE_FORMAT_YYYYMMDD),
      classification: Source.MDV_LAB_RESULT,
      messageGroupId: Source.MDV_LAB_RESULT.replace(/ /g, '_')
    });
  });

  it('should send MDV Item ETL SQS message if ETL is possible', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await mdvHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.MDV_ITEM]: { path: `jp/mdv/mdv/all/2020-01-01/raw/DPC/`, bucket: SOURCE_BUCKET }, //
        [Source.MDV_HIA_ITEM]: { path: `jp/mdv/mdv/all/2023-11-01/raw/HIA/`, bucket: SOURCE_BUCKET }
      },
      version: dayjs(new Date(latestDataset)).format(DATE_FORMAT_YYYYMMDD),
      classification: Source.MDV_ITEM,
      messageGroupId: Source.MDV_ITEM.replace(/ /g, '_')
    });
  });

  it('should send ETL SQS message for all MDV ETLs', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await mdvHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledTimes(3);
  });
});
