import { getVersionDateTo<PERSON>heck, TriggerEtlJob, triggerJobs } from '../common/trigger-job';
import { DATE_FORMAT_YYYY_MM_DD, REGION_AP_NORTHEAST_1, SOURCE_BUCKET } from '../common/constants';
import { latestS3PartitionDate } from '../common/s3-functions';

export const Source = {
  MDV_DISEASE: 'MDV Disease',
  MDV_HIA_DISEASE: 'MDV HIA Disease',
  MDV_ITEM: 'MDV Item',
  MDV_HIA_ITEM: 'MDV HIA Item',
  MDV_LAB_RESULT: 'MDV Lab Result',
  MDV_DIAGNOSIS: 'MDV Diagnosis'
};

const MDV_PARTITION = 'jp/mdv/mdv/all/';
const DPC_SUB_PATH = 'raw/DPC/';
const HIA_SUB_PATH = 'raw/HIA/';

export const MDV_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.MDV_LAB_RESULT,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      region: REGION_AP_NORTHEAST_1,
      prefix: MDV_PARTITION,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.MDV_LAB_RESULT,
        fullPathPlaceholder: `${MDV_PARTITION}{partition}/${DPC_SUB_PATH}`
      }
    ]
  },
  {
    codingSystem: Source.MDV_DIAGNOSIS,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      region: REGION_AP_NORTHEAST_1,
      prefix: MDV_PARTITION,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.MDV_HIA_DISEASE,
        // HIA is no longer support a new version. But we still keep ETL run with this version for backward compatibility.
        fullPathPlaceholder: `${MDV_PARTITION}2023-11-01/${HIA_SUB_PATH}`
      },
      {
        bucket: SOURCE_BUCKET,
        classification: Source.MDV_DISEASE,
        fullPathPlaceholder: `${MDV_PARTITION}{partition}/${DPC_SUB_PATH}`
      }
    ]
  },
  {
    codingSystem: Source.MDV_ITEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      region: REGION_AP_NORTHEAST_1,
      prefix: MDV_PARTITION,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.MDV_HIA_ITEM,
        // HIA is no longer support a new version. But we still keep ETL run with this version for backward compatibility.
        fullPathPlaceholder: `${MDV_PARTITION}2023-11-01/${HIA_SUB_PATH}`
      },
      {
        bucket: SOURCE_BUCKET,
        classification: Source.MDV_ITEM,
        fullPathPlaceholder: `${MDV_PARTITION}{partition}/${DPC_SUB_PATH}`
      }
    ]
  }
];

export const mdvHandler = async (event, context) => {
  return await triggerJobs(MDV_TRIGGER_JOBS);
};
