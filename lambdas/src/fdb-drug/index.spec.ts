import * as common from '../common';
import * as s3Functions from '../common/s3-functions';
import * as sqsFunctions from '../common/sqs-functions';
import { DATA_LAKE_BUCKET_US_WEST_2, DATE_FORMAT_YYYY_MM_DD, ETL_QUEUE_URL } from '../common/constants';
import dayjs from 'dayjs';
import { FDB_DATA_PREFIX, fdbDrugHandler, Source } from './index';

jest.mock('../common');
jest.mock('../common/s3-functions');
jest.mock('../common/sqs-functions');

const mockFindLatestS3PartitionDate = s3Functions.latestS3PartitionDate as jest.MockedFunction<
  typeof s3Functions.latestS3PartitionDate
>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

describe('FDB Drug ETL Trigger', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should not send message if etl not possible', async () => {
    mockFindLatestS3PartitionDate.mockResolvedValue(dayjs('2020-01-01').format(DATE_FORMAT_YYYY_MM_DD));
    mockCheckEtlJobPossibility.mockResolvedValue(false);
    const results = await fdbDrugHandler(event, context);
    expect(results[Source.FDB_DRUG_CODING_SYSTEM]).toBeFalsy();
    expect(mockSendEtlMessage).not.toBeCalled();
  });

  it('should send ETL SQS message if ETL is possible', async () => {
    mockFindLatestS3PartitionDate.mockResolvedValue(dayjs('2020-01-01').format(DATE_FORMAT_YYYY_MM_DD));
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    const results = await fdbDrugHandler(event, context);
    expect(results[Source.FDB_DRUG_CODING_SYSTEM]).toBe('2020-01-01');

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.FDB_DRUG_CODING_SYSTEM]: { bucket: DATA_LAKE_BUCKET_US_WEST_2, path: FDB_DATA_PREFIX + '2020-01-01' }
      },
      version: '20200101',
      classification: Source.FDB_DRUG_CODING_SYSTEM,
      messageGroupId: Source.FDB_DRUG_CODING_SYSTEM.replace(/ /g, '_')
    });
  });
});
