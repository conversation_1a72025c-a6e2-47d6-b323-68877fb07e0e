import { DATA_LAKE_BUCKET_US_WEST_2, DATE_FORMAT_YYYY_MM_DD, REGION_US_WEST_2 } from '../common/constants';
import { getVersionDateToCheck, TriggerEtlJob, triggerJobs } from '../common/trigger-job';
import { latestS3PartitionDate } from '../common/s3-functions';

export const FDB_DATA_PREFIX = 'us/nddf/fdb/';

export const Source = {
  FDB_DRUG_CODING_SYSTEM: 'FDB Drug'
};

export const FDB_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.FDB_DRUG_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: DATA_LAKE_BUCKET_US_WEST_2,
      prefix: FDB_DATA_PREFIX,
      region: REGION_US_WEST_2,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: DATA_LAKE_BUCKET_US_WEST_2,
        classification: Source.FDB_DRUG_CODING_SYSTEM,
        fullPathPlaceholder: `${FDB_DATA_PREFIX}{partition}`
      }
    ]
  }
];

export const fdbDrugHandler = async function (event, context) {
  return await triggerJobs(FDB_TRIGGER_JOBS);
};
