import { getVersionDateT<PERSON><PERSON><PERSON><PERSON>, TriggerEtlJob, triggerJobs } from '../common/trigger-job';
import { DATE_FORMAT_YYYY_MM_DD, REGION_AP_NORTHEAST_1, SOURCE_BUCKET } from '../common/constants';
import { latestS3PartitionDate } from '../common/s3-functions';

export const Source = {
  DESC_ITEM: 'DESC Item',
  DESC_DIAGNOSIS: 'DESC Diagnosis'
};

const DESC_PREFIX = 'jp/desc/amgen/5tas/';
const DESC_SUB_PATH = 'transformed/split/desc_english/';

export const DESC_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.DESC_DIAGNOSIS,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      region: REGION_AP_NORTHEAST_1,
      prefix: DESC_PREFIX,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateTo<PERSON>heck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.DESC_DIAGNOSIS,
        fullPathPlaceholder: `${DESC_PREFIX}{partition}/${DESC_SUB_PATH}`
      }
    ]
  },
  {
    codingSystem: Source.DESC_ITEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: SOURCE_BUCKET,
      region: REGION_AP_NORTHEAST_1,
      prefix: DESC_PREFIX,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: SOURCE_BUCKET,
        classification: Source.DESC_ITEM,
        fullPathPlaceholder: `${DESC_PREFIX}{partition}/${DESC_SUB_PATH}`
      }
    ]
  }
];

export const descHandler = async (event, context) => {
  return await triggerJobs(DESC_TRIGGER_JOBS);
};
