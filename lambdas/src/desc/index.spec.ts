import * as common from '../common';
import * as s3Functions from '../common/s3-functions';
import * as sqsFunctions from '../common/sqs-functions';
import dayjs from 'dayjs';
import {DATE_FORMAT_YYYYMMDD, ETL_QUEUE_URL, SOURCE_BUCKET} from '../common/constants';
import {descHandler, Source} from './index';

jest.mock('../common');
jest.mock('../common/s3-functions');
jest.mock('../common/sqs-functions');

const mockFindPartition = s3Functions.latestS3PartitionDate as jest.MockedFunction<
  typeof s3Functions.latestS3PartitionDate
>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

describe('DESC ETL Trigger', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should throw an error if partition is not date format', () => {
    mockFindPartition.mockResolvedValue('abcde');
    mockCheckEtlJobPossibility.mockResolvedValue(true);
    expect(descHandler(event, context)).rejects.toThrow('Found partition can not be converted to a date: abcde');
  });

  it('should not message if etl not possible', async () => {
    mockFindPartition.mockResolvedValue('2020-01-01');
    mockCheckEtlJobPossibility.mockResolvedValue(false);

    const result = await descHandler(event, context);
    expect(result[Source.DESC_DIAGNOSIS]).toBeFalsy();
    expect(result[Source.DESC_ITEM]).toBeFalsy();
    expect(mockSendEtlMessage).not.toBeCalled();
  });

  it('should send Desc Item ETL SQS message if ETL is possible', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await descHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.DESC_ITEM]: { path: 'jp/desc/amgen/5tas/2020-01-01/transformed/split/desc_english/', bucket: SOURCE_BUCKET },
      },
      version: dayjs(new Date(latestDataset)).format(DATE_FORMAT_YYYYMMDD),
      classification: Source.DESC_ITEM,
      messageGroupId: Source.DESC_ITEM.replace(/ /g, '_')
    });
  });

  it('should send Desc Diagnosis ETL SQS message if ETL is possible', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await descHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledWith({
      queueUrl: ETL_QUEUE_URL,
      paths: {
        [Source.DESC_DIAGNOSIS]: { path: `jp/desc/amgen/5tas/2020-01-01/transformed/split/desc_english/`, bucket: SOURCE_BUCKET }
      },
      version: dayjs(new Date(latestDataset)).format(DATE_FORMAT_YYYYMMDD),
      classification: Source.DESC_DIAGNOSIS,
      messageGroupId: Source.DESC_DIAGNOSIS.replace(/ /g, '_')
    });
  });

  it('should send ETL SQS message for all DESC ETLs', async () => {
    const latestDataset = '2020-01-01';
    mockFindPartition.mockResolvedValue(latestDataset);
    mockCheckEtlJobPossibility.mockResolvedValue(true);

    await descHandler(event, context);

    expect(mockSendEtlMessage).toHaveBeenCalledTimes(2);
  });
});
