import { getS3Client } from './index';
import { ListObjectsV2Command, ListObjectsV2CommandOutput } from '@aws-sdk/client-s3';
import { getDateFromFileNameWithFormatDDMMMYYYY } from './date-functions';
import { DATE_FORMAT_YYYY_MM_DD, DATE_FORMAT_YYYYMM } from './constants';
import { Upload } from '@aws-sdk/lib-storage';
import { Entry, Parse } from 'unzipper';
import fetch from 'node-fetch';

export interface LatestS3SourceParameters {
  bucket: string;
  prefix: string;
  region: string;
  partitionFormat?: string;
}

export const latestS3PartitionDate = async ({ bucket, prefix, region, partitionFormat }: LatestS3SourceParameters) => {
  const response = await getListObject(bucket, prefix, region);
  if (!response.CommonPrefixes || response.CommonPrefixes.length === 0) {
    throw new Error(`No objects were found matching prefix s3://${bucket}/${prefix}`);
  }

  const partitions = response.CommonPrefixes.map((commonPrefix) => commonPrefix.Prefix)
    .filter((path) => path.endsWith('/'))
    //Remove last /
    .map((path) => path.substring(0, path.length - 1))
    .filter((partition) => partition.match(getDateRegex(partitionFormat)))
    .map((path) => path.split('/').pop());

  if (!partitions || partitions.length == 0) {
    throw new Error(`Unable to find a partition for data at s3://${bucket}/${prefix}`);
  }
  return partitions.reduce((dateOne, dateTwo) => (dateOne > dateTwo ? dateOne : dateTwo));
};

export const findLatestS3Object = async ({ bucket, prefix, region }: LatestS3SourceParameters) => {
  const response = await getListObject(bucket, prefix, region);
  if (!response.Contents || response.Contents.length === 0) {
    throw new Error(`No objects were found matching prefix s3://${bucket}/${prefix}`);
  }

  const filesWithDate = response.Contents.map((content) => content.Key)
    .filter((key) => !key.endsWith('/'))
    .map((key) => key.split('/').pop())
    .filter((key) => getDateFromFileNameWithFormatDDMMMYYYY(key) !== null);

  if (filesWithDate.length == 0) {
    throw new Error(`Unable to find a file with date at s3://${bucket}/${prefix}`);
  }

  return filesWithDate.reduce((fileOne, fileTwo) => getLastFile(fileOne, fileTwo));
};

const getLastFile = (fileOne: string, fileTwo: string): string => {
  const dateOne = getDateFromFileNameWithFormatDDMMMYYYY(fileOne);
  const dateTwo = getDateFromFileNameWithFormatDDMMMYYYY(fileTwo);

  if (dateOne !== null && dateTwo !== null) {
    return dateTwo > dateOne ? fileTwo : fileOne;
  } else if (dateOne !== null) {
    return fileOne;
  } else {
    return fileTwo;
  }
};

const getListObject = async (bucket: string, prefix: string, region: string) => {
  const s3Client = getS3Client(region);
  const command = new ListObjectsV2Command({ Bucket: bucket, Prefix: prefix, Delimiter: '/' });
  const response: ListObjectsV2CommandOutput = await s3Client.send(command);

  return response;
};

const getDateRegex = (dateFormat: string): RegExp | null => {
  if (dateFormat === DATE_FORMAT_YYYY_MM_DD) {
    return /(\d{4}-\d{2}-\d{2}).*/;
  } else if (dateFormat == DATE_FORMAT_YYYYMM) {
    return /(\d{4}\d{2}).*/;
  }
  return null;
};

export const unzipAndStoreFilesInS3 = (fileUrls: string[], bucket: string, region: string, pathPrefix: string) => {
  return Promise.all(
    fileUrls.map((fileUrl) => {
      return new Promise((res, rej) => {
        fetch(fileUrl)
          .then((response) => {
            if (response.ok) {
              response.body.pipe(Parse()).on('entry', (entry: Entry) => {
                new Upload({
                  client: getS3Client(region),
                  params: {
                    Bucket: bucket,
                    Key: `${pathPrefix}/${entry.path}`,
                    Body: entry
                  }
                }).done();
              });
              response.body.on('close', res);
              response.body.on('error', rej);
            } else {
              throw Error('Fetch pbs files fail');
            }
          })
          .catch(rej);
      });
    })
  );
};
