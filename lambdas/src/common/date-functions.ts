import dayjs from 'dayjs';

export const getDateFromFileNameWithFormatDDMMMYYYY = (key: String): Date => {
  const dateRegex =
    /(\d{2}(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dev|JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEV)\d{4}).*/;
  const dateInFileName = key.split('_').pop().split('.')[0];
  if (dateInFileName.match(dateRegex) && dateInFileName.length == 9) {
    return new Date(dateInFileName);
  }
  return null;
};

export const getLastDateOfMonth = (year: string, month: string): Date => {
  const daysInMonth = dayjs(`${year}-${month}-01`).daysInMonth();
  return new Date(`${year}-${month}-${daysInMonth}`);
};

export const isValidDate = (dateObject) => {
  return new Date(dateObject).toString() !== 'Invalid Date';
};
