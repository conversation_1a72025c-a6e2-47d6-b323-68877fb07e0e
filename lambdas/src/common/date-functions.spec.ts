import { getDateFromFileNameWithFormatDDMMMYYYY, getLastDateOfMonth } from './date-functions';

describe('Date Functions', () => {
  it('return date if file name is valid date format', async () => {
    const objectPath = 'folder1/folder2/file_09MAR2022.xlsx';
    const date = getDateFromFileNameWithFormatDDMMMYYYY(objectPath);

    expect(date).toEqual(new Date('09MAR2022'));
  });

  it('should return null if file name is wrong date format', async () => {
    const objectPath = 'folder1/folder2/file_01JANX2022.xlsx';
    const date = getDateFromFileNameWithFormatDDMMMYYYY(objectPath);

    expect(date).toEqual(null);
  });

  it('should return right latest date of month if leap year', async () => {
    const date = getLastDateOfMonth('2020', '02');
    expect(date).toEqual(new Date('2020-02-29'));
  });

  it('should return right latest date of month', async () => {
    const date = getLastDateOfMonth('2021', '02');
    expect(date).toEqual(new Date('2021-02-28'));
  });
});
