import { findLatestS3Object, latestS3PartitionDate } from '../common/s3-functions';
import { ListObjectsV2Command, S3Client } from '@aws-sdk/client-s3';
import { mockClient } from 'aws-sdk-client-mock';
import 'aws-sdk-client-mock-jest';
import { DATE_FORMAT_YYYY_MM_DD, DATE_FORMAT_YYYYMM } from './constants';

const s3Mock = mockClient(S3Client);

describe('S3 Functions', () => {
  beforeEach(() => {
    jest.resetAllMocks();
    s3Mock.reset();
  });

  it('should throw an error if a partition is not found', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: []
    });
    await expect(
      async () =>
        await latestS3PartitionDate({
          bucket: 'source',
          prefix: 'prefix/',
          region: 'region',
          partitionFormat: DATE_FORMAT_YYYY_MM_DD
        })
    ).rejects.toThrowError('No objects were found matching prefix s3://source/prefix/');
  });

  it('should return the latest date partition after a prefix', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: [
        { Prefix: 'prefix/2020-01-01/' },
        { Prefix: 'prefix/2021-01-01/' },
        { Prefix: 'prefix/2019-01-01/' }
      ]
    });

    const latestPartition = await latestS3PartitionDate({
      bucket: 'source',
      prefix: 'prefix/',
      region: 'region',
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    });

    expect(latestPartition).toEqual('2021-01-01');
  });

  it('should throw error if partition found is not a valid date', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: [{ Prefix: 'prefix/notadate/' }]
    });

    await expect(
      async () =>
        await latestS3PartitionDate({
          bucket: 'source',
          prefix: 'prefix/',
          region: 'region',
          partitionFormat: DATE_FORMAT_YYYY_MM_DD
        })
    ).rejects.toThrowError('Unable to find a partition for data at s3://source/prefix/');
  });

  it('should return correct partition when valid and invalid date partitions are found', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: [{ Prefix: 'prefix/notadate/' }, { Prefix: 'prefix/2021-01-01/' }]
    });

    const latestPartition = await latestS3PartitionDate({
      bucket: 'source',
      prefix: 'prefix/',
      region: 'region',
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    });

    expect(latestPartition).toEqual('2021-01-01');
  });

  it('should throw an error if a latest file is not found', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      Contents: []
    });
    await expect(
      async () => await findLatestS3Object({ bucket: 'source', prefix: 'prefix/', region: 'region' })
    ).rejects.toThrowError('No objects were found matching prefix s3://source/prefix/');
  });

  it('should return the latest file after a prefix', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      Contents: [{ Key: 'file_21JAN2022.xlsx' }, { Key: 'file_21FEB2022.xlsx' }, { Key: 'file_21MAR2022.xlsx' }]
    });

    const latestPartition = await findLatestS3Object({ bucket: 'source', prefix: 'prefix/', region: 'region' });

    expect(latestPartition).toEqual('file_21MAR2022.xlsx');
  });

  it('should throw error if file found is not a valid date', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      Contents: [{ Key: 'prefixnotadate.xlsx' }]
    });

    await expect(
      async () => await findLatestS3Object({ bucket: 'source', prefix: 'prefix/', region: 'region' })
    ).rejects.toThrowError('Unable to find a file with date at s3://source/prefix/');
  });

  it('should return correct file when valid and invalid date files are found', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      Contents: [{ Key: 'source/prefixnotadate.xlsx' }, { Key: 'source/file_21JAN2022.xlsx' }]
    });

    const latestFile = await findLatestS3Object({ bucket: 'source', prefix: 'prefix/', region: 'region' });

    expect(latestFile).toEqual('file_21JAN2022.xlsx');
  });

  it('should return latest partition with YYYYMM', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: [
        { Prefix: 'source/prefix/202208/' },
        { Prefix: 'source/prefix/202211/' },
        { Prefix: 'source/prefix/2s2211sq/' }
      ]
    });

    const latestPartition = await latestS3PartitionDate({
      bucket: 'source',
      prefix: 'prefix/',
      region: 'region',
      partitionFormat: DATE_FORMAT_YYYYMM
    });

    expect(latestPartition).toEqual('202211');
  });

  it('should return latest partition with YYYY-MM-DD', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: [
        { Prefix: 'source/prefix/2022-08-21/' },
        { Prefix: 'source/prefix/2022-11-07/' },
        { Prefix: 'source/prefix/2s2211sq/' }
      ]
    });

    const latestPartition = await latestS3PartitionDate({
      bucket: 'source',
      prefix: 'prefix/',
      region: 'region',
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    });

    expect(latestPartition).toEqual('2022-11-07');
  });

  it('should throw an error if a latest partition is not found in prefix', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: []
    });

    await expect(
      async () =>
        await latestS3PartitionDate({
          bucket: 'source',
          prefix: 'prefix/',
          region: 'region',
          partitionFormat: DATE_FORMAT_YYYY_MM_DD
        })
    ).rejects.toThrowError('No objects were found matching prefix s3://source/prefix/');
  });

  it('should throw an error if a latest partition is matching with date format', async () => {
    s3Mock.on(ListObjectsV2Command).resolves({
      CommonPrefixes: [{ Prefix: 'source/prefix/2022-0908-21/' }]
    });

    await expect(
      async () =>
        await latestS3PartitionDate({
          bucket: 'source',
          prefix: 'prefix/',
          region: 'region',
          partitionFormat: DATE_FORMAT_YYYY_MM_DD
        })
    ).rejects.toThrowError('Unable to find a partition for data at s3://source/prefix/');
  });
});
