import { SendMessageCommand } from '@aws-sdk/client-sqs';
import { sqsClient } from './index';

export interface SendEtlMessageParams {
  queueUrl: string;
  paths: { [key: string]: EtlInputPath };
  version: string;
  classification: string;
  messageGroupId: string;
}

export interface EtlInputPath {
  bucket?: string;
  path: string;
}

export const sendEtlMessage = (params: SendEtlMessageParams) => {
  return sqsClient.send(
    new SendMessageCommand({
      MessageGroupId: params.messageGroupId,
      QueueUrl: params.queueUrl,
      MessageBody: JSON.stringify({
        classification: params.classification,
        paths: params.paths,
        version: params.version
      })
    })
  );
};
