import fetch from 'node-fetch';
import { S3Client } from '@aws-sdk/client-s3';
import { SQSClient } from '@aws-sdk/client-sqs';

export interface EtlJobPossibility {
  classification: String;
  lastUpdatedDate: Date;
}

export const checkEtlJobPossibility = async ({
  classification,
  lastUpdatedDate
}: EtlJobPossibility): Promise<Boolean> => {
  const pullVersion = lastUpdatedDate.toISOString().split('T')[0];
  const response = await fetch(getCheckEtlJobPossibilityUrl(classification, pullVersion));
  if (!response.ok) {
    throw Error('Check etl job possibility fail');
  }
  return Boolean(await response.json());
};

const getCheckEtlJobPossibilityUrl = (classification: String, pullVersion: String) =>
  `${process.env.REF_DATA_SERVICE_URL}/api/ref-data-v2/coding-systems/check-etl-job-possibility?classification=${classification}&pullVersion=${pullVersion}`;

const s3ClientsByRegion = {};
export const getS3Client = (region: string) => {
  const s3Client = s3ClientsByRegion[region];
  if (!s3Client) {
    const s3Client = new S3Client({
      region,
      ...(process.env.ENDPOINT ? { endpoint: process.env.ENDPOINT } : {}),
      forcePathStyle: true
    });
    s3ClientsByRegion[region] = s3Client;
    return s3Client;
  }
  return s3Client;
};

export const sqsClient = new SQSClient({
  region: 'ap-southeast-2',
  endpoint: process.env.ENDPOINT
});
