import { LatestS3SourceParameters } from './s3-functions';
import { DATE_FORMAT_YYYY_MM_DD, DATE_FORMAT_YYYYMMDD, ETL_QUEUE_URL, PARTITION_PLACEHOLDER } from './constants';
import { checkEtlJobPossibility } from './index';
import { EtlInputPath, sendEtlMessage } from './sqs-functions';
import dayjs from 'dayjs';
import { isValidDate } from './date-functions';

export interface TriggerEtlJob {
  codingSystem: string;
  etlFiles: EtlFile[];
  findLatestSource: (parameters?: LatestS3SourceParameters) => Promise<string>;
  latestSourceParameters?: LatestS3SourceParameters;
  getVersionDateToCheck: (partition: String) => Promise<Date>;
  download?: (version: Date) => Promise<void>;
}

export interface EtlFile {
  bucket: string;
  classification: string;
  fullPathPlaceholder: string;
}

export const getVersionDateToCheck = async (partition: string) => new Date(partition);

export const triggerJobs = async (jobs: TriggerEtlJob[]) => {
  const triggerJobs = await Promise.all(
    jobs.map(async (job) => {
      const latestSource = await job.findLatestSource(job.latestSourceParameters);
      const versionDate = await job.getVersionDateToCheck(latestSource);
      if (!isValidDate(versionDate)) {
        throw new Error('Found partition can not be converted to a date: ' + latestSource);
      }
      console.log(`Latest partition found for ${job.codingSystem} is ${latestSource}.`);
      const canEtl = await checkEtlJobPossibility({ classification: job.codingSystem, lastUpdatedDate: versionDate });

      if (canEtl) {
        if (job.download) {
          await job.download(versionDate);
        }

        await sendJobToEtlQueue(job, latestSource, versionDate);
        return { [job.codingSystem]: dayjs(versionDate).format(DATE_FORMAT_YYYY_MM_DD) };
      }
      return { [job.codingSystem]: null };
    })
  );

  return triggerJobs.reduce((mergedMap, jobMap) => {
    return { ...mergedMap, ...jobMap };
  }, {});
};

const sendJobToEtlQueue = async (job: TriggerEtlJob, partition: string, versionDate: Date) => {
  console.log(`Triggering ${job.codingSystem} for ${versionDate}`);

  const createEtlPath = (etlFile: EtlFile): EtlInputPath => ({
    bucket: etlFile.bucket,
    path: etlFile.fullPathPlaceholder.replace(PARTITION_PLACEHOLDER, partition)
  });

  const etlPaths: { [key: string]: EtlInputPath } = job.etlFiles.reduce(
    (records, etlFile) => ({
      ...records,
      [etlFile.classification]: createEtlPath(etlFile)
    }),
    {}
  );

  const etlMessage = {
    queueUrl: ETL_QUEUE_URL,
    paths: etlPaths,
    version: dayjs(versionDate).format(DATE_FORMAT_YYYYMMDD),
    classification: job.codingSystem,
    messageGroupId: job.codingSystem.replace(/ /g, '_')
  };

  await sendEtlMessage(etlMessage);
  console.log(`Triggered ${job.codingSystem} for ${versionDate}`);
};
