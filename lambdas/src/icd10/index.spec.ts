import * as common from '../common';
import * as s3Functions from '../common/s3-functions';
import { LatestS3SourceParameters } from '../common/s3-functions';
import * as sqsFunctions from '../common/sqs-functions';
import { DATA_LAKE_BUCKET_US_WEST_2, ETL_QUEUE_URL } from '../common/constants';
import { ICD10_TRIGGER_JOBS, icd10Handler, Source } from './index';

jest.mock('../common');
jest.mock('../common/s3-functions');
jest.mock('../common/sqs-functions');

const DIAGNOSIS_PREFIX = ICD10_TRIGGER_JOBS.find((job) => job.codingSystem == Source.ICD_DIAGNOSIS_CODING_SYSTEM)
  .latestSourceParameters.prefix;
const PROCEDURE_PREFIX = ICD10_TRIGGER_JOBS.find((job) => job.codingSystem == Source.ICD_PROCEDURE_CODING_SYSTEM)
  .latestSourceParameters.prefix;

const mockFindLatestS3PartitionDate = s3Functions.latestS3PartitionDate as jest.MockedFunction<
  typeof s3Functions.latestS3PartitionDate
>;
const mockCheckEtlJobPossibility = common.checkEtlJobPossibility as jest.MockedFunction<
  typeof common.checkEtlJobPossibility
>;
const mockSendEtlMessage = sqsFunctions.sendEtlMessage as jest.MockedFunction<typeof sqsFunctions.sendEtlMessage>;

describe('ICD10 ETL Trigger', () => {
  let event, context;

  beforeEach(() => {
    // Mock lambda event and context here
    event = null;
    context = null;
    jest.resetAllMocks();
  });

  it('should not send message if etl not possible', async () => {
    mockFindLatestS3PartitionDate.mockResolvedValue('2020-01-01');
    mockCheckEtlJobPossibility.mockResolvedValue(false);

    const result = await icd10Handler(event, context);
    expect(result[Source.ICD_DIAGNOSIS_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.ICD_PROCEDURE_CODING_SYSTEM]).toBeFalsy();

    expect(mockSendEtlMessage).not.toBeCalled();
  });

  it('should send ETL SQS message if ETL is ICD Procedure possible only', async () => {
    const lastPartition = '2020-01-01';
    mockFindLatestS3PartitionDate.mockResolvedValue(lastPartition);
    mockCheckEtlJobPossibility.mockImplementation((parameters) =>
      Promise.resolve(Source.ICD_PROCEDURE_CODING_SYSTEM === parameters.classification)
    );
    const result = await icd10Handler(event, context);

    expect(result[Source.ICD_DIAGNOSIS_CODING_SYSTEM]).toBeFalsy();
    expect(result[Source.ICD_PROCEDURE_CODING_SYSTEM]).toBe(lastPartition);

    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.ICD_PROCEDURE_CODING_SYSTEM, lastPartition, PROCEDURE_PREFIX)
    );
  });

  it('should send ETL SQS message if ETL is possible for ICD Diagnosis and ICD Procedure', async () => {
    const lastPartitionIcdDiagnosis = '2020-01-01';
    const lastPartitionIcdProcedure = '2020-01-04';

    mockFindLatestS3PartitionDate.mockImplementation((parameters: LatestS3SourceParameters) => {
      if (DIAGNOSIS_PREFIX == parameters.prefix) {
        return Promise.resolve(lastPartitionIcdDiagnosis);
      } else if (PROCEDURE_PREFIX == parameters.prefix) {
        return Promise.resolve(lastPartitionIcdProcedure);
      }
      return null;
    });

    mockCheckEtlJobPossibility.mockResolvedValue(true);

    const result = await icd10Handler(event, context);
    expect(result[Source.ICD_DIAGNOSIS_CODING_SYSTEM]).toBe(lastPartitionIcdDiagnosis);
    expect(result[Source.ICD_PROCEDURE_CODING_SYSTEM]).toBe(lastPartitionIcdProcedure);

    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.ICD_DIAGNOSIS_CODING_SYSTEM, lastPartitionIcdDiagnosis, DIAGNOSIS_PREFIX)
    );
    expect(mockSendEtlMessage).toHaveBeenCalledWith(
      createEtlMessage(Source.ICD_PROCEDURE_CODING_SYSTEM, lastPartitionIcdProcedure, PROCEDURE_PREFIX)
    );
  });
});

const createEtlMessage = (codingSystem, lastPartitionDate, partitionPrefix): any => {
  return {
    queueUrl: ETL_QUEUE_URL,
    paths: {
      [codingSystem]: {
        bucket: DATA_LAKE_BUCKET_US_WEST_2,
        path: partitionPrefix + lastPartitionDate
      }
    },
    version: lastPartitionDate.replace(/-/g, ''),
    classification: codingSystem,
    messageGroupId: codingSystem.replace(/ /g, '_')
  };
};
