import { DATA_LAKE_BUCKET_US_WEST_2, DATE_FORMAT_YYYY_MM_DD, REGION_US_WEST_2 } from '../common/constants';
import { getVersionDateToCheck, TriggerEtlJob, triggerJobs } from '../common/trigger-job';
import { latestS3PartitionDate } from '../common/s3-functions';

export const Source = {
  ICD_DIAGNOSIS_CODING_SYSTEM: 'ICD Diagnosis',
  ICD_PROCEDURE_CODING_SYSTEM: 'ICD Procedure'
};

export const ICD10_TRIGGER_JOBS: TriggerEtlJob[] = [
  {
    codingSystem: Source.ICD_DIAGNOSIS_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: DATA_LAKE_BUCKET_US_WEST_2,
      prefix: 'us/cms/icd/cm/',
      region: REGION_US_WEST_2,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: DATA_LAKE_BUCKET_US_WEST_2,
        classification: Source.ICD_DIAGNOSIS_CODING_SYSTEM,
        fullPathPlaceholder: 'us/cms/icd/cm/{partition}'
      }
    ]
  },
  {
    codingSystem: Source.ICD_PROCEDURE_CODING_SYSTEM,
    findLatestSource: latestS3PartitionDate,
    latestSourceParameters: {
      bucket: DATA_LAKE_BUCKET_US_WEST_2,
      prefix: 'us/cms/icd/pcs/',
      region: REGION_US_WEST_2,
      partitionFormat: DATE_FORMAT_YYYY_MM_DD
    },
    getVersionDateToCheck,
    etlFiles: [
      {
        bucket: DATA_LAKE_BUCKET_US_WEST_2,
        classification: Source.ICD_PROCEDURE_CODING_SYSTEM,
        fullPathPlaceholder: 'us/cms/icd/pcs/{partition}'
      }
    ]
  }
];

export const icd10Handler = async function (event, context) {
  return await triggerJobs(ICD10_TRIGGER_JOBS);
};
