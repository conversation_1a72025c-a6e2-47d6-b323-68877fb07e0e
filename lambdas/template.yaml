AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: lambdas for ETL
  
Globals:
  Function:
    Timeout: 900
    MemorySize: 2048
    Environment:
      Variables:
        ENDPOINT: http://host.docker.internal:4566
        OUTPUT_S3_BUCKET: pd-au-local-ref-data-v2
        ETL_QUEUE_URL: http://host.docker.internal:4566/000000000000/pd-au-local-etl-queue.fifo
        AWS_ACCESS_KEY_ID: accesskey
        AWS_SECRET_ACCESS_KEY: secretkey
        REF_DATA_SERVICE_URL: http://host.docker.internal:17080
        DATA_LAKE_S3_BUCKET: prospection-data-lake-local

Resources:
  PbsItemsScraper:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.pbsHandler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build
  MdvEtlTrigger:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.mdvHandler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build
  PbsAuthorityScraper:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.pbsAuthorityHandler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build
  FdbDrugEtlTrigger:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.fdbDrugHandler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build
  Icd10EtlTrigger:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.icd10Handler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build
  JmdcEtlTrigger:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.jmdcHandler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build
  DescEtlTrigger:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageConfig:
        Command: build/index.descHandler
      Architectures:
        - x86_64
    Metadata:
      Dockerfile: Dockerfile
      DockerContext: ./
      DockerTag: snapshot
      DockerBuildTarget: build