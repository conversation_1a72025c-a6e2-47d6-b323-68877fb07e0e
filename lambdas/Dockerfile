### Reference Data Lambdas ###
# CMD is supplied by AWS SAM/Lambda when invoked. This allow us to reuse the container with different handlers.

# ---- Base Node ----
FROM public.ecr.aws/lambda/nodejs:20.2024.07.10.10 as base

WORKDIR ${LAMBDA_TASK_ROOT}

# ---- Dependencies ----
COPY package.json package-lock.json ./
RUN npm ci
COPY ./ ${LAMBDA_TASK_ROOT}

# ---- Build ----
FROM base as build
RUN npm run build

FROM base as test
RUN npm run test
