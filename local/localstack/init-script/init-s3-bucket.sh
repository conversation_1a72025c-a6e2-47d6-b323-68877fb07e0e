#!/bin/bash

REF_BUCKET_NAME="pd-au-local-ref-data-v2"
DATALAKE_BUCKET_NAME="prospection-data-lake-local"

BUCKET_EXISTS=$(awslocal s3api head-bucket --bucket $REF_BUCKET_NAME 2>&1 || true)
if [ -n "$BUCKET_EXISTS" ]; then
  echo "# -- > Create S3 bucket: $REF_BUCKET_NAME"
  awslocal s3 mb s3://$REF_BUCKET_NAME

  /scripts/copy-test-data-to-s3.sh
fi

BUCKET_EXISTS=$(awslocal s3api head-bucket --bucket $DATALAKE_BUCKET_NAME 2>&1 || true)
if [ -n "$BUCKET_EXISTS" ]; then
  echo "# -- > Create S3 bucket: $DATALAKE_BUCKET_NAME"
  awslocal s3 mb s3://$DATALAKE_BUCKET_NAME
  echo " " | awslocal s3 cp - s3://$DATALAKE_BUCKET_NAME/jp/mdv/mdv/all/2022-09-09/raw/HIA/M_Act.txt.gz
  echo " " | awslocal s3 cp - s3://$DATALAKE_BUCKET_NAME/jp/mdv/mdv/all/2022-09-09/raw/HIA/M_Drug.txt.gz
  echo " " | awslocal s3 cp - s3://$DATALAKE_BUCKET_NAME/jp/mdv/janssen/all/2022-09-09/raw/M_Act.txt.gz
  echo " " | awslocal s3 cp - s3://$DATALAKE_BUCKET_NAME/jp/mdv/janssen/all/2022-09-09/raw/M_Drug.txt.gz
fi

BUCKET_NAME="pd-au-local-report-export"

BUCKET_EXISTS=$(awslocal s3api head-bucket --bucket $BUCKET_NAME 2>&1 || true)
if [ -n "$BUCKET_EXISTS" ]; then
  echo "# -- > Create S3 bucket: $BUCKET_NAME"
  awslocal s3 mb s3://$BUCKET_NAME
fi

echo "# -- > Init S3 script done"
