#!/bin/bash

QUEUE_NAME="pd-au-local-etl-queue"

# Unlike S3, SQS queue is created every time docker container is up. It's based on the assumption that the message in the queue on local env isn't worthwhile to keep when the docker container is restarted.
awslocal sqs delete-queue --queue-url http://localhost:4566/000000000000/${QUEUE_NAME}.fifo || true
awslocal sqs delete-queue --queue-url http://localhost:4566/000000000000/${QUEUE_NAME}-deadletter.fifo || true

# configs should match with sqs.tf
awslocal sqs create-queue --queue-name ${QUEUE_NAME}-deadletter.fifo --attributes "FifoQueue=true,DelaySeconds=0,MessageRetentionPeriod=1209600,ContentBasedDeduplication=true"
awslocal sqs create-queue --queue-name ${QUEUE_NAME}.fifo --attributes "FifoQueue=true,DelaySeconds=0,MessageRetentionPeriod=1209600,ReceiveMessageWaitTimeSeconds=20,VisibilityTimeout=1200,ContentBasedDeduplication=true,RedrivePolicy=\"{\\\"deadLetterTargetArn\\\":\\\"arn:aws:sqs:ap-southeast-2:000000000000:${QUEUE_NAME}-deadletter.fifo\\\",\\\"maxReceiveCount\\\":\\\"3\\\"}\""
