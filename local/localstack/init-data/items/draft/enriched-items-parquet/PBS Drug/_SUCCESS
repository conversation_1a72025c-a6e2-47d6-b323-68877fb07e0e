{"name": "org.apache.hadoop.fs.s3a.commit.files.SuccessData/1", "timestamp": 1663747255206, "date": "Wed Sep 21 08:00:55 UTC 2022", "hostname": "DN-LAPTOP-01", "committer": "magic", "description": "Task committer attempt_202209210800538766512051115372936_0000_m_000000_0", "jobId": "105a3c1e-1dcb-4ba2-8695-14ceeba894f0", "jobIdSource": "spark.sql.sources.writeJobUUID", "metrics": {"files_created": 5, "stream_write_block_uploads_aborted": 0, "committer_commits_reverted": 0, "action_http_get_request.failures": 0, "committer_magic_files_created": 2, "object_copy_requests": 0, "stream_read_close_operations": 11, "store_io_retry": 0, "S3guard_metadatastore_put_path_latency90thPercentileLatency": 0, "stream_write_block_uploads_committed": 0, "S3guard_metadatastore_throttle_rate75thPercentileFrequency (Hz)": 0, "committer_stage_file_upload.failures": 0, "S3guard_metadatastore_throttle_rate90thPercentileFrequency (Hz)": 0, "s3guard_metadatastore_authoritative_directories_updated": 0, "op_create": 5, "action_http_head_request": 64, "stream_read_fully_operations": 0, "committer_commits_completed": 2, "S3guard_metadatastore_throttle_rate95thPercentileFrequency (Hz)": 0, "op_xattr_list.failures": 0, "stream_read_seek_policy_changed": 18, "committer_commits_created": 0, "s3guard_metadatastore_put_path_request": 0, "op_get_delegation_token": 0, "stream_write_exceptions": 0, "directories_created": 10, "op_xattr_get_named.failures": 0, "S3guard_metadatastore_throttle_rateNumEvents": 0, "files_delete_rejected": 0, "stream_write_total_data": 2072565, "op_hsync": 0, "Store_io_throttle_rate99thPercentileFrequency (Hz)": 0, "action_http_get_request": 20, "files_copied_bytes": 0, "stream_write_block_uploads_data_pending": 0, "op_list_located_status": 0, "object_bulk_delete_request": 25, "committer_commits_aborted": 0, "action_executor_acquired.failures": 0, "committer_stage_file_upload": 0, "action_http_head_request.failures": 0, "stream_read_opened": 20, "op_hflush": 0, "op_list_status": 3, "op_xattr_get_named_map": 0, "stream_write_queue_duration.failures": 0, "op_get_file_checksum": 0, "op_xattr_get_map.failures": 0, "ignored_errors": 10, "committer_bytes_uploaded": 2053365, "op_list_files": 9, "files_deleted": 8, "op_is_directory": 3, "s3guard_metadatastore_throttled": 0, "stream_read_seek_backward_operations": 9, "multipart_upload_started": 0, "stream_write_total_time": 136, "object_delete_request.failures": 0, "fake_directories_created": 0, "stream_read_seek_operations": 9, "stream_read_seek_forward_operations": 0, "object_put_bytes": 2079082, "op_is_file": 0, "S3guard_metadatastore_throttle_rate99thPercentileFrequency (Hz)": 0, "store_io_request": 203, "stream_write_block_uploads": 2, "committer_commits.failures": 0, "object_delete_objects": 116, "op_xattr_get_named_map.failures": 0, "committer_commit_job": 0, "S3guard_metadatastore_put_path_latencyNumOps": 0, "multipart_upload_part_put": 0, "op_open": 11, "s3guard_metadatastore_record_reads": 0, "object_put_request": 19, "s3guard_metadatastore_initialization": 0, "multipart_upload_abort_under_path_invoked": 0, "committer_commit_job.failures": 0, "stream_read_bytes_backwards_on_seek": 677850, "stream_read_seek_bytes_discarded": 0, "multipart_upload_part_put_bytes": 0, "Store_io_throttle_rate95thPercentileFrequency (Hz)": 0, "op_abort.failures": 0, "multipart_upload_aborted": 0, "committer_bytes_committed": 2053365, "committer_materialize_file": 0, "object_metadata_request": 64, "s3guard_metadatastore_retry": 0, "stream_write_block_uploads_active": 0, "object_put_request_completed": 19, "op_create_non_recursive": 0, "stream_write_queue_duration": 18, "Store_io_throttle_rate90thPercentileFrequency (Hz)": 0, "object_put_request_active": 0, "delegation_tokens_issued.failures": 0, "op_abort": 0, "committer_jobs_completed": 2, "multipart_instantiated": 0, "Store_io_throttle_rate75thPercentileFrequency (Hz)": 0, "S3guard_metadatastore_put_path_latency50thPercentileLatency": 0, "stream_read_operations": 38, "fake_directories_deleted": 108, "object_bulk_delete_request.failures": 0, "op_rename": 0, "object_multipart_aborted": 0, "stream_aborted": 0, "op_get_file_status": 35, "stream_read_total_bytes": 677654, "s3guard_metadatastore_record_deletes": 0, "committer_materialize_file.failures": 0, "op_glob_status": 0, "stream_read_exceptions": 0, "op_exists": 16, "stream_read_version_mismatches": 0, "stream_write_bytes": 0, "action_executor_acquired": 0, "Store_io_throttle_rateNumEvents": 0, "stream_write_exceptions_completing_upload": 0, "op_xattr_list": 0, "S3guard_metadatastore_throttle_rate50thPercentileFrequency (Hz)": 0, "object_select_requests": 0, "S3guard_metadatastore_put_path_latency95thPercentileLatency": 0, "stream_write_block_uploads_pending": 3, "object_delete_request": 0, "object_multipart_initiated": 2, "op_xattr_get_named": 2, "S3guard_metadatastore_put_path_latency99thPercentileLatency": 0, "committer_jobs_failed": 0, "stream_read_operations_incomplete": 22, "op_delete": 8, "object_put_bytes_pending": 0, "stream_read_bytes": 677598, "object_list_request.failures": 0, "object_continue_list_request.failures": 0, "stream_read_bytes_discarded_in_abort": 0, "committer_tasks_completed": 2, "object_list_request": 68, "store_io_throttled": 0, "files_copied": 0, "committer_tasks_failed": 0, "s3guard_metadatastore_record_writes": 0, "stream_read_seek_bytes_skipped": 0, "multipart_upload_completed": 0, "Store_io_throttle_rate50thPercentileFrequency (Hz)": 0, "object_continue_list_request": 0, "op_mkdirs": 6, "op_copy_from_local_file": 0, "S3guard_metadatastore_put_path_latency75thPercentileLatency": 0, "stream_read_closed": 20, "directories_deleted": 0, "op_xattr_get_map": 0, "delegation_tokens_issued": 0, "stream_read_bytes_discarded_in_close": 56}, "diagnostics": {"fs.s3a.authoritative.path": "", "fs.s3a.metadatastore.impl": "org.apache.hadoop.fs.s3a.s3guard.NullMetadataStore", "fs.s3a.metadatastore.authoritative": "false"}, "filenames": ["/items/draft/enriched-items-parquet/PBS Drug/part-00000-89c87193-7a89-41d2-9100-5b98a7b44db8-c000.snappy.parquet"], "iostatistics": {"counters": {"stream_write_exceptions_completing_upload": 0, "committer_commits.failures": 0, "stream_write_block_uploads": 1, "committer_commit_job": 1, "stream_write_exceptions": 0, "committer_commits_reverted": 0, "stream_write_total_data": 317941, "op_hsync": 0, "object_multipart_aborted.failures": 0, "committer_jobs_failed": 0, "committer_commit_job.failures": 0, "committer_tasks_completed": 0, "op_abort.failures": 0, "committer_commits_aborted": 0, "action_executor_acquired.failures": 0, "committer_stage_file_upload": 0, "committer_stage_file_upload.failures": 0, "committer_bytes_committed": 317941, "op_hflush": 0, "committer_materialize_file": 1, "committer_commits_completed": 1, "committer_bytes_uploaded": 0, "committer_tasks_failed": 0, "multipart_upload_completed": 0, "stream_write_queue_duration": 0, "op_abort": 0, "committer_jobs_completed": 1, "object_multipart_aborted": 0, "committer_commits_created": 0, "stream_write_total_time": 0, "multipart_upload_completed.failures": 0, "committer_materialize_file.failures": 0, "action_executor_acquired": 0, "stream_write_bytes": 317941}, "gauges": {"stream_write_block_uploads_data_pending": 0, "stream_write_block_uploads_pending": 0}, "minimums": {"op_abort.failures.min": -1, "object_multipart_aborted.failures.min": -1, "object_multipart_aborted.min": -1, "action_executor_acquired.min": 1, "committer_commit_job.min": 85, "committer_stage_file_upload.min": -1, "multipart_upload_completed.min": -1, "committer_commit_job.failures.min": -1, "committer_materialize_file.min": 31, "committer_materialize_file.failures.min": -1, "committer_stage_file_upload.failures.min": -1, "multipart_upload_completed.failures.min": -1, "action_executor_acquired.failures.min": -1, "op_abort.min": -1}, "maximums": {"action_executor_acquired.failures.max": -1, "committer_stage_file_upload.failures.max": -1, "committer_materialize_file.failures.max": -1, "multipart_upload_completed.failures.max": -1, "op_abort.max": -1, "committer_commit_job.max": 85, "object_multipart_aborted.failures.max": -1, "committer_materialize_file.max": 31, "op_abort.failures.max": -1, "action_executor_acquired.max": 1, "object_multipart_aborted.max": -1, "committer_stage_file_upload.max": -1, "multipart_upload_completed.max": -1, "committer_commit_job.failures.max": -1}, "meanstatistics": {"committer_stage_file_upload.mean": {"samples": 0, "sum": 0}, "op_abort.mean": {"samples": 0, "sum": 0}, "committer_materialize_file.mean": {"samples": 1, "sum": 31}, "action_executor_acquired.mean": {"samples": 1, "sum": 1}, "committer_materialize_file.failures.mean": {"samples": 0, "sum": 0}, "committer_commit_job.failures.mean": {"samples": 0, "sum": 0}, "committer_stage_file_upload.failures.mean": {"samples": 0, "sum": 0}, "object_multipart_aborted.failures.mean": {"samples": 0, "sum": 0}, "op_abort.failures.mean": {"samples": 0, "sum": 0}, "multipart_upload_completed.failures.mean": {"samples": 0, "sum": 0}, "multipart_upload_completed.mean": {"samples": 0, "sum": 0}, "object_multipart_aborted.mean": {"samples": 0, "sum": 0}, "action_executor_acquired.failures.mean": {"samples": 0, "sum": 0}, "committer_commit_job.mean": {"samples": 1, "sum": 85}}}}