version: '2'

services:
  pd-ref-data-service-postgresql:
    image: postgres:15.5
    environment:
      - POSTGRES_USER=refdata
      - POSTGRES_PASSWORD=Prospection123
    ports:
      - 5435:5432 # Postgres
  localstack:
    container_name: localstack
    image: localstack/localstack:3.8.1
    ports:
      - "4566:4566" # port of to where localstack can be addressed to
    environment:
      - SERVICES=s3,sqs
      - DATA_DIR=/tmp/localstack/data
      - DEBUG=0 # Debug level 1 if you want to log, 0 if you want to disable
      - DEFAULT_REGION=ap-southeast-2
    volumes:
      - "./localstack/init-data:/init-data"
      - "./localstack/scripts:/scripts"
      - "./localstack/init-script:/docker-entrypoint-initaws.d"
      - localstack-data:/tmp/localstack
      - "/var/run/docker.sock:/var/run/docker.sock"
volumes:
  localstack-data:
