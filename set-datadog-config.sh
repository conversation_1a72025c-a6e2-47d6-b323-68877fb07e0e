#! /busybox/sh

# Set DD_AGENT_HOST environment variable

COMMAND_TO_EXTRACT_IP_ADDRESS=$(grep HostPrivateIPv4Address $ECS_CONTAINER_METADATA_FILE | grep -o -E "([0-9]{1,3}\.){3}[0-9]{1,3}")
ECS_INSTANCE_PRIVATE_IP=`echo $COMMAND_TO_EXTRACT_IP_ADDRESS`

# According to AWS documentation, the ECS Container Metadata File should be ready within 1 second after the container starts.
# The following conditional statement is to cater for the potential 1 second race condition.
if [ -z "$ECS_INSTANCE_PRIVATE_IP" ]
then
  echo Waiting 10 seconds for ECS Container Metadata File to be ready...
  sleep 10
  ECS_INSTANCE_PRIVATE_IP=`echo $COMMAND_TO_EXTRACT_IP_ADDRESS`
fi

export DD_AGENT_HOST=$ECS_INSTANCE_PRIVATE_IP
