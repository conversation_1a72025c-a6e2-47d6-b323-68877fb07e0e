PAR1L      001 ,6 (001001   	     &�5 codez�&6&6 (001001 ,      L      A01 ,6 (A01A01   	     &�5 atc_codez�&�&�6 (A01A01 ,      ,0L   T   Sample description ,6 (Sample descriptionSample description   	     &�5 description��&�&�6 (Sample descriptionSample description ,      "L   8   Sample drug ,6 (Sample drugSample drug   	     &�5 	drug_name��&�&�6 (Sample drugSample drug ,       $L   <   Sample brand ,6 (Sample brandSample brand   	     &�5 
brand_name��&�
&�
6 (Sample brandSample brand ,      L   	    12345 ,6 (1234512345   	     &�5 
indication_id��&�
&�
6 (1234512345 ,      .2L   X   Sample manufacturer ,6 (Sample manufacturerSample manufacturer   	     &�5 manufacturer_name��&�&�6 (Sample manufacturerSample manufacturer ,      
L      N ,6 (NN   	     &�5 restriction_flagnv&�&�6 (NN ,      BFL   !�   Sample indication description ,6 (Sample indication descriptionSample indication description   	     &�5 indication_description��&�&�6 (Sample indication descriptionSample indication description ,      L   (   Phase 1 ,6 (Phase 1Phase 1   	     &�5 treatment_phase��&�&�6 (Phase 1Phase 1 ,      $(L   D   Sample LI drug ,6 (Sample LI drugSample LI drug   	     &�5 li_drug_name��&�&�6 (Sample LI drugSample LI drug ,      (,L   L   Sample formulary ,6 (Sample formularySample formulary   	     &� 5 	formulary��&�&�6 (Sample formularySample formulary ,       L   4
   2024-05-01 ,6 (
2024-05-01
2024-05-01   	     &�#5 date_last_listed��&�"&�"6 (
2024-05-01
2024-05-01 ,       L   4
   2024-04-01 ,6 (
2024-04-01
2024-04-01   	     &�&5 date_first_listed��&�%&�$6 (
2024-04-01
2024-04-01 ,      �5 schema %code% L   %atc_code% L   %description% L   %	drug_name% L   %
brand_name% L   %
indication_id% L   %manufacturer_name% L   %restriction_flag% L   %indication_description% L   %treatment_phase% L   %li_drug_name% L   %	formulary% L   %date_last_listed% L   %date_first_listed% L   �&�5 codez�&6&6 (001001 ,      &�5 atc_codez�&�&�6 (A01A01 ,      &�5 description��&�&�6 (Sample descriptionSample description ,      &�5 	drug_name��&�&�6 (Sample drugSample drug ,      &�5 
brand_name��&�
&�
6 (Sample brandSample brand ,      &�5 
indication_id��&�
&�
6 (1234512345 ,      &�5 manufacturer_name��&�&�6 (Sample manufacturerSample manufacturer ,      &�5 restriction_flagnv&�&�6 (NN ,      &�5 indication_description��&�&�6 (Sample indication descriptionSample indication description ,      &�5 treatment_phase��&�&�6 (Phase 1Phase 1 ,      &�5 li_drug_name��&�&�6 (Sample LI drugSample LI drug ,      &� 5 	formulary��&�&�6 (Sample formularySample formulary ,      &�#5 date_last_listed��&�"&�"6 (
2024-05-01
2024-05-01 ,      &�&5 date_first_listed��&�%&�$6 (
2024-04-01
2024-04-01 ,      �&�  ,pandas�{"index_columns": [], "column_indexes": [], "columns": [{"name": "code", "field_name": "code", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "atc_code", "field_name": "atc_code", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "description", "field_name": "description", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "drug_name", "field_name": "drug_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "brand_name", "field_name": "brand_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "indication_id", "field_name": "indication_id", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "manufacturer_name", "field_name": "manufacturer_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "restriction_flag", "field_name": "restriction_flag", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "indication_description", "field_name": "indication_description", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "treatment_phase", "field_name": "treatment_phase", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "li_drug_name", "field_name": "li_drug_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "formulary", "field_name": "formulary", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "date_last_listed", "field_name": "date_last_listed", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "date_first_listed", "field_name": "date_first_listed", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}], "creator": {"library": "pyarrow", "version": "16.1.0"}, "pandas_version": "2.2.2"} ARROW:schema�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  parquet-cpp-arrow version 16.1.0�                             �  PAR1