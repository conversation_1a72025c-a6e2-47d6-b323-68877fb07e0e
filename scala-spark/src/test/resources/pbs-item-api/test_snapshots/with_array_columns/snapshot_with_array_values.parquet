PAR1L      001 ,6 (001001   	     &�5 codez�&6&6 (001001 ,      L      A01 ,6 (A01A01   	     &�5 atc_codez�&�&�6 (A01A01 ,      ,0L   T   Sample description ,6 (Sample descriptionSample description   	     &�5 description��&�&�6 (Sample descriptionSample description ,      "L   8   Sample drug ,6 (Sample drugSample drug   	     &�5 	drug_name��&�&�6 (Sample drugSample drug ,       $L   <   Sample brand ,6 (Sample brandSample brand   	     &�5 
brand_name��&�
&�
6 (Sample brandSample brand ,      L   	    12345 ,6 (1234512345   	     &�5 
indication_id��&�
&�
6 (1234512345 ,      .2L   X   Sample manufacturer ,6 (Sample manufacturerSample manufacturer   	     &�5 manufacturer_name��&�&�6 (Sample manufacturerSample manufacturer ,      
L      N ,6 (NN   	     &�5 restriction_flagnv&�&�6 (NN ,      BFL   !�   Sample indication description ,6 (Sample indication descriptionSample indication description   	     &�5 indication_description��&�&�6 (Sample indication descriptionSample indication description ,      L   (   Phase 1 ,6 (Phase 1Phase 1   	     &�5 treatment_phase��&�&�6 (Phase 1Phase 1 ,      $(L   D   Sample LI drug ,6 (Sample LI drugSample LI drug   	     &�5 li_drug_name��&�&�6 (Sample LI drugSample LI drug ,      (,L   L   Sample formulary ,6 (Sample formularySample formulary   	     &� 5 	formulary��&�&�6 (Sample formularySample formulary ,      X:L   ,T   Sample condition 1R  2 ",6 (Sample condition 2Sample condition 1   8      &�#5 8indication_conditionlistelement��&�"&�"6 (Sample condition 2Sample condition 1 ,      ,0L   T   Sample episodicity ",6 (Sample episodicitySample episodicity   8        &�'5 8indication_episodicitylistelement��&�&&�%6 (Sample episodicitySample episodicity ,      &*L   H   Sample severity ",6 (Sample severitySample severity   8        &�+5 8indication_severitylistelement��&�*&�)6 (Sample severitySample severity ,       L   4
   2024-05-01 ,6 (
2024-05-01
2024-05-01   	     &�.5 date_last_listed��&�-&�-6 (
2024-05-01
2024-05-01 ,       L   4
   2024-04-01 ,6 (
2024-04-01
2024-04-01   	     &�15 date_first_listed��&�0&�/6 (
2024-04-01
2024-04-01 ,      �5 schema" %code% L   %atc_code% L   %description% L   %	drug_name% L   %
brand_name% L   %
indication_id% L   %manufacturer_name% L   %restriction_flag% L   %indication_description% L   %treatment_phase% L   %li_drug_name% L   %	formulary% L   5indication_conditionL<   5list %element% L   5indication_episodicityL<   5list %element% L   5indication_severityL<   5list %element% L   %date_last_listed% L   %date_first_listed% L   �&�5 codez�&6&6 (001001 ,      &�5 atc_codez�&�&�6 (A01A01 ,      &�5 description��&�&�6 (Sample descriptionSample description ,      &�5 	drug_name��&�&�6 (Sample drugSample drug ,      &�5 
brand_name��&�
&�
6 (Sample brandSample brand ,      &�5 
indication_id��&�
&�
6 (1234512345 ,      &�5 manufacturer_name��&�&�6 (Sample manufacturerSample manufacturer ,      &�5 restriction_flagnv&�&�6 (NN ,      &�5 indication_description��&�&�6 (Sample indication descriptionSample indication description ,      &�5 treatment_phase��&�&�6 (Phase 1Phase 1 ,      &�5 li_drug_name��&�&�6 (Sample LI drugSample LI drug ,      &� 5 	formulary��&�&�6 (Sample formularySample formulary ,      &�#5 8indication_conditionlistelement��&�"&�"6 (Sample condition 2Sample condition 1 ,      &�'5 8indication_episodicitylistelement��&�&&�%6 (Sample episodicitySample episodicity ,      &�+5 8indication_severitylistelement��&�*&�)6 (Sample severitySample severity ,      &�.5 date_last_listed��&�-&�-6 (
2024-05-01
2024-05-01 ,      &�15 date_first_listed��&�0&�/6 (
2024-04-01
2024-04-01 ,      �&�  ,pandas�{"index_columns": [], "column_indexes": [], "columns": [{"name": "code", "field_name": "code", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "atc_code", "field_name": "atc_code", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "description", "field_name": "description", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "drug_name", "field_name": "drug_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "brand_name", "field_name": "brand_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "indication_id", "field_name": "indication_id", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "manufacturer_name", "field_name": "manufacturer_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "restriction_flag", "field_name": "restriction_flag", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "indication_description", "field_name": "indication_description", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "treatment_phase", "field_name": "treatment_phase", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "li_drug_name", "field_name": "li_drug_name", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "formulary", "field_name": "formulary", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "indication_condition", "field_name": "indication_condition", "pandas_type": "list[unicode]", "numpy_type": "object", "metadata": null}, {"name": "indication_episodicity", "field_name": "indication_episodicity", "pandas_type": "list[unicode]", "numpy_type": "object", "metadata": null}, {"name": "indication_severity", "field_name": "indication_severity", "pandas_type": "list[unicode]", "numpy_type": "object", "metadata": null}, {"name": "date_last_listed", "field_name": "date_last_listed", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}, {"name": "date_first_listed", "field_name": "date_first_listed", "pandas_type": "unicode", "numpy_type": "object", "metadata": null}], "creator": {"library": "pyarrow", "version": "16.1.0"}, "pandas_version": "2.2.2"} ARROW:schema�$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  parquet-cpp-arrow version 16.1.0�                                   �$  PAR1