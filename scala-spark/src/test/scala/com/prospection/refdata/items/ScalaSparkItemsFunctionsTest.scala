package com.prospection.refdata.items

import com.prospection.refdata.etl.AbstractTest
import org.apache.spark.sql.types.{ArrayType, StringType, StructType}
import org.apache.spark.sql.{Dataset, Row}
import org.scalatest.BeforeAndAfterAll

class ScalaSparkItemsFunctionsTest extends AbstractTest with BeforeAndAfterAll {

    var dataTest: Dataset[Row] = _

    override protected def beforeAll(): Unit = {
        val data = List(
            Row("Tony", List("Java", "Scala", "C++"), "[AU*]"),
            Row("Peter", List("Spark", "Java", "C++"), "[US&|EN#]"),
            Row("Tom", null, "[]"),
        )

        val schema = new StructType()
            .add("name", StringType)
            .add("subjects", ArrayType(StringType))
            .add("country", StringType)

        dataTest = spark.createDataFrame(
            spark.sparkContext.parallelize(data), schema)
    }

    test("should return true when checking array") {
        val isArray = ScalaSparkItemsFunctions.isArrayTypeColumn(dataTest, "subjects")
        assert(isArray)
    }

    test("should return false when checking array") {
        val isArray = ScalaSparkItemsFunctions.isArrayTypeColumn(dataTest, "name")
        assert(!isArray)
    }

    test("should return string after join array") {
        val result = ScalaSparkItemsFunctions.joinAllArrayColumns(dataTest).collect()

        assert(result.apply(0).getAs[String]("subjects") === "[Java|Scala|C++]")
        assert(result.apply(1).getAs[String]("subjects") === "[Spark|Java|C++]")
        assert(result.apply(2).getAs[String]("subjects") === "[]")
    }

    test("should convert to array from string") {
        val result = ScalaSparkItemsFunctions.splitAllColumns(dataTest).collect()
        assert(result.apply(0).getAs("country").equals(List("AU*")))
        assert(result.apply(1).getAs("country").equals(List("US&","EN#")))
        assert(result.apply(2).getAs("country").equals(List.empty))
    }

    test("should not throw NullPointerException when process a null value for string type") {
        val data = List(
            Row(null, null, ""),
            Row("Tony", List("Java", "Scala", "C++"), "[AU*]"),
            Row("Peter", List("Spark", "Java", "C++"), "[US&|EN#]"),
        )

        val schema = new StructType()
            .add("name", StringType)
            .add("subjects", ArrayType(StringType))
            .add("country", StringType)

        dataTest = spark.createDataFrame(spark.sparkContext.parallelize(data), schema)

        val result = ScalaSparkItemsFunctions.splitAllColumns(dataTest).collect()
        assert(result.nonEmpty)
        assert(result.head.getAs[String]("name") == null)
    }
}