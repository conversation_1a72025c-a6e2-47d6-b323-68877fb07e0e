package com.prospection.refdata.etl.desc.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.desc.datasets.DescDiagnosisDataset
import com.prospection.refdata.etl.desc.parser.DescDiagnosisParser
import com.prospection.refdata.etl.desc.rows.DescDiagnosisOutputRow

class DescDiagnosisTransformerTest extends AbstractIntegrationTest {

    test("should transform all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("desc").getPath

        val parseResult = new DescDiagnosisParser(spark, pathPrefix).parse()

        val transformInput = DescDiagnosisTransformerInput(
            descDiagnosisDataset = new DescDiagnosisDataset(parseResult.descDiagnosis),
        )

        val transformer = new DescDiagnosisTransformer(spark)

        val output = transformer.transform(transformInput).collectAsList()

        assert(output.size() == 3)
        assertRow(DescDiagnosisOutputRow(
            icd10_code = "A33",
            code = "8835069",
            diagnosis_name_jp = "新生児破傷風",
            diagnosis_name = "Tetanus neonatorum",
        ), output.get(0))

        assertRow(DescDiagnosisOutputRow(
            icd10_code = "A38",
            code = "341002",
            diagnosis_name_jp = "猩紅熱",
            diagnosis_name = "Scarlet fever"
        ), output.get(1))

        assertRow(DescDiagnosisOutputRow(
            icd10_code = "A46",
            code = "8846507",
            diagnosis_name_jp = "丹毒",
            diagnosis_name = "Erysipelas"
        ), output.get(2))

    }

    private def assertRow(expected: DescDiagnosisOutputRow, actual: DescDiagnosisOutputRow): Unit = {
        assert(expected.code == actual.code)
        assert(expected.icd10_code == actual.icd10_code)
        assert(expected.diagnosis_name_jp == actual.diagnosis_name_jp)
        assert(expected.diagnosis_name == actual.diagnosis_name)
    }
}
