package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_PROCEDURE
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class JmdcProcedureEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("jmdc").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(JMDC_PROCEDURE ->  resourcePath))
        val spyEtlJob = spy(new JmdcProcedureEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
