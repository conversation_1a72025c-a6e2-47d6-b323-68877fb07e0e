package com.prospection.refdata.etl.icd10

import com.prospection.refdata.etl.common.CodingSystems.ICD_DIAGNOSIS
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class IcdDiagnosisEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("icd10/diagnosis").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(ICD_DIAGNOSIS -> resourcePath),  "20230303")
        val spyEtlJob = spy(new IcdDiagnosisEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
