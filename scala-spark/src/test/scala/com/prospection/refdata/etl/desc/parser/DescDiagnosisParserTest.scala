package com.prospection.refdata.etl.desc.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.desc.rows.DescDiagnosisRow

class DescDiagnosisParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("desc").getPath

        val result = new DescDiagnosisParser(spark, pathPrefix).parse()

        // assert restriction
        verifyOutput(result.descDiagnosis, List(
            DescDiagnosisRow(
                icd10_code = "A33",
                code = "8835069",
                diagnosis_name_jp = "新生児破傷風",
                diagnosis_name = "Tetanus neonatorum"
            ),
            DescDiagnosisRow(
                icd10_code = "A38",
                code = "341002",
                diagnosis_name_jp = "猩紅熱",
                diagnosis_name = "Scarlet fever"
            ),
            DescDiagnosisRow(
                icd10_code = "A46",
                code = "8846507",
                diagnosis_name_jp = "丹毒",
                diagnosis_name = "Ery<PERSON>pelas"
            )
        ))

        assert(3 == result.descDiagnosis.count())
    }
}
