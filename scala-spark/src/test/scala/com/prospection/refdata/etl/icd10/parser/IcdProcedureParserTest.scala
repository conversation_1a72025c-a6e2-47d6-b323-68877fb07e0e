package com.prospection.refdata.etl.icd10.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.icd10.rows.{CcsrRow, Icd10OrderRow, Icd9Row, IcdGemRow}
import org.apache.spark.sql.Dataset
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import java.time.LocalDate

class IcdProcedureParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("icd10/procedure").getPath

        val result = new IcdProcedureParser(spark, pathPrefix, LocalDate.parse("2023-02-03")).parse()

        // assert restriction
        verifyOutput(result.icd10Dataset(2014),
            List(Icd10OrderRow(
                code = "HZ99ZZZ",
                short_description = "Pharmacotherapy for Substance Abuse, Oth Replace Med",
                description = "Pharmacotherapy for Substance Abuse Treatment, Other Replacement Medication"
            ))
        )

        verifyOutput(result.icd10Dataset(2015),
            List(Icd10OrderRow(
                code = "0016078",
                short_description = "Bypass Cereb Vent to Bone Mar with Autol Sub, Open Approach",
                description = "Bypass Cerebral Ventricle to Bone Marrow with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2016),
            List(Icd10OrderRow(
                code = "0016077",
                short_description = "Bypass Cereb Vent to Urinary Tract w Autol Sub, Open",
                description = "Bypass Cerebral Ventricle to Urinary Tract with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2017),
            List(Icd10OrderRow(
                code = "0016076",
                short_description = "Bypass Cereb Vent to Periton Cav w Autol Sub, Open",
                description = "Bypass Cerebral Ventricle to Peritoneal Cavity with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2018),
            List(Icd10OrderRow(
                code = "0016075",
                short_description = "Bypass Cereb Vent to Intestine with Autol Sub, Open Approach",
                description = "Bypass Cerebral Ventricle to Intestine with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2019),
            List(Icd10OrderRow(
                code = "0016074",
                short_description = "Bypass Cereb Vent to Pleural Cav w Autol Sub, Open",
                description = "Bypass Cerebral Ventricle to Pleural Cavity with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2020),
            List(Icd10OrderRow(
                code = "0016073",
                short_description = "Bypass Cereb Vent to Blood Vess w Autol Sub, Open",
                description = "Bypass Cerebral Ventricle to Blood Vessel with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2021),
            List(Icd10OrderRow(
                code = "0016072",
                short_description = "Bypass Cereb Vent to Atrium with Autol Sub, Open Approach",
                description = "Bypass Cerebral Ventricle to Atrium with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2022),
            List(Icd10OrderRow(
                code = "0016071",
                short_description = "Bypass Cereb Vent to Mastoid Sinus w Autol Sub, Open",
                description = "Bypass Cerebral Ventricle to Mastoid Sinus with Autologous Tissue Substitute, Open Approach",
            ))
        )

        verifyOutput(result.icd10Dataset(2023),
            List(Icd10OrderRow(
                code = "0016070",
                short_description = "Bypass Cereb Vent to Nasophar with Autol Sub, Open Approach",
                description = "Bypass Cerebral Ventricle to Nasopharynx with Autologous Tissue Substitute, Open Approach",
            ),
                Icd10OrderRow(
                    code = "0016071",
                    short_description = "Bypass Cereb Vent to Mastoid Sinus w Autol Sub, Open",
                    description = "Bypass Cerebral Ventricle to Mastoid Sinus with Autologous Tissue Substitute, Open Approach",
                ))
        )

        verifyOutput(result.icd9GemDataset, List(
            IcdGemRow(code = "5A0512C", icd9_code = "0049"),
            IcdGemRow(code = "5A0522C", icd9_code = "0049"),
        ))

        verifyOutput(result.icd9ShortDescDataset, List(
            Icd9Row(code = "0049", description = "SuperSat O2 therapy")
        ))

        verifyOutput(result.icd9LongDescDataset, List(
            Icd9Row(code = "0049", description = "Supersaturated oxygen therapy")
        ))

        verifyOutput(result.ccsrDataset, List(
            CcsrRow(
                code = "'0016070'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016071'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016072'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016073'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016074'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016075'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016076'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016077'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'0016078'",
                ccsr_category_1 = "'CNS010'",
                ccsr_category_1_description = "Cerebrospinal fluid shunt procedures",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'HZ99ZZZ'",
                ccsr_category_1 = "'SUD002'",
                ccsr_category_1_description = "Pharmacotherapy for substance use",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            ),
            CcsrRow(
                code = "'5A0522C'",
                ccsr_category_1 = "'ESA011'",
                ccsr_category_1_description = "Hyperbaric oxygen therapy",
                ccsr_category_2 = null,
                ccsr_category_2_description = null,
                ccsr_category_3 = null,
                ccsr_category_3_description = null,
                ccsr_category_4 = null,
                ccsr_category_4_description = null,
                ccsr_category_5 = null,
                ccsr_category_5_description = null,
                ccsr_category_6 = null,
                ccsr_category_6_description = null,
            )
        ))
    }
}
