package com.prospection.refdata.etl.fdbdrug

import com.prospection.refdata.etl.common.CodingSystems.FDB_DRUG
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class FdbDrugEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("fdb-drug/raw").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(FDB_DRUG -> resourcePath))
        val spyEtlJob = spy(new FdbDrugEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
