package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.jmdc.row.JmdcDrugRow

class JmdcDrugParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("jmdc").getPath

        val result = new JmdcDrugParser(spark, pathPrefix).parse()

        // assert restriction
        verifyOutput(result.dataset, List(
            JmdcDrugRow(
                code = "100000041358",
                brand_name = "Calonal",
                description = "Calonal Fine Granules 20%",
                atc_code = "N02BE01",
                atc_ephmra = "N02B-",
                nhi_code = "1141007C1075",
                strength_unit = "mg",
                strength_number = "200.000000",
                generic_flag = "1",
                drug_name = "Acetaminophen",
                drug_usage = "Oral Use",
                dose_form_sml = "Fine Granule",
                dose_form_med = "Powder(Oral Use)",
            ),
            JmdcDrugRow(
                code = "100000084687",
                brand_name = "Fluticasone [SANWA]",
                description = "Fluticasone Nasal Solution 50ug [SANWA] 56sprays",
                atc_code = "R01AD08",
                atc_ephmra = "R01A1",
                nhi_code = "1329707Q3230",
                strength_unit = null,
                strength_number = null,
                generic_flag = "1",
                drug_name = "Fluticasone Propionate",
                drug_usage = "External Use",
                dose_form_sml = "Nasal Solution",
                dose_form_med = "Ophthalmologic/Otorhinolaryngologic Use",
            )
        ))
    }
}
