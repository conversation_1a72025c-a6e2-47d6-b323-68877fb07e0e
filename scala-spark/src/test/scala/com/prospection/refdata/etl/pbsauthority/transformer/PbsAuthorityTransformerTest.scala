package com.prospection.refdata.etl.pbsauthority.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.pbsauthority.datasets.PbsAuthorityRestrictionDataset
import com.prospection.refdata.etl.pbsauthority.rows.{PbsAuthorityOutputRow, PbsAuthorityRestrictionRow}

class PbsAuthorityTransformerTest extends AbstractIntegrationTest {
    import spark.implicits._

    val SampleRestrictionRow: PbsAuthorityRestrictionRow = PbsAuthorityRestrictionRow(
        code = "4306",
        description = "Test restriction text"
    )

    val SampleInput: PbsAuthorityTransformerInput = PbsAuthorityTransformerInput(
        restrictions = new PbsAuthorityRestrictionDataset(Seq(SampleRestrictionRow).toDS())
    )

    test("PBS Authority transformation - 1 row per each dataset should be joined all together and create 1 output row") {
        val transformer = new PbsAuthorityTransformer(spark)
        val output = transformer.transform(SampleInput).collectAsList()

        assert(output.size() == 1)
        assert(output.get(0) == PbsAuthorityOutputRow(
            code = "4306",
            description = "Test restriction text"
        ))
    }

    test("PBS Authority transformation - multiple rows per each dataset should be joined all together and create multiple output rows") {
        val transformer = new PbsAuthorityTransformer(spark)
        val output = transformer.transform( PbsAuthorityTransformerInput(
            restrictions = new PbsAuthorityRestrictionDataset(Seq(
                PbsAuthorityRestrictionRow("item1", "Restriction Text 1"),
                PbsAuthorityRestrictionRow("item2", "Restriction Text 2"),
                PbsAuthorityRestrictionRow("item3", "Restriction Text 3")
            ).toDS()),

        )).collect().sortBy(r => (r.code, r.description))

        assert(output.length == 3)
        assert(output(0) == PbsAuthorityOutputRow(
            code = "item1",
            description = "Restriction Text 1"
        ))
        assert(output(1) == PbsAuthorityOutputRow(
            code = "item2",
            description = "Restriction Text 2"
        ))
        assert(output(2) == PbsAuthorityOutputRow(
            code = "item3",
            description = "Restriction Text 3"
        ))
    }
}
