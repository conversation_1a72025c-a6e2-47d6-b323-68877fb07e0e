package com.prospection.refdata.etl.desc.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.desc.datasets.{DescDrugEphmraDataset, DescDrugMainDataset, DescDrugReceDataset, DescProcedureDataset}
import com.prospection.refdata.etl.desc.parser.DescItemParser
import com.prospection.refdata.etl.desc.rows.DescItemOutputRow
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class DescItemTransformerTest extends AbstractIntegrationTest {

    test("should transform all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("desc").getPath

        val parseResult = new DescItemParser(spark, pathPrefix).parse()

        val transformInput = DescItemTransformerInput(
            descProcedureDataset = new DescProcedureDataset(parseResult.descProcedure),
            descDrugMainDataset = new DescDrugMainDataset(parseResult.descDrugMain),
            descDrugReceDataset = new DescDrugReceDataset(parseResult.descDrugRece),
            descDrugEphmraDataset = new DescDrugEphmraDataset(parseResult.descDrugEphmra)
        )

        val transformer = new DescItemTransformer(spark)

        val output = transformer.transform(transformInput).collectAsList()

        assert(output.size() == 7)

        output should contain allElementsOf Seq(
            DescItemOutputRow(
                atc_ephmra = "N01A2",
                drug_name_jp = "プロポフォール",
                drug_name = "propofol",
                code = "620000199",
                description = "1% Propofol inj. [Maruishi]",
                drug_usage = "Injection",
                generic_flag = "Generic",
                procedure_name = null
            ),
            DescItemOutputRow(
                atc_ephmra = "N01B2",
                drug_name_jp = "リドカイン塩酸塩・アドレナリン酒石酸水素塩",
                drug_name = "lidocaine hydrochloride / epinephrine bitartrate",
                code = "628302301",
                description = "ORA Inj. Dental Cartridge 1.8mL",
                drug_usage = "Dental medication",
                generic_flag = "Generic",
                procedure_name = null
            ),
            DescItemOutputRow(
                atc_ephmra = "N01A2",
                drug_name_jp = "プロポフォール",
                drug_name = "propofol",
                code = "620000199",
                description = "1% Propofol inj. [Maruishi]",
                drug_usage = "Injection",
                generic_flag = "Generic",
                procedure_name = null
            ),
            DescItemOutputRow(
                atc_ephmra = "N05B1",
                drug_name_jp = "ブロモバレリル尿素",
                drug_name = "bromovalerylurea",
                code = "610408645",
                description = "Bromovalerylurea",
                drug_usage = "Oral medication",
                generic_flag = "Non - Generic",
                procedure_name = null
            ),
            DescItemOutputRow(
                atc_ephmra = "N05B1",
                drug_name_jp = "エスタゾラム",
                drug_name = "estazolam",
                code = "610453022",
                description = "ESTAZOLAM",
                drug_usage = "Oral medication",
                generic_flag = "Generic",
                procedure_name = null
            ),
            DescItemOutputRow(
                code = "111012970",
                description = "pregnant woman, after-hours fee (first visit)",
                procedure_name = "妊婦時間外加算（初診）",
                atc_ephmra = null,
                drug_name_jp = null,
                drug_name = null,
                drug_usage = null,
                generic_flag = null
            )
        )
    }


}
