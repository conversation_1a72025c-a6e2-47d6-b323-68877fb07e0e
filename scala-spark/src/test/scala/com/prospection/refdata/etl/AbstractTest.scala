package com.prospection.refdata.etl

import com.prospection.refdata.etl.common.job.EtlJobParams
import org.scalatest.funsuite.AnyFunSuite
import org.scalatestplus.mockito.MockitoSugar

abstract class AbstractTest extends AnyFunSuite with MockitoSugar with SparkTest {

    def getEtlJobParams(inputPaths: Map[String, String],
                        snapshotPath: String = s"s3a://snapshots",
                        version: String = "20230303"
                       ): EtlJobParams = {


        val uploadPath = "s3a://RAW_UPLOAD/$codingSystem"
        val warehousePath = s"s3a://warehouse"

        EtlJobParams(
            version,
            inputPaths,
            uploadPath,
            warehousePath,
            snapshotPath
        )
    }
}
