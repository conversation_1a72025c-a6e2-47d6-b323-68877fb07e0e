package com.prospection.refdata.etl

import com.prospection.refdata.etl.common.job.EtlJob
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito._

abstract class AbstractEtlTest extends AbstractTest {

  def mockCommon(etlJob: EtlJob): Unit = {
      doNothing().when(etlJob).storeSnapshot(any())
      doAnswer(_ => any()).when(etlJob).readSnapshotData()
      doNothing().when(etlJob).storeRawItem(any())
  }

    def verifyCommon(etlJob: EtlJob): Unit = {
        verify(etlJob).storeRawItem(any())
        verify(etlJob).storeSnapshot(any())
    }
}
