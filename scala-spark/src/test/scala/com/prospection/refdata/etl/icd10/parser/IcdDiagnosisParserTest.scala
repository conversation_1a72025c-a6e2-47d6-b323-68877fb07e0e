package com.prospection.refdata.etl.icd10.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.icd10.rows.{CcsrRow, Icd10OrderRow, Icd9Row, IcdGemRow}
import org.apache.spark.sql.Dataset
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

import java.time.LocalDate

class IcdDiagnosisParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("icd10/diagnosis").getPath

        val result = new IcdDiagnosisParser(spark, pathPrefix, LocalDate.parse("2023-02-03")).parse()

        // assert restriction
        verifyOutput(result.icd10Dataset(2014), List(
            Icd10OrderRow(code = "A0103", short_description = "Typhoid pneumonia", description = "Typhoid pneumonia")
        ))

        verifyOutput(result.icd10Dataset(2015),
            List(Icd10OrderRow(
                code = "A0102",
                short_description = "Typhoid fever with heart involvement",
                description = "Typhoid fever with heart involvement",
            ))
        )

        verifyOutput(result.icd10Dataset(2016), List(
            Icd10OrderRow(code = "A0101", short_description = "Typhoid meningitis", description = "Typhoid meningitis")
        ))

        verifyOutput(result.icd10Dataset(2017), List(
            Icd10OrderRow(code = "A0100", short_description = "Typhoid fever, unspecified", description = "Typhoid fever, unspecified")
        ))

        verifyOutput(result.icd10Dataset(2018), List(
            Icd10OrderRow(code = "A010", short_description = "Typhoid fever", description = "Typhoid fever")
        ))

        verifyOutput(result.icd10Dataset(2019), List(
            Icd10OrderRow(code = "A01", short_description = "Typhoid and paratyphoid fevers", description = "Typhoid and paratyphoid fevers")
        ))

        verifyOutput(result.icd10Dataset(2020), List(
            Icd10OrderRow(code = "A009", short_description = "Cholera, unspecified", description = "Cholera, unspecified")
        ))

        verifyOutput(result.icd10Dataset(2021),
            List(Icd10OrderRow(
                code = "A001",
                short_description = "Cholera due to Vibrio cholerae 01, biovar eltor",
                description = "Cholera due to Vibrio cholerae 01, biovar eltor",
            ))
        )

        verifyOutput(result.icd10Dataset(2022),
            List(Icd10OrderRow(
                code = "A000",
                short_description = "Cholera due to Vibrio cholerae 01, biovar cholerae",
                description = "Cholera due to Vibrio cholerae 01, biovar cholerae",
            ))
        )

        verifyOutput(result.icd10Dataset(2023), List(
            Icd10OrderRow(code = "A00", short_description = "Cholera short", description = "Cholera long")
        ))

        verifyOutput(result.icd9GemDataset, List(
            IcdGemRow(code = "A000", icd9_code = "0010"),
            IcdGemRow(code = "A0101", icd9_code = "0010"),
        ))

        verifyOutput(result.icd9ShortDescDataset, List(
            Icd9Row(code = "0010", description = "Cholera d/t vib cholerae")
        ))

        verifyOutput(result.icd9LongDescDataset, List(
            Icd9Row(code = "0010", description = "Cholera due to vibrio cholerae ä é è")
        ))

        verifyOutput(result.ccsrDataset, List(
            CcsrRow(
                code = "'A000'",
                ccsr_category_1 = "'DIG001'",
                ccsr_category_1_description = "Intestinal infection",
                ccsr_category_2 = "'INF003'",
                ccsr_category_2_description = "Bacterial infections",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A001'",
                ccsr_category_1 = "'DIG001'",
                ccsr_category_1_description = "Intestinal infection",
                ccsr_category_2 = "'INF003'",
                ccsr_category_2_description = "Bacterial infections",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A009'",
                ccsr_category_1 = "'DIG001'",
                ccsr_category_1_description = "Intestinal infection",
                ccsr_category_2 = "'INF003'",
                ccsr_category_2_description = "Bacterial infections",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0100'",
                ccsr_category_1 = "'DIG001'",
                ccsr_category_1_description = "Intestinal infection",
                ccsr_category_2 = "'INF003'",
                ccsr_category_2_description = "Bacterial infections",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0101'",
                ccsr_category_1 = "'INF003'",
                ccsr_category_1_description = "Bacterial infections",
                ccsr_category_2 = "'NVS001'",
                ccsr_category_2_description = "Meningitis",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0102'",
                ccsr_category_1 = "'INF003'",
                ccsr_category_1_description = "Bacterial infections",
                ccsr_category_2 = "' '",
                ccsr_category_2_description = " ",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0103'",
                ccsr_category_1 = "'INF003'",
                ccsr_category_1_description = "Bacterial infections",
                ccsr_category_2 = "'RSP002'",
                ccsr_category_2_description = "Pneumonia (except that caused by tuberculosis)",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0104'",
                ccsr_category_1 = "'INF003'",
                ccsr_category_1_description = "Bacterial infections",
                ccsr_category_2 = "'MUS001'",
                ccsr_category_2_description = "Infective arthritis",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0105'",
                ccsr_category_1 = "'INF003'",
                ccsr_category_1_description = "Bacterial infections",
                ccsr_category_2 = "'MUS002'",
                ccsr_category_2_description = "Osteomyelitis",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            ),
            CcsrRow(
                code = "'A0109'",
                ccsr_category_1 = "'DIG001'",
                ccsr_category_1_description = "Intestinal infection",
                ccsr_category_2 = "'INF003'",
                ccsr_category_2_description = "Bacterial infections",
                ccsr_category_3 = "' '",
                ccsr_category_3_description = " ",
                ccsr_category_4 = "' '",
                ccsr_category_4_description = " ",
                ccsr_category_5 = "' '",
                ccsr_category_5_description = " ",
                ccsr_category_6 = "' '",
                ccsr_category_6_description = " ",
            )
        ))
    }
}
