package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.jmdc.row.JmdcMaterialRow

class JmdcMaterialParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("jmdc").getPath

        val result = new JmdcMaterialParser(spark, pathPrefix).parse()

        // assert restriction
        verifyOutput(result.dataset, List(
            JmdcMaterialRow(
                code = "700710000",
                material_code = "ZE019",
                material_name = "Film for image recording (half cut) (356 * 432 mm)",
                material_cat_med = "Film",
                material_cat_large = "Specified material for diagnostic imaging",
                material_version = "200804",
            ),
            JmdcMaterialRow(
                code = "737280000",
                material_code = "Z127",
                material_name = "Artificial cardiopulmonary circuit (individual function product/safety valve)",
                material_cat_med = "Artificial cardiopulmonary circuit",
                material_cat_large = "Cardiac/vascular material",
                material_version = "201204",
            )
        ))
    }
}
