package com.prospection.refdata.etl.mdvitem

import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class MdvItemEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("mdv-item").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map("MDV Item" -> resourcePath, "MDV HIA Item" -> resourcePath))
        val spyEtlJob = spy(new MdvItemEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
