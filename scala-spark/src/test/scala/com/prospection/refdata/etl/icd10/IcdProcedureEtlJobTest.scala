package com.prospection.refdata.etl.icd10

import com.prospection.refdata.etl.common.CodingSystems.ICD_PROCEDURE
import com.prospection.refdata.etl.{AbstractEtlTest,  EtlJobExecutor}
import org.mockito.Mockito.spy

class IcdProcedureEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("icd10/procedure").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(ICD_PROCEDURE -> resourcePath))
        val spyEtlJob = spy(new IcdProcedureEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
