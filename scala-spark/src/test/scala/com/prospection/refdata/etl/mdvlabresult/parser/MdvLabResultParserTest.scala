package com.prospection.refdata.etl.mdvlabresult.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.mdvlabresult.rows.MdvLabResultRow

class MdvLabResultParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns from a M_Labo.txt file to a row") {
        val parser = new MdvLabResultParser(spark, getClass.getClassLoader.getResource("mdv-lab-result").getPath)

        val labos = parser.parse().ds.getDataset

        assert(labos.count() == 1)
        assert(labos.first() == MdvLabResultRow(
            code = "4B035000000000000",
            lab_test_name_jp = "FT4",
            lab_test_name = "Free thyroxine",
            unit = "ng/dL"
        ))
    }
}
