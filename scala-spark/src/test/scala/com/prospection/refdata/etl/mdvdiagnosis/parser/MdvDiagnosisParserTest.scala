package com.prospection.refdata.etl.mdvdiagnosis.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.mdvdiagnosis.rows.MdvDiagnosisRow

class MdvDiagnosisParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns from a M_Disease.txt file to a row") {
        val parser = new MdvDiagnosisParser(spark, getClass.getClassLoader.getResource("mdv-diagnosis").getPath)

        val drugs = parser.parse().ds.getDataset

        assert(drugs.count() == 1)
        assert(drugs.first() == MdvDiagnosisRow(
            code = "0703002",
            icd10_code = "B169",
            diagnosis_name_jp = "Ｂ型肝炎",
            diagnosis_name = "hepatitis B",
        ))
    }
}
