package com.prospection.refdata.etl.ndcdispensing.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.ndcdispensing.datasets.NdcDispensingDataset
import com.prospection.refdata.etl.ndcdispensing.rows.NdcDispensingRow
import com.prospection.refdata.etl.ndcdispensing.transformer.NdcDispensingTransformer.ProductTypeBulkIngredient

class NdcDispensingTransformerTest extends AbstractIntegrationTest {
    import spark.implicits._

    private val EmptyDispensingRow = NdcDispensingRow(
        code = "", brand_name = "", drug_name = "", ingredient_name = "", manufacturer_name = "", dose_form = "", route_of_administration = "", package_description = "", strength_number = "", strength_unit = "", pharmaceutical_classes = "", indication_description = "", marketing_category = "", product_type_name = ""
    )

    test("The dash characters (-) in 11 digit NDC codes should be replaced with empty spaces") {
        val input = NdcDispensingTransformerInput(new NdcDispensingDataset(
            Seq(EmptyDispensingRow.copy(code = "0002-1433-61")).toDS()
        ))

        val transformer = new NdcDispensingTransformer(spark)
        val outputRow = transformer.transform(input).first()

        assert(outputRow.code == "0002143361")
    }

    test(s"should filter out the rows where the value of the Product Type Name column are equal to $ProductTypeBulkIngredient") {
        val input = NdcDispensingTransformerInput(new NdcDispensingDataset(
            Seq(
                EmptyDispensingRow.copy(code = "a", product_type_name = ProductTypeBulkIngredient),
                EmptyDispensingRow.copy(code = "b")
            ).toDS()
        ))

        val transformer = new NdcDispensingTransformer(spark)
        val output = transformer.transform(input)

        assert(output.count() == 1)
        assert(output.first().code == "b")
    }

    test("product_type_name column isn't included in the final output. It should be used for filtering and dropped after filtering") {
        val input = NdcDispensingTransformerInput(new NdcDispensingDataset(
            Seq(
                EmptyDispensingRow.copy(code = "whatever-code", product_type_name = "whatever-product-type-name"),
            ).toDS()
        ))

        val transformer = new NdcDispensingTransformer(spark)
        val output = transformer.transform(input)

        assert(!output.columns.contains("product_type_name"))
    }
}
