package com.prospection.refdata.etl

import com.prospection.refdata.etl.common.job.EtlJobParams
import org.apache.spark.sql.Dataset
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.must.Matchers
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

abstract class AbstractIntegrationTest extends AnyFunSuite with Matchers with SparkTest {

    protected val etlJobParams: EtlJobParams = EtlJobParams(
        version = "20230202",
        inputPaths = Map(),
        outputPath = "",
        snapshotPath = "",
        warehousePath = ""
    )

    protected def verifyOutput[T](actualData: Dataset[T], expectedRows: List[T]): Unit = {
        assert(expectedRows.size == actualData.count())
        expectedRows should contain theSameElementsAs actualData.collect().toList
    }
}
