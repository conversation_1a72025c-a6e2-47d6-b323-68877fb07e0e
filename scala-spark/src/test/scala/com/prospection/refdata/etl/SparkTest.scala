package com.prospection.refdata.etl

import org.apache.spark.sql.SparkSession

trait SparkTest {
    protected val spark: SparkSession = {
        val sc = SparkSession.builder()
            .appName("Test Spark App")
            .master("local[1]")
            .config("spark.sql.shuffle.partitions", 1)
            .config("spark.default.parallelism", 1)
            .config("spark.sql.adaptive.enabled", "false")
            .config("spark.shuffle.compress", "false")
            .config("spark.broadcast.compress", "false")
            .config("spark.ui.enabled", "false")
            .getOrCreate()
        sc.sparkContext.setLogLevel("WARN")
        sc
    }

}
