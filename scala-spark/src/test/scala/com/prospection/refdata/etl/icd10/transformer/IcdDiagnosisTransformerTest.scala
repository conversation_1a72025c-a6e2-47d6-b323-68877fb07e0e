package com.prospection.refdata.etl.icd10.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.icd10.parser.IcdDiagnosisParser
import com.prospection.refdata.etl.icd10.rows.IcdOutputRow

import java.time.LocalDate

class IcdDiagnosisTransformerTest extends AbstractIntegrationTest {

    test("should transform all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("icd10/diagnosis").getPath

        val parseResult = new IcdDiagnosisParser(spark, pathPrefix, LocalDate.parse("2023-02-03")).parse()

        val transformInput = IcdTransformerInput(
            icd10s = parseResult.icd10Dataset,
            icd9LongDesc = parseResult.icd9LongDescDataset,
            icd9ShortDesc = parseResult.icd9ShortDescDataset,
            ccsrDataset = parseResult.ccsrDataset,
            parseResult.icd9GemDataset
        )

        val transformer = new IcdTransformer(spark)
        val output = transformer.transform(transformInput, "ICD-10-CM", "ICD-9-CM Dx").collectAsList()

        assert(output.size() == 11)
        assertRow(IcdOutputRow(
            code = "0010",
            description =  "Cholera due to vibrio cholerae ä é è",
            short_description = "Cholera d/t vib cholerae",
            icd_type = "ICD-9-CM Dx",
            default_ccsr_code = "DIG001",
            default_ccsr_description = "Intestinal infection",
            all_ccsr_codes = Array("DIG001", "INF003", "NVS001"),
            all_ccsr_descriptions = Array("Bacterial infections", "Intestinal infection", "Meningitis"),
        ), output.get(0))

        assertRow(IcdOutputRow(
            code = "A00",
            description = "Cholera long",
            short_description = "Cholera short",
            icd_type = "ICD-10-CM",
            default_ccsr_code = null,
            default_ccsr_description = null,
            all_ccsr_codes = Array(),
            all_ccsr_descriptions = Array(),
        ), output.get(1))

        assertRow(IcdOutputRow(
            code = "A000",
            description = "Cholera due to Vibrio cholerae 01, biovar cholerae",
            short_description = "Cholera due to Vibrio cholerae 01, biovar cholerae",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "DIG001",
            default_ccsr_description = "Intestinal infection",
            all_ccsr_codes = Array("DIG001", "INF003"),
            all_ccsr_descriptions = Array("Intestinal infection", "Bacterial infections"),
        ), output.get(2))

        assertRow(IcdOutputRow(
            code = "A001",
            description = "Cholera due to Vibrio cholerae 01, biovar eltor",
            short_description = "Cholera due to Vibrio cholerae 01, biovar eltor",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "DIG001",
            default_ccsr_description = "Intestinal infection",
            all_ccsr_codes = Array("DIG001", "INF003"),
            all_ccsr_descriptions = Array("Intestinal infection", "Bacterial infections"),
        ), output.get(3))

        assertRow(IcdOutputRow(
            code = "A009",
            description = "Cholera, unspecified",
            short_description = "Cholera, unspecified",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "DIG001",
            default_ccsr_description = "Intestinal infection",
            all_ccsr_codes = Array("DIG001", "INF003"),
            all_ccsr_descriptions = Array("Intestinal infection", "Bacterial infections"),
        ), output.get(4))

        assertRow(IcdOutputRow(
            code = "A01",
            description = "Typhoid and paratyphoid fevers",
            short_description = "Typhoid and paratyphoid fevers",
            icd_type = "ICD-10-CM",
            default_ccsr_code = null,
            default_ccsr_description = null,
            all_ccsr_codes = Array(),
            all_ccsr_descriptions = Array(),
        ), output.get(5))

        assertRow(IcdOutputRow(
            code = "A010",
            description = "Typhoid fever",
            short_description = "Typhoid fever",
            icd_type = "ICD-10-CM",
            default_ccsr_code = null,
            default_ccsr_description = null,
            all_ccsr_codes = Array(),
            all_ccsr_descriptions = Array(),
        ), output.get(6))

        assertRow(IcdOutputRow(
            code = "A0100",
            description = "Typhoid fever, unspecified",
            short_description = "Typhoid fever, unspecified",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "DIG001",
            default_ccsr_description = "Intestinal infection",
            all_ccsr_codes = Array("DIG001", "INF003"),
            all_ccsr_descriptions = Array("Intestinal infection", "Bacterial infections"),
        ), output.get(7))

        assertRow(IcdOutputRow(
            code = "A0101",
            description = "Typhoid meningitis",
            short_description = "Typhoid meningitis",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "INF003",
            default_ccsr_description = "Bacterial infections",
            all_ccsr_codes = Array("INF003", "NVS001"),
            all_ccsr_descriptions = Array("Bacterial infections", "Meningitis"),
        ), output.get(8))

        assertRow(IcdOutputRow(
            code = "A0102",
            description = "Typhoid fever with heart involvement",
            short_description = "Typhoid fever with heart involvement",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "INF003",
            default_ccsr_description = "Bacterial infections",
            all_ccsr_codes = Array("INF003"),
            all_ccsr_descriptions = Array("Bacterial infections"),
        ), output.get(9))

        assertRow(IcdOutputRow(
            code = "A0103",
            description = "Typhoid pneumonia",
            short_description = "Typhoid pneumonia",
            icd_type = "ICD-10-CM",
            default_ccsr_code = "INF003",
            default_ccsr_description = "Bacterial infections",
            all_ccsr_codes = Array("INF003", "RSP002"),
            all_ccsr_descriptions = Array("Bacterial infections", "Pneumonia (except that caused by tuberculosis)"),
        ), output.get(10))
    }

    private def assertRow(expected: IcdOutputRow, actual: IcdOutputRow): Unit = {
        assert(expected.code == actual.code)
        assert(expected.short_description == actual.short_description)
        assert(expected.description == actual.description)
        assert(expected.icd_type == actual.icd_type)
        assert(expected.default_ccsr_code == actual.default_ccsr_code)
        assert(expected.default_ccsr_description == actual.default_ccsr_description)
        assert(expected.all_ccsr_codes.sameElements(actual.all_ccsr_codes))
        assert(expected.all_ccsr_descriptions.sameElements(actual.all_ccsr_descriptions))
    }
}
