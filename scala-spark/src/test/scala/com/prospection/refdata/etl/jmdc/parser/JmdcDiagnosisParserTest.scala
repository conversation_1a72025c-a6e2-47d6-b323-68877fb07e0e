package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.jmdc.row.JmdcDiagnosisRow

class JmdcDiagnosisParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("jmdc").getPath

        val result = new JmdcDiagnosisParser(spark, pathPrefix).parse()

        // assert restriction
        verifyOutput(result.dataset, List(
            JmdcDiagnosisRow(code = "8834451", diagnosis_name = "knee bruise", icd10_code = "S800"),
            JmdcDiagnosisRow(code = "2392027", diagnosis_name = "facial skin neoplasm", icd10_code = "D485")
        ))
    }
}
