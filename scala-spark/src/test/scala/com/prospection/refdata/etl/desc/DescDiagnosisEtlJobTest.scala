package com.prospection.refdata.etl.desc

import com.prospection.refdata.etl.common.CodingSystems.DESC_DIAGNOSIS
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class DescDiagnosisEtlJobTest extends AbstractEtlTest {

    test("should execute ETL job and verify data stored in right place") {

        val resourcePath = getClass.getClassLoader.getResource("desc").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(DESC_DIAGNOSIS -> resourcePath))
        val spyEtlJob = spy(new DescDiagnosisEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
