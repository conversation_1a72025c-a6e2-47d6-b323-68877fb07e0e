package com.prospection.refdata.etl.mdvitem.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.mdvitem.datasets.{MdvItemDataset, MdvProcedureDataset}
import com.prospection.refdata.etl.mdvitem.rows.{MdvItemRow, MdvProcedureRow, OutputRow}

class MdvItemTransformerTest extends AbstractIntegrationTest {
    import spark.implicits._

    test("Drug rows and procedure rows are merged") {
        val input = MdvItemTransformerInput(
            new MdvItemDataset(
                Seq(MdvItemRow(
                    atc_ephmra = "A10M1",
                    drug_name_jp = "ナテグリニド",
                    drug_name = "Nateglinide",
                    code = "610432027",
                    description_jp = "スターシス錠９０ｍｇ",
                    description = "Starsis Tablets 90mg",
                    drug_usage = "1",
                    generic_flag = "0"
                )).toDS()
            ),
            new MdvProcedureDataset(
                Seq(MdvProcedureRow(
                    kubuncode = "B0012",
                    code = "113000510",
                    procedure_name = "特定薬剤治療管理料１（第４月目以降）",
                    description = "Specific drug treatment management fee 1 (fourth month and after)"
                )).toDS()
            ),
        )

        val transformer = new MdvItemTransformer(spark)
        val outputRows = transformer.transform(input).collect()

        assert(outputRows.length == 2)
        assert(outputRows(0) == OutputRow(
            atc_ephmra = "A10M1",
            drug_name_jp = "ナテグリニド",
            drug_name = "Nateglinide",
            code = "610432027",
            description_jp = "スターシス錠９０ｍｇ",
            description = "Starsis Tablets 90mg",
            drug_usage = "Oral medication",
            generic_flag = "Non - Generic",
            kubuncode = null,
            procedure_name = null,
        ))
        assert(outputRows(1) == OutputRow(
            kubuncode = "B0012",
            code = "113000510",
            procedure_name = "特定薬剤治療管理料１（第４月目以降）",
            description = "Specific drug treatment management fee 1 (fourth month and after)",
            atc_ephmra = null,
            drug_name_jp = null,
            drug_name = null,
            drug_usage = null,
            generic_flag = null,
            description_jp = null,
        ))
    }
}
