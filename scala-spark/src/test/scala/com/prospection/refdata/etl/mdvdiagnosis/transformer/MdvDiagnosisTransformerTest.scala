package com.prospection.refdata.etl.mdvdiagnosis.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.mdvdiagnosis.datasets.MdvDiagnosisDataset
import com.prospection.refdata.etl.mdvdiagnosis.rows.{MdvDiagnosisRow, OutputRow}

class MdvDiagnosisTransformerTest extends AbstractIntegrationTest {
    import spark.implicits._

    test("should transform to output row correctly") {
        val input = MdvDiagnosisTransformerInput(
            new MdvDiagnosisDataset(
                Seq(MdvDiagnosisRow(
                    code = "0703002",
                    icd10_code = "B169",
                    diagnosis_name_jp = "Ｂ型肝炎",
                    diagnosis_name = "hepatitis B",
                )).toDS()
            )
        )

        val transformer = new MdvDiagnosisTransformer(spark)
        val outputRows = transformer.transform(input).collect()

        assert(outputRows.length == 1)
        assert(outputRows(0) == OutputRow(
            code = "0703002",
            icd10_code = "B169",
            diagnosis_name_jp = "Ｂ型肝炎",
            diagnosis_name = "hepatitis B",
        ))
    }
}
