package com.prospection.refdata.etl.icd10.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.icd10.parser.IcdProcedureParser
import com.prospection.refdata.etl.icd10.rows.IcdOutputRow

import java.time.LocalDate

class IcdProcedureTransformerTest extends AbstractIntegrationTest {

    test("should transform all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("icd10/procedure").getPath

        val parseResult = new IcdProcedureParser(spark, pathPrefix, LocalDate.parse("2023-02-03")).parse()

        val transformInput = IcdTransformerInput(
            icd10s = parseResult.icd10Dataset,
            icd9LongDesc = parseResult.icd9LongDescDataset,
            icd9ShortDesc = parseResult.icd9ShortDescDataset,
            ccsrDataset = parseResult.ccsrDataset,
            parseResult.icd9GemDataset
        )

        val transformer = new IcdTransformer(spark)
        val output = transformer.transform(transformInput, "ICD-10-PCS", "ICD-9-CM Px").collectAsList()

        assert(output.size() == 11)
        assertRow(IcdOutputRow(
            code = "0016070",
            description = "Bypass Cerebral Ventricle to Nasopharynx with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Nasophar with Autol Sub, Open Approach",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(0))

        assertRow(IcdOutputRow(
            code = "0016071",
            description = "Bypass Cerebral Ventricle to Mastoid Sinus with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Mastoid Sinus w Autol Sub, Open",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(1))

        assertRow(IcdOutputRow(
            code = "0016072",
            description = "Bypass Cerebral Ventricle to Atrium with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Atrium with Autol Sub, Open Approach",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(2))

        assertRow(IcdOutputRow(
            code = "0016073",
            description = "Bypass Cerebral Ventricle to Blood Vessel with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Blood Vess w Autol Sub, Open",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(3))

        assertRow(IcdOutputRow(
            code = "0016074",
            description = "Bypass Cerebral Ventricle to Pleural Cavity with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Pleural Cav w Autol Sub, Open",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(4))

        assertRow(IcdOutputRow(
            code = "0016075",
            description = "Bypass Cerebral Ventricle to Intestine with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Intestine with Autol Sub, Open Approach",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(5))

        assertRow(IcdOutputRow(
            code = "0016076",
            description = "Bypass Cerebral Ventricle to Peritoneal Cavity with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Periton Cav w Autol Sub, Open",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(6))

        assertRow(IcdOutputRow(
            code = "0016077",
            description = "Bypass Cerebral Ventricle to Urinary Tract with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Urinary Tract w Autol Sub, Open",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(7))

        assertRow(IcdOutputRow(
            code = "0016078",
            description = "Bypass Cerebral Ventricle to Bone Marrow with Autologous Tissue Substitute, Open Approach",
            short_description = "Bypass Cereb Vent to Bone Mar with Autol Sub, Open Approach",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "CNS010",
            default_ccsr_description = "Cerebrospinal fluid shunt procedures",
            all_ccsr_codes = Array("CNS010"),
            all_ccsr_descriptions = Array("Cerebrospinal fluid shunt procedures"),
        ), output.get(8))

        assertRow(IcdOutputRow(
            code = "0049",
            description = "Supersaturated oxygen therapy",
            short_description = "SuperSat O2 therapy",
            icd_type = "ICD-9-CM Px",
            default_ccsr_code = "ESA011",
            default_ccsr_description = "Hyperbaric oxygen therapy",
            all_ccsr_codes = Array("ESA011"),
            all_ccsr_descriptions = Array("Hyperbaric oxygen therapy")
        ), output.get(9))

        assertRow(IcdOutputRow(
            code = "HZ99ZZZ",
            description = "Pharmacotherapy for Substance Abuse Treatment, Other Replacement Medication",
            short_description = "Pharmacotherapy for Substance Abuse, Oth Replace Med",
            icd_type = "ICD-10-PCS",
            default_ccsr_code = "SUD002",
            default_ccsr_description = "Pharmacotherapy for substance use",
            all_ccsr_codes = Array("SUD002"),
            all_ccsr_descriptions = Array("Pharmacotherapy for substance use"),
        ), output.get(10))
    }

    private def assertRow(expected: IcdOutputRow, actual: IcdOutputRow): Unit = {
        assert(expected.code == actual.code)
        assert(expected.short_description == actual.short_description)
        assert(expected.description == actual.description)
        assert(expected.icd_type == actual.icd_type)
        assert(expected.default_ccsr_code == actual.default_ccsr_code)
        assert(expected.default_ccsr_description == actual.default_ccsr_description)
        assert(expected.all_ccsr_codes.sameElements(actual.all_ccsr_codes))
        assert(expected.all_ccsr_descriptions.sameElements(actual.all_ccsr_descriptions))
    }
}
