package com.prospection.refdata.etl.desc

import com.prospection.refdata.etl.common.CodingSystems.DESC_ITEM
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class DescItemEtlJobTest extends AbstractEtlTest {

    test("should execute ETL job and verify data stored in right place") {

        val resourcePath = getClass.getClassLoader.getResource("desc").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(DESC_ITEM -> resourcePath))
        val spyEtlJob = spy(new DescItemEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
