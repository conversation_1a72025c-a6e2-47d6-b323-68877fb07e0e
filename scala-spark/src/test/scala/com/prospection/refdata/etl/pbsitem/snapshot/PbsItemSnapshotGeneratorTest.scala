package com.prospection.refdata.etl.pbsitem.snapshot

import com.prospection.refdata.etl.AbstractTest
import com.prospection.refdata.etl.pbsitem.domain.{OutputRow, SnapshotRow}


class PbsItemSnapshotGeneratorTest extends AbstractTest {

    import spark.implicits._

    test("should be able to merge the current dataset with the previous dataset that has missing fields") {
        // given

        val currentDataset = Seq(
            OutputRow(
                code = "001",
                atc_code = "A01",
                description = "Sample description",
                drug_name = "Sample drug",
                brand_name = "Sample brand",
                indication_id = "12345",
                manufacturer_name = "Sample manufacturer",
                restriction_flag = "N",
                indication_description = "Sample indication description",
                treatment_phase = "Phase 1",
                li_drug_name = "Sample LI drug",
                formulary = "Sample formulary",
                indication_condition = Seq("Sample condition"),
                indication_episodicity = Seq("Sample episodicity"),
                indication_severity = Seq("Sample severity"),
                route_of_administration = "Sample route"
            )
        ).toDS()

        val previousSnapshotPath = getClass.getClassLoader.getResource("pbs-item-api/test_snapshots/missing_columns").getPath

        val pbsItemSnapshotGenerator = PbsItemSnapshotGenerator(
            spark,
            currentDataset,
            "20240601",
            previousSnapshotPath
        )

        // when
        val generatedSnapshot = pbsItemSnapshotGenerator.generate()

        // then
        val snapshotRows = generatedSnapshot.collect()
        assert(snapshotRows.length == 2)
        var snapshotRow = snapshotRows(0)
        assert(snapshotRow.indication_condition == null)
        assert(snapshotRow.indication_episodicity == null)
        assert(snapshotRow.indication_severity == null)
        snapshotRow = snapshotRows(1)
        assert(snapshotRow.indication_condition == Seq("Sample condition"))
        assert(snapshotRow.indication_episodicity == Seq("Sample episodicity"))
        assert(snapshotRow.indication_severity == Seq("Sample severity"))
    }

    test("should be able to merge the current dataset with the previous dataset that has string values instead of arrays") {
        // given

        val currentDataset = Seq(
            OutputRow(
                code = "001",
                atc_code = "A01",
                description = "Sample description",
                drug_name = "Sample drug",
                brand_name = "Sample brand",
                indication_id = "12345",
                manufacturer_name = "Sample manufacturer",
                restriction_flag = "N",
                indication_description = "Sample indication description",
                treatment_phase = "Phase 1",
                li_drug_name = "Sample LI drug",
                formulary = "Sample formulary",
                indication_condition = Seq("Sample condition", "Another condition"),
                indication_episodicity = Seq("Sample episodicity"),
                indication_severity = Seq("Sample severity"),
                route_of_administration = "Sample route"
            )
        ).toDS()

        val previousSnapshotPath = getClass.getClassLoader.getResource("pbs-item-api/test_snapshots/string_becomes_array").getPath

        val pbsItemSnapshotGenerator = PbsItemSnapshotGenerator(
            spark,
            currentDataset,
            "20240601",
            previousSnapshotPath
        )

        // when
        val generatedSnapshot = pbsItemSnapshotGenerator.generate()

        // then
        val snapshotRows = generatedSnapshot.collect()
        assert(snapshotRows.length == 1)
        var snapshotRow = snapshotRows.head
        assert(snapshotRow.indication_condition == Seq("Sample condition", "Another condition"))
        assert(snapshotRow.indication_episodicity == Seq("Sample episodicity"))
        assert(snapshotRow.indication_severity == Seq("Sample severity"))
    }

    test("should be able to merge the current dataset with the previous dataset that has array values in different order") {
        // given

        val currentDataset = Seq(
            OutputRow(
                code = "001",
                atc_code = "A01",
                description = "Sample description",
                drug_name = "Sample drug",
                brand_name = "Sample brand",
                indication_id = "12345",
                manufacturer_name = "Sample manufacturer",
                restriction_flag = "N",
                indication_description = "Sample indication description",
                treatment_phase = "Phase 1",
                li_drug_name = "Sample LI drug",
                formulary = "Sample formulary",
                indication_condition = Seq("Sample condition 2", "Sample condition 1"),
                indication_episodicity = Seq("Sample episodicity"),
                indication_severity = Seq("Sample severity"),
                route_of_administration = "Sample route"
            )
        ).toDS()

        val previousSnapshotPath = getClass.getClassLoader.getResource("pbs-item-api/test_snapshots/with_array_columns").getPath

        val pbsItemSnapshotGenerator = PbsItemSnapshotGenerator(
            spark,
            currentDataset,
            "20240601",
            previousSnapshotPath
        )

        // when
        val generatedSnapshot = pbsItemSnapshotGenerator.generate()

        // then
        val snapshotRows = generatedSnapshot.collect()
        assert(snapshotRows.length == 1)
        var snapshotRow = snapshotRows.head
        assert(snapshotRow.indication_condition == Seq("Sample condition 1", "Sample condition 2"))
    }

}
