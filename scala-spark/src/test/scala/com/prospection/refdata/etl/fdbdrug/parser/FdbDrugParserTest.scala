package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.fdbdrug.rows._

class FdbDrugParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("fdb-drug/raw").getPath

        val result = new FdbDrugParser(spark, pathPrefix).parse()

        // assert restriction
        verifyOutput(result.ndcDataset, List(
            FdbNdcRow(
                code = "00002010102",
                brand_name = "AMMONIUM CHLORIDE",
                label_name = "AMMONIUM CHLORIDE 500 MG ENS",
                package_size = "00000100.000",
                package_description = "BOTTLE",
                lblrid = "A00002",
                gcn_seqno = "008162",
                obsolete_date = "20220413"
            ),
            FdbNdcRow(code = "00002050101",
                brand_name = "NEBCIN",
                label_name = "NEBCIN PED 10 MG/ML VIAL",
                package_size = "00000002.000",
                package_description = "VIAL",
                lblrid = "A00002",
                gcn_seqno = "009309",
                obsolete_date = null
            ),
            FdbNdcRow(
                code = "00777050924",
                brand_name = "NEBCIN",
                label_name = "NEBCIN 40 MG/ML SYRINGE",
                package_size = "00000001.500",
                package_description = "SYRINGE",
                lblrid = "A00777",
                gcn_seqno = "009305",
                obsolete_date = "19870115",
            ),
            FdbNdcRow(
                code = "99999099213",
                brand_name = "EUA PATIENT ASSESSMENT",
                label_name = "EUA PATIENT ASSESSMENT",
                package_size = "00000001.000",
                package_description = "BOX",
                lblrid = "A99999",
                gcn_seqno = "083641",
                obsolete_date = "00000000",
            )
        ))

        verifyOutput(result.ndcDescDataset, List(
            FdbNdcDescRow(lblrid = "A00002", labeller_name = "ELI LILLY & CO."),
            FdbNdcDescRow(lblrid = "A00777", labeller_name = "DISTA LABS"),
            FdbNdcDescRow(lblrid = "A99999", labeller_name = "NCPDP EMERGENCY")),
        )

        verifyOutput(result.ndcDeletionReasonDataset, List(
            FdbNdcDeletionReasonRow(code = "00002010102", previous_version_delete_date = "20171005"),
            FdbNdcDeletionReasonRow(code = "99999099213", previous_version_delete_date = "20171005"),
        ))

        verifyOutput(result.doseDescDataset, List(
            FdbDoseDescRow(gcdf = "TE", dose_form = "TABLET, DELAYED RELEASE (ENTERIC COATED)"),
            FdbDoseDescRow(gcdf = "HV", dose_form = "VIAL (ML)"),
            FdbDoseDescRow(gcdf = "HQ", dose_form = "SYRINGE (ML)"),
            FdbDoseDescRow(gcdf = "ZA", dose_form = "MISCELLANEOUS")),
        )

        verifyOutput(result.genericCodeNumberDataset, List(
            FdbGenericCodeNumberRow(gcn_seqno = "008162", gcdf = "TE", gcrt = "1", hic3_seqn = "000421", hicl_seqno = "003639"),
            FdbGenericCodeNumberRow(gcn_seqno = "009309", gcdf = "HV", gcrt = "2", hic3_seqn = "000481", hicl_seqno = "004034"),
            FdbGenericCodeNumberRow(gcn_seqno = "009305", gcdf = "HQ", gcrt = "2", hic3_seqn = "000481", hicl_seqno = "004034"),
            FdbGenericCodeNumberRow(gcn_seqno = "083641", gcdf = "ZA", gcrt = "M", hic3_seqn = "018325", hicl_seqno = "048169")),
        )

        verifyOutput(result.ingredientStrengthDataset, List(
            FdbIngredientStrengthRow(gcn_seqno = "008162", strength_number = "0000000000500.000000", strength_uom_id = "00000001"),
            FdbIngredientStrengthRow(gcn_seqno = "009309", strength_number = "0000000000010.000000", strength_uom_id = "00000001"),
            FdbIngredientStrengthRow(gcn_seqno = "009305", strength_number = "0000000000040.000000", strength_uom_id = "00000001"),
            FdbIngredientStrengthRow(gcn_seqno = "083641", strength_number = "0000000000000.000000", strength_uom_id = "00000000"))
        )

        verifyOutput(result.strengthUomDataset, List(
            FdbStrengthUomRow(strength_unit_description_abbreviation = "mg", uom_id = "00000001"))
        )

        verifyOutput(result.routeDescDataset, List(
            FdbRouteDescRow(gcrt = "1", route_of_administration = "ORAL"),
            FdbRouteDescRow(gcrt = "2", route_of_administration = "INJECTION"),
            FdbRouteDescRow(gcrt = "M", route_of_administration = "MISCELLANEOUS"))
        )

        verifyOutput(result.hierarchicalIngredientListDataset, List(
            FdbHierarchicalIngredientListRow(drug_name = "ammonium chloride", hicl_seqno = "003639"),
            FdbHierarchicalIngredientListRow(drug_name = "tobramycin sulfate", hicl_seqno = "004034"),
            FdbHierarchicalIngredientListRow(drug_name = "EUA patient assessment", hicl_seqno = "048169"),
        ))

        verifyOutput(result.hicDescDataset, List(
            FdbHicDescRow(hic_seqn = "002276", ingredient_name = "ammonium chloride"),
            FdbHicDescRow(hic_seqn = "002780", ingredient_name = "tobramycin sulfate"),
            FdbHicDescRow(hic_seqn = "019131", ingredient_name = "EUA patient assessment"),
        ))

        verifyOutput(result.hicLinkDataset, List(
            FdbHicLinkRow(hicl_seqno = "003639", hic_seqn = "002276"),
            FdbHicLinkRow(hicl_seqno = "004034", hic_seqn = "002780"),
            FdbHicLinkRow(hicl_seqno = "048169", hic_seqn = "019131"),
        ))


        verifyOutput(result.atcLinkDataset, List(
            FdbAtcLinkRow(gcn_seqno = "009309", atc_code = "J01GB01"),
            FdbAtcLinkRow(gcn_seqno = "009305", atc_code = "J01GB01"),
            FdbAtcLinkRow(gcn_seqno = "008162", atc_code = "G04BA01"))
        )

        verifyOutput(result.hierarchicalIngredientDataset, List(
            FdbHierarchicalIngredientRow(hic3_seqn = "000421", hic3_description = "INORGANIC SALT DIURETICS", hic3_classification = "R1C"),
            FdbHierarchicalIngredientRow(hic3_seqn = "000481", hic3_description = "AMINOGLYCOSIDE ANTIBIOTICS", hic3_classification = "W1F"),
            FdbHierarchicalIngredientRow(hic3_seqn = "018325", hic3_description = "DIAGNOSTIC TEST DEVICES, SUPPLIES, AND SERVICES", hic3_classification = "X6C"))
        )

        verifyOutput(result.ahfsLinkDataset, List(
            FdbAhfsLinkRow(gcn_seqno = "008162", ahfs_classification = "40040000"),
            FdbAhfsLinkRow(gcn_seqno = "009309", ahfs_classification = "08120200"),
            FdbAhfsLinkRow(gcn_seqno = "009305", ahfs_classification = "08120200"),
            FdbAhfsLinkRow(gcn_seqno = "083641", ahfs_classification = "94000000"),
        ))

        verifyOutput(result.ahfsDescDataset, List(
            FdbAhfsDescRow(ahfs_classification = "40040000", ahfs_description = "ACIDIFYING AGENTS"),
            FdbAhfsDescRow(ahfs_classification = "08120200", ahfs_description = "AMINOGLYCOSIDE ANTIBIOTICS"),
            FdbAhfsDescRow(ahfs_classification = "94000000", ahfs_description = "DEVICES"),
        ))

        verifyOutput(result.indicationLinkDataset, List(
            FdbIndicationLinkRow(gcn_seqno = "008162", indcts = "02090"),
            FdbIndicationLinkRow(gcn_seqno = "009309", indcts = "01490"),
            FdbIndicationLinkRow(gcn_seqno = "009305", indcts = "01490"),
            FdbIndicationLinkRow(gcn_seqno = "083641", indcts = "00079"))
        )

        assert(55 == result.indicationDataset.count())

        assert(239 == result.icdSearchDataset.count())

        verifyOutput(result.diseaseIdentifierDataset, List(
            FdbDiseaseIdentifierRow(dxid = "00013671", dxid_desc100 = "ventricular rate control in atrial fibrillation"),
            FdbDiseaseIdentifierRow(dxid = "00001578", dxid_desc100 = "chronic heart failure"),
            FdbDiseaseIdentifierRow(dxid = "00001538", dxid_desc100 = "paroxysmal supraventricular tachycardia"),
            FdbDiseaseIdentifierRow(dxid = "00003075", dxid_desc100 = "fatigue"),
            FdbDiseaseIdentifierRow(dxid = "00009033", dxid_desc100 = "synergy for nosocomial pneumonia due to Pseudomonas aeruginosa"),
            FdbDiseaseIdentifierRow(dxid = "00003036", dxid_desc100 = "drowsy"))
        )

        verifyOutput(result.medicareDataset, List(
            FdbMedicareRow(code = "00002010102", mcr_ref = "J3260"),
            FdbMedicareRow(code = "00002050101", mcr_ref = "J3260"),
            FdbMedicareRow(code = "00777050924", mcr_ref = "J3260"),
            FdbMedicareRow(code = "99999099213", mcr_ref = "J3260"),
        ))

        verifyOutput(result.medicareReferenceDescDataset, List(
            FdbMedicareReferenceDescRow(jcode = "J3260", jcode_desc = "Tobramycin sulfate inj, 80 MG"),
            FdbMedicareReferenceDescRow(jcode = "A4216", jcode_desc = "Sterile water/saline, 10 ml")
        ))

        verifyOutput(result.etcDataset, List(
            FdbEtcRow(gcn_seqno = "008162", etc_classification = "00002536"),
            FdbEtcRow(gcn_seqno = "008162", etc_classification = "00003096"),
            FdbEtcRow(gcn_seqno = "009309", etc_classification = "00000034"),
            FdbEtcRow(gcn_seqno = "009305", etc_classification = "00000034"),
            FdbEtcRow(gcn_seqno = "083641", etc_classification = "00001207"),
            FdbEtcRow(gcn_seqno = "083641", etc_classification = "00005904"),
        ))

        verifyOutput(result.etcIdDataset, List(
            FdbEtcIdRow(etc_classification = "00002536", etc_description = "Diuretic - Inorganic Salt"),
            FdbEtcIdRow(etc_classification = "00003096", etc_description = "Urinary Acidifier - Others"),
            FdbEtcIdRow(etc_classification = "00000034", etc_description = "Aminoglycoside Antibiotic"),
            FdbEtcIdRow(etc_classification = "00001207", etc_description = "Medical Supplies and DME - Miscellaneous Other"),
            FdbEtcIdRow(etc_classification = "00005904", etc_description = "Medical Supply, FDB Superset"),
        ))

        verifyOutput(result.uscLinkDataset, List(
            FdbUscNdcLinkRow(code = "00002010102", usc_classification = "41190"),
            FdbUscNdcLinkRow(code = "00002050101", usc_classification = "15170"),
            FdbUscNdcLinkRow(code = "00777050924", usc_classification = "15170"),
            FdbUscNdcLinkRow(code = "99999099213", usc_classification = "40400"),
        ))

        verifyOutput(result.uscDescDataset, List(
            FdbUscDescRow(usc_classification = "41190", usc_description = "DIURETICS, OTHER"),
            FdbUscDescRow(usc_classification = "15170", usc_description = "AMINOGLYCOSIDES"),
            FdbUscDescRow(usc_classification = "40400", usc_description = "DIAGNOSTICS OTHERS"),
        ))
    }
}
