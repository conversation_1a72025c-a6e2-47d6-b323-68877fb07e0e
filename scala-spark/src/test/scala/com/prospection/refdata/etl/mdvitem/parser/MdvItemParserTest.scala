package com.prospection.refdata.etl.mdvitem.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.mdvitem.rows.{MdvItemRow, MdvProcedureRow}

class MdvItemParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns from a M_Drug.txt file to a drug row") {
        val parser = new MdvItemParser(spark, getClass.getClassLoader.getResource("mdv-item").getPath)

        val drugs = parser.parse().drugs.getDataset

        assert(drugs.count() == 1)
        assert(drugs.first() == MdvItemRow(
            atc_ephmra = "A10M1",
            drug_name_jp = "ナテグリニド",
            drug_name = "Nateglinide",
            code = "610432027",
            description_jp = "スターシス錠９０ｍｇ",
            description = "Starsis Tablets 90mg",
            drug_usage = "1",
            generic_flag = "0"
        ))
    }

    test("should parse all the necessary columns from a M_Act.txt file to a procedure row") {
        val parser = new MdvItemParser(spark, getClass.getClassLoader.getResource("mdv-item").getPath)

        val procedures = parser.parse().procedures.getDataset

        assert(procedures.count() == 1)
        assert(procedures.first() == MdvProcedureRow(
            kubuncode = "B0012",
            code = "113000510",
            procedure_name = "特定薬剤治療管理料１（第４月目以降）",
            description = "Specific drug treatment management fee 1 (fourth month and after)"
        ))
    }
}
