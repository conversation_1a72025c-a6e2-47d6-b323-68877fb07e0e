package com.prospection.refdata.etl.pbsitem.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.pbsitem.datasets.{CriteriaDataset, CriteriaParameterRelationshipDataset, IndicationDataset, ItemAtcRelationshipDataset, ItemDataset, ItemRestrictionRelationshipDataset, ManufacturerDataset, PrescribingTextDataset, RestrictionDataset, RestrictionPrescribingTextRelationshipDataset}
import com.prospection.refdata.etl.pbsitem.domain.{CriteriaParameterRelationshipRow, CriteriaRow, IndicationDescriptionRow, IndicationRow, ItemAtcRelationshipRow, ItemRestrictionRelationshipRow, ItemRow, OrganisationRow, PrescribingTextRow, RestrictionPrescribingTextRelationshipRow, RestrictionRow}
import org.scalatest.matchers.should.Matchers.convertToAnyShouldWrapper

class IndicationDescriptionTransformerTest extends AbstractIntegrationTest {

    import spark.implicits._

    // ITEMS
    val item1: ItemRow = ItemRow(
        code = "item1",
        drug_name = "Drug 1",
        brand_name = "Brand 1",
        restriction_flag = "A",
        manufacturer_id = "manufacturer1",
        description = "Description 1",
        li_drug_name = "Li Drug 1",
        formulary = "F1"
    )

    // RESTRICTIONS
    val restriction1: RestrictionRow = RestrictionRow(
        res_code = "RES1",
        indication_id = "indication1",
        treatment_phase = "Test phase1",
        indication_description = "Unusable restriction text 1",
        criteria_relationship = "ALL"
    )
    val restriction2: RestrictionRow = RestrictionRow(
        res_code = "RES2",
        indication_id = "indication2",
        treatment_phase = null,
        indication_description = "Unusable restriction text 2",
        criteria_relationship = "ALL"
    )

    // ITEM RESTRICTION RELATIONSHIP
    val itemRestriction1: ItemRestrictionRelationshipRow = ItemRestrictionRelationshipRow(
        code = "item1",
        res_code = "RES1"
    )
    val itemRestriction2: ItemRestrictionRelationshipRow = ItemRestrictionRelationshipRow(
        code = "item1",
        res_code = "RES2"
    )

    // RESTRICTION PRESCRIBING TEXT RELATIONSHIP
    val res1Indication: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT1",
        criteria_position = "1"
    )
    val res1Clinical1: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT2",
        criteria_position = "2"
    )
    val res1Clinical2: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT3",
        criteria_position = "3"
    )
    val res1Treatment1: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT4",
        criteria_position = "4"
    )
    val res1Treatment2: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT5",
        criteria_position = "5"
    )
    val res1Population: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT6",
        criteria_position = "6"
    )
    val res1Prescribing: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT7",
        criteria_position = "7"
    )
    val res2Indication: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES2",
        prescribing_text_id = "TXT8",
        criteria_position = "1"
    )
    val res2AdminAdvice: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "TXT9",
        criteria_position = "2"
    )

    // CRITERIA PRESCRIBING TEXT
    val indicationTextRes1: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT1",
        prescribing_type = "INDICATION",
        prescribing_txt = "Indication Text 1"
    )
    val clinicalCriteria1Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT2",
        prescribing_type = "CRITERIA",
        prescribing_txt = "Unusable restriction text 1"
    )
    val clinicalCriteria2Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT3",
        prescribing_type = "CRITERIA",
        prescribing_txt = "Unusable restriction text 2"
    )
    val treatmentCriteria1Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT4",
        prescribing_type = "CRITERIA",
        prescribing_txt = "Unusable restriction text 3"
    )
    val treatmentCriteria2Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT5",
        prescribing_type = "CRITERIA",
        prescribing_txt = "Unusable restriction text 4"
    )
    val populationCriteriaText: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT6",
        prescribing_type = "CRITERIA",
        prescribing_txt = "Unusable restriction text 5"
    )
    val prescribingInstructionsText: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT7",
        prescribing_type = "PRESCRIBING_INSTRUCTIONS",
        prescribing_txt = "prescribing instruction 1"
    )
    val indicationTextRes2: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT8",
        prescribing_type = "INDICATION",
        prescribing_txt = "Indication Text 2"
    )
    val adminAdviceTextRes2: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT8",
        prescribing_type = "ADMINISTRATIVE_ADVICE",
        prescribing_txt = "This is administrative advice text. It should not be included in the description."
    )

    // PARAMETER PRESCRIBING TEXT
    val clinicalCriteria1Param1Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT2_1",
        prescribing_type = "PARAMETER",
        prescribing_txt = "clinical criteria 1_1"
    )
    val clinicalCriteria1Param2Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT2_2",
        prescribing_type = "PARAMETER",
        prescribing_txt = "clinical criteria 1_2"
    )
    val clinicalCriteria2Param1Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT3_1",
        prescribing_type = "PARAMETER",
        prescribing_txt = "clinical criteria 2_1"
    )
    val clinicalCriteria2Param2Text: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT3_2",
        prescribing_type = "PARAMETER",
        prescribing_txt = "clinical criteria 2_2"
    )
    val treatmentCriteria1ParamText: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT4_1",
        prescribing_type = "PARAMETER",
        prescribing_txt = "treatment criteria 1"
    )
    val treatmentCriteria2ParamText: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT5_1",
        prescribing_type = "PARAMETER",
        prescribing_txt = "treatment criteria 2"
    )
    val populationCriteriaParamText: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "TXT6_1",
        prescribing_type = "PARAMETER",
        prescribing_txt = "population criteria"
    )

    // CRITERIA
    val clinicalCriteria1: CriteriaRow = CriteriaRow(
        criteria_prescribing_txt_id = "TXT2",
        criteria_type = "CLINICAL",
        parameter_relationship = "ANY"
    )
    val clinicalCriteria2: CriteriaRow = CriteriaRow(
        criteria_prescribing_txt_id = "TXT3",
        criteria_type = "CLINICAL",
        parameter_relationship = "ALL"
    )
    val treatmentCriteria1: CriteriaRow = CriteriaRow(
        criteria_prescribing_txt_id = "TXT4",
        criteria_type = "TREATMENT",
        parameter_relationship = "ANY"
    )
    val treatmentCriteria2: CriteriaRow = CriteriaRow(
        criteria_prescribing_txt_id = "TXT5",
        criteria_type = "TREATMENT",
        parameter_relationship = "ALL"
    )
    val populationCriteria: CriteriaRow = CriteriaRow(
        criteria_prescribing_txt_id = "TXT6",
        criteria_type = "POPULATION",
        parameter_relationship = "ANY"
    )

    // CRITERIA PARAMETER RELATIONSHIP
    val clinicalCriteria1Param1: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT2",
        prescribing_text_id = "TXT2_1",
        parameter_position = "1"
    )
    val clinicalCriteria1Param2: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT2",
        prescribing_text_id = "TXT2_2",
        parameter_position = "2"
    )
    val clinicalCriteria2Param1: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT3",
        prescribing_text_id = "TXT3_1",
        parameter_position = "1"
    )
    val clinicalCriteria2Param2: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT3",
        prescribing_text_id = "TXT3_2",
        parameter_position = "2"
    )
    val treatmentCriteria1Parameter1: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT4",
        prescribing_text_id = "TXT4_1",
        parameter_position = "1"
    )
    val treatmentCriteria2Parameter1: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT5",
        prescribing_text_id = "TXT5_1",
        parameter_position = "1"
    )
    val populationCriteriaParameter: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "TXT6",
        prescribing_text_id = "TXT6_1",
        parameter_position = "1"
    )

    val indication1: IndicationRow = IndicationRow(
        indication_prescribing_txt_id = "TXT1",
        condition = "Condition 1",
        episodicity = "Episodicity 1",
        severity = "Severity 1"
    )

    val indication2: IndicationRow = IndicationRow(
        indication_prescribing_txt_id = "TXT2",
        condition = "Condition 2",
        episodicity = "Episodicity 2",
        severity = "Severity 2"
    )

    test("should resolve indication description for all types of criteria and with criteria relationship ALL and handle relationships between criteria parameters both ALL and ANY. And should ignore admin advice text.") {
        // prepare
        val input = PbsItemTransformerInput(
            items = new ItemDataset(Seq(item1).toDS()),
            restrictions = new RestrictionDataset(Seq(restriction1, restriction2).toDS()),
            manufacturers = new ManufacturerDataset(Seq.empty[OrganisationRow].toDS()),
            itemAtcRelationships = new ItemAtcRelationshipDataset(Seq.empty[ItemAtcRelationshipRow].toDS()),
            itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(itemRestriction1, itemRestriction2).toDS()),
            criteria = new CriteriaDataset(Seq(clinicalCriteria1, clinicalCriteria2, treatmentCriteria1, treatmentCriteria2, populationCriteria).toDS()),
            restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(res1Indication, res1Clinical1, res1Clinical2, res1Treatment1, res1Treatment2, res1Population, res1Prescribing, res2Indication, res2AdminAdvice).toDS()),
            prescribingTexts = new PrescribingTextDataset(Seq(indicationTextRes1, clinicalCriteria1Text, clinicalCriteria2Text, treatmentCriteria1Text, treatmentCriteria2Text, populationCriteriaText, prescribingInstructionsText, indicationTextRes2, clinicalCriteria1Param1Text, clinicalCriteria1Param2Text, clinicalCriteria2Param1Text, clinicalCriteria2Param2Text, treatmentCriteria1ParamText, treatmentCriteria2ParamText, populationCriteriaParamText, adminAdviceTextRes2).toDS()),
            criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(clinicalCriteria1Param1, clinicalCriteria1Param2, clinicalCriteria2Param1, clinicalCriteria2Param2, treatmentCriteria1Parameter1, treatmentCriteria2Parameter1, populationCriteriaParameter).toDS()),
            indication = new IndicationDataset(Seq(indication1, indication2).toDS())
        )

        // execute
        val results = new IndicationDescriptionTransformer(spark).transform(input).collect().sortBy(_.indication_id)

        // verify
        results.length shouldBe 2
        assert(results(0) == IndicationDescriptionRow(
            code = "item1",
            indication_id = "indication1",
            indication_description = "Indication Text 1 Treatment Phase: Test phase1 Clinical criteria: * clinical criteria 1_1; OR * clinical criteria 1_2, AND * clinical criteria 2_1; AND * clinical criteria 2_2 Treatment criteria: * treatment criteria 1, AND * treatment criteria 2 Population criteria: * population criteria prescribing instruction 1",
            treatment_phase = "Test phase1",
            indication_condition = Seq("Condition 1", "Condition 2"),
            indication_episodicity = Seq("Episodicity 1", "Episodicity 2"),
            indication_severity = Seq("Severity 1", "Severity 2")
        ))
        assert(results(1) == IndicationDescriptionRow(
            code = "item1",
            indication_id = "indication2",
            indication_description = "Indication Text 2",
            treatment_phase = null,
            indication_condition = null,
            indication_episodicity = null,
            indication_severity = null
        ))

    }

    test("should resolve indication description with criteria relationship ANY") {
        // prepare
        val restriction1WithAny = restriction1.copy(criteria_relationship = "ANY")
        val input = PbsItemTransformerInput(
            items = new ItemDataset(Seq(item1).toDS()),
            restrictions = new RestrictionDataset(Seq(restriction1WithAny).toDS()),
            manufacturers = new ManufacturerDataset(Seq.empty[OrganisationRow].toDS()),
            itemAtcRelationships = new ItemAtcRelationshipDataset(Seq.empty[ItemAtcRelationshipRow].toDS()),
            itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(itemRestriction1).toDS()),
            criteria = new CriteriaDataset(Seq(clinicalCriteria1, clinicalCriteria2).toDS()),
            restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(res1Indication, res1Clinical1, res1Clinical2).toDS()),
            prescribingTexts = new PrescribingTextDataset(Seq(indicationTextRes1, clinicalCriteria1Text, clinicalCriteria2Text, clinicalCriteria1Param1Text, clinicalCriteria1Param2Text, clinicalCriteria2Param1Text, clinicalCriteria2Param2Text).toDS()),
            criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(clinicalCriteria1Param1, clinicalCriteria1Param2, clinicalCriteria2Param1, clinicalCriteria2Param2).toDS()),
            indication = new IndicationDataset(Seq.empty[IndicationRow].toDS())
        )

        // execute
        val results = new IndicationDescriptionTransformer(spark).transform(input).collect().sortBy(_.indication_id)

        // verify
        results.length shouldBe 1
        assert(results(0) == IndicationDescriptionRow(
            code = "item1",
            indication_id = "indication1",
            indication_description = "Indication Text 1 Treatment Phase: Test phase1 Clinical criteria: * clinical criteria 1_1; OR * clinical criteria 1_2, OR * clinical criteria 2_1; AND * clinical criteria 2_2",
            treatment_phase = "Test phase1",
            indication_condition = null,
            indication_episodicity = null,
            indication_severity = null
        ))
    }

}
