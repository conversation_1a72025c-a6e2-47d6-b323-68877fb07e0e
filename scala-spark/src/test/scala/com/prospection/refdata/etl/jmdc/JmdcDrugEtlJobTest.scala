package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_DRUG
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito._

class JmdcDrugEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("jmdc").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(JMDC_DRUG -> resourcePath))
        val spyEtlJob = spy(new JmdcDrugEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
