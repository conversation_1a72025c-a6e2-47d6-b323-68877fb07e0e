package com.prospection.refdata.etl.pbsitem.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.parser.writer.{PbsItemHistoricalWriter, PbsItemHistoricalWriterInput}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{doNothing, spy, verify}

class PbsItemHistoricalWriterTest extends AbstractIntegrationTest {
    import spark.implicits._

    test("should store historical data in correct locations") {
        val mockDs = List(("colA", "colB")).toDF("colA", "colB")

        val spyWriter = spy(PbsItemHistoricalWriter(
            PbsItemHistoricalWriterInput(
                itemDataset = mockDs,
                restrictionDataset = mockDs,
                manufacturerDataset = mockDs,
                itemAtcRelationshipDataset = mockDs,
                itemRestrictionRelationshipDataset = mockDs,
                criteriaDataset = mockDs,
                restrictionPrescribingTextRelationshipDataset = mockDs,
                prescribingTextDataset = mockDs,
                criteriaParameterRelationshipDataset = mockDs
            )
        ))

        doNothing().when(spyWriter).storeHistoricalData(any(), any(), any())

        val testVersion = "202406"
        val testPath = "s3://test/path"
        spyWriter.write(testPath, testVersion)

        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/items/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/restrictions/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/organisations/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/item-atc-relationships/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/item-restriction-relationships/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/criteria/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/restriction-prescribing-text-relationships/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/prescribing-texts/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))
        verify(spyWriter).storeHistoricalData(mockDs, s"$testPath/criteria-parameter-relationships/month=202406", SparkOptions(delimiter = CommonDelimiters.COMMA))

    }

}