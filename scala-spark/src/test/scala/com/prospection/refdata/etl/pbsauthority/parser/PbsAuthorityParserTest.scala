package com.prospection.refdata.etl.pbsauthority.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityRestrictionRow
import org.apache.spark.sql.SparkSession
import org.scalatest.matchers.must.Matchers.{contain, convertToAnyMustWrapper}

class PbsAuthorityParserTest extends AbstractIntegrationTest {
    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("pbs-authority/raw/PBS_10PRCNT_QUARTERLY_REPORT_CAVEATS_25SEP2022.xlsx").getPath

        val result = new PbsAuthorityParser(spark, pathPrefix).parse()

        // assert restriction
        assert(result.restrictionDataset.count() == 5)

        result.restrictionDataset.collect().toList must contain allOf(
            PbsAuthorityRestrictionRow(code = "1000", description = "Abetalipoproteinaemia"),
            PbsAuthorityRestrictionRow(code = "1001", description = "Acromegaly"),
            PbsAuthorityRestrictionRow(code = "1002", description = "Acute bacterial enterocolitis"),
            PbsAuthorityRestrictionRow(code = "1003", description = "Acute lactose intolerance in children over 1 year of age"),
            PbsAuthorityRestrictionRow(code = "1004", description = "Acute lactose intolerance in infants up to the age of 12 months.")
        )
    }

    test("should parse all the necessary columns with xml") {
        val pathPrefix = getClass.getClassLoader.getResource("pbs-authority/raw/PBS_10PRCNT_QUARTERLY_REPORT_CAVEATS_24SEP2022.xls").getPath

        val result = new PbsAuthorityParser(spark, pathPrefix).parse()

        // assert restriction
        assert(result.restrictionDataset.count() == 4)

        result.restrictionDataset.collect().toList must contain allOf(
            PbsAuthorityRestrictionRow(code = "1006", description = "Acute myelogenous leukaemia"),
            PbsAuthorityRestrictionRow(code = "1007", description = "Adjunctive therapy for use with ifosfamide and high dose cyclophosphamide"),
            PbsAuthorityRestrictionRow(code = "1008", description = "Adjunctive therapy in late stage Parkinson's disease in patients being treated with levodopa - decarboxylase inhibitor combinations"),
            PbsAuthorityRestrictionRow(code = "1009", description = "Adjunctive therapy of malignant melanoma following surgery in patients with nodal involvement")
        )
    }

    test("should parse all the necessary columns with new format xml") {
        val pathPrefix = getClass.getClassLoader.getResource("pbs-authority/raw/PBS_10PRCNT_MONTHLY_REPORT_CAVEATS_23APR2025.xls").getPath

        val result = new PbsAuthorityParser(spark, pathPrefix).parse()

        // assert restriction
        assert(result.restrictionDataset.count() == 5)

        result.restrictionDataset.collect().toList must contain allOf(
            PbsAuthorityRestrictionRow(code = "1000", description = "Abetalipoproteinaemia"),
            PbsAuthorityRestrictionRow(code = "1001", description = "Acromegaly"),
            PbsAuthorityRestrictionRow(code = "1002", description = "Acute bacterial enterocolitis"),
            PbsAuthorityRestrictionRow(code = "1003", description = "Acute lactose intolerance in children over 1 year of age"),
            PbsAuthorityRestrictionRow(code = "1004", description = "Acute lactose intolerance in infants up to the age of 12 months. Age of patient must be shown on the prescription")
        )
    }
}
