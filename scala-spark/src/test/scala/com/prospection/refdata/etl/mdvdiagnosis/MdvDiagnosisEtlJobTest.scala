package com.prospection.refdata.etl.mdvdiagnosis

import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class MdvDiagnosisEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("mdv-diagnosis").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map("MDV Disease" -> resourcePath, "MDV HIA Disease" -> resourcePath))

        val spyEtlJob = spy(new MdvDiagnosisEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
