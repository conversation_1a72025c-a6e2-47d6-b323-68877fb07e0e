package com.prospection.refdata.etl.fdbdrug.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.fdbdrug.parser.FdbDrugParser
import com.prospection.refdata.etl.fdbdrug.rows.FdbDrugOutputRow

class FdbDrugTransformerTest extends AbstractIntegrationTest {

    test("should transform all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("fdb-drug/raw").getPath

        val parseResult = new FdbDrugParser(spark, pathPrefix).parse()

        val transformInput = FdbDrugTransformerInput(
            ndcDataset = parseResult.ndcDataset,
            ndcDescDataset = parseResult.ndcDescDataset,
            ndcDeletionReasonDataset = parseResult.ndcDeletionReasonDataset,
            doseDescDataset = parseResult.doseDescDataset,
            genericCodeNumberDataset = parseResult.genericCodeNumberDataset,
            ingredientStrengthDataset = parseResult.ingredientStrengthDataset,
            strengthUomDataset = parseResult.strengthUomDataset,
            routeDescDataset = parseResult.routeDescDataset,
            hierarchicalIngredientListDataset = parseResult.hierarchicalIngredientListDataset,
            hicDescDataset = parseResult.hicDescDataset,
            hicLinkDataset = parseResult.hicLinkDataset,
            atcLinkDataset = parseResult.atcLinkDataset,
            hierarchicalIngredientDataset = parseResult.hierarchicalIngredientDataset,
            ahfsLinkDataset = parseResult.ahfsLinkDataset,
            ahfsDescDataset = parseResult.ahfsDescDataset,
            indicationLinkDataset = parseResult.indicationLinkDataset,
            indicationDataset = parseResult.indicationDataset,
            icdSearchDataset = parseResult.icdSearchDataset,
            diseaseIdentifierDataset = parseResult.diseaseIdentifierDataset,
            medicareDataset = parseResult.medicareDataset,
            medicareReferenceDescDataset = parseResult.medicareReferenceDescDataset,
            etcDataset = parseResult.etcDataset,
            etcIdDataset = parseResult.etcIdDataset,
            uscLinkDataset = parseResult.uscLinkDataset,
            uscDescDataset = parseResult.uscDescDataset,
        )

        val transformer = new FdbDrugTransformer(spark)
        val output = transformer.transform(transformInput).collectAsList()

        assert(output.size() == 3)
        assertRow(FdbDrugOutputRow(
            code = "00002010102",
            label_name = "AMMONIUM CHLORIDE 500 MG ENS",
            brand_name = "AMMONIUM CHLORIDE",
            drug_name = "ammonium chloride",
            ingredient_name = Array("ammonium chloride"),
            strength_number = Array("500.0"),
            strength_unit_description_abbreviation = Array("mg"),
            package_size = "100.0",
            dose_form = "TABLET, DELAYED RELEASE (ENTERIC COATED)",
            package_description = "BOTTLE",
            route_of_administration = "ORAL",
            labeller_name = "ELI LILLY & CO.",
            jcode = Array("J3260"),
            jcode_desc = Array("Tobramycin sulfate inj, 80 MG"),
            hic3_classification = "R1C",
            hic3_description = "INORGANIC SALT DIURETICS",
            etc_classification = Array("00002536", "00003096"),
            etc_description = Array("Diuretic - Inorganic Salt", "Urinary Acidifier - Others"),
            usc_classification = "41190",
            usc_description = "DIURETICS, OTHER",
            ahfs_classification = Array("40040000"),
            ahfs_description = Array("ACIDIFYING AGENTS"),
            atc_code = "G04BA01",
            on_label_indication = Array("chronic heart failure", "ventricular rate control in atrial fibrillation"),
            off_label_indication = Array("paroxysmal supraventricular tachycardia"),
            icd_code = Array("I47.1", "428.0", "427.0"),
            obsolete_date = "20220413",
            previous_version_delete_date = Array("20171005"),
        ), output.get(0))

        assertRow(FdbDrugOutputRow(
            code = "00002050101",
            label_name = "NEBCIN PED 10 MG/ML VIAL",
            brand_name = "NEBCIN",
            drug_name = "tobramycin sulfate",
            ingredient_name = Array("tobramycin sulfate"),
            strength_number = Array("10.0"),
            strength_unit_description_abbreviation = Array("mg"),
            package_size = "2.0",
            dose_form = "VIAL (ML)",
            package_description = "VIAL",
            route_of_administration = "INJECTION",
            labeller_name = "ELI LILLY & CO.",
            jcode = Array("J3260"),
            jcode_desc = Array("Tobramycin sulfate inj, 80 MG"),
            hic3_classification = "W1F",
            hic3_description = "AMINOGLYCOSIDE ANTIBIOTICS",
            etc_classification = Array("00000034"),
            etc_description = Array("Aminoglycoside Antibiotic"),
            usc_classification = "15170",
            usc_description = "AMINOGLYCOSIDES",
            ahfs_classification = Array("08120200"),
            ahfs_description = Array("AMINOGLYCOSIDE ANTIBIOTICS"),
            atc_code = "J01GB01",
            on_label_indication = Array("synergy for nosocomial pneumonia due to Pseudomonas aeruginosa"),
            off_label_indication = Array(),
            icd_code = Array(),
            obsolete_date = null,
            previous_version_delete_date = Array(),
        ), output.get(1))

        assertRow(FdbDrugOutputRow(
            code = "99999099213",
            label_name = "EUA PATIENT ASSESSMENT",
            brand_name = "EUA PATIENT ASSESSMENT",
            drug_name = "EUA patient assessment",
            ingredient_name = Array("EUA patient assessment"),
            strength_number = Array("0.0"),
            strength_unit_description_abbreviation = Array(""),
            package_size = "1.0",
            dose_form = "MISCELLANEOUS",
            package_description = "BOX",
            route_of_administration = "MISCELLANEOUS",
            labeller_name = "NCPDP EMERGENCY",
            jcode = Array("J3260"),
            jcode_desc = Array("Tobramycin sulfate inj, 80 MG"),
            hic3_classification = "X6C",
            hic3_description = "DIAGNOSTIC TEST DEVICES, SUPPLIES, AND SERVICES",
            etc_classification = Array("00001207", "00005904"),
            etc_description = Array("Medical Supplies and DME - Miscellaneous Other", "Medical Supply, FDB Superset"),
            usc_classification = "40400",
            usc_description = "DIAGNOSTICS OTHERS",
            ahfs_classification = Array("94000000"),
            ahfs_description = Array("DEVICES"),
            atc_code = null,
            on_label_indication = Array("drowsy", "fatigue"),
            off_label_indication = Array(),
            icd_code = Array("R53.83", "780.79"),
            obsolete_date = "00000000",
            previous_version_delete_date = Array("20171005"),
        ), output.get(2))
    }

    private def assertRow(expected: FdbDrugOutputRow, actual: FdbDrugOutputRow): Unit = {
        assert(expected.code == actual.code)
        assert(expected.label_name == actual.label_name)
        assert(expected.brand_name == actual.brand_name)
        assert(expected.drug_name == actual.drug_name)
        assert(expected.ingredient_name.sameElements(actual.ingredient_name))
        assert(expected.strength_number.sameElements(actual.strength_number))
        assert(expected.strength_unit_description_abbreviation.sameElements(actual.strength_unit_description_abbreviation))
        assert(expected.package_size == actual.package_size)
        assert(expected.dose_form == actual.dose_form)
        assert(expected.package_description == actual.package_description)
        assert(expected.route_of_administration == actual.route_of_administration)
        assert(expected.labeller_name == actual.labeller_name)
        assert(expected.jcode.sameElements(actual.jcode))
        assert(expected.jcode_desc.sameElements(actual.jcode_desc))
        assert(expected.hic3_classification == actual.hic3_classification)
        assert(expected.hic3_description == actual.hic3_description)
        assert(expected.etc_classification.sameElements(actual.etc_classification))
        assert(expected.etc_description.sameElements(actual.etc_description))
        assert(expected.usc_classification == actual.usc_classification)
        assert(expected.usc_description == actual.usc_description)
        assert(expected.ahfs_classification.sameElements(actual.ahfs_classification))
        assert(expected.ahfs_description.sameElements(actual.ahfs_description))
        assert(expected.atc_code == actual.atc_code)
        assert(expected.on_label_indication.sameElements(actual.on_label_indication))
        assert(expected.off_label_indication.sameElements(actual.off_label_indication))
        assert(expected.icd_code.sameElements(actual.icd_code))
        assert(expected.obsolete_date == actual.obsolete_date)
        assert(expected.previous_version_delete_date.sameElements(actual.previous_version_delete_date))
    }
}
