package com.prospection.refdata.etl.pbsitem.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.pbsitem.domain.{CriteriaParameterRelationshipRow, CriteriaRow, IndicationRow, ItemAtcRelationshipRow, ItemRestrictionRelationshipRow, ItemRow, OrganisationRow, PrescribingTextRow, RestrictionPrescribingTextRelationshipRow, RestrictionRow}

class PbsItemParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("pbs-item-api/raw").getPath
        val version = "2024-06-01"

        val result = new PbsItemParser(spark, pathPrefix, version).parse()

        // assert items
        assert(result.itemDataSet.count() == 3)
        assert(result.itemDataSet.filter("code = '13565Y'").first() == new ItemRow(
            code = "13565Y",
            drug_name = "Candesartan",
            brand_name = "Candesartan Sandoz",
            restriction_flag = "R",
            manufacturer_id = "90",
            description = "candesartan cilexetil 16 mg tablet, 30",
            li_drug_name = "Candesartan",
            formulary = "F2"
        ))

        // assert restrictions
        assert(result.restrictionDataSet.count() == 2)
        assert(result.restrictionDataSet.first() == new RestrictionRow(
            res_code = "14238_14238_R",
            indication_id = "14238",
            treatment_phase = "Test phase",
            indication_description = "<h1>Listing of Pharmaceutical Benefits (NHL) - Schedule 4 part 1</h1><p>The condition must be stable for the prescriber to consider the listed maximum quantity of this medicine suitable for this patient.</p>",
            criteria_relationship = "ALL"
        ))

        // assert organisations
        assert(result.manufacturerDataSet.count() == 2)
        assert(result.manufacturerDataSet.first() == new OrganisationRow(
            manufacturer_id = "30",
            manufacturer_name = "Bayer Australia Ltd"
        ))

        // assert item-restriction-relationships
        assert(result.itemRestrictionRelationshipDataSet.count() == 3)
        assert(result.itemRestrictionRelationshipDataSet.first() == new ItemRestrictionRelationshipRow(
            code = "13521P",
            res_code = "14226_14300_R"
        ))

        // assert item-atc-relationships
        assert(result.itemAtcRelationshipDataSet.count() == 2)
        assert(result.itemAtcRelationshipDataSet.first() == new ItemAtcRelationshipRow(
            code = "13589F",
            atc_code = "C10AA07"
        ))

        // assert criteria
        assert(result.criteriaDataSet.count() == 2)
        assert(result.criteriaDataSet.first() == new CriteriaRow(
            criteria_prescribing_txt_id = "30503",
            criteria_type = "CLINICAL",
            parameter_relationship = "ANY"
        ))

        // assert restriction-prescribing-text-relationships
        assert(result.restrictionPrescribingTextRelationshipDataSet.count() == 7)
        assert(result.restrictionPrescribingTextRelationshipDataSet.first() == new RestrictionPrescribingTextRelationshipRow(
            res_code = "14226_14300_R",
            prescribing_text_id = "7902",
            criteria_position = "1"
        ))

        // assert prescribing-texts
        assert(result.prescribingTextDataSet.count() == 9)
        assert(result.prescribingTextDataSet.first() == new PrescribingTextRow(
            prescribing_text_id = "30502",
            prescribing_type = "PARAMETER",
            prescribing_txt = "The condition must be stable for the prescriber to consider the listed maximum quantity of this medicine suitable for this patient"
        ))

        // assert criteria-parameter-relationships
        assert(result.criteriaParameterRelationshipDataset.count() == 2)
        assert(result.criteriaParameterRelationshipDataset.first() == new CriteriaParameterRelationshipRow(
            criteria_prescribing_txt_id = "7904",
            prescribing_text_id = "7903",
            parameter_position = "1"
        ))

        // assert indications
        assert(result.indicationDataset.count() == 2)
        assert(result.indicationDataset.first() == new IndicationRow(
            indication_prescribing_txt_id = "32349",
            condition = "Hereditary transthyretin amyloidosis",
            episodicity = null,
            severity = null
        ))
    }
}
