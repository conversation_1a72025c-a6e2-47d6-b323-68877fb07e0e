package com.prospection.refdata.etl.ndcdispensing.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.ndcdispensing.rows.NdcDispensingRow

class NdcDispensingParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns from a TSV file to dispensing row") {
        val parser = new NdcDispensingParser(spark, getClass.getClassLoader.getResource("ndc-dispensing").getPath, "whatever")

        val dispensingDataset = parser.parse().dispensing.getDataset

        assert(dispensingDataset.count() == 1)
        assert(dispensingDataset.first() == new NdcDispensingRow(
            code = "00002-1433-61",
            brand_name = "Trulicity",
            drug_name = "Dulaglutide",
            ingredient_name = "DULAGLUTIDE",
            manufacturer_name = "Eli Lilly and Company",
            dose_form = "INJECTION, SOLUTION",
            route_of_administration = "SUBCUTANEOUS",
            package_description = "2 SYRINGE in 1 CARTON (0002-1433-61)  > .5 mL in 1 SYRINGE",
            strength_number = ".75",
            strength_unit = "mg/.5mL",
            pharmaceutical_classes = "GLP-1 Receptor Agonist [EPC],Glucagon-Like Peptide 1 [CS],Glucagon-like Peptide-1 (GLP-1) Agonists [MoA]",
            indication_description = "TRULICITY® is indicated: 1 as an adjunct to diet and exercise to improve glycemic control in adults with type 2 diabetes mellitus., 2 to reduce the risk of major adverse cardiovascular events (cardiovascular death, non-fatal myocardial infarction, or non-fatal stroke) in adults with type 2 diabetes mellitus who have established cardiovascular disease or multiple cardiovascular risk factors.",
            marketing_category = "BLA",
            product_type_name = "HUMAN PRESCRIPTION DRUG"
        ))
    }
}
