package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.jmdc.row.JmdcProcedureRow

class JmdcProcedureParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("jmdc").getPath

        val result = new JmdcProcedureParser(spark, pathPrefix).parse()

        // assert restriction
        verifyOutput(result.dataset, List(
            JmdcProcedureRow(
                code = "160017110",
                description = "direct bilirubin (DBIL)",
                kubuncode = "D007",
                procedure_cat_med = "test",
                procedure_cat_sml = "laboratory tests fee",
                procedure_cat_subclass = "biochemical test (1)",
                procedure_version = "201804"),
            JmdcProcedureRow(
                code = "120003470",
                description = "additional fee for anti-malignant tumor drug management <prescription fee>",
                kubuncode = "F400",
                procedure_cat_med = "administration",
                procedure_cat_sml = "prescription fee",
                procedure_cat_subclass = "prescription fee",
                procedure_version = "201404"),

        ))
    }
}
