package com.prospection.refdata.etl.mdvlabresult

import com.prospection.refdata.etl.common.CodingSystems.MDV_LAB_RESULT
import com.prospection.refdata.etl.{AbstractEtlTest, EtlJobExecutor}
import org.mockito.Mockito.spy

class MdvLabResultEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("mdv-lab-result").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(MDV_LAB_RESULT -> resourcePath))
        val spyEtlJob = spy(new MdvLabResultEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
