package com.prospection.refdata.etl.mdvlabresult.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.mdvlabresult.datasets.MdvLabResultDataset
import com.prospection.refdata.etl.mdvlabresult.rows.{MdvLabResultRow, OutputRow}

class MdvLabResultTransformerTest extends AbstractIntegrationTest {
    import spark.implicits._

    test("should transform to output row correctly") {
        val input = MdvLabResultTransformerInput(
            new MdvLabResultDataset(
                Seq(MdvLabResultRow(
                    code = "4B035000000000000",
                    lab_test_name_jp = "FT4",
                    lab_test_name = "Free thyroxine",
                    unit = "ng/dL",
                )).toDS()
            )
        )

        val transformer = new MdvLabResultTransformer(spark)
        val outputRows = transformer.transform(input).collect()

        assert(outputRows.length == 1)
        assert(outputRows(0) == OutputRow(
            code = "4B035000000000000",
            lab_test_name_jp = "FT4",
            lab_test_name = "Free thyroxine",
            unit = "ng/dL"
        ))
    }
}
