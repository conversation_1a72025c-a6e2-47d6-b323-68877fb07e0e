package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_DIAGNOSIS
import com.prospection.refdata.etl.{AbstractEtlTest,  EtlJobExecutor}
import org.mockito.Mockito.spy

class JmdcDiagnosisEtlJobTest extends AbstractEtlTest {
    test("should store put right place") {

        val resourcePath = getClass.getClassLoader.getResource("jmdc").getPath
        val etlJobParams = getEtlJobParams(inputPaths = Map(JMDC_DIAGNOSIS -> resourcePath))
        val spyEtlJob = spy(new JmdcDiagnosisEtlJob(spark, etlJobParams))

        mockCommon(spyEtlJob)

        EtlJobExecutor(spyEtlJob).execute()

        verifyCommon(spyEtlJob)
    }
}
