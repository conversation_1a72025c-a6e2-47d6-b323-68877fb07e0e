package com.prospection.refdata.etl.desc.parser

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.desc.rows.{DescDrugEphmraRow, DescDrugMainRow, DescDrugReceRow, DescProcedureRow}

class DescItemParserTest extends AbstractIntegrationTest {

    test("should parse all the necessary columns") {
        val pathPrefix = getClass.getClassLoader.getResource("desc").getPath

        val result = new DescItemParser(spark, pathPrefix).parse()

        // assert procedures
        assert(3 == result.descProcedure.count())
        verifyOutput(result.descProcedure, List(
            DescProcedureRow(
                code = "111012970",
                procedure_name = "妊婦時間外加算（初診）",
                description = "pregnant woman, after-hours fee (first visit)",
            ),
            DescProcedureRow(
                code = "111000370",
                procedure_name = "乳幼児加算（初診）",
                description = "infant additional fee (first visit)",
            ),
            DescProcedureRow(
                code = "111013070",
                procedure_name = "妊婦休日加算（初診）",
                description = "pregnant woman, holiday fee (first visit)",
            ),
        ))

        // assert drug_rece
        assert(5 == result.descDrugRece.count())
        verifyOutput(result.descDrugRece, List(
            DescDrugReceRow(
                code = "620000199",
                drug_usage = "4",
                generic_flag = "1",
            ),
            DescDrugReceRow(
                code = "628302301",
                drug_usage = "8",
                generic_flag = "1",
            ),
            DescDrugReceRow(
                code = "610408645",
                drug_usage = "1",
                generic_flag = "0",
            ),
            DescDrugReceRow(
                code = "610453022",
                drug_usage = "1",
                generic_flag = "1",
            ),
            DescDrugReceRow(
                code = "661110021",
                drug_usage = "6",
                generic_flag = "0",
            )
        ))

        // assert drug_main
        assert(7 == result.descDrugMain.count())
        assert(result.descDrugMain.filter("code = '620000199'").first() == new DescDrugMainRow(
            drug_name_jp = "プロポフォール",
            drug_name = "propofol",
            code = "620000199",
            description = "1% Propofol inj. [Maruishi]",
        ))

        // assert drug_ephmra
        assert(7 == result.descDrugEphmra.count())
        assert(result.descDrugEphmra.filter("code = '620000199'").first() == new DescDrugEphmraRow(
            code = "620000199",
            atc_ephmra = "N01A2"
        ))

    }
}
