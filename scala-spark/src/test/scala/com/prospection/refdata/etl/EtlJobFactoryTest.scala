package com.prospection.refdata.etl

import com.prospection.refdata.etl.common.CodingSystems._
import com.prospection.refdata.etl.common.job.EtlJobParams
import com.prospection.refdata.etl.fdbdrug.FdbDrugEtlJob
import com.prospection.refdata.etl.icd10.{IcdDiagnosisEtlJob, IcdProcedureEtlJob}
import com.prospection.refdata.etl.jmdc.{JmdcDiagnosisEtlJob, JmdcDrug<PERSON>tl<PERSON>ob, JmdcMaterialEtlJob, JmdcProcedureEtlJob}
import com.prospection.refdata.etl.mdvdiagnosis.MdvDiagnosisEtlJob
import com.prospection.refdata.etl.mdvitem.MdvItemEtlJob
import com.prospection.refdata.etl.mdvlabresult.MdvLabResultEtlJob
import com.prospection.refdata.etl.pbsauthority.PbsAuthorityEtlJob
import com.prospection.refdata.etl.pbsitem.PbsItemEtlJob
import org.scalatest.Assertions

class EtlJobFactoryTest extends AbstractIntegrationTest {

    test("Should throw an exception when there is no etl job for classification") {

        val testClassification = "test-classification"

        Assertions.intercept[RuntimeException] {
            EtlJobFactory(testClassification, spark, etlJobParams).getEtlJobExecutor
        }
    }

    test("Should return the right etl jobs for classifications") {
        val assertList = Map(
            PBS_DRUG -> classOf[PbsItemEtlJob],
            PBS_AUTHORITY -> classOf[PbsAuthorityEtlJob],
            MDV_ITEM -> classOf[MdvItemEtlJob],
            MDV_LAB_RESULT -> classOf[MdvLabResultEtlJob],
            MDV_DIAGNOSIS -> classOf[MdvDiagnosisEtlJob],
            FDB_DRUG -> classOf[FdbDrugEtlJob],
            ICD_DIAGNOSIS -> classOf[IcdDiagnosisEtlJob],
            ICD_PROCEDURE -> classOf[IcdProcedureEtlJob],
            JMDC_DIAGNOSIS -> classOf[JmdcDiagnosisEtlJob],
            JMDC_PROCEDURE -> classOf[JmdcProcedureEtlJob],
            JMDC_DRUG -> classOf[JmdcDrugEtlJob],
            JMDC_MATERIAL -> classOf[JmdcMaterialEtlJob],
        )

        assertList.foreach(etlJob => {
            val result = EtlJobFactory(etlJob._1, spark, etlJobParams).getEtlJobExecutor.etlJob
            assert(result.getClass == etlJob._2)
        })
    }
}

