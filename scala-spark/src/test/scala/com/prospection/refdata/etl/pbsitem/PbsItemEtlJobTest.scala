package com.prospection.refdata.etl.pbsitem

import com.prospection.refdata.etl.AbstractTest
import com.prospection.refdata.etl.common.CodingSystems.PBS_ITEM
import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.job.EtlJobParams
import com.prospection.refdata.etl.pbsitem.parser.PbsItemParserOutput

class PbsItemEtlJobTest extends AbstractTest {

    private final val sparkOptions = SparkOptions()
    private val resourcePath: String = getClass.getClassLoader.getResource("pbs-item-api").getPath
    private val etlJobParams = EtlJobParams(
        "2024-06-01",
        inputPaths = Map(PBS_ITEM -> s"$resourcePath/raw"),
        s"$resourcePath/output",
        s"$resourcePath/warehouse",
        s"$resourcePath/snapshots"
    )

    test("can parse raw files and transform them and produce correct snapshot") {
        val etlJob = new PbsItemEtlJob(spark, etlJobParams)
        val parseOutput = etlJob.parse()
        val transformedDf = etlJob.transform(parseOutput)

        assert(transformedDf.count() == 6)  // expecting 6 rows in the snapshot - 3 items from the previous snapshot, and 1 new item and 2 updated item

        // verify correctly transformed data (on example of one row)
        val _12402jRows = transformedDf.filter("code = '13402J'").collect()
        assert(_12402jRows.length == 1)
        val _12402j = _12402jRows.head
        assert(_12402j.getAs[String]("description") == "lisinopril 20 mg tablet, 30")
        assert(_12402j.getAs[String]("drug_name") == "Lisinopril")
        assert(_12402j.getAs[String]("brand_name") == "Lisinopril Sandoz")
        assert(_12402j.getAs[String]("indication_id") == "14238")
        assert(_12402j.getAs[String]("manufacturer_name") == "Sandoz Pty Ltd")
        assert(_12402j.getAs[String]("restriction_flag") == "R")
        assert(_12402j.getAs[String]("indication_description") == "<h1>Listing of Pharmaceutical Benefits (NHL) - Schedule 4 part 1</h1><p>The condition must be stable for the prescriber to consider the listed maximum quantity of this medicine suitable for this patient.</p>")
        assert(_12402j.getAs[String]("date_first_listed") == "20240501")

        // this item was deleted from the data so date_last_listed should be the date when it last appeared in the data
        assert(_12402j.getAs[String]("date_last_listed") == "20240501")

        // verify correctly handling updated items (description changed)
        val _13521PRows = transformedDf.filter("code = '13521P'").collect()
        assert(_13521PRows.length == 2)
        val _13521POneRow = transformedDf.filter("code = '13521P' and date_first_listed = '20240501'").collect().head
        val _13521PTwoRow = transformedDf.filter("code = '13521P' and date_first_listed = '20240601'").collect().head
        assert(_13521POneRow.getAs[String]("date_last_listed") == "20240501")
        assert(_13521POneRow.getAs[String]("description") == "rivaroxaban 10 mg tablet, 30 (to be updated)")
        assert(_13521PTwoRow.getAs[String]("date_last_listed") == "20240601")
        assert(_13521PTwoRow.getAs[String]("description") == "rivaroxaban 10 mg tablet, 30")

        // verify indication description is built correctly
        assert(_13521PTwoRow.getAs[String]("indication_description") == "Prevention of recurrent venous thromboembolism Treatment Phase: Continuing treatment Clinical criteria: * The condition must be stable for the prescriber to consider the listed maximum quantity of this medicine suitable for this patient, AND * Patient must have a history of venous thromboembolism")

        // verify correctly handling new items
        val _13565YRows = transformedDf.filter("code = '13565Y'").collect()
        assert(_13565YRows.length == 1)
        val _13565Y = _13565YRows.head
        assert(_13565Y.getAs[String]("date_first_listed") == "20240601")
        assert(_13565Y.getAs[String]("date_last_listed") == "20240601")

        // verify correctly handling unchanged items
        val _13589FRows = transformedDf.filter("code = '13589F'").collect()
        assert(_13589FRows.length == 2)
        val _13589FOneRows = transformedDf.filter("code = '13589F' and date_first_listed = '20240501'").collect().head
        val _13589FTwoRows = transformedDf.filter("code = '13589F' and date_first_listed = '20240601'").collect().head
        assert(_13589FOneRows.getAs[String]("date_first_listed") == "20240501")
        assert(_13589FOneRows.getAs[String]("treatment_phase") == null)
        assert(_13589FOneRows.getAs[String]("li_drug_name") == null)
        assert(_13589FOneRows.getAs[String]("formulary") == null)
        assert(_13589FOneRows.getAs[String]("indication_condition") == null)
        assert(_13589FOneRows.getAs[String]("indication_episodicity") == null)
        assert(_13589FOneRows.getAs[String]("indication_severity") == null)
        assert(_13589FTwoRows.getAs[String]("date_last_listed") == "20240601")
        assert(_13589FTwoRows.getAs[String]("treatment_phase") == "Test phase")
        assert(_13589FTwoRows.getAs[String]("li_drug_name") == "Rosuvastatin")
        assert(_13589FTwoRows.getAs[String]("formulary") == "F2")
        assert(_13589FTwoRows.getAs[scala.collection.Seq[String]]("indication_condition").toSeq == Seq("The condition must be stable for the prescriber to consider the listed maximum quantity of this medicine suitable for this patient."))
        assert(_13589FTwoRows.getAs[Seq[String]]("indication_episodicity") == null)
        assert(_13589FTwoRows.getAs[Seq[String]]("indication_severity") == null)

    }

    test("should correctly produce snapshot timestamp") {
        val etlJob = new PbsItemEtlJob(spark, etlJobParams)
        assert(etlJob.getSnapshotTimestamp == "202406")
    }

    test("can store data in warehouse") {
        val etlJob = new PbsItemEtlJob(spark, etlJobParams)
        val parseOutput = etlJob.parse()
        etlJob.storeInWarehouse(parseOutput)

        // verify
        val itemsPath = s"${etlJobParams.warehousePath}/items/month=202406"
        val restrictionsPath = s"${etlJobParams.warehousePath}/restrictions/month=202406"
        val organisationsPath = s"${etlJobParams.warehousePath}/organisations/month=202406"
        val itemAtcRelationshipsPath = s"${etlJobParams.warehousePath}/item-atc-relationships/month=202406"
        val ItemRestrictionRelationshipsPath = s"${etlJobParams.warehousePath}/item-restriction-relationships/month=202406"
        val criteriaPath = s"${etlJobParams.warehousePath}/criteria/month=202406"

        val writtenItems = spark.read.options(sparkOptions.toMap).csv(itemsPath).toDF()
        assert(parseOutput.asInstanceOf[PbsItemParserOutput].itemRawDataset.collect().diff(writtenItems.collect()).isEmpty)

        val writtenRestrictions = spark.read.options(sparkOptions.toMap).csv(restrictionsPath).toDF()
        assert(parseOutput.asInstanceOf[PbsItemParserOutput].restrictionRawDataset.collect().diff(writtenRestrictions.collect()).isEmpty)

        val writtenOrganisations = spark.read.options(sparkOptions.toMap).csv(organisationsPath).toDF()
        assert(parseOutput.asInstanceOf[PbsItemParserOutput].manufacturerRawDataset.collect().diff(writtenOrganisations.collect()).isEmpty)

        val writtenItemAtcRelationships = spark.read.options(sparkOptions.toMap).csv(itemAtcRelationshipsPath).toDF()
        assert(parseOutput.asInstanceOf[PbsItemParserOutput].itemAtcRelationshipRawDataset.collect().diff(writtenItemAtcRelationships.collect()).isEmpty)

        val writtenItemRestrictionRelationships = spark.read.options(sparkOptions.toMap).csv(ItemRestrictionRelationshipsPath).toDF()
        assert(parseOutput.asInstanceOf[PbsItemParserOutput].itemRestrictionRelationshipRawDataset.collect().diff(writtenItemRestrictionRelationships.collect()).isEmpty)

        val writtenCriteria = spark.read.options(sparkOptions.toMap).csv(criteriaPath).toDF()
        assert(parseOutput.asInstanceOf[PbsItemParserOutput].criteriaRawDataset.collect().diff(writtenCriteria.collect()).isEmpty)
    }

    test("can store etl output without date_first_listed and date_last_listed columns") {
        val etlJob = new PbsItemEtlJob(spark, etlJobParams)
        val parseOutput = etlJob.parse()
        val transformedDf = etlJob.transform(parseOutput)
        etlJob.storeRawItem(transformedDf)

        // verify
        val writtenRawItems = spark.read.options(sparkOptions.toMap).parquet(etlJobParams.outputPath).toDF()
        assert(!writtenRawItems.columns.contains("date_first_listed"))
        assert(!writtenRawItems.columns.contains("date_last_listed"))
    }

}
