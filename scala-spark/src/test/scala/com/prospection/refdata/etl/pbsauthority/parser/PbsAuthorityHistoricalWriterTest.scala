package com.prospection.refdata.etl.pbsauthority.parser


import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.pbsauthority.parser.writer.{PbsAuthorityHistoricalWriter, PbsAuthorityHistoricalWriterInput}
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{doNothing, spy}

class PbsAuthorityHistoricalWriterTest extends AbstractIntegrationTest {
    import spark.implicits._

    test("test should call spark write method with correct arguments") {
        val mockDs = List(("colA", "colB")).toDF("colA", "colB")
        val testVersion = "202205"
        val testPath = "s3://test/path"


        val input = PbsAuthorityHistoricalWriterInput(
            restrictionDataset = mockDs,
        )

        val spyWriter = spy(PbsAuthorityHistoricalWriter(input))
        doNothing().when(spyWriter).write(any(), any(), any())

        spyWriter.write(testPath, testVersion)

    }

}