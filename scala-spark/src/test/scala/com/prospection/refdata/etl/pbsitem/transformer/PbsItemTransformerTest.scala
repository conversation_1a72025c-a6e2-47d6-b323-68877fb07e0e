package com.prospection.refdata.etl.pbsitem.transformer

import com.prospection.refdata.etl.AbstractIntegrationTest
import com.prospection.refdata.etl.pbsitem.datasets._
import com.prospection.refdata.etl.pbsitem.domain._

class PbsItemTransformerTest extends AbstractIntegrationTest {
    import spark.implicits._

    val sampleItemRow: ItemRow = ItemRow(
        code = "10001J",
        drug_name = "rifaximin",
        brand_name = "Xifaxan",
        restriction_flag = "A",
        manufacturer_id = "NE",
        description = "rifaximin 550 mg tablet, 56",
        li_drug_name = "Rifaximin",
        formulary = "F1"
    )
    val sampleItmAtcRelationshipRow: ItemAtcRelationshipRow = ItemAtcRelationshipRow(
        code = "10001J",
        atc_code = "A07AA11"
    )
    val sampleRestrictionRow: RestrictionRow = RestrictionRow(
        res_code = "RES1",
        indication_id = "4306",
        treatment_phase = "Test phase1",
        indication_description = "Unusable restriction text",
        criteria_relationship = "ALL"
    )
    val sampleManufacturerRow: OrganisationRow = OrganisationRow(
        manufacturer_id = "NE",
        manufacturer_name = "Norgine Pty. Ltd."
    )
    val sampleItmRestrictionRelationshipRow: ItemRestrictionRelationshipRow = ItemRestrictionRelationshipRow(
        code = "10001J",
        res_code = "RES1"
    )
    val sampleCriteriaRow: CriteriaRow = CriteriaRow(
        criteria_prescribing_txt_id = "CR1",
        criteria_type = "CLINICAL",
        parameter_relationship = "ALL"
    )

    val samplePrescribingTxtRow: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "IND1",
        prescribing_type = "INDICATION",
        prescribing_txt = "This is indication text"
    )
    val samplePrescribingTxtRow2: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "CR1",
        prescribing_type = "CRITERIA",
        prescribing_txt = "Clinical Criteria: Test restriction text"
    )
    val samplePrescribingTxtRow3: PrescribingTextRow = PrescribingTextRow(
        prescribing_text_id = "CR1_1",
        prescribing_type = "PARAMETER",
        prescribing_txt = "Parameter restriction text"
    )

    val sampleRestrictionPrescribingTextRelationshipRow: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "IND1",
        criteria_position = "1"
    )
    val sampleRestrictionPrescribingTextRelationshipRow2: RestrictionPrescribingTextRelationshipRow = RestrictionPrescribingTextRelationshipRow(
        res_code = "RES1",
        prescribing_text_id = "CR1",
        criteria_position = "2"
    )

    val sampleCriteriaParameterRelationshipRow: CriteriaParameterRelationshipRow = CriteriaParameterRelationshipRow(
        criteria_prescribing_txt_id = "CR1",
        prescribing_text_id = "CR1_1",
        parameter_position = "1"
    )

    val sampleIndicationRow: IndicationRow = IndicationRow(
        indication_prescribing_txt_id = "CR1",
        condition = "Test-condition",
        episodicity = "Test-episodicity",
        severity = "Test-severity"
    )

    val sampleInput: PbsItemTransformerInput = PbsItemTransformerInput(
        items = new ItemDataset(Seq(sampleItemRow).toDS()),
        restrictions = new RestrictionDataset(Seq(sampleRestrictionRow).toDS()),
        manufacturers = new ManufacturerDataset(Seq(sampleManufacturerRow).toDS()),
        itemAtcRelationships = new ItemAtcRelationshipDataset(Seq(sampleItmAtcRelationshipRow).toDS()),
        itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(sampleItmRestrictionRelationshipRow).toDS()),
        criteria = new CriteriaDataset(Seq(sampleCriteriaRow).toDS()),
        restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(sampleRestrictionPrescribingTextRelationshipRow, sampleRestrictionPrescribingTextRelationshipRow2).toDS()),
        prescribingTexts = new PrescribingTextDataset(Seq(samplePrescribingTxtRow, samplePrescribingTxtRow2, samplePrescribingTxtRow3).toDS()),
        criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(sampleCriteriaParameterRelationshipRow).toDS()),
        indication = new IndicationDataset(Seq(sampleIndicationRow).toDS())
    )

    test("PBS Items transformation - 1 row per each dataset should be joined all together and create 1 output row") {
        val transformer = new PbsItemTransformer(spark)
        val value1 = transformer.transform(sampleInput)
        val output = value1.collectAsList()

        assert(output.size() == 1)
        assert(output.get(0) == OutputRow(
            code = "10001J",
            description = "rifaximin 550 mg tablet, 56",
            atc_code = "A07AA11",
            drug_name = "rifaximin",
            brand_name = "Xifaxan",
            restriction_flag = "A",
            manufacturer_name = "Norgine Pty. Ltd.",
            indication_id = "4306",
            indication_description = "This is indication text Treatment Phase: Test phase1 Clinical criteria: * Parameter restriction text",
            route_of_administration = null,
            li_drug_name = "Rifaximin",
            formulary = "F1",
            treatment_phase = "Test phase1",
            indication_condition = Seq("Test-condition"),
            indication_episodicity = Seq("Test-episodicity"),
            indication_severity = Seq("Test-severity")
        ))
    }

    test("PBS Items transformation - restriction flag with value S should be converted to A in the output row") {
        val transformer = new PbsItemTransformer(spark)

        val sampleItemRow2: ItemRow = ItemRow(
            code = "10001J",
            drug_name = "rifaximin",
            brand_name = "Xifaxan",
            restriction_flag = "S",
            manufacturer_id = "NE",
            description = "rifaximin 550 mg tablet, 56",
            li_drug_name = "Rifaximin",
            formulary = "F2"
        )

        val sampleInput2: PbsItemTransformerInput = PbsItemTransformerInput(
            items = new ItemDataset(Seq(sampleItemRow2).toDS()),
            restrictions = new RestrictionDataset(Seq(sampleRestrictionRow).toDS()),
            manufacturers = new ManufacturerDataset(Seq(sampleManufacturerRow).toDS()),
            itemAtcRelationships = new ItemAtcRelationshipDataset(Seq(sampleItmAtcRelationshipRow).toDS()),
            itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(sampleItmRestrictionRelationshipRow).toDS()),
            criteria = new CriteriaDataset(Seq(sampleCriteriaRow).toDS()),
            restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(sampleRestrictionPrescribingTextRelationshipRow, sampleRestrictionPrescribingTextRelationshipRow2).toDS()),
            prescribingTexts = new PrescribingTextDataset(Seq(samplePrescribingTxtRow, samplePrescribingTxtRow2, samplePrescribingTxtRow3).toDS()),
            criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(sampleCriteriaParameterRelationshipRow).toDS()),
            indication = new IndicationDataset(Seq(sampleIndicationRow).toDS())
        )
        val output = transformer.transform(sampleInput2).collectAsList()

        assert(output.size() == 1)
        assert(output.get(0) == OutputRow(
            code = "10001J",
            description = "rifaximin 550 mg tablet, 56",
            atc_code = "A07AA11",
            drug_name = "rifaximin",
            brand_name = "Xifaxan",
            restriction_flag = "A",
            manufacturer_name = "Norgine Pty. Ltd.",
            indication_id = "4306",
            indication_description = "This is indication text Treatment Phase: Test phase1 Clinical criteria: * Parameter restriction text",
            route_of_administration = null,
            li_drug_name = "Rifaximin",
            formulary = "F2",
            treatment_phase = "Test phase1",
            indication_condition = Seq("Test-condition"),
            indication_episodicity = Seq("Test-episodicity"),
            indication_severity = Seq("Test-severity")
        ))
    }

    test("PBS Items transformation - multiple rows per each dataset should be joined all together and create multiple output rows") {
        val transformer = new PbsItemTransformer(spark)

        // ITEMS
        val item1: ItemRow = ItemRow(
            code = "item1",
            drug_name = "Drug 1",
            brand_name = "Brand 1",
            restriction_flag = "A",
            manufacturer_id = "manufacturer1",
            description = "Description 1",
            li_drug_name = "Li Drug Name 1",
            formulary = "F1"
        )
        val item2: ItemRow = ItemRow(
            code = "item2",
            drug_name = "Drug 2",
            brand_name = "Brand 2",
            restriction_flag = "B",
            manufacturer_id = "manufacturer2",
            description = "Description 2",
            li_drug_name = "Li Drug Name 2",
            formulary = "F2"
        )

        // RESTRICTIONS
        val restriction1: RestrictionRow = RestrictionRow(
            res_code = "RES1",
            indication_id = "indication1",
            treatment_phase = "Test phase1",
            indication_description = "Unusable restriction text 1",
            criteria_relationship = "ALL"
        )
        val restriction2: RestrictionRow = RestrictionRow(
            res_code = "RES2",
            indication_id = "indication2",
            treatment_phase = "Test phase2",
            indication_description = "Unusable restriction text 2",
            criteria_relationship = "ALL"
        )
        val restriction3: RestrictionRow = RestrictionRow(
            res_code = "RES3",
            indication_id = "indication3",
            treatment_phase = "Test phase3",
            indication_description = "Unusable restriction text 3",
            criteria_relationship = "ALL"
        )

        // MANUFACTURERS
        val organisation1 = OrganisationRow(
            manufacturer_id = "manufacturer1",
            manufacturer_name = "Manufacturer 1"
        )
        val organisation2 = OrganisationRow(
            manufacturer_id = "manufacturer2",
            manufacturer_name = "Manufacturer 2"
        )
        val organisation3 = OrganisationRow(
            manufacturer_id = "manufacturer3",
            manufacturer_name = "Manufacturer 3"
        )

        // ITEM ATC RELATIONSHIP
        val itemAtc1: ItemAtcRelationshipRow = ItemAtcRelationshipRow(
            code = "item1",
            atc_code = "atc1"
        )
        val itemAtc2: ItemAtcRelationshipRow = ItemAtcRelationshipRow(
            code = "item2",
            atc_code = "atc3"
        )
        val itemAtc3: ItemAtcRelationshipRow = ItemAtcRelationshipRow(
            code = "item3",
            atc_code = "atc3"
        )

        // ITEM RESTRICTION RELATIONSHIP
        val itemRestriction1 = ItemRestrictionRelationshipRow(
            code = "item1",
            res_code = "RES1"
        )
        val itemRestriction2 = ItemRestrictionRelationshipRow(
            code = "item1",
            res_code = "RES2"
        )
        val itemRestriction3 = ItemRestrictionRelationshipRow(
            code = "item2",
            res_code = "RES3"
        )

        // PRESCRIBING TEXT
        val restrictionText1 = RestrictionPrescribingTextRelationshipRow(
            res_code = "RES1",
            prescribing_text_id = "TXT1",
            criteria_position = "1"
        )
        val restrictionText5 = RestrictionPrescribingTextRelationshipRow(
            res_code = "RES2",
            prescribing_text_id = "TXT2",
            criteria_position = "1"
        )
        val restrictionText6 = RestrictionPrescribingTextRelationshipRow(
            res_code = "RES3",
            prescribing_text_id = "TXT3",
            criteria_position = "1"
        )
        val prescribingText1 = PrescribingTextRow(
            prescribing_text_id = "TXT1",
            prescribing_type = "INDICATION",
            prescribing_txt = "Indication Text 1"
        )
        val prescribingText2 = PrescribingTextRow(
            prescribing_text_id = "TXT2",
            prescribing_type = "INDICATION",
            prescribing_txt = "Indication Text 2"
        )
        val prescribingText3 = PrescribingTextRow(
            prescribing_text_id = "TXT3",
            prescribing_type = "INDICATION",
            prescribing_txt = "Indication Text 3"
        )

        val output = transformer.transform(PbsItemTransformerInput(
            items = new ItemDataset(Seq(item1, item2).toDS()),
            restrictions = new RestrictionDataset(Seq(restriction1, restriction2, restriction3).toDS()),
            manufacturers = new ManufacturerDataset(Seq(organisation1, organisation2, organisation3).toDS()),
            itemAtcRelationships = new ItemAtcRelationshipDataset(Seq(itemAtc1, itemAtc2, itemAtc3).toDS()),
            itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(itemRestriction1, itemRestriction2, itemRestriction3).toDS()),
            criteria = new CriteriaDataset(Seq.empty[CriteriaRow].toDS()),
            restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(restrictionText1, restrictionText5, restrictionText6).toDS()),
            prescribingTexts = new PrescribingTextDataset(Seq(prescribingText1, prescribingText2, prescribingText3).toDS()),
            criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq.empty[CriteriaParameterRelationshipRow].toDS()),
            indication = new IndicationDataset(Seq.empty[IndicationRow].toDS())
        )).collect().sortBy(r => (r.code, r.indication_id))

        assert(output.length == 3)
        assert(output(0) == OutputRow(
            code = "item1",
            description = "Description 1",
            atc_code = "atc1",
            drug_name = "Drug 1",
            brand_name = "Brand 1",
            restriction_flag = "A",
            manufacturer_name = "Manufacturer 1",
            indication_id = "indication1",
            indication_description = "Indication Text 1 Treatment Phase: Test phase1",
            route_of_administration = null,
            li_drug_name = "Li Drug Name 1",
            formulary = "F1",
            treatment_phase = "Test phase1",
            indication_condition = null,
            indication_episodicity = null,
            indication_severity = null
        ))
        assert(output(1) == OutputRow(
            code = "item1",
            description = "Description 1",
            atc_code = "atc1",
            drug_name = "Drug 1",
            brand_name = "Brand 1",
            restriction_flag = "A",
            manufacturer_name = "Manufacturer 1",
            indication_id = "indication2",
            indication_description = "Indication Text 2 Treatment Phase: Test phase2",
            route_of_administration = null,
            li_drug_name = "Li Drug Name 1",
            formulary = "F1",
            treatment_phase = "Test phase2",
            indication_condition = null,
            indication_episodicity = null,
            indication_severity = null
        ))
        assert(output(2) == OutputRow(
            code = "item2",
            description = "Description 2",
            atc_code = "atc3",
            drug_name = "Drug 2",
            brand_name = "Brand 2",
            restriction_flag = "B",
            manufacturer_name = "Manufacturer 2",
            indication_id = "indication3",
            indication_description = "Indication Text 3 Treatment Phase: Test phase3",
            route_of_administration = null,
            li_drug_name = "Li Drug Name 2",
            formulary = "F2",
            treatment_phase = "Test phase3",
            indication_condition = null,
            indication_episodicity = null,
            indication_severity = null
        ))
    }

    test("Validate if dataset is empty or not per each dataset") {
        val testSets = Seq(
            Tuple2(sampleInput.copy(items = new ItemDataset(spark.emptyDataset[ItemRow])), "items dataset is empty"),
            Tuple2(sampleInput.copy(restrictions = new RestrictionDataset(spark.emptyDataset[RestrictionRow])), "restrictions dataset is empty"),
            Tuple2(sampleInput.copy(manufacturers = new ManufacturerDataset(spark.emptyDataset[OrganisationRow])), "manufacturers dataset is empty"),
            Tuple2(sampleInput.copy(itemAtcRelationships = new ItemAtcRelationshipDataset(spark.emptyDataset[ItemAtcRelationshipRow])), "item-atc-relationships dataset is empty"),
            Tuple2(sampleInput.copy(itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(spark.emptyDataset[ItemRestrictionRelationshipRow])), "item-restriction-relationships dataset is empty"),
        )

        val transformer = new PbsItemTransformer(spark)
        testSets.foreach(testSet => {
            val (input, expectedErrorMessage) = testSet
            assertExceptionThrownWithMessage(() => transformer.validate(input))(expectedErrorMessage)
        })
    }

    test("Validate if duplicate restriction code exists in the restriction dataset") {
        val input = sampleInput.copy(restrictions = new RestrictionDataset(Seq(
            RestrictionRow("4305", "", "", "", ""), // no dupe
            RestrictionRow("4306", "", "", "", ""), // dupe
            RestrictionRow("4306", "", "", "", ""), // dupe
            RestrictionRow("4307", "", "", "", ""), // dupe
            RestrictionRow("4307", "", "", "", "") // dupe
        ).toDS()))

        val transformer = new PbsItemTransformer(spark)

        assertExceptionThrownWithMessage(() => transformer.validate(input))("res_code in restrictions dataset has duplicates. Showing max 10 duplicate examples here [4306][4307]")
    }

    test("Validate if duplicate manufacturer ID exists in the manufacturer dataset") {
        val input = sampleInput.copy(manufacturers = new ManufacturerDataset(Seq(
            OrganisationRow(manufacturer_id = "m1", manufacturer_name = "M1"), // no dupe
            OrganisationRow(manufacturer_id = "m2", manufacturer_name = "M2"), // dupe
            OrganisationRow(manufacturer_id = "m2", manufacturer_name = "M 2"), // dupe
            OrganisationRow(manufacturer_id = "m3", manufacturer_name = "M3"), // dupe
            OrganisationRow(manufacturer_id = "m3", manufacturer_name = "M3") // dupe
        ).toDS()))

        val transformer = new PbsItemTransformer(spark)

        assertExceptionThrownWithMessage(() => transformer.validate(input))("manufacturer_id in manufacturers dataset has duplicates. Showing max 10 duplicate examples here [m2][m3]")
    }

    test("if validating non-null value columns is performed for each dataset") {
        val testDatasets = Map(
            "items" -> Seq(
                Tuple2(sampleInput.copy(items = new ItemDataset(Seq(sampleItemRow.copy(code = null)).toDS())), s"${ItemRow.Columns.Code} in items dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(items = new ItemDataset(Seq(sampleItemRow.copy(drug_name = null)).toDS())), s"${ItemRow.Columns.DrugName} in items dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(items = new ItemDataset(Seq(sampleItemRow.copy(brand_name = null)).toDS())), s"${ItemRow.Columns.BrandName} in items dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(items = new ItemDataset(Seq(sampleItemRow.copy(restriction_flag = null)).toDS())), s"${ItemRow.Columns.RestrictionFlag} in items dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(items = new ItemDataset(Seq(sampleItemRow.copy(manufacturer_id = null)).toDS())), s"${ItemRow.Columns.ManufacturerId} in items dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(items = new ItemDataset(Seq(sampleItemRow.copy(li_drug_name = null)).toDS())), s"${ItemRow.Columns.LiDrugName} in items dataset has null or empty value(s), which is not permitted."),
            ),
            "manufacturers" -> Seq(
                Tuple2(sampleInput.copy(manufacturers = new ManufacturerDataset(Seq(sampleManufacturerRow.copy(manufacturer_id = null)).toDS())), s"${OrganisationRow.Columns.ManufacturerCode} in manufacturers dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(manufacturers = new ManufacturerDataset(Seq(sampleManufacturerRow.copy(manufacturer_name = null)).toDS())), s"${OrganisationRow.Columns.ManufacturerName} in manufacturers dataset has null or empty value(s), which is not permitted."),
            ),
            "restrictions" -> Seq(
                Tuple2(sampleInput.copy(restrictions = new RestrictionDataset(Seq(sampleRestrictionRow.copy(res_code = null)).toDS())), s"${RestrictionRow.Columns.RestrictionCode} in restrictions dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(restrictions = new RestrictionDataset(Seq(sampleRestrictionRow.copy(treatment_phase = null)).toDS())), s"${RestrictionRow.Columns.TreatmentPhase} in restrictions dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(restrictions = new RestrictionDataset(Seq(sampleRestrictionRow.copy(criteria_relationship = null)).toDS())), s"${RestrictionRow.Columns.CriteriaRelationship} in restrictions dataset has null or empty value(s), which is not permitted."),
            ),
            "item-atc-relationships" -> Seq(
                Tuple2(sampleInput.copy(itemAtcRelationships = new ItemAtcRelationshipDataset(Seq(sampleItmAtcRelationshipRow.copy(code = null)).toDS())), s"${ItemAtcRelationshipRow.Columns.Code} in item-atc-relationships dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(itemAtcRelationships = new ItemAtcRelationshipDataset(Seq(sampleItmAtcRelationshipRow.copy(atc_code = null)).toDS())), s"${ItemAtcRelationshipRow.Columns.AtcCode} in item-atc-relationships dataset has null or empty value(s), which is not permitted."),
            ),
            "item-restriction-relationships" -> Seq(
                Tuple2(sampleInput.copy(itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(sampleItmRestrictionRelationshipRow.copy(code = null)).toDS())), s"${ItemRestrictionRelationshipRow.Columns.Code} in item-restriction-relationships dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(itemRestrictionRelationships = new ItemRestrictionRelationshipDataset(Seq(sampleItmRestrictionRelationshipRow.copy(res_code = null)).toDS())), s"${ItemRestrictionRelationshipRow.Columns.RestrictionCode} in item-restriction-relationships dataset has null or empty value(s), which is not permitted."),
            ),
            "criteria" -> Seq(
                Tuple2(sampleInput.copy(criteria = new CriteriaDataset(Seq(sampleCriteriaRow.copy(criteria_prescribing_txt_id = null)).toDS())), s"${CriteriaRow.Columns.CriteriaPrescriptionTextId} in criteria dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(criteria = new CriteriaDataset(Seq(sampleCriteriaRow.copy(criteria_type = null)).toDS())), s"${CriteriaRow.Columns.CriteriaType} in criteria dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(criteria = new CriteriaDataset(Seq(sampleCriteriaRow.copy(parameter_relationship = null)).toDS())), s"${CriteriaRow.Columns.ParameterRelationship} in criteria dataset has null or empty value(s), which is not permitted."),
            ),
            "criteria-parameter-relationships" -> Seq(
                Tuple2(sampleInput.copy(criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(sampleCriteriaParameterRelationshipRow.copy(criteria_prescribing_txt_id = null)).toDS())), s"${CriteriaParameterRelationshipRow.Columns.CriteriaPrescribingTextId} in criteria-parameter-relationships dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(sampleCriteriaParameterRelationshipRow.copy(prescribing_text_id = null)).toDS())), s"${CriteriaParameterRelationshipRow.Columns.ParameterPrescribingTextId} in criteria-parameter-relationships dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(criteriaParameterRelationships = new CriteriaParameterRelationshipDataset(Seq(sampleCriteriaParameterRelationshipRow.copy(parameter_position = null)).toDS())), s"${CriteriaParameterRelationshipRow.Columns.ParameterPosition} in criteria-parameter-relationships dataset has null or empty value(s), which is not permitted."),
            ),
            "restriction-prescribing-text-relationships" -> Seq(
                Tuple2(sampleInput.copy(restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(sampleRestrictionPrescribingTextRelationshipRow.copy(res_code = null)).toDS())), s"${RestrictionPrescribingTextRelationshipRow.Columns.RestrictionCode} in restriction-prescribing-text-relationships dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(sampleRestrictionPrescribingTextRelationshipRow.copy(prescribing_text_id = null)).toDS())), s"${RestrictionPrescribingTextRelationshipRow.Columns.PrescribingTextId} in restriction-prescribing-text-relationships dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(restrictionPrescribingTextRelationships = new RestrictionPrescribingTextRelationshipDataset(Seq(sampleRestrictionPrescribingTextRelationshipRow.copy(criteria_position = null)).toDS())), s"${RestrictionPrescribingTextRelationshipRow.Columns.CriteriaPosition} in restriction-prescribing-text-relationships dataset has null or empty value(s), which is not permitted."),
            ),
            "prescribing-texts" -> Seq(
                Tuple2(sampleInput.copy(prescribingTexts = new PrescribingTextDataset(Seq(samplePrescribingTxtRow.copy(prescribing_text_id = null)).toDS())), s"${PrescribingTextRow.Columns.PrescribingTextId} in prescribing-texts dataset has null or empty value(s), which is not permitted."),
                Tuple2(sampleInput.copy(prescribingTexts = new PrescribingTextDataset(Seq(samplePrescribingTxtRow.copy(prescribing_type = null)).toDS())), s"${PrescribingTextRow.Columns.PrescribingType} in prescribing-texts dataset has null or empty value(s), which is not permitted."),
            )
        )

        val transformer = new PbsItemTransformer(spark)

        testDatasets.foreach(dataset => {
            val (_, testSets) = dataset

            testSets.foreach(testSet => {
                val (input, expectedMessage) = testSet
                assertExceptionThrownWithMessage(() => transformer.validate(input))(expectedMessage)
            })
        })
    }

    private def assertExceptionThrownWithMessage(callback: () => Unit)(message: String): Unit = {
        val caught = intercept[RuntimeException] {
            callback()
        }

        assert(caught.getMessage == message)
    }
}
