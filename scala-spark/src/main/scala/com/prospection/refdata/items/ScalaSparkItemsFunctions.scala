package com.prospection.refdata.items

import com.prospection.refdata.etl.common.CommonDelimiters.PIPE
import org.apache.spark.sql.expressions.UserDefinedFunction
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types.{ArrayType, StringType}
import org.apache.spark.sql.{Column, DataFrame, Dataset, Row}

import scala.collection.mutable

object ScalaSparkItemsFunctions {
    private val OPEN_SQUARE_BRACKET = "["
    private val CLOSED_SQUARE_BRACKET = "]"

    val emptyArray: Column = lit(Array.empty[String])

    def explodeDs(ds: DataFrame, columnName: String): DataFrame = {
        ds.withColumn(columnName, explode(col(columnName)))
    }

    def toLowerCase(column: Column): Column = {
        lower(column)
    }

    def nullToEmpty(column: Column): Column = {
        coalesce(column, lit(""))
    }

    def nullToEmptyArray(column: Column): Column = {
        coalesce(column, emptyArray)
    }

    def toLowercaseArrayString(column: Column): Column = {
        transform(column, (v: Column) => lower(v))
    }

    def addItemGroupName(items: DataFrame, codeColumn: String, nameColumn: String, itemGroupKeyToName:
    mutable.Map[String, String]): DataFrame = {
        items.withColumn(nameColumn, getItemGroupNameMapperUdf(itemGroupKeyToName)(col(codeColumn)))
    }

    def filterOutNullFromArray(ds: Dataset[Row], columnName: String): Dataset[Row] = {
        ds.withColumn(columnName, array_except(ds.col(columnName), array(lit(null))))
    }

    def filterOutItemsWithoutItemGroups(ds: Dataset[Row], columnName: String): Dataset[Row] = {
        ds.filter(size(col(columnName)) > 0)
    }

    def getArrayFieldValuationFunction(column: Column, value: String, operator: String): Column = {
        operator match {
            case RuleBuilderOperators.EqualsTo => array_contains(column, value)
            case RuleBuilderOperators.IsNotEqualTo => !array_contains(column, value)
            case RuleBuilderOperators.Contains => exists(column, f => f.contains(value))
            case RuleBuilderOperators.DoesNotContain => !exists(column, f => f.contains(value))
            case RuleBuilderOperators.BeginsWith => exists(column, f => f.startsWith(value))
            case RuleBuilderOperators.DoesNotBeginWith => !exists(column, f => f.startsWith(value))
            case _ => throw new RuntimeException(s"""Unsupported rule operator $operator found""")
        }
    }

    def getItemGroupNameMapperUdf(itemGroupKeyToName: mutable.Map[String, String]): UserDefinedFunction = udf((businessKey: String) => {
        itemGroupKeyToName.get(businessKey)
    })

    def joinDs(ds1: Dataset[Row], ds2: Dataset[Row], col: String): Dataset[Row] = {
        ds1.join(ds2, Seq(col))
    }

    def leftJoinDs(ds1: Dataset[Row], ds2: Dataset[Row], col: String): Dataset[Row] = {
        ds1.join(ds2, Seq(col), "left")
    }

    def joinAllArrayColumns(ds: Dataset[Row]): Dataset[Row] = {
        ds.select(
            ds.columns.map(it =>
                if (ds.schema(it).dataType.isInstanceOf[ArrayType])
                    array_join(array(lit(OPEN_SQUARE_BRACKET), concat_ws(PIPE, col(it)), lit(CLOSED_SQUARE_BRACKET)), "").as(it)
                else
                    col(it)
            ).toIndexedSeq: _*
        )
    }

    /**
     * Convert all column with string array with '[..|..]' to array colum
     * +-------------+ convert to +----------------+
     * |subjects     |            |subjects (array)|
     * +-------------+            +----------------+
     * |[Java|Go|C++]|            |[Java, Go, C++] |
     * |[Python]     |            |[Python]        |
     * |[]           |            |[]              |
     * +-------------+            +----------------+
     */
    def splitAllColumns(ds: Dataset[Row]): Dataset[Row] = {
        val stringArrayColumns = getStringArrayColumn(ds)
        ds.select(
            ds.columns.map(it =>
                if (stringArrayColumns.contains(it)) {
                    //Remove symbol [ and ]
                    val trimColumn = trim(col(it).substr(lit(2), length(col(it)) - 2))
                    when(length(trimColumn) === 0, lit(emptyArray))
                        .otherwise(split(trimColumn, s"[$PIPE]"))
                        .as(it)
                } else
                    col(it)
            ).toIndexedSeq: _*
        )
    }

    def arrayContains(columnName: String, value: String): Column = {
        array_contains(col(columnName), value)
    }

    def addHashUid(dataset: Dataset[Row]): Dataset[Row] = {
        dataset.withColumn("uid", md5(expr("concat_ws(',',*)")))
    }

    def toArrayMap(dataset: Dataset[Row]): Array[Map[String, Any]] = {
        dataset.collect().map(r => Map(dataset.columns.zip(r.toSeq).toIndexedSeq: _*))
    }

    def isArrayTypeColumn(dataset: Dataset[Row], colName: String): Boolean = {
        dataset.schema.exists(_.name == colName) && dataset.schema(colName).dataType.isInstanceOf[ArrayType]
    }

    def isStringTypeColumn(dataset: Dataset[Row], colName: String): Boolean = {
        dataset.schema.exists(_.name == colName) && dataset.schema(colName).dataType.isInstanceOf[StringType]
    }

    private def getStringArrayColumn(dataset: Dataset[Row]): Array[String] = {
        val firstRow = dataset.first()
        dataset.columns.filter(it =>
            if (isStringTypeColumn(dataset, it)) {
                val contentOption = Option(firstRow.getAs[String](it))
                contentOption.exists(content => content.startsWith(OPEN_SQUARE_BRACKET) && content.endsWith(CLOSED_SQUARE_BRACKET))
            } else {
                false
            })
    }
}
