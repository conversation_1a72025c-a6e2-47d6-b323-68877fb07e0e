package com.prospection.refdata.etl.desc.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.desc.rows.{DescProcedureRow, RawDescProcedureRow}
import org.apache.spark.sql.functions.max
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class DescProcedureRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[DescProcedureRow](sparkSession, path, SparkOptions(delimiter = CommonDelimiters.TAB)) {

    override val colMappings: List[ColumnMapping] = List(
        ColumnMapping(RawDescProcedureRow.Columns.MedicalProcedureCode, DescProcedureRow.Columns.Code),
        ColumnMapping(RawDescProcedureRow.Columns.MedicalProcedureName, DescProcedureRow.Columns.ProcedureName),
        ColumnMapping(RawDescProcedureRow.Columns.MedicalProcedureNameEn, DescProcedureRow.Columns.Description),
        ColumnMapping(RawDescProcedureRow.Columns.VersionCode, DescProcedureRow.Columns.VersionCode)
    )

    override protected def encode(rawDs: DataFrame): Dataset[DescProcedureRow] = {
        import sparkSession.implicits._

        // Get the latest version for each MedicalProcedureCode
        val latestVersions = rawDs
                .groupBy(DescProcedureRow.Columns.Code)
                .agg(max(DescProcedureRow.Columns.VersionCode).as(DescProcedureRow.Columns.VersionCode))

        // Join with the original DataFrame to filter only the latest versions
        val filteredDs = rawDs
                .join(latestVersions, Seq(DescProcedureRow.Columns.Code, DescProcedureRow.Columns.VersionCode))
                .drop(DescProcedureRow.Columns.VersionCode)
                .distinct()

        filteredDs.as[DescProcedureRow]
    }
}
