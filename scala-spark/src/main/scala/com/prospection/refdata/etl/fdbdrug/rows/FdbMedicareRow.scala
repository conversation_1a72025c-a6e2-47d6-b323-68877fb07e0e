package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RMCRMA1_MEDICARE_MSTR file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbMedicareRow(
                             code: String,
                             mcr_ref: String,
                         )

object FdbMedicareRow {
    object Columns {
        val Ndc = StandardColumns.Code
        val McrRef = "mcr_ref"
    }
}

object RawFdbMedicareRow {
    object Columns {
        val Ndc = "_c0"
        val McrRef = "_c3"
    }
}