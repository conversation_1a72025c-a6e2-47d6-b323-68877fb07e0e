package com.prospection.refdata.etl.pbsitem.domain

// Represent a row in criteria_{date}.csv file
case class CriteriaRow(
                          criteria_prescribing_txt_id: String, // criteria_prescribing_txt_id
                          criteria_type: String, // criteria_type
                          parameter_relationship: String // parameter_relationship
                  )

object CriteriaRow {
    object Columns {
        val CriteriaPrescriptionTextId: String = PbsApiColumns.CriteriaPrescriptionTextId
        val CriteriaType: String = PbsApiColumns.CriteriaType
        val ParameterRelationship: String = PbsApiColumns.ParameterRelationship
    }
}

object RawCriteriaRow {
    object Columns {
        val CriteriaPrescriptionTextId = "criteria_prescribing_txt_id"
        val CriteriaType = "criteria_type"
        val ParameterRelationship = "parameter_relationship"
    }
}