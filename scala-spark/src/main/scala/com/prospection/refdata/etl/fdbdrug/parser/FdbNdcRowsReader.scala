package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbNdcRow, RawFdbNdcRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbNdcRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbNdcRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbNdcRow.Columns.Ndc, FdbNdcRow.Columns.Ndc),
        ColumnMapping(RawFdbNdcRow.Columns.Lblrid, FdbNdcRow.Columns.Lblrid),
        ColumnMapping(RawFdbNdcRow.Columns.GcnSeqno, FdbNdcRow.Columns.GcnSeqno),
        ColumnMapping(RawFdbNdcRow.Columns.Ln, FdbNdcRow.Columns.Ln),
        ColumnMapping(RawFdbNdcRow.Columns.Bn, FdbNdcRow.Columns.Bn),
        ColumnMapping(RawFdbNdcRow.Columns.Pd, FdbNdcRow.Columns.Pd),
        ColumnMapping(RawFdbNdcRow.Columns.Ps, FdbNdcRow.Columns.Ps),
        ColumnMapping(RawFdbNdcRow.Columns.Obsdtec, FdbNdcRow.Columns.Obsdtec),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbNdcRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbNdcRow]
    }
}
