package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in item-atc-relationships_{date}.csv file
case class ItemAtcRelationshipRow(
                      code: String, // pbs_code
                      atc_code: String // atc_code
                  )

object ItemAtcRelationshipRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val AtcCode: String = StandardColumns.AtcCode
    }
}

object RawItemAtcRelationshipRow {
    object Columns {
        val ItemCode = "pbs_code"
        val AtcCode = "atc_code"
    }
}