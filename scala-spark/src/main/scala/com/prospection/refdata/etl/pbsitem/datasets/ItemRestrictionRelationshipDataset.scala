package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.ItemRestrictionRelationshipRow
import com.prospection.refdata.etl.pbsitem.domain.ItemRestrictionRelationshipRow.Columns
import org.apache.spark.sql.Dataset

class ItemRestrictionRelationshipDataset(dataset: Dataset[ItemRestrictionRelationshipRow]) extends EtlDataset[ItemRestrictionRelationshipRow](dataset, "item-restriction-relationships") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Code,
        Columns.RestrictionCode
    )
}
