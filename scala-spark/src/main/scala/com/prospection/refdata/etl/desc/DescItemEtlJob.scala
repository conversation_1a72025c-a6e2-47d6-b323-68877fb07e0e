package com.prospection.refdata.etl.desc

import com.prospection.refdata.etl.common.CodingSystems.DESC_ITEM
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.desc.datasets.{DescDrugEphmraDataset, DescDrugMainDataset, DescDrugReceDataset, DescProcedureDataset}
import com.prospection.refdata.etl.desc.parser.{DescItemParser, DescItemParserOutput}
import com.prospection.refdata.etl.desc.transformer.{DescItemTransformer, DescItemTransformerInput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class DescItemEtlJob(val spark: SparkSession, val params: EtlJobParams) extends
        Etl<PERSON>ob(spark, params) {

    override val name: String = "DESC Item Etl Job"

    override def parse(): ParserOutput = {
        new DescItemParser(spark, getInputFilePath(DESC_ITEM)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {

        val parseResult = parseOutput.asInstanceOf[DescItemParserOutput]
        val transformInput = DescItemTransformerInput(
            new DescDrugEphmraDataset(parseResult.descDrugEphmra),
            new DescDrugMainDataset(parseResult.descDrugMain),
            new DescDrugReceDataset(parseResult.descDrugRece),
            new DescProcedureDataset(parseResult.descProcedure)
        )
        new DescItemTransformer(spark).transform(transformInput).toDF()

    }


}
