package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RATCGC0_ATC_GCNSEQNO_LINK file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbAtcLinkRow(
                            gcn_seqno: String,
                            atc_code: String,
                        )

object FdbAtcLinkRow {
    object Columns {
        val GcnSeqno = "gcn_seqno"
        val Atc = StandardColumns.AtcCode
    }
}

object RawAtcLinkRow {
    object Columns {
        val GcnSeqno = "_c0"
        val Atc = "_c1"
    }
}