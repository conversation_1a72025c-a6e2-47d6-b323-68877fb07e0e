package com.prospection.refdata.etl.desc.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.desc.parser.reader.DescDiagnosisRowsReader
import com.prospection.refdata.etl.desc.rows.DescDiagnosisRow
import org.apache.spark.sql.{Dataset, SparkSession}

class DescDiagnosisParser(
                                 sparkSession: SparkSession,
                                 pathPrefix: String,
                         ) {

    def parse(): DescDiagnosisParserOutput = {
        val descDiagnosisOutput = DescDiagnosisRowsReader(sparkSession, s"$pathPrefix/m_icd10").read()

        DescDiagnosisParserOutput(
            descDiagnosis = descDiagnosisOutput.dataset,
        )
    }
}

case class DescDiagnosisParserOutput(
                                            descDiagnosis: Dataset[DescDiagnosisRow],
                                    ) extends ParserOutput