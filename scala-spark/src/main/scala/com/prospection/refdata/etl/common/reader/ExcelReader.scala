package com.prospection.refdata.etl.common.reader

import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.types.StructType

abstract class ExcelReader[T](spark: SparkSession, path: String, options: Map[String, String], schema: StructType) extends Reader [T] {

    final def read(): ReaderOutput[T] = {
        val rawDs = spark.read
            .format("excel")
            .options(options)
            .schema(schema)
            .load(path)
            .na
            //Drop Rows with NULL Values in Any Columns
            .drop("all")

        toReaderOutput(rawDs)
    }
}