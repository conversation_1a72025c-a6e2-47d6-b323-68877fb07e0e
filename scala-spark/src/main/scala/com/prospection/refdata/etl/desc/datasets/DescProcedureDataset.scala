package com.prospection.refdata.etl.desc.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.desc.rows.DescProcedureRow
import com.prospection.refdata.etl.desc.rows.DescProcedureRow.Columns
import org.apache.spark.sql.Dataset

class DescProcedureDataset(dataset: Dataset[DescProcedureRow]) extends EtlDataset[DescProcedureRow](dataset, "procedure") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Code,
        Columns.ProcedureName,
        Columns.Description
    )
}
