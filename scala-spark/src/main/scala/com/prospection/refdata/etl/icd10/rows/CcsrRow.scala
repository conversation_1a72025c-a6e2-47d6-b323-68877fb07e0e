package com.prospection.refdata.etl.icd10.rows

import com.prospection.refdata.etl.common.StandardColumns

case class CcsrRow(
                        code: String,
                        ccsr_category_1: String,
                        ccsr_category_1_description: String,
                        ccsr_category_2: String,
                        ccsr_category_2_description: String,
                        ccsr_category_3: String,
                        ccsr_category_3_description: String,
                        ccsr_category_4: String,
                        ccsr_category_4_description: String,
                        ccsr_category_5: String,
                        ccsr_category_5_description: String,
                        ccsr_category_6: String,
                        ccsr_category_6_description: String,
                    )

object CcsrRow {
    object Columns {
        val IcdCode = StandardColumns.Code
        val CcsrCategory1 = "ccsr_category_1"
        val CcsrCategory1Description = "ccsr_category_1_description"
        val CcsrCategory2 = "ccsr_category_2"
        val CcsrCategory2Description = "ccsr_category_2_description"
        val CcsrCategory3 = "ccsr_category_3"
        val CcsrCategory3Description = "ccsr_category_3_description"
        val CcsrCategory4 = "ccsr_category_4"
        val CcsrCategory4Description = "ccsr_category_4_description"
        val CcsrCategory5 = "ccsr_category_5"
        val CcsrCategory5Description = "ccsr_category_5_description"
        val CcsrCategory6 = "ccsr_category_6"
        val CcsrCategory6Description = "ccsr_category_6_description"
    }
}

object RawDxCcsrRow {
    object Columns {
        val Icd10CMCode = "'ICD-10-CM CODE'"
        val CcsrCategory1 = "'CCSR CATEGORY 1'"
        val CcsrCategory1Desc = "'CCSR CATEGORY 1 DESCRIPTION'"
        val CcsrCategory2 = "'CCSR CATEGORY 2'"
        val CcsrCategory2Desc = "'CCSR CATEGORY 2 DESCRIPTION'"
        val CcsrCategory3 = "'CCSR CATEGORY 3'"
        val CcsrCategory3Desc = "'CCSR CATEGORY 3 DESCRIPTION'"
        val CcsrCategory4 = "'CCSR CATEGORY 4'"
        val CcsrCategory4Desc = "'CCSR CATEGORY 4 DESCRIPTION'"
        val CcsrCategory5 = "'CCSR CATEGORY 5'"
        val CcsrCategory5Desc = "'CCSR CATEGORY 5 DESCRIPTION'"
        val CcsrCategory6 = "'CCSR CATEGORY 6'"
        val CcsrCategory6Desc = "'CCSR CATEGORY 6 DESCRIPTION'"
    }
}

object RawPrCcsrRow {
    object Columns {
        val Icd10PCSCode = "'ICD-10-PCS'"
        val Icd10PcsDescription = "'ICD-10-PCS DESCRIPTION'"
        val PrCcsr = "'PRCCSR'"
        val PrCcsrDescription = "'PRCCSR DESCRIPTION'"
        val ClinicalDomain = "'CLINICAL DOMAIN'"
    }
}