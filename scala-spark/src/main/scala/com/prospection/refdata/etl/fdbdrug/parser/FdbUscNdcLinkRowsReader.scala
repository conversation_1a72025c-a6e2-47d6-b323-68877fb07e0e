package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbUscNdcLinkRow, RawFdbUscNdcLinkRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbUscNdcLinkRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbUscNdcLinkRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbUscNdcLinkRow.Columns.Usc, FdbUscNdcLinkRow.Columns.Usc),
        ColumnMapping(RawFdbUscNdcLinkRow.Columns.Ndc, FdbUscNdcLinkRow.Columns.Ndc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbUscNdcLinkRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbUscNdcLinkRow]
    }
}
