package com.prospection.refdata.etl.desc.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.desc.rows.DescDrugEphmraRow
import com.prospection.refdata.etl.desc.rows.DescDrugEphmraRow.Columns
import org.apache.spark.sql.Dataset

class DescDrugEphmraDataset(dataset: Dataset[DescDrugEphmraRow]) extends EtlDataset[DescDrugEphmraRow](dataset, "drug_ephmra_atc") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Code,
        Columns.AtcEphmra,
    )
}
