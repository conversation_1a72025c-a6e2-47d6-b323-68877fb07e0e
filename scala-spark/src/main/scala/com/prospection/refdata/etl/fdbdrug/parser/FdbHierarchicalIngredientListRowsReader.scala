package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbHierarchicalIngredientListRow, RawFdbHierarchicalIngredientListRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbHierarchicalIngredientListRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbHierarchicalIngredientListRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbHierarchicalIngredientListRow.Columns.Gnn60, FdbHierarchicalIngredientListRow.Columns.Gnn60),
        ColumnMapping(RawFdbHierarchicalIngredientListRow.Columns.HiclSeqno, FdbHierarchicalIngredientListRow.Columns.HiclSeqno),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbHierarchicalIngredientListRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbHierarchicalIngredientListRow]
    }
}
