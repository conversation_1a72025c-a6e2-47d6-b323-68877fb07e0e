package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in organisations_{date}.csv file
case class OrganisationRow(
                             manufacturer_id: String, // organisation_id
                             manufacturer_name: String, // name
                         )

object OrganisationRow {
    object Columns {
        val ManufacturerCode: String = StandardColumns.ManufacturerId
        val ManufacturerName: String = StandardColumns.ManufacturerName
    }
}

object RawOrganisationRow {
    object Columns {
        val OrganisationId = "organisation_id"
        val Name = "name"
    }
}