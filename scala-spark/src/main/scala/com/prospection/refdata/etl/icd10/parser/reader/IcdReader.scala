package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.common.reader.{Reader, ReaderOutput}
import org.apache.spark.sql.functions.{col, lit, substring}
import org.apache.spark.sql.{SparkSession, functions}

abstract class IcdReader[T](
                               spark: SparkSession,
                               path: String,
                               options: SparkOptions = SparkOptions(header = false, delimiter = CommonDelimiters.BREAK_LINE)
                           ) extends Reader[T] {

    protected val tableStructs: List[IcdOrderStruct]

    def read(): ReaderOutput[T] = {
        val rawDs = spark.read
            .options(options.toMap)
            .csv(path)

        val processedRawDs = rawDs.select(
            tableStructs.map(f => {
                functions.trim(lit(substring(col("_c0"), f.position, f.length))).as(f.colName)
            }): _*
        )

        toReaderOutput(processedRawDs)
    }
}

case class IcdOrderStruct(
                                 colName: String,
                                 position: Int,
                                 length: Int,
                             )