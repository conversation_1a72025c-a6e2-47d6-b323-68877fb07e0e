package com.prospection.refdata.etl.common.reader

import com.prospection.refdata.etl.common.SparkOptions
import org.apache.spark.sql.{SparkSession}

abstract class CsvReader[T](spark: SparkSession, path: String, sparkOptions: SparkOptions) extends Reader[T] {

    final def read(): ReaderOutput[T] = {
        val rawDs = spark.read
            .options(sparkOptions.toMap)
            .csv(path)
        toReaderOutput(rawDs)
    }
}