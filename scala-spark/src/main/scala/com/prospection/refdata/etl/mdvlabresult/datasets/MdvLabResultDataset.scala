package com.prospection.refdata.etl.mdvlabresult.datasets

import com.prospection.refdata.etl.common.{EtlDataset, StandardColumns}
import com.prospection.refdata.etl.mdvlabresult.rows.MdvLabResultRow
import org.apache.spark.sql.Dataset

class MdvLabResultDataset(dataset: Dataset[MdvLabResultRow]) extends EtlDataset[MdvLabResultRow](dataset, "mdvLabResults") {
    override def getNonNullableColumns: Set[String] = Set(
        StandardColumns.Code,
    )
}
