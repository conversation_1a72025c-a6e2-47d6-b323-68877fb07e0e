package com.prospection.refdata.etl.desc.rows

import com.prospection.refdata.etl.common.StandardColumns

case class DescDrugReceRow(
                                  code: String,
                                  drug_usage: String,
                                  generic_flag: String
                          )

object DescDrugReceRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val DrugUsage: String = StandardColumns.DrugUsage
        val GenericFlag: String = StandardColumns.GenericFlag
        val VersionCode = "version_code"
    }
}

object RawDescDrugReceRow {
    object Columns {
        val DrugCode = "drug_code"
        val FormulationCode = "formulation_code"
        val GenericCode = "generic_code"
        val VersionCode = "version_code"
    }
}