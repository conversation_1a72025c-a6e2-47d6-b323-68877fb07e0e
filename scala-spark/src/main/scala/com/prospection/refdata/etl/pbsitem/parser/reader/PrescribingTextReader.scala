package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{PrescribingTextRow, RawPrescribingTextRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class PrescribingTextReader(sparkSession: SparkSession, path: String) extends CsvReader[PrescribingTextRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawPrescribingTextRow.Columns.PrescribingTextId, PrescribingTextRow.Columns.PrescribingTextId),
        ColumnMapping(RawPrescribingTextRow.Columns.PrescribingType, PrescribingTextRow.Columns.PrescribingType),
        ColumnMapping(RawPrescribingTextRow.Columns.PrescribingText, PrescribingTextRow.Columns.PrescribingText)
    )

    override protected def encode(rawDs: DataFrame): Dataset[PrescribingTextRow] = {
        import sparkSession.implicits._
        rawDs.as[PrescribingTextRow]
    }

}
