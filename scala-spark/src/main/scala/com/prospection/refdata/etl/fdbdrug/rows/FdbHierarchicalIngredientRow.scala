package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RHIC3D3_HIC_THERAP_CLASS_DESC file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbHierarchicalIngredientRow(
                                           hic3_seqn: String,
                                           hic3_classification: String,
                                           hic3_description: String,
                                       )

object FdbHierarchicalIngredientRow {
    object Columns {
        val Hic3Seqn = "hic3_seqn"
        val Hic3 = StandardColumns.Hic3Classification
        val Hic3Desc = StandardColumns.Hic3Description
    }
}

object RawFdbHierarchicalIngredientRow {
    object Columns {
        val Hic3Seqn = "_c0"
        val Hic3 = "_c1"
        val Hic3Desc = "_c2"
    }
}