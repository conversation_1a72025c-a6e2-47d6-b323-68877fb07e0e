package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbIcdSearchRow, RawFdbIcdSearchRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbIcdSearchRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbIcdSearchRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbIcdSearchRow.Columns.RelatedDxid, FdbIcdSearchRow.Columns.RelatedDxid),
        ColumnMapping(RawFdbIcdSearchRow.Columns.SearchIcdCd, FdbIcdSearchRow.Columns.SearchIcdCd),
        ColumnMapping(RawFdbIcdSearchRow.Columns.FmlNavCode, FdbIcdSearchRow.Columns.FmlNavCode),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbIcdSearchRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbIcdSearchRow]
    }
}
