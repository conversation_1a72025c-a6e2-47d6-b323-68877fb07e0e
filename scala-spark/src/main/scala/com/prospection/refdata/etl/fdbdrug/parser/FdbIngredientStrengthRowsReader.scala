package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbIngredientStrengthRow, RawIngredientStrengthRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbIngredientStrengthRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbIngredientStrengthRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawIngredientStrengthRow.Columns.GcnSeqno, FdbIngredientStrengthRow.Columns.GcnSeqno),
        ColumnMapping(RawIngredientStrengthRow.Columns.Strength, FdbIngredientStrengthRow.Columns.Strength),
        ColumnMapping(RawIngredientStrengthRow.Columns.StrengthUomId, FdbIngredientStrengthRow.Columns.StrengthUomId),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbIngredientStrengthRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbIngredientStrengthRow]
    }
}
