package com.prospection.refdata.etl.desc.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.desc.rows.DescDrugReceRow
import com.prospection.refdata.etl.desc.rows.DescDrugReceRow.Columns
import org.apache.spark.sql.Dataset

class DescDrugReceDataset(dataset: Dataset[DescDrugReceRow]) extends EtlDataset[DescDrugReceRow](dataset, "drug_rece") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Code,
        Columns.DrugUsage,
        Columns.GenericFlag,
    )
}
