package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RFMLDX0_DXID file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbDiseaseIdentifierRow(
                                      dxid: String,
                                      dxid_desc100: String,
                                  )

object FdbDiseaseIdentifierRow {
    object Columns {
        val Dxid = "dxid"
        val DxidDesc100 = "dxid_desc100"
    }
}

object RawFdbDiseaseIdentifierRow {
    object Columns {
        val Dxid = "_c0"
        val DxidDesc100 = "_c2" //DXID_DESC100
    }
}