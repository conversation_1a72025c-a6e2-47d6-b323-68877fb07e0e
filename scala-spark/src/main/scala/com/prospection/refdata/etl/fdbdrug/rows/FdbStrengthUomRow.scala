package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RSTRUOM0_STRENGTH_UOM file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbStrengthUomRow(
                                strength_unit_description_abbreviation: String,
                                uom_id: String,
                            )

object FdbStrengthUomRow {
    object Columns {
        val UomId = "uom_id"
        val UomPreferredDesc = StandardColumns.StrengthUnitDescriptionAbbreviation
    }
}

object RawFdbStrengthUomRow {
    object Columns {
        val UomId = "_c0"
        val UomPreferredDesc = "_c2"
    }
}