package com.prospection.refdata.etl.common.job

import com.prospection.refdata.etl.common.{ParserOutput, SparkOptions}
import org.apache.spark.sql.{DataFrame, Dataset, SaveMode, SparkSession}

import java.time.LocalDate
import java.time.format.{DateTimeFormatter, DateTimeFormatterBuilder, SignStyle}
import java.time.temporal.ChronoField

abstract class EtlJob(spark: SparkSession, params: EtlJobParams) {

    protected final val sparkOptions = SparkOptions()
    val name: String

    def parse(): ParserOutput

    def transform(parseOutput: ParserOutput): DataFrame

    def getVersion: LocalDate = LocalDate.parse(params.version, DateTimeFormatter.BASIC_ISO_DATE)

    def storeRawItem(df: DataFrame): Unit = {
        writeParquet(df, params.outputPath)
    }

    def storeSnapshot(df: DataFrame): Unit = {
        writeParquet(df, getSnapshotTimestampPath)
    }

    def readSnapshotData(): DataFrame = {
        readParquet(getSnapshotTimestampPath)
    }

    protected def writeParquet[T](ds: Dataset[T], outputPath: String): Unit = {
        ds.write.options(sparkOptions.toMap).mode(SaveMode.Overwrite).parquet(outputPath)
    }

    private def readParquet(path: String): DataFrame = {
        spark.read.options(sparkOptions.toMap).parquet(path).toDF()
    }

    protected def getInputFilePath(key: String): String = {
        params.inputPaths.getOrElse(key,
            throw new RuntimeException(s"$key not found in input paths: ${params.inputPaths.toString()}"))
    }

    protected def getSnapshotTimestamp: String = params.version

    protected def getMonthVersion(date: LocalDate): String = {
        date.format(
            new DateTimeFormatterBuilder()
                .appendValue(ChronoField.YEAR, 4, 10, SignStyle.EXCEEDS_PAD)
                .appendValue(ChronoField.MONTH_OF_YEAR, 2)
                .toFormatter
        )
    }



    private def getSnapshotTimestampPath = s"${params.snapshotPath}/timestamp=$getSnapshotTimestamp"
}