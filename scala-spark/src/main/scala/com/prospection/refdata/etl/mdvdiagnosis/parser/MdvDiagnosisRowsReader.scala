package com.prospection.refdata.etl.mdvdiagnosis.parser

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions, StandardColumns}
import com.prospection.refdata.etl.mdvdiagnosis.rows.{MdvDiagnosisRow, RawMdvDiagnosisRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class MdvDiagnosisRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[MdvDiagnosisRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.TAB)
) {

    override val colMappings = List(
        ColumnMapping(RawMdvDiagnosisRow.Columns.DiseaseCode, StandardColumns.Code),
        ColumnMapping(RawMdvDiagnosisRow.Columns.Icd10Code, MdvDiagnosisRow.Columns.Icd10Code),
        ColumnMapping(RawMdvDiagnosisRow.Columns.DiseaseName, MdvDiagnosisRow.Columns.DiagnosisNameJp),
        ColumnMapping(RawMdvDiagnosisRow.Columns.DiseaseNameEng, MdvDiagnosisRow.Columns.DiagnosisName),
    )

    override protected def encode(rawDs: DataFrame): Dataset[MdvDiagnosisRow] = {
        import sparkSession.implicits._
        rawDs.as[MdvDiagnosisRow]
    }
}
