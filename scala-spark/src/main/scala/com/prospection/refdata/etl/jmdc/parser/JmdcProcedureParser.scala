package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.jmdc.parser.reader.JmdcProcedureRowsReader
import com.prospection.refdata.etl.jmdc.row.JmdcProcedureRow
import org.apache.spark.sql.{Dataset, SparkSession}

class JmdcProcedureParser(
                             sparkSession: SparkSession,
                             pathPrefix: String,
                         ) {

    def parse(): JmdcProcedureParserOutput = {

        val jmdcProcedureDataSet = JmdcProcedureRowsReader(sparkSession, s"$pathPrefix/transformed/split/procedure_master").read()

        JmdcProcedureParserOutput(
            jmdcProcedureDataSet.dataset
        )
    }
}

case class JmdcProcedureParserOutput(dataset: Dataset[JmdcProcedureRow]) extends ParserOutput