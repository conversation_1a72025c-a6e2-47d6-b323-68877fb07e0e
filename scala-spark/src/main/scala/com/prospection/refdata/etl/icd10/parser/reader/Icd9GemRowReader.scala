package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.icd10.rows.{IcdGemRow, RawIcdGemRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class Icd9GemRowsReader(sparkSession: SparkSession, path: String) extends IcdReader[IcdGemRow](
    sparkSession,
    path,
) {

    override val tableStructs = List(
        IcdOrderStruct(colName = RawIcdGemRow.Columns.Icd9Code, position =  1, length = 5),
        IcdOrderStruct(colName = RawIcdGemRow.Columns.IcdCode, position =  7, length = 7),
        IcdOrderStruct(colName = RawIcdGemRow.Columns.ApproximateFlag, position =  15, length = 1),
        IcdOrderStruct(colName = RawIcdGemRow.Columns.NoMapFlag, position =  16, length = 1),
        IcdOrderStruct(colName = RawIcdGemRow.Columns.CombinationFlag, position =  17, length = 1),
        IcdOrderStruct(colName = RawIcdGemRow.Columns.ScenarioFlag, position =  18, length = 1),
        IcdOrderStruct(colName = RawIcdGemRow.Columns.ChoiceListFlag, position =  19, length = 1),
    )
    override val colMappings = List(
        ColumnMapping(RawIcdGemRow.Columns.IcdCode, IcdGemRow.Columns.IcdCode),
        ColumnMapping(RawIcdGemRow.Columns.Icd9Code, IcdGemRow.Columns.Icd9Code),
    )

    override protected def encode(rawDs: DataFrame): Dataset[IcdGemRow] = {
        import sparkSession.implicits._
        rawDs.as[IcdGemRow]
    }
}