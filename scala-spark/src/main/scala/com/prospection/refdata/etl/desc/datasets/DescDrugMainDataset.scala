package com.prospection.refdata.etl.desc.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.desc.rows.DescDrugMainRow
import com.prospection.refdata.etl.desc.rows.DescDrugMainRow.Columns
import org.apache.spark.sql.Dataset

class DescDrugMainDataset(dataset: Dataset[DescDrugMainRow]) extends EtlDataset[DescDrugMainRow](dataset, "drug_main") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.DrugNameJp,
        Columns.DrugName,
        Columns.Code,
        Columns.Description,
    )
}
