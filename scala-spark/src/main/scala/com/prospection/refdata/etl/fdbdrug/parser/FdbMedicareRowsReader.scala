package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbHicLinkRow, FdbMedicareRow, RawFdbHicLinkRow, RawFdbMedicareRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbMedicareRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbMedicareRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbMedicareRow.Columns.Ndc, FdbMedicareRow.Columns.Ndc),
        ColumnMapping(RawFdbMedicareRow.Columns.McrRef, FdbMedicareRow.Columns.McrRef),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbMedicareRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbMedicareRow]
    }
}
