package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbDiseaseIdentifierRow, RawFdbDiseaseIdentifierRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbDiseaseIdentifierRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbDiseaseIdentifierRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbDiseaseIdentifierRow.Columns.Dxid, FdbDiseaseIdentifierRow.Columns.Dxid),
        ColumnMapping(RawFdbDiseaseIdentifierRow.Columns.DxidDesc100, FdbDiseaseIdentifierRow.Columns.DxidDesc100),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbDiseaseIdentifierRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbDiseaseIdentifierRow]
    }
}
