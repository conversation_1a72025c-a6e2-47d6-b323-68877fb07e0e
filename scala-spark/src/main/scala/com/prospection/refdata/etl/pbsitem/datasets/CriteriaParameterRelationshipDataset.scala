package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.CriteriaParameterRelationshipRow
import com.prospection.refdata.etl.pbsitem.domain.CriteriaParameterRelationshipRow.Columns
import org.apache.spark.sql.Dataset

class CriteriaParameterRelationshipDataset(dataset: Dataset[CriteriaParameterRelationshipRow]) extends EtlDataset[CriteriaParameterRelationshipRow](dataset, "criteria-parameter-relationships") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.CriteriaPrescribingTextId,
        Columns.ParameterPrescribingTextId,
        Columns.ParameterPosition
    )
}
