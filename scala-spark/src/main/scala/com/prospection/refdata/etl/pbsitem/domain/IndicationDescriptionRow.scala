package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns

case class IndicationDescriptionRow(
                                   code: String, // pbs_code
                                   indication_id: String, // indication_id
                                   indication_description: String, // indication_description
                                   treatment_phase: String, // treatment_phase
                                   indication_condition: Seq[String], // condition
                                   indication_episodicity: Seq[String], // episodicity
                                   indication_severity: Seq[String] // severity
                                   )
object IndicationDescriptionRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val IndicationId: String = StandardColumns.IndicationId
        val IndicationDescription: String = StandardColumns.IndicationDescription
        val TreatmentPhase: String = PbsApiColumns.TreatmentPhase
        val Condition: String = StandardColumns.IndicationCondition
        val Episodicity: String = StandardColumns.IndicationEpisodicity
        val Severity: String = StandardColumns.IndicationSeverity
    }
}
