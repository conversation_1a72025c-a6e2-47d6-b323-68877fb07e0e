package com.prospection.refdata.etl.desc.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.desc.rows.{DescDiagnosisRow, RawDescDiagnosisRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class DescDiagnosisRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[DescDiagnosisRow](sparkSession, path, SparkOptions(delimiter = CommonDelimiters.TAB)) {

    override val colMappings = List(
        ColumnMapping(RawDescDiagnosisRow.Columns.Icd10Code, DescDiagnosisRow.Columns.Icd10Code),
        ColumnMapping(RawDescDiagnosisRow.Columns.DiseaseCode, DescDiagnosisRow.Columns.Code),
        ColumnMapping(RawDescDiagnosisRow.Columns.Icd10Name, DescDiagnosisRow.Columns.DiagnosisNameJapan),
        ColumnMapping(RawDescDiagnosisRow.Columns.icd10NameEn, DescDiagnosisRow.Columns.DiagnosisName)
    )

    override protected def encode(rawDs: DataFrame): Dataset[DescDiagnosisRow] = {
        import sparkSession.implicits._
        rawDs.as[DescDiagnosisRow]
    }
}
