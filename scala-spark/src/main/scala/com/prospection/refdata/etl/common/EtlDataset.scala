package com.prospection.refdata.etl.common

import org.apache.spark.sql.Dataset

// a wrapper for spark dataset + additional information we need for processing and validation
abstract class EtlDataset[T](d: Dataset[T], alias: String) {
    private val dataset = d.alias(alias)

    // Validation to check
    // - if the columns do exist or not
    // - if null value exists or not
    // will be performed on these columns
    def getNonNullableColumns: Set[String]

    // Validation to check if duplicate value exists or not will be performed on this column
    def getUniqueColumn: Option[String] = None

    def getDataset: Dataset[T] = dataset
}
