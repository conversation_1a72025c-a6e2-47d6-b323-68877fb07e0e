package com.prospection.refdata.etl.fdbdrug.transformer

import com.prospection.refdata.etl.fdbdrug.rows._
import org.apache.spark.sql.Dataset

case class FdbDrugTransformerInput(
                                  ndcDataset: Dataset[FdbNdcRow],
                                  ndcDescDataset: Dataset[FdbNdcDescRow],
                                  ndcDeletionReasonDataset: Dataset[FdbNdcDeletionReasonRow],
                                  doseDescDataset: Dataset[FdbDoseDescRow],
                                  genericCodeNumberDataset: Dataset[FdbGenericCodeNumberRow],
                                  ingredientStrengthDataset: Dataset[FdbIngredientStrengthRow],
                                  strengthUomDataset: Dataset[FdbStrengthUomRow],
                                  routeDescDataset: Dataset[FdbRouteDescRow],
                                  hierarchicalIngredientListDataset: Dataset[FdbHierarchicalIngredientListRow],
                                  hicDescDataset: Dataset[FdbHicDescRow],
                                  hicLinkDataset: Dataset[FdbHicLinkRow],
                                  atcLinkDataset: Dataset[FdbAtcLinkRow],
                                  hierarchicalIngredientDataset: Dataset[FdbHierarchicalIngredientRow],
                                  ahfsLinkDataset: Dataset[FdbAhfsLinkRow],
                                  ahfsDescDataset: Dataset[FdbAhfsDescRow],
                                  indicationLinkDataset: Dataset[FdbIndicationLinkRow],
                                  indicationDataset: Dataset[FdbIndicationRow],
                                  icdSearchDataset: Dataset[FdbIcdSearchRow],
                                  diseaseIdentifierDataset: Dataset[FdbDiseaseIdentifierRow],
                                  medicareDataset: Dataset[FdbMedicareRow],
                                  medicareReferenceDescDataset: Dataset[FdbMedicareReferenceDescRow],
                                  etcDataset: Dataset[FdbEtcRow],
                                  etcIdDataset: Dataset[FdbEtcIdRow],
                                  uscLinkDataset: Dataset[FdbUscNdcLinkRow],
                                  uscDescDataset: Dataset[FdbUscDescRow],
                              )
