package com.prospection.refdata.etl.icd10.rows

import com.prospection.refdata.etl.common.StandardColumns

case class Icd10OrderRow(
                           code: String,
                           short_description: String,
                           description: String,
                          )

object Icd10OrderRow {
    object Columns {
        val IcdCode = StandardColumns.Code
        val ShortDescription = StandardColumns.ShortDescription
        val LongDescription = StandardColumns.Description
    }
}

object RawIcd10OrderRow{
    object Columns {
        val OrderNumber = "order_number"
        val IcdCode = "icd_code"
        val HipaaCovered = "hipaa_covered"
        val ShortDescription = "short_description"
        val LongDescription = "long_description"
    }
}