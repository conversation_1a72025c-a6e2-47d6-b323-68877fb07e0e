package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{ItemAtcRelationshipRow, RawItemAtcRelationshipRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class ItemAtcRelationshipReader(sparkSession: SparkSession, path: String) extends CsvReader[ItemAtcRelationshipRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawItemAtcRelationshipRow.Columns.ItemCode, ItemAtcRelationshipRow.Columns.Code),
        ColumnMapping(RawItemAtcRelationshipRow.Columns.AtcCode, ItemAtcRelationshipRow.Columns.AtcCode)
    )

    override protected def encode(rawDs: DataFrame): Dataset[ItemAtcRelationshipRow] = {
        import sparkSession.implicits._
        rawDs.as[ItemAtcRelationshipRow]
    }

}
