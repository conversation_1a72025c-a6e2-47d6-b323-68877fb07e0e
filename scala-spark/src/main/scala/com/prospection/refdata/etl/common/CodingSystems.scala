package com.prospection.refdata.etl.common

object CodingSystems {
    val PBS_DRUG = "PBS Drug"
    val PBS_ITEM = "PBS_Drug_API"
    val PBS_AUTHORITY = "PBS Authority"

    val FDB_DRUG = "FDB Drug"
    val ICD_DIAGNOSIS = "ICD Diagnosis"
    val ICD_PROCEDURE = "ICD Procedure"

    val MDV_ITEM = "MDV Item"
    val MDV_LAB_RESULT = "MDV Lab Result"
    val MDV_DIAGNOSIS = "MDV Diagnosis"

    val JMDC_DIAGNOSIS = "JMDC Diagnosis"
    val JMDC_PROCEDURE = "JMDC Procedure"
    val JMDC_DRUG = "JMDC Drug"
    val JMDC_MATERIAL = "JMDC Material"

    val DESC_ITEM = "DESC Item"
    val DESC_DIAGNOSIS = "DESC Diagnosis"
}