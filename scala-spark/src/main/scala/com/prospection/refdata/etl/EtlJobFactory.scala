package com.prospection.refdata.etl

import com.prospection.refdata.etl.common.CodingSystems._
import com.prospection.refdata.etl.common.job.EtlJobParams
import com.prospection.refdata.etl.desc.{DescDiagnosisEtlJob, DescItemEtlJob}
import com.prospection.refdata.etl.fdbdrug.FdbDrugEtlJob
import com.prospection.refdata.etl.icd10.{IcdDiagnosisEtlJob, IcdProcedureEtlJob}
import com.prospection.refdata.etl.jmdc.{JmdcDiagnosisEtlJob, JmdcDrugEtlJob, JmdcMaterialEtlJob, JmdcProcedureEtlJob}
import com.prospection.refdata.etl.mdvdiagnosis.MdvDiagnosisEtlJob
import com.prospection.refdata.etl.mdvitem.MdvItemEtlJob
import com.prospection.refdata.etl.mdvlabresult.MdvLabResultEtlJob
import com.prospection.refdata.etl.pbsauthority.PbsAuthorityEtlJob
import com.prospection.refdata.etl.pbsitem.PbsItemEtlJob
import org.apache.spark.sql.SparkSession

case class EtlJobFactory(classification: String, sparkSession: SparkSession, params: EtlJobParams) {
    def getEtlJobExecutor: EtlJobExecutor = {
        val etlJob = classification match {
            case PBS_DRUG  => new PbsItemEtlJob (sparkSession, params)
            case PBS_AUTHORITY  => new PbsAuthorityEtlJob (sparkSession, params)
            case MDV_ITEM  => new MdvItemEtlJob (sparkSession, params)
            case MDV_LAB_RESULT => new MdvLabResultEtlJob(sparkSession, params)
            case MDV_DIAGNOSIS => new MdvDiagnosisEtlJob(sparkSession, params)
            case FDB_DRUG  => new FdbDrugEtlJob (sparkSession, params)
            case ICD_DIAGNOSIS  => new IcdDiagnosisEtlJob (sparkSession, params)
            case ICD_PROCEDURE  => new IcdProcedureEtlJob (sparkSession, params)
            case JMDC_DIAGNOSIS  => new JmdcDiagnosisEtlJob (sparkSession, params)
            case JMDC_PROCEDURE  => new JmdcProcedureEtlJob (sparkSession, params)
            case JMDC_DRUG  => new JmdcDrugEtlJob (sparkSession, params)
            case JMDC_MATERIAL  => new JmdcMaterialEtlJob (sparkSession, params)
            case DESC_DIAGNOSIS => new DescDiagnosisEtlJob (sparkSession, params)
            case DESC_ITEM => new DescItemEtlJob (sparkSession, params)
            case _ => throw new RuntimeException("There is no suitable etl job for $classification")
        }

        EtlJobExecutor(etlJob)
    }
}
