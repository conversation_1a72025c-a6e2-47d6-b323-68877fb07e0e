package com.prospection.refdata.etl.pbsitem.domain

// Represent a row in restriction-prescribing-text-relationships file
case class CriteriaParameterRelationshipRow(
                                               criteria_prescribing_txt_id: String, // criteria_prescribing_txt_id
                                               prescribing_text_id: String, // parameter_prescribing_txt_id
                                               parameter_position: String // parameter_position
                                                    )

object CriteriaParameterRelationshipRow {
    object Columns {
        val CriteriaPrescribingTextId: String = PbsApiColumns.CriteriaPrescriptionTextId
        val ParameterPrescribingTextId: String = PbsApiColumns.PrescribingTextId
        val ParameterPosition: String = PbsApiColumns.ParameterPosition
    }
}

object RawCriteriaParameterRelationshipRow {
    object Columns {
        val CriteriaPrescribingTextId = "criteria_prescribing_txt_id"
        val ParameterPrescribingTextId = "parameter_prescribing_txt_id"
        val Position = "pt_position"
    }
}