package com.prospection.refdata.etl.jmdc.parser.reader

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.jmdc.row.{JmdcDrugRow, RawJmdcDrugRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class JmdcDrugRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[JmdcDrugRow](
    sparkSession,
    path,
    SparkOptions()
) {

    override val colMappings = List(
        ColumnMapping(RawJmdcDrugRow.Columns.JmdcDrugCode, JmdcDrugRow.Columns.Code),
        ColumnMapping(RawJmdcDrugRow.Columns.DrugName, JmdcDrugRow.Columns.Description),
        ColumnMapping(RawJmdcDrugRow.Columns.AtcLevel4Code, JmdcDrugRow.Columns.AtcEpharm),
        ColumnMapping(RawJmdcDrugRow.Columns.WhoAtcCode, JmdcDrugRow.Columns.AtcCode),
        ColumnMapping(RawJmdcDrugRow.Columns.DrugCode, JmdcDrugRow.Columns.NhiCode),
        ColumnMapping(RawJmdcDrugRow.Columns.GeneralName, JmdcDrugRow.Columns.DrugName),
        ColumnMapping(RawJmdcDrugRow.Columns.BrandName, JmdcDrugRow.Columns.BranchName),
        ColumnMapping(RawJmdcDrugRow.Columns.GenericDrugFlag, JmdcDrugRow.Columns.GenericFlag),
        ColumnMapping(RawJmdcDrugRow.Columns.FormulationLargeClassificationName, JmdcDrugRow.Columns.DrugUsage),
        ColumnMapping(RawJmdcDrugRow.Columns.FormulationMediumClassificationName, JmdcDrugRow.Columns.DoseFormMed),
        ColumnMapping(RawJmdcDrugRow.Columns.FormulationSmallClassificationName, JmdcDrugRow.Columns.DoseFormSml),
        ColumnMapping(RawJmdcDrugRow.Columns.Titer, JmdcDrugRow.Columns.StrengthNumber),
        ColumnMapping(RawJmdcDrugRow.Columns.TiterUnit, JmdcDrugRow.Columns.StrengthUnit),
    )

    override protected def encode(rawDs: DataFrame): Dataset[JmdcDrugRow] = {
        import sparkSession.implicits._
        rawDs.as[JmdcDrugRow]
    }
}


