package com.prospection.refdata.etl.fdbdrug.rows

// Represent a row in RINDMGC0_INDCTS_GCNSEQNO_LINK file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbIndicationLinkRow(
                                   gcn_seqno: String,
                                   indcts: String,
                               )

object FdbIndicationLinkRow {
    object Columns {
        val GcnSeqno = "gcn_seqno"
        val Indcts = "indcts"
    }
}

object RawFdbIndicationLinkRow {
    object Columns {
        val GcnSeqno = "_c0"
        val Indcts = "_c1"
    }
}