package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{ItemRestrictionRelationshipRow, RawItemRestrictionRelationshipRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class ItemRestrictionRelationshipReader(sparkSession: SparkSession, path: String) extends CsvReader[ItemRestrictionRelationshipRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawItemRestrictionRelationshipRow.Columns.ItemCode, ItemRestrictionRelationshipRow.Columns.Code),
        ColumnMapping(RawItemRestrictionRelationshipRow.Columns.ResCode, ItemRestrictionRelationshipRow.Columns.RestrictionCode)
    )

    override protected def encode(rawDs: DataFrame): Dataset[ItemRestrictionRelationshipRow] = {
        import sparkSession.implicits._
        rawDs.as[ItemRestrictionRelationshipRow]
    }

}
