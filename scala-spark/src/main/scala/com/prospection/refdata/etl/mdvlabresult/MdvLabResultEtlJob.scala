package com.prospection.refdata.etl.mdvlabresult

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.mdvlabresult.parser.MdvLabResultParser
import com.prospection.refdata.etl.mdvlabresult.transformer.MdvLabResultTransformer
import org.apache.spark.sql.{DataFrame, SparkSession}

class MdvLabResultEtlJob(val spark: SparkSession, val params: EtlJobParams) extends EtlJob(spark, params) {
    override val name: String = "MDV Lab Result Etl Job"
    final val MDV_LAB_RESULT = "MDV Lab Result"

    override def parse(): ParserOutput = null

    override def transform(parseOutput: ParserOutput): DataFrame = {
        val mdvLabResultParseResult = new MdvLabResultParser(spark, getInputFilePath(MDV_LAB_RESULT)).parse()

        new MdvLabResultTransformer(spark)
            .transform(mdvLabResultParseResult)
            .toDF()
    }
}
