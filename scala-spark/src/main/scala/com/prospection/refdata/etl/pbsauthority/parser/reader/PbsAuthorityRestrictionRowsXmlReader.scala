package com.prospection.refdata.etl.pbsauthority.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, XmlReader}
import com.prospection.refdata.etl.common.{SparkXmlOptions, StandardColumns}
import com.prospection.refdata.etl.pbsauthority.parser.reader.ExcelTab._
import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityRestrictionRow
import com.prospection.refdata.etl.pbsauthority.rows.RawPbsAuthorityRestrictionRow.Columns.{RestrictionNumber, RestrictionText}
import org.apache.spark.sql.functions.{col, explode}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class PbsAuthorityRestrictionRowsXmlReader(sparkSession: SparkSession, path: String) extends XmlReader[PbsAuthorityRestrictionRow](
    sparkSession,
    path,
    SparkXmlOptions(rowTag = Worksheet).toMap,
    schema = new StructType()
        .add(AttributeName, StringType)
        .add(Table, new StructType()
            .add(Row, ArrayType(new StructType()
                .add(Cell, ArrayType(new StructType()
                    .add(Data, new StructType()
                        .add(AttributeType, StringType)
                        .add(TagValue, StringType))
                ))
            ))
        )
) {

    override protected def preProcessRawData(rawDs: DataFrame): DataFrame = {
        val ExplodeCol = "data"
        rawDs.filter(rawDs(AttributeName) === "Restriction Description")
            /* +-----------------------+--------------------+
             * |               _ss:Name|               Table|
             * +-----------------------+--------------------+
             * |Restriction Description|{[{[{{String,  }}...|
             * +-----------------------+--------------------+
             */
            .select(explode(col(s"$Table.$Row.$Cell")).as(ExplodeCol))
            /* +--------------------------------------------------------------+
             * |data                                                          |
             * +--------------------------------------------------------------+
             * |[{{String, }}]                                                |
             * |[{{String, Description}}]                                     |
             * |[{{String, }}]                                                |
             * |null                                                          |
             * |[{{String, Restriction Number}}, {{String, Restriction Text}}]|
             * |[{{Number, 1006}, {{String, Acute myelogenous leukaemia}}]    |
             * |...                                                           |
             * |null                                                          |
             * +--------------------------------------------------------------+
             */
            .filter(col(ExplodeCol).getItem(0).getField(Data).getField(AttributeType) === "Number"
                && col(ExplodeCol).getItem(1).getField(Data).getField(AttributeType) === "String")
            /*
             * +------------------------------------------------------------+
             * |data                                                        |
             * +------------------------------------------------------------+
             * |[{{Number, 1006}}, {{String, Acute myelogenous leukaemia}}] |
             * |...                                                         |
             * +------------------------------------------------------------+
             */
            .withColumn(RestrictionNumber, col(ExplodeCol).getItem(0).getField(Data).getField(TagValue))
            .withColumn(RestrictionText, col(ExplodeCol).getItem(1).getField(Data).getField(TagValue))
            .drop(ExplodeCol)
            /* +------------------+---------------------------+
             * |Restriction Number|Restriction Text           |
             * +------------------+---------------------------+
             * |1006              |Acute myelogenous leukaemia|
             * |..                |...				          |
             * +------------------+---------------------------+
             */
    }

    override val colMappings = List(
        ColumnMapping(RestrictionNumber, StandardColumns.Code),
        ColumnMapping(RestrictionText, StandardColumns.Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[PbsAuthorityRestrictionRow] = {
        import sparkSession.implicits._
        rawDs.as[PbsAuthorityRestrictionRow]
    }
}

object ExcelTab {
    val Worksheet = "Worksheet"
    val Table = "Table"
    val Row = "Row"
    val Cell = "Cell"
    val Data = "Data"
    val AttributeName = "_ss:Name"
    val AttributeType = "_ss:Type"
    val TagValue = "_VALUE"
}