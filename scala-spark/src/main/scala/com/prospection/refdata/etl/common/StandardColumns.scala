package com.prospection.refdata.etl.common

object StandardColumns {
    val Code = "code"
    val Description = "description"
    val ShortDescription = "short_description"
    val DescriptionJp = "description_jp"

    val GenericFlag = "generic_flag"

    val DrugName = "drug_name"
    val DrugNameJp = "drug_name_jp"
    val BrandName = "brand_name"
    val LabelName = "label_name"
    val DrugUsage = "drug_usage"
    val IngredientName = "ingredient_name"
    val ManufacturerName = "manufacturer_name"
    val ManufacturerId = "manufacturer_id"

    val DoseForm = "dose_form"
    val DoseFormSml = "dose_form_sml"
    val DoseFormMed = "dose_form_med"

    val RouteOfAdministration = "route_of_administration"
    val PackageDescription = "package_description"
    val PackageSize = "package_size"
    val StrengthNumber = "strength_number"
    val StrengthUnit = "strength_unit"
    val StrengthUnitDescription = "strength_unit_description"
    val StrengthUnitDescriptionAbbreviation = "strength_unit_description_abbreviation"
    val PharmaceuticalClasses = "pharmaceutical_classes"
    val IndicationId = "indication_id"
    val IndicationDescription = "indication_description"
    val IndicationCondition = "indication_condition"
    val IndicationEpisodicity = "indication_episodicity"
    val IndicationSeverity = "indication_severity"
    val MarketingCategory = "marketing_category"
    val ProductTypeName = "product_type_name"

    val KubunCode = "kubuncode"
    val AtcEphmra = "atc_ephmra"

    val AtcCode = "atc_code"
    val RestrictionFlag = "restriction_flag"

    val ProcedureName = "procedure_name"
    val IcdCode = "icd_code"
    val Icd10Code = "icd10_code"
    val NhiCode = "nhi_code"

    val Jcode = "jcode"
    val JcodeDesc = "jcode_desc"
    val Usc = "usc_classification"
    val UscDescription = "usc_description"
    val EtcId = "etc_classification"
    val EtcName = "etc_description"
    val Ahfs8 = "ahfs_classification"
    val AhfsDescription = "ahfs_description"
    val NdcDeleteDate = "previous_version_delete_date"
    val OnLabelIndication = "on_label_indication"
    val OffLabelIndication = "off_label_indication"
    val Hic3Classification = "hic3_classification"
    val Hic3Description = "hic3_description"
    val ObsoleteDate = "obsolete_date"
    val LabellerName = "labeller_name"
    val DiagnosisName = "diagnosis_name"
    val DiagnosisNameJp = "diagnosis_name_jp"
    val ProcedureCatMed = "procedure_cat_med"
    val ProcedureCatSml = "procedure_cat_sml"
    val ProcedureCatSubclass = "procedure_cat_subclass"
    val ProcedureVersion = "procedure_version"

    val MaterialCatLarge = "material_cat_large"
    val MaterialCatMed = "material_cat_med"
    val MaterialCode = "material_code"
    val MaterialName = "material_name"
    val MaterialVersion = "material_version"
}
