package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.icd10.rows.{CcsrRow, RawDxCcsrRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class DxCcsrRowReader(sparkSession: SparkSession, path: String) extends CsvReader[CcsrRow](
    sparkSession,
    path,
    SparkOptions()
) {

    override val colMappings = List(
        ColumnMapping(RawDxCcsrRow.Columns.Icd10CMCode, CcsrRow.Columns.IcdCode),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory1, CcsrRow.Columns.CcsrCategory1),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory2, CcsrRow.Columns.CcsrCategory2),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory3, CcsrRow.Columns.CcsrCategory3),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory4, CcsrRow.Columns.CcsrCategory4),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory5, CcsrRow.Columns.CcsrCategory5),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory6, CcsrRow.Columns.CcsrCategory6),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory1Desc, CcsrRow.Columns.CcsrCategory1Description),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory2Desc, CcsrRow.Columns.CcsrCategory2Description),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory3Desc, CcsrRow.Columns.CcsrCategory3Description),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory4Desc, CcsrRow.Columns.CcsrCategory4Description),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory5Desc, CcsrRow.Columns.CcsrCategory5Description),
        ColumnMapping(RawDxCcsrRow.Columns.CcsrCategory6Desc, CcsrRow.Columns.CcsrCategory6Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[CcsrRow] = {
        import sparkSession.implicits._
        rawDs.as[CcsrRow]
    }
}