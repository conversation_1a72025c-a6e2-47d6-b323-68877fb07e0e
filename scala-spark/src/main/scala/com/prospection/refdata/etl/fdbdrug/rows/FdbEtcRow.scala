package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RETCGC0_ETC_GCNSEQNO file (Enhanced Therapeutic Classification)
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbEtcRow(
                        gcn_seqno: String,
                        etc_classification: String,
                    )

object FdbEtcRow {
    object Columns {
        val GcnSeqno = "gcn_seqno"
        val EtcId = StandardColumns.EtcId
    }
}

object RawFdbEtcRow {
    object Columns {
        val GcnSeqno = "_c0"
        val EtcId = "_c1"
    }
}