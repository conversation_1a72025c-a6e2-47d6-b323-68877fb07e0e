package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RUSCNDC0_USC_NDC_LINK file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbUscNdcLinkRow(
                            code: String,
                            usc_classification: String,
                        )

object FdbUscNdcLinkRow {
    object Columns {
        val Ndc = StandardColumns.Code
        val Usc = StandardColumns.Usc
    }
}

object RawFdbUscNdcLinkRow {
    object Columns {
        val Ndc = "_c0"
        val Usc = "_c1"
    }
}