package com.prospection.refdata.etl.pbsitem.transformer

import com.prospection.refdata.etl.pbsitem.domain._
import org.apache.spark.sql.functions.{col, collect_list, struct}
import org.apache.spark.sql.{Dataset, SparkSession}

/**
 * <p>Transforms text fragments into indication description for each item code and indication id.</p>
 *
 * <p>
 *     Indication description is a text description of the indication for a given item code and indication id. Indications
 *     are stored in "restrictions" table. They are linked to items via the "item_restriction_relationships" table.
 * </p>
 * <p>
 *     Unfortunately, the description in the restrictions table is not usable for us, as it is different to what used to be
 *     provided in PBS file downloads. But we can build the description to match the old format by combining the text fragments
 *     stored in prescribing_texts table.
 * </p>
 * <h2>Indication description creation approach</h2>
 * <p>
 *     The text fragments are stored in prescribing_texts table. Each fragment has a prescribing_type which can be one of the following:
 *     <ul>
 *      <li>INDICATION</li>
 *      <li>CRITERIA</li>
 *      <li>PRESCRIBING_INSTRUCTIONS</li>
 *      <li>ADMINISTRATIVE_ADVICE</li>
 *     </ul>
 *
 *     <p>Note: we're not interested in ADMINISTRATIVE_ADVICE and we ignore it.</p>
 * </p>
 *
 * <p>
 *  The criteria fragments can be of type clinical, treatment or population. There criteria fragments in turn can consist
 *  of multiple text pieces (let's call them "Parameters").These are stored in prescribing_texts table with prescribing_type PARAMETER.
 *  Thus we have a hierarchy of text fragments: criteria -> parameters.
 * </p>
 *
 * <p>
 *  Criteria level fragments are glued together using criteria_relationship field. Depending on the value of this field
 *  we can either concatenate all criteria fragments with "AND" or "OR" separator.
 * </p>
 *
 * <p>
 *  Parameter level fragments are glued together using parameter_relationship field. Similar to criteria level fragments,
 *  depending on the value of the parameter_relationship field we can either concatenate all parameter fragments with "AND" or "OR" separator.
 * </p>
 *
 * <p>
 *  The text fragments are ordered by criteria_position and text fragments within criteria are ordered by parameter_position.
 * </p>
 *
 * <p>
 *   The final indication description is a concatenation of all the fragments in the order they appear in the data
 *   (criteria_position and parameter_position) and concatenated either with "AND" or "OR" separator depending on the
 *   criteria_relationship and parameter_relationship fields.
 * </p>
 *
 *
 */
class IndicationDescriptionTransformer(spark: SparkSession) extends Serializable {

    private val RELATIONSHIP_ALL = "ALL"
    private val RELATIONSHIP_ANY = "ANY"

    /**
     * Map of functions that resolve indication description fragments for a given prescribing_type.
     */
    private val resolveFunctionsMap: Map[String, Seq[TextFragmentRow] => String] = Map(
        "INDICATION" -> resolveIndication,
        "CLINICAL_CRITERIA" -> resolveClinicalCriteria,
        "TREATMENT_CRITERIA" -> resolveTreatmentCriteria,
        "POPULATION_CRITERIA" -> resolvePopulationCriteria,
        "PRESCRIBING_INSTRUCTIONS" -> resolvePrescribingInstructions
    )

    def transform(input: PbsItemTransformerInput): Dataset[IndicationDescriptionRow] = {
        import spark.implicits._

        // read all the text fragments
        val textFragmentsDF = readTextFragmentsDF(input)

        // group text fragments by restriction code, indication id and code. Each group is a list of fragments for a given indication id and item code.
        // Note: collect_list does not guarantee order of elements in the list, but we don't rely on it as we sort the fragments later.
        val groupedFragmentsDF = textFragmentsDF.groupBy(
            textFragmentsDF(TextFragmentRow.Columns.RestrictionCode),
            textFragmentsDF(TextFragmentRow.Columns.IndicationId),
            textFragmentsDF(TextFragmentRow.Columns.Code),
            textFragmentsDF(TextFragmentRow.Columns.TreatmentPhase))
            .agg(collect_list(struct(textFragmentsDF.columns.map(col).toIndexedSeq: _*)).as("descriptionFragments")).as[(String, String, String, String, Seq[TextFragmentRow])]

        // Build indication description for each item and indication using resolveIndicationDescription function.
        groupedFragmentsDF.map { case (_, indicationId, code, treatment_phase, groupedRows) =>
            val resolvedDescription = resolveIndicationDescription(groupedRows)
            val conditions = dedupeAndCollect(groupedRows, _.condition)
            val episodicities = dedupeAndCollect(groupedRows, _.episodicity)
            val severities = dedupeAndCollect(groupedRows, _.severity)
            IndicationDescriptionRow(code, indicationId, resolvedDescription, treatment_phase, conditions, episodicities, severities)
        }.as[IndicationDescriptionRow]
    }

    /**
     * Produces dataset of text fragments for each item code and indication id.
     * The equivalent SQL query is:
     * <pre>
     * select irr.pbs_code,
     * irr.res_code,
     * r.criteria_relationship,
     * r.treatment_of_code,
     * r.treatment_phase,
     * cast(rptr.pt_position as integer) criteria_position,
     * c.parameter_relationship,
     * c.criteria_type,
     * cpr.criteria_prescribing_txt_id,
     * cast(cpr.pt_position as integer)  parameter_position,
     * restrictions_txt.prescribing_type,
     * restrictions_txt.prescribing_txt criteria_text,
     * parameter_txt.prescribing_txt parameter_text
     * from restrictions r
     * inner join item_restriction_relationships irr on r.res_code = irr.res_code
     * inner join restriction_prescribing_text_relationships rptr on r.res_code = rptr.res_code
     * inner join prescribing_texts restrictions_txt on restrictions_txt.prescribing_txt_id = rptr.prescribing_text_id
     * left join criteria c on c.criteria_prescribing_txt_id = restrictions_txt.prescribing_txt_id
     * left join criteria_parameter_relationships cpr on cpr.criteria_prescribing_txt_id = restrictions_txt.prescribing_txt_id
     * left outer join prescribing_texts parameter_txt on parameter_txt.prescribing_txt_id = cpr.parameter_prescribing_txt_id
     * where
     * restrictions_txt.prescribing_type <> 'ADMINISTRATIVE_ADVICE'
     * order by irr.pbs_code, irr.res_code, criteria_position, parameter_position;
     * </pre>
     *
     */
    private def readTextFragmentsDF(input: PbsItemTransformerInput): Dataset[TextFragmentRow] = {
        import spark.implicits._

        val restrictionsDF = input.restrictions.getDataset
        val itemRestrictionRelationshipsDF = input.itemRestrictionRelationships.getDataset
        val restrictionPrescribingTextRelationshipsDF = input.restrictionPrescribingTextRelationships.getDataset
        val prescribingTextsDF = input.prescribingTexts.getDataset
        val criteriaDF = input.criteria.getDataset
        val criteriaParameterRelationshipsDF = input.criteriaParameterRelationships.getDataset
        val indicationDS = input.indication.getDataset

        // need to create Columns this way so that spark does not complain about ambiguous columns.
        // Unfortunately syntax like prescribingTextsDF.as("restriction_txt")(PrescribingTextRow.Columns.PrescribingTextId) does not work.
        val RESTRICTION_TEXT_ALIAS = "restriction_txt"
        val PARAMETER_TEXT_ALIAS = "parameter_txt"
        val restrictionTextPrescribingTextIdColumn = $"$RESTRICTION_TEXT_ALIAS.${PrescribingTextRow.Columns.PrescribingTextId}"
        val restrictionTextPrescribingTypeColumn = $"$RESTRICTION_TEXT_ALIAS.${PrescribingTextRow.Columns.PrescribingType}"
        val restrictionTextPrescribingTextColumn = $"$RESTRICTION_TEXT_ALIAS.${PrescribingTextRow.Columns.PrescribingText}"
        val parameterTextPrescribingTextIdColumn = $"$PARAMETER_TEXT_ALIAS.${PrescribingTextRow.Columns.PrescribingTextId}"
        val parameterTextPrescribingTextColumn = $"$PARAMETER_TEXT_ALIAS.${PrescribingTextRow.Columns.PrescribingText}"

        restrictionsDF
            .join(itemRestrictionRelationshipsDF, restrictionsDF(RestrictionRow.Columns.RestrictionCode) === itemRestrictionRelationshipsDF(ItemRestrictionRelationshipRow.Columns.RestrictionCode))
            .join(restrictionPrescribingTextRelationshipsDF, restrictionsDF(RestrictionRow.Columns.RestrictionCode) === restrictionPrescribingTextRelationshipsDF(RestrictionPrescribingTextRelationshipRow.Columns.RestrictionCode))
            .join(prescribingTextsDF.as(RESTRICTION_TEXT_ALIAS), restrictionTextPrescribingTextIdColumn === restrictionPrescribingTextRelationshipsDF(RestrictionPrescribingTextRelationshipRow.Columns.PrescribingTextId))
            .filter(restrictionTextPrescribingTypeColumn =!= "ADMINISTRATIVE_ADVICE")
            .join(criteriaDF, criteriaDF(CriteriaRow.Columns.CriteriaPrescriptionTextId) === restrictionTextPrescribingTextIdColumn, "left_outer")
            .join(criteriaParameterRelationshipsDF, criteriaParameterRelationshipsDF(CriteriaParameterRelationshipRow.Columns.CriteriaPrescribingTextId) === restrictionTextPrescribingTextIdColumn, "left_outer")
            .join(prescribingTextsDF.as(PARAMETER_TEXT_ALIAS), parameterTextPrescribingTextIdColumn === criteriaParameterRelationshipsDF(CriteriaParameterRelationshipRow.Columns.ParameterPrescribingTextId), "left_outer")
            .join(indicationDS, restrictionTextPrescribingTextIdColumn === indicationDS(IndicationRow.Columns.IndicationPrescribingTextId), "left_outer")
            .select(
                itemRestrictionRelationshipsDF(ItemRestrictionRelationshipRow.Columns.Code),
                restrictionsDF(RestrictionRow.Columns.RestrictionCode),
                restrictionsDF(RestrictionRow.Columns.CriteriaRelationship),
                restrictionsDF(RestrictionRow.Columns.IndicationId),
                restrictionsDF(RestrictionRow.Columns.TreatmentPhase),
                restrictionPrescribingTextRelationshipsDF(RestrictionPrescribingTextRelationshipRow.Columns.CriteriaPosition).cast("int"),
                criteriaDF(CriteriaRow.Columns.ParameterRelationship),
                criteriaDF(CriteriaRow.Columns.CriteriaType),
                criteriaParameterRelationshipsDF(CriteriaParameterRelationshipRow.Columns.CriteriaPrescribingTextId),
                criteriaParameterRelationshipsDF(CriteriaParameterRelationshipRow.Columns.ParameterPosition).cast("int"),
                restrictionTextPrescribingTypeColumn,
                restrictionTextPrescribingTextColumn.alias("criteria_text"),
                parameterTextPrescribingTextColumn.alias("parameter_text"),
                indicationDS(IndicationRow.Columns.Condition),
                indicationDS(IndicationRow.Columns.Episodicity),
                indicationDS(IndicationRow.Columns.Severity)
            )
            .orderBy(
                itemRestrictionRelationshipsDF(ItemRestrictionRelationshipRow.Columns.Code),
                restrictionsDF(RestrictionRow.Columns.RestrictionCode),
                restrictionPrescribingTextRelationshipsDF(RestrictionPrescribingTextRelationshipRow.Columns.CriteriaPosition),
                criteriaParameterRelationshipsDF(CriteriaParameterRelationshipRow.Columns.ParameterPosition)
            ).as[TextFragmentRow]
    }

    private def dedupeAndCollect[T](rows: Seq[TextFragmentRow], extractor: TextFragmentRow => T): Seq[T] = {
        val concatenatedFragments = rows
            .map(extractor)
            .filter(_ != null)
            .distinct
        if (concatenatedFragments.isEmpty) null else concatenatedFragments
    }

    /**
     * Builds Indication Description for a group of rows. The group of rows represents all the text fragments for a given item code and indication id.
     */
    private def resolveIndicationDescription(rows: Seq[TextFragmentRow]): String = {
        // need to make sure that rows are sorted by criteria_position and parameter_position.
        // They possibly come already sorted from the query, but we can't rely on that due to spark optimization.
        val sortedRows = sortFragments(rows)

        // group rows by criteria type clusters. Note: we group rows with the same criteria type that are next to each other.
        // This is important as we need to resolve fragments in the order they appear in the data.
        // As a result we get a list of groups of rows. Each group represents a cluster of rows with the same criteria type.
        val fragmentGroups = sortedRows.foldLeft(Seq.empty[(String, Seq[TextFragmentRow])]) {
            (acc, row) => {
                val rowCriteriaType = if (row.criteria_type != null) s"${row.criteria_type}_${row.prescribing_type}" else row.prescribing_type
                acc.lastOption match {
                    case Some((criteriaType, group)) if criteriaType == rowCriteriaType =>
                        acc.init :+ (criteriaType, group :+ row) // Add to the last group
                    case _ =>
                        acc :+ (rowCriteriaType, Seq(row)) // Start a new group
                }
            }
        }

        // Resolve each fragment by calling appropriate function and concatenate them together.
        // Also remove extra spaces.
        fragmentGroups.flatMap { case (criteriaType, group) =>
            resolveFunctionsMap.get(criteriaType).map(_(group))
        }.mkString(" ").replaceAll(" +", " ").trim
    }

    /**
     * Sorts text fragments by criteria_position and parameter_position. The order of text fragments is important
     * as it will affect order in which fragments will appear in the final indication description.
     */
    private def sortFragments(fragments: Seq[TextFragmentRow]): Seq[TextFragmentRow] = {
        fragments.sortBy(fragment => (fragment.criteria_position, fragment.parameter_position))
    }

    private def resolveIndication(rows: Seq[TextFragmentRow]): String = {
        // expected to always have one and only one INDICATION row
        val indicationRow = rows.find(_.prescribing_type == "INDICATION").getOrElse(return "")
        val indication = indicationRow.criteria_text
        val treatmentPhase = if (indicationRow.treatment_phase != null && indicationRow.treatment_phase.nonEmpty) {
            "Treatment Phase: " + indicationRow.treatment_phase
        } else {
            ""
        }
        (indication, treatmentPhase) match {
            case (ind, "") => ind
            case (ind, tp) => s"$ind $tp"
        }
    }

    private def resolveClinicalCriteria(rows: Seq[TextFragmentRow]): String = {
        val clinicalCriteriaRows = rows.filter(row => row.prescribing_type == "CRITERIA" && row.criteria_type == "CLINICAL")
        val clinicalCriteria = resolveCriteriaFragment(clinicalCriteriaRows)
        if (clinicalCriteria == null || clinicalCriteria.isEmpty) "" else "Clinical criteria: " + clinicalCriteria
    }

    private def resolveTreatmentCriteria(rows: Seq[TextFragmentRow]): String = {
        val treatmentCriteriaRows = rows.filter(row => row.prescribing_type == "CRITERIA" && row.criteria_type == "TREATMENT")
        val treatmentCriteria = resolveCriteriaFragment(treatmentCriteriaRows)
        if (treatmentCriteria == null || treatmentCriteria.isEmpty) "" else "Treatment criteria: " + treatmentCriteria
    }

    private def resolvePopulationCriteria(rows: Seq[TextFragmentRow]): String = {
        val populationCriteriaRows = rows.filter(row => row.prescribing_type == "CRITERIA" && row.criteria_type == "POPULATION")
        val populationCriteria = resolveCriteriaFragment(populationCriteriaRows)
        if (populationCriteria == null || populationCriteria.isEmpty) "" else "Population criteria: " + populationCriteria
    }

    private def resolvePrescribingInstructions(rows: Seq[TextFragmentRow]): String = {
        val prescribingInstructionsRows = rows.filter(row => row.prescribing_type == "PRESCRIBING_INSTRUCTIONS")
        val prescribingInstructionsFragments = resolveParameterFragments(prescribingInstructionsRows)

        val criteriaFragment = prescribingInstructionsFragments.mkString(" ")
        criteriaFragment
    }

    private def resolveCriteriaFragment(criteriaRows: Seq[TextFragmentRow]): String = {
        val criteriaRelationship = criteriaRows.find(_.criteria_relationship != null).map(_.criteria_relationship).getOrElse(RELATIONSHIP_ALL)
        val criteriaFragments = resolveParameterFragments(criteriaRows)

        /*
            Concatenate criteria fragments following the logic:
            - If criteriaRelationship is "ALL" then concatenate all clinicalCriteriaFragments with ", AND" separator
            - If criteriaRelationship is "ANY" then concatenate all parameterTexts with ", OR" separator
         */
        val criteriaFragment = criteriaRelationship match {
            case RELATIONSHIP_ALL => criteriaFragments.mkString(start = "* ", sep=", AND * ", end = "")
            case RELATIONSHIP_ANY => criteriaFragments.mkString(start = "* ", sep= ", OR * ", end = "")
            case _ => ", " // Default separator if neither "ALL" nor "ANY"
        }
        criteriaFragment
    }

    /**
     * Resolves parameter fragments for criteria.
     * The function logic is as follows:
     *  - Group rows by criteriaPrescribingTextId
     *  - Apply resolveParameterFragment function to each group. parameterRelationship for the function is taken from the first row in the group.
     *  - Collect resolveParameterFragment results into a list
     */
    private def resolveParameterFragments(rows: Seq[TextFragmentRow]): Seq[String] = {
        // We can't use groupBy here as it does not guarantee order of groups. We need to preserver order of groups.
        // So we need to group rows manually.

        // Step 1: Identify unique criteria_prescribing_txt_id values in order
        val orderedCriteriaIds = rows.map(_.criteria_prescribing_txt_id).distinct

        // Step 2: Group rows by criteria_prescribing_txt_id while preserving order
        val orderedGroups = orderedCriteriaIds.map(id => rows.filter(_.criteria_prescribing_txt_id == id))

        // Step 3: Resolve each group and return the resolved fragments in order
        orderedGroups.flatMap(group => {
            val parameterRelationship = group.head.parameter_relationship
            Some(resolveParameterFragment(group, parameterRelationship))
        })
    }

    /**
     * Resolves text fragment for a group of rows belonging to the same criteria (they are called "parameters" in the context of a criteria).
     * The function logic is as follows:
     * - If there is ony one item in a group, then return parameterText, or if parameterText is null then return criteriaText.
     * - If multiple rows within a group, concatenate them together using following logic:
     * - If parameterRelationship is "ALL" then concatenate all parameterTexts with "; AND * " separator
     * - If parameterRelationship is "ANY" then concatenate all parameterTexts with "; OR * " separator
     */
    private def resolveParameterFragment(rows: Seq[TextFragmentRow], parameterRelationship: String): String = {
        if (rows.length == 1) {
            rows.headOption.flatMap(row => Option(row.parameter_text).orElse(Option(row.criteria_text))).getOrElse("")
        } else {
            val separator = parameterRelationship match {
                case RELATIONSHIP_ALL => "; AND * "
                case RELATIONSHIP_ANY => "; OR * "
                case _ => "; " // Default separator if neither "ALL" nor "ANY"
            }
            rows.flatMap(row => Option(row.parameter_text).orElse(Option(row.criteria_text))).mkString(separator)
        }
    }


}

case class TextFragmentRow(
                              code: String,
                              res_code: String,
                              criteria_relationship: String,
                              indication_id: String,
                              treatment_phase: String,
                              criteria_position: Integer,
                              parameter_relationship: String,
                              criteria_type: String,
                              criteria_prescribing_txt_id: String,
                              parameter_position: Integer,
                              prescribing_type: String,
                              criteria_text: String,
                              parameter_text: String,
                              condition: String,
                              episodicity: String,
                              severity: String
                          )

object TextFragmentRow {
    object Columns {
        val Code: String = ItemRestrictionRelationshipRow.Columns.Code
        val RestrictionCode: String = RestrictionRow.Columns.RestrictionCode
        val CriteriaRelationship: String = RestrictionRow.Columns.CriteriaRelationship
        val IndicationId: String = RestrictionRow.Columns.IndicationId
        val TreatmentPhase: String = RestrictionRow.Columns.TreatmentPhase
        val CriteriaPosition: String = RestrictionPrescribingTextRelationshipRow.Columns.CriteriaPosition
        val ParameterRelationship: String = CriteriaRow.Columns.ParameterRelationship
        val CriteriaType: String = CriteriaRow.Columns.CriteriaType
        val CriteriaPrescribingTextId: String = CriteriaRow.Columns.CriteriaPrescriptionTextId
        val ParameterPosition: String = CriteriaParameterRelationshipRow.Columns.ParameterPosition
        val PrescribingType: String = PrescribingTextRow.Columns.PrescribingType
        val CriteriaText = "criteria_text"
        val ParameterText = "parameter_text"
    }
}