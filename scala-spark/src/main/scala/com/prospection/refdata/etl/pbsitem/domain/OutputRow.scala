package com.prospection.refdata.etl.pbsitem.domain

case class OutputRow(
                        code: String,
                        description: String,
                        atc_code: String,
                        drug_name: String,
                        brand_name: String,
                        restriction_flag: String,
                        manufacturer_name: String,
                        indication_id: String,
                        indication_description: String,
                        route_of_administration: String,
                        li_drug_name: String,
                        formulary: String,
                        treatment_phase: String,
                        indication_condition: Seq[String],
                        indication_episodicity: Seq[String],
                        indication_severity: Seq[String]
                    )

object OutputRow {
    object Columns {
        val code: String = ItemRow.Columns.Code
        val description: String = ItemRow.Columns.Description
        val atc_code: String = ItemAtcRelationshipRow.Columns.AtcCode
        val drug_name: String = ItemRow.Columns.DrugName
        val brand_name: String = ItemRow.Columns.BrandName
        val restriction_flag: String = ItemRow.Columns.RestrictionFlag
        val manufacturer_name: String = OrganisationRow.Columns.ManufacturerName
        val indication_id: String = IndicationDescriptionRow.Columns.IndicationId
        val indication_description: String = IndicationDescriptionRow.Columns.IndicationDescription
        val route_of_administration: String = "route_of_administration"
        val li_drug_name: String = ItemRow.Columns.LiDrugName
        val formulary: String = ItemRow.Columns.Formulary
        val treatment_phase: String = RestrictionRow.Columns.TreatmentPhase
        val indication_condition: String = IndicationDescriptionRow.Columns.Condition
        val indication_episodicity: String = IndicationDescriptionRow.Columns.Episodicity
        val indication_severity: String = IndicationDescriptionRow.Columns.Severity
    }
}
