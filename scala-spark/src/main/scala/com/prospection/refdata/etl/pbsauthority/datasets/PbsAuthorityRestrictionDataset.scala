package com.prospection.refdata.etl.pbsauthority.datasets

import com.prospection.refdata.etl.common.{EtlDataset, StandardColumns}
import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityRestrictionRow
import org.apache.spark.sql.Dataset

class PbsAuthorityRestrictionDataset(dataset: Dataset[PbsAuthorityRestrictionRow]) extends EtlDataset[PbsAuthorityRestrictionRow](dataset, "restrictions") {
    override def getNonNullableColumns: Set[String] = Set(
        StandardColumns.Code,
        StandardColumns.Description,
    )
    override def getUniqueColumn: Option[String] = Some(StandardColumns.Code)
}
