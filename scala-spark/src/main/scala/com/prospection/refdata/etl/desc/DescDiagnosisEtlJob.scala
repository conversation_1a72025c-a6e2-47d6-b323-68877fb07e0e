package com.prospection.refdata.etl.desc

import com.prospection.refdata.etl.common.CodingSystems.DESC_DIAGNOSIS
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.desc.datasets.DescDiagnosisDataset
import com.prospection.refdata.etl.desc.parser.{DescDiagnosisParser, DescDiagnosisParserOutput}
import com.prospection.refdata.etl.desc.transformer.{DescDiagnosisTransformer, DescDiagnosisTransformerInput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class DescDiagnosisEtlJob(val spark: SparkSession, val params: EtlJobParams) extends
        EtlJob(spark, params) {

    override val name: String = "DESC Diagnosis Etl Job"

    override def parse(): ParserOutput = {
        new DescDiagnosisParser(spark, getInputFilePath(DESC_DIAGNOSIS)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {

        val parseResult = parseOutput.asInstanceOf[DescDiagnosisParserOutput]
        val transformInput = DescDiagnosisTransformerInput(
            new DescDiagnosisDataset(parseResult.descDiagnosis),
        )
        new DescDiagnosisTransformer(spark).transform(transformInput).toDF()

    }


}
