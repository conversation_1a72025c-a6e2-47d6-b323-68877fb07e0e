package com.prospection.refdata.etl.pbsitem.parser.writer

import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import org.apache.spark.sql.{Dataset, Row, SaveMode}

case class PbsItemHistoricalWriter(
                                      input: PbsItemHistoricalWriterInput
                                  ) {

    def write(warehousePathPrefix: String, version: String): Unit = {

        storeHistoricalData(input.itemDataset, getHistoricalPath(warehousePathPrefix, "items", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.restrictionDataset, getHistoricalPath(warehousePathPrefix, "restrictions", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.manufacturerDataset, getHistoricalPath(warehousePathPrefix, "organisations", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.itemAtcRelationshipDataset, getHistoricalPath(warehousePathPrefix, "item-atc-relationships", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.itemRestrictionRelationshipDataset, getHistoricalPath(warehousePathPrefix, "item-restriction-relationships", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.criteriaDataset, getHistoricalPath(warehousePathPrefix, "criteria", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.restrictionPrescribingTextRelationshipDataset, getHistoricalPath(warehousePathPrefix, "restriction-prescribing-text-relationships", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.prescribingTextDataset, getHistoricalPath(warehousePathPrefix, "prescribing-texts", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
        storeHistoricalData(input.criteriaParameterRelationshipDataset, getHistoricalPath(warehousePathPrefix, "criteria-parameter-relationships", version), SparkOptions(delimiter = CommonDelimiters.COMMA))
    }

    private def getHistoricalPath(warehousePathPrefix: String, dataCategory: String, version: String): String = {
        s"$warehousePathPrefix/$dataCategory/month=$version"
    }

    def storeHistoricalData(df: Dataset[Row], path: String, sparkOptions: SparkOptions): Unit = {
        df.write.mode(SaveMode.Overwrite).options(sparkOptions.toMap).csv(path)
    }
}

case class PbsItemHistoricalWriterInput(
                                           itemDataset: Dataset[Row],
                                           restrictionDataset: Dataset[Row],
                                           manufacturerDataset: Dataset[Row],
                                           itemAtcRelationshipDataset: Dataset[Row],
                                           itemRestrictionRelationshipDataset: Dataset[Row],
                                           criteriaDataset: Dataset[Row],
                                           restrictionPrescribingTextRelationshipDataset: Dataset[Row],
                                           prescribingTextDataset: Dataset[Row],
                                           criteriaParameterRelationshipDataset: Dataset[Row]
                                       )
