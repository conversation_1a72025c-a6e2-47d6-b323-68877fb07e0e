package com.prospection.refdata.etl.mdvitem.parser

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions, StandardColumns}
import com.prospection.refdata.etl.mdvitem.rows.{MdvProcedureRow, RawMdvProcedureRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class MdvProcedureRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[MdvProcedureRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.TAB)
) {

    override val colMappings = List(
        ColumnMapping(RawMdvProcedureRow.Columns.KubunCode, StandardColumns.KubunCode),
        ColumnMapping(RawMdvProcedureRow.Columns.ReceiptCode, StandardColumns.Code),
        ColumnMapping(RawMdvProcedureRow.Columns.ReceiptName, StandardColumns.ProcedureName),
        ColumnMapping(RawMdvProcedureRow.Columns.ReceiptNameEng, StandardColumns.Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[MdvProcedureRow] = {
        import sparkSession.implicits._
        rawDs.as[MdvProcedureRow]
    }
}
