package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbHicDescRow, RawFdbHicDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbHicDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbHicDescRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbHicDescRow.Columns.HicSeqn, FdbHicDescRow.Columns.HicSeqn),
        ColumnMapping(RawFdbHicDescRow.Columns.HicDesc, FdbHicDescRow.Columns.HicDesc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbHicDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbHicDescRow]
    }
}
