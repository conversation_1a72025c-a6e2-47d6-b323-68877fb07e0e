package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbEtcRow, RawFdbEtcRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbEtcRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbEtcRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbEtcRow.Columns.GcnSeqno, FdbEtcRow.Columns.GcnSeqno),
        ColumnMapping(RawFdbEtcRow.Columns.EtcId, FdbEtcRow.Columns.EtcId),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbEtcRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbEtcRow]
    }
}
