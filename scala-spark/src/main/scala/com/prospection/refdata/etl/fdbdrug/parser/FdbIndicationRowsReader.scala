package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbIndicationRow, RawFdbIndicationRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbIndicationRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbIndicationRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbIndicationRow.Columns.Indcts, FdbIndicationRow.Columns.Indcts),
        ColumnMapping(RawFdbIndicationRow.Columns.IndctsLbl, FdbIndicationRow.Columns.IndctsLbl),
        ColumnMapping(RawFdbIndicationRow.Columns.Dxid, FdbIndicationRow.Columns.Dxid),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbIndicationRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbIndicationRow]
    }
}
