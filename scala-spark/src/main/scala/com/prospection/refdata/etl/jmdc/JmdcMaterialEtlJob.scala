package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_MATERIAL
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.jmdc.parser.{JmdcMaterialParser, JmdcMaterialParserOutput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class JmdcMaterialEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) {

    override val name: String = "JMDC Material Etl Job"

    override def parse(): ParserOutput = {
        new JmdcMaterialParser(spark, getInputFilePath(JMDC_MATERIAL)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {
        parseOutput.asInstanceOf[JmdcMaterialParserOutput].dataset.toDF()
    }
}
