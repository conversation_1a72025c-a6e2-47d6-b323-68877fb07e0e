package com.prospection.refdata.etl.mdvdiagnosis.datasets

import com.prospection.refdata.etl.common.{EtlDataset, StandardColumns}
import com.prospection.refdata.etl.mdvdiagnosis.rows.MdvDiagnosisRow
import org.apache.spark.sql.Dataset

class MdvDiagnosisDataset(dataset: Dataset[MdvDiagnosisRow]) extends EtlDataset[MdvDiagnosisRow](dataset, "diagnosis") {
    override def getNonNullableColumns: Set[String] = Set(
        StandardColumns.Code
    )
}
