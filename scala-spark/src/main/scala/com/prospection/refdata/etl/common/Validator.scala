package com.prospection.refdata.etl.common

import org.apache.spark.sql.catalyst.plans.logical.SubqueryAlias
import org.apache.spark.sql.functions.{col, lit}
import org.apache.spark.sql.{Column, Dataset}

object Validator {
    def validate(etlDatasets: EtlDataset[_]*): Unit = {
        etlDatasets.foreach(etlDataset => {
            val dataset = etlDataset.getDataset

            dataset.checkNotEmpty()

            etlDataset.getUniqueColumn.foreach(dataset.checkDuplicateBy(_))

            etlDataset.getNonNullableColumns.foreach(column =>
                if (!dataset.filter(isBlank(column)).isEmpty) {
                    throw new RuntimeException(s"$column in ${dataset.getAlias} dataset has null or empty value(s), which is not permitted.")
                }
            )
        })
    }

    implicit class DatasetWrapperForValidation[T](val dataset: Dataset[T]) {
        def checkNotEmpty(): Unit = {
            if (dataset.isEmpty) {
                throw new RuntimeException(s"${dataset.getAlias} dataset is empty")
            }
        }

        def checkDuplicateBy(column: String): Unit = {
            val duplicateRows = dataset.exceptAll(dataset.dropDuplicates(column))

            if (duplicateRows.count() > 0) {
                val duplicateValues = duplicateRows.select(column).take(10)
                throw new RuntimeException(s"$column in ${dataset.getAlias} dataset has duplicates. Showing max 10 duplicate examples here ${duplicateValues.mkString}")
            }
        }

        def getAlias: String = dataset.queryExecution.analyzed match {
            case SubqueryAlias(alias, _) => alias.name
            case _ => "No alias"
        }
    }

    def isBlank(columnName: String): Column = {
        val column = col(columnName)
        column.isNull or (column <=> lit(""))
    }
}
