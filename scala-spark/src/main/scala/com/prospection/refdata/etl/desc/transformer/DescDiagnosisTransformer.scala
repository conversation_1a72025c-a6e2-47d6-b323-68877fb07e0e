package com.prospection.refdata.etl.desc.transformer

import com.prospection.refdata.etl.desc.rows.DescDiagnosisOutputRow
import org.apache.spark.sql.{Dataset, SparkSession}

class DescDiagnosisTransformer(spark: SparkSession) {
    def transform(input: DescDiagnosisTransformerInput): Dataset[DescDiagnosisOutputRow] = {
        import spark.implicits._
        input.descDiagnosisDataset.getDataset.as[DescDiagnosisOutputRow]
    }

}
