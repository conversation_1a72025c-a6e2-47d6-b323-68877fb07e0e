package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RFMLISR1_ICD_SEARCH file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbIcdSearchRow(
                              related_dxid: String,
                              icd_code: String,
                              fml_nav_code: String,
                          )

object FdbIcdSearchRow {
    object Columns {
        val RelatedDxid = "related_dxid"
        val SearchIcdCd = StandardColumns.IcdCode
        val FmlNavCode = "fml_nav_code"
    }
}

object RawFdbIcdSearchRow {
    object Columns {
        val SearchIcdCd = "_c0"
        val RelatedDxid = "_c2"
        val FmlNavCode = "_c4"

    }
}