package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RLBLRID3_LBLR_DESC file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbNdcDescRow(
                            lblrid: String,
                            labeller_name: String,
                        )

object FdbNdcDescRow {
    object Columns {
        val Lblrid = "lblrid"
        val Mfg = StandardColumns.LabellerName
    }
}

object RawFdbNdcDescRow {
    object Columns {
        val Lblrid = "_c0"
        val Mfg = "_c1"
    }
}