package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.jmdc.parser.reader.JmdcDrugRowsReader
import com.prospection.refdata.etl.jmdc.row.JmdcDrugRow
import org.apache.spark.sql.{Dataset, SparkSession}

class JmdcDrugParser(
                             sparkSession: SparkSession,
                             pathPrefix: String,
                         ) {

    def parse(): JmdcDrugParserOutput = {

        val jmdcDrugDataSet = JmdcDrugRowsReader(sparkSession, s"$pathPrefix/transformed/split/drug_master").read()

        JmdcDrugParserOutput(
            jmdcDrugDataSet.dataset
        )
    }
}

case class JmdcDrugParserOutput(dataset: Dataset[JmdcDrugRow]) extends ParserOutput