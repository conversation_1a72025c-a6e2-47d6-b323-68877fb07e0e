package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_DRUG
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.jmdc.parser.{JmdcDrugParser, JmdcDrugParserOutput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class JmdcDrugEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends <PERSON>tl<PERSON>ob(spark, params) {

    override val name: String = "JMDC Drug Etl Job"

    override def parse(): ParserOutput = {
        new JmdcDrugParser(spark, getInputFilePath(JMDC_DRUG)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {
        parseOutput.asInstanceOf[JmdcDrugParserOutput].dataset.toDF()
    }
}
