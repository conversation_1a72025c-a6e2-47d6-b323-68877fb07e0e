package com.prospection.refdata.etl.icd10.parser

import com.prospection.refdata.etl.icd10.parser.reader.{Icd10OrderRowsReader, Icd9GemRowsReader, Icd9PC<PERSON>owsReader, PrCcsrRowReader}
import com.prospection.refdata.etl.icd10.rows.{Icd10OrderRow, IcdParserOutput}
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.{Dataset, SparkSession}

import java.time.LocalDate

class IcdProcedureParser(
                            sparkSession: SparkSession,
                            pathPrefix: String,
                            versionDate: LocalDate,
                        ) {

    private val ICD10PCS_MIN_YEAR = 2014
    private final val TransformedPath = s"$pathPrefix/transformed"
    private val logger = Logger(classOf[IcdProcedureParser])

    def parse(): IcdParserOutput = {

        val icd10PCSDs = getIcd10PCSOrderDataset(versionDate)

        val icd9LongDescData = Icd9PCSRowsReader(sparkSession, s"$TransformedPath/CMS32_DESC_LONG_SG.txt").read()
        val icd9ShortDescData = Icd9PCSRowsReader(sparkSession, s"$TransformedPath/CMS32_DESC_SHORT_SG.txt").read()

        val dxCcsrData = PrCcsrRowReader(sparkSession, s"$TransformedPath/PRCCSR*.CSV").read()

        val icd9GemData = Icd9GemRowsReader(sparkSession, s"$TransformedPath/gem_i9pcs.txt").read()

        IcdParserOutput(
            icd10PCSDs,
            icd9LongDescData.dataset,
            icd9ShortDescData.dataset,
            dxCcsrData.dataset,
            icd9GemData.dataset
        )
    }

    private def getIcd10PCSOrderDataset (versionDate: LocalDate): Map[Int, Dataset[Icd10OrderRow]] = {

        var icd10CMDs = Map[Int, Dataset[Icd10OrderRow]]()
        val currentYear =  versionDate.getYear

        val datasetOfNextYear =  getIcd10PCSOrderDatasetOfNextYear(currentYear)

        if(datasetOfNextYear != null) {
            icd10CMDs += (currentYear + 1 -> datasetOfNextYear)
        }

        for (year <- ICD10PCS_MIN_YEAR to currentYear) {
            val dataSetByYear = Icd10OrderRowsReader(sparkSession, s"$TransformedPath/icd10pcs_order_$year.txt").read()
            icd10CMDs += (year -> dataSetByYear.dataset)
        }

        icd10CMDs
    }

    //We need get the order on next year because it is provided at current year
    // Example: ICD10CM Order 2023 is provided at 2022-07-27.
    private def getIcd10PCSOrderDatasetOfNextYear(currentYear: Int): Dataset[Icd10OrderRow] = {
        val nextYear = currentYear + 1
        try {
            Icd10OrderRowsReader(sparkSession, s"$TransformedPath/icd10pcs_order_$nextYear.txt").read().dataset
        } catch {
            case _: Throwable => logger.info(s"Icd10 PCS Order file of $nextYear is not exits")
        }

        null
    }
}