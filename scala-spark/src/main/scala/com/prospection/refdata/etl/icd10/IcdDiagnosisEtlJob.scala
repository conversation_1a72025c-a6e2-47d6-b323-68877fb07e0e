package com.prospection.refdata.etl.icd10

import com.prospection.refdata.etl.common.CodingSystems.ICD_DIAGNOSIS
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.icd10.parser.IcdDiagnosisParser
import com.prospection.refdata.etl.icd10.rows.IcdParserOutput
import com.prospection.refdata.etl.icd10.transformer.{IcdTransformer, IcdTransformerInput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class IcdDiagnosisEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) {

    override val name: String = "ICD Diagnosis Etl Job"
    private val ICD_10CM_TYPE = "ICD-10-CM"
    private val ICD_9CM_TYPE = "ICD-9-CM Dx"

    override def parse(): ParserOutput = {
        new IcdDiagnosisParser(
            spark,
            getInputFilePath(ICD_DIAGNOSIS),
            getVersion
        ).parse()
    }

    def transform(parseOutput: ParserOutput): DataFrame = {
        val parseResult = parseOutput.asInstanceOf[IcdParserOutput]
        val transformInput = IcdTransformerInput(
            parseResult.icd10Dataset,
            parseResult.icd9LongDescDataset,
            parseResult.icd9ShortDescDataset,
            parseResult.ccsrDataset,
            parseResult.icd9GemDataset
        )
        new IcdTransformer(spark).transform(transformInput, ICD_10CM_TYPE, ICD_9CM_TYPE).toDF()
    }
}
