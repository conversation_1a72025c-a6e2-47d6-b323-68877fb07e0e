package com.prospection.refdata.etl.mdvdiagnosis.parser

import com.prospection.refdata.etl.mdvdiagnosis.datasets.MdvDiagnosisDataset
import com.prospection.refdata.etl.mdvdiagnosis.transformer.MdvDiagnosisTransformerInput
import org.apache.spark.sql.SparkSession

class MdvDiagnosisParser(sparkSession: SparkSession, pathPrefix: String) {
    def parse(drugFileName: String = "M_Disease.txt.gz"): MdvDiagnosisTransformerInput = {
        val diseases = MdvDiagnosisRowsReader(sparkSession, s"$pathPrefix/$drugFileName").read()

        MdvDiagnosisTransformerInput(new MdvDiagnosisDataset(diseases.dataset))
    }
}
