package com.prospection.refdata.etl.pbsauthority.parser.writer

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.writer.CsvWriter
import org.apache.spark.sql.{Dataset, Row}

case class PbsAuthorityHistoricalWriter(input: PbsAuthorityHistoricalWriterInput) extends CsvWriter {

    def write(warehousePathPrefix: String, version: String): Unit = {
        val path = getHistoricalPath(warehousePathPrefix, "restrictions", version)
        write(input.restrictionDataset, path, SparkOptions().toMap)
    }

    private def getHistoricalPath(warehousePathPrefix: String, dataCategory: String, version: String): String = {
        s"$warehousePathPrefix/$dataCategory/month=$version"
    }
}

case class PbsAuthorityHistoricalWriterInput(
                                                restrictionDataset: Dataset[Row],
                                            )
