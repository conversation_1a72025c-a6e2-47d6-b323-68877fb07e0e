package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns
import com.prospection.refdata.etl.pbsitem.domain.PbsApiColumns

// Represent a row in item_{date}.csv file
case class ItemRow(
                      code: String, // pbs_code
                      drug_name: String, // drug_name
                      brand_name: String, // brand_name
                      restriction_flag: String, // benefit_type_code
                      manufacturer_id: String, // organisation_id
                      description: String, // schedule_form
                      li_drug_name: String, // li_drug_name
                      formulary: String // formulary
                  )

object ItemRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val DrugName: String = StandardColumns.DrugName
        val BrandName: String = StandardColumns.BrandName
        val RestrictionFlag: String = StandardColumns.RestrictionFlag
        val ManufacturerId: String = StandardColumns.ManufacturerId
        val Description: String = StandardColumns.Description
        val LiDrugName: String = PbsApiColumns.LiDrugName
        val Formulary: String = PbsApiColumns.Formulary
    }
}

object RawItemRow {
    object Columns {
        val ItemCode = "pbs_code"
        val DrugName = "drug_name"
        val BrandName = "brand_name"
        val RestrictionFlag = "benefit_type_code"
        val ManufacturerId = "organisation_id"
        val Description = "schedule_form"
        val LiDrugName = "li_drug_name"
        val Formulary = "formulary"
    }
}