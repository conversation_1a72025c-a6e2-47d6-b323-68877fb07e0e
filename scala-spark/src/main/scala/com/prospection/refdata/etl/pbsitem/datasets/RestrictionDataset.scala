package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.RestrictionRow
import com.prospection.refdata.etl.pbsitem.domain.RestrictionRow.Columns
import org.apache.spark.sql.Dataset

class RestrictionDataset(dataset: Dataset[RestrictionRow]) extends EtlDataset[RestrictionRow](dataset, "restrictions") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.RestrictionCode,
        Columns.TreatmentPhase,
        Columns.CriteriaRelationship,
    )

    override def getUniqueColumn: Option[String] = Some(Columns.RestrictionCode)
}
