package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RNDC14_NDC_MSTR  file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbNdcRow(
                        code: String,
                        brand_name: String,
                        label_name: String,
                        package_size: String,
                        package_description: String,
                        lblrid: String,
                        gcn_seqno: String,
                        obsolete_date: String,
                    )

object FdbNdcRow {
    object Columns {
        val Ndc = StandardColumns.Code
        val Bn = StandardColumns.BrandName
        val Ln = StandardColumns.LabelName
        val Ps = StandardColumns.PackageSize
        val Pd = StandardColumns.PackageDescription
        val Obsdtec = StandardColumns.ObsoleteDate
        val Lblrid = "lblrid"
        val GcnSeqno = "gcn_seqno"
    }
}

object RawFdbNdcRow {
    object Columns {
        val Ndc = "_c0"
        val Lblrid = "_c1"
        val GcnSeqno = "_c2"
        val Ps = "_c3"
        val Ln = "_c6"
        val Bn = "_c7"
        val Obsdtec = "_c26"
        val Pd = "_c48"
    }
}