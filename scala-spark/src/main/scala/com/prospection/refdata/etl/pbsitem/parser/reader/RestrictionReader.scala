package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{RawRestrictionRow, RestrictionRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class RestrictionReader(sparkSession: SparkSession, path: String) extends CsvReader[RestrictionRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {
    override val colMappings = List(
        ColumnMapping(RawRestrictionRow.Columns.ResCode, RestrictionRow.Columns.RestrictionCode),
        ColumnMapping(RawRestrictionRow.Columns.IndicationId, RestrictionRow.Columns.IndicationId),
        ColumnMapping(RawRestrictionRow.Columns.TreatmentPhase, RestrictionRow.Columns.TreatmentPhase),
        ColumnMapping(RawRestrictionRow.Columns.RestrictionIndicationText, RestrictionRow.Columns.IndicationDescription),
        ColumnMapping(RawRestrictionRow.Columns.CriteriaRelationship, RestrictionRow.Columns.CriteriaRelationship)
    )

    override def encode(rawDs: DataFrame): Dataset[RestrictionRow] = {
        import sparkSession.implicits._
        rawDs.as[RestrictionRow]
    }
}