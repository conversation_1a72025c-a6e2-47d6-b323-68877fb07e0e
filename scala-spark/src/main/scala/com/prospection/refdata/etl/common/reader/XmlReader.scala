package com.prospection.refdata.etl.common.reader

import org.apache.spark.sql.types._
import org.apache.spark.sql.SparkSession

abstract class XmlReader[T](spark: SparkSession, path: String, options: Map[String, String], schema: StructType) extends Reader[T] {

    final def read(): ReaderOutput[T] = {
        val rawDs = spark.read
            .format("xml")
            .options(options)
            .schema(schema)
            .load(path)

        val processedRawDs = preProcessRawData(rawDs)

        toReaderOutput(processedRawDs)
    }
}