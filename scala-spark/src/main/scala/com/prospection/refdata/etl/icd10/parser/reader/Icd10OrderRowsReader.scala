package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.icd10.rows.{Icd10OrderRow, RawIcd10OrderRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class Icd10OrderRowsReader(sparkSession: SparkSession, path: String) extends IcdReader[Icd10OrderRow](
    sparkSession,
    path,
) {

    private val MAX_CHARACTERS = 400 //Define in icd10 order file

    override val tableStructs = List(
        IcdOrderStruct(colName = RawIcd10OrderRow.Columns.OrderNumber,position =  1, length = 5),
        IcdOrderStruct(colName = RawIcd10OrderRow.Columns.IcdCode,position =  7, length = 7),
        IcdOrderStruct(colName = RawIcd10OrderRow.Columns.HipaaCovered,position =  15, length = 1),
        IcdOrderStruct(colName = RawIcd10OrderRow.Columns.ShortDescription,position =  17, length = 60),
        IcdOrderStruct(colName = RawIcd10OrderRow.Columns.LongDescription,position =  77, length = MAX_CHARACTERS - 77)

    )
    override val colMappings = List(
        ColumnMapping(RawIcd10OrderRow.Columns.IcdCode, Icd10OrderRow.Columns.IcdCode),
        ColumnMapping(RawIcd10OrderRow.Columns.ShortDescription, Icd10OrderRow.Columns.ShortDescription),
        ColumnMapping(RawIcd10OrderRow.Columns.LongDescription, Icd10OrderRow.Columns.LongDescription),
    )

    override protected def encode(rawDs: DataFrame): Dataset[Icd10OrderRow] = {
        import sparkSession.implicits._
        rawDs.as[Icd10OrderRow]
    }
}