package com.prospection.refdata.etl.icd10.rows

import com.prospection.refdata.etl.common.StandardColumns

case class IcdOutputRow(
                                    code: String,
                                    description: String,
                                    short_description: String,
                                    icd_type: String,
                                    default_ccsr_code: String,
                                    default_ccsr_description: String,
                                    all_ccsr_codes: Array[String],
                                    all_ccsr_descriptions: Array[String],
                                )
object IcdOutputRow {
    object Columns {
        val Code = StandardColumns.Code
        val Description = StandardColumns.Description
        val ShortDescription  = "short_description"
        val IcdType  = "icd_type"
        val DefaultCcsrCode  = "default_ccsr_code"
        val DefaultCcsrDescription  = "default_ccsr_description"
        val AllCcsrCodes = "all_ccsr_codes"
        val AllCcsrDescriptions = "all_ccsr_descriptions"
    }
}
