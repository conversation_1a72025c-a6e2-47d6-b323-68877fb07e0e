package com.prospection.refdata.etl.mdvitem

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.mdvitem.parser.MdvItemParser
import com.prospection.refdata.etl.mdvitem.transformer.MdvItemTransformer
import org.apache.spark.sql.{DataFrame, SparkSession}

class MdvItemEtlJob(val spark: SparkSession, val params: EtlJobParams) extends EtlJob(spark, params) {
    override val name: String = "MDV Item Etl Job"

    final val MDV_ITEM_KEY = "MDV Item"
    final val MDV_HIA_ITEM_KEY = "MDV HIA Item"

    override def parse(): ParserOutput = null

    override def transform(parseOutput: ParserOutput): DataFrame = {

        val mdvItemParseResult = new MdvItemParser(spark, getInputFilePath(MDV_ITEM_KEY)).parse()

        val mdvHiaItemParseResult = new MdvItemParser(spark, getInputFilePath(MDV_HIA_ITEM_KEY)).parse()

        val transformer = new MdvItemTransformer(spark)
        val mdvItemTransformedResult = transformer.transform(mdvItemParseResult)
        val mdvHiaItemTransformedResult = transformer.transform(mdvHiaItemParseResult)

        mdvItemTransformedResult.unionByName(mdvHiaItemTransformedResult).toDF()}
}
