package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbAhfsDescRow, RawFdbAhfsDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbAhfsDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbAhfsDescRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbAhfsDescRow.Columns.Ahfs8, FdbAhfsDescRow.Columns.Ahfs8),
        ColumnMapping(RawFdbAhfsDescRow.Columns.AhfsDesc, FdbAhfsDescRow.Columns.AhfsDesc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbAhfsDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbAhfsDescRow]
    }
}
