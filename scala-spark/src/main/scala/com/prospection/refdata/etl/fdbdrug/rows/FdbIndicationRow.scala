package com.prospection.refdata.etl.fdbdrug.rows

// Represent a row in RINDMMA2_INDCTS_MSTR file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbIndicationRow(
                               dxid: String,
                               indcts_lbl: String,
                               indcts: String,
                           )

object FdbIndicationRow {
    object Columns {
        val Indcts = "indcts"
        val IndctsLbl = "indcts_lbl"
        val Dxid = "dxid"
    }
}

object RawFdbIndicationRow {
    object Columns {
        val Indcts = "_c0"
        val IndctsLbl = "_c2"
        val Dxid = "_c4"
    }
}