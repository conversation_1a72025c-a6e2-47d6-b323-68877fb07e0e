package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{CriteriaParameterRelationshipRow, RawCriteriaParameterRelationshipRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class CriteriaParameterRelationshipReader(sparkSession: SparkSession, path: String) extends CsvReader[CriteriaParameterRelationshipRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawCriteriaParameterRelationshipRow.Columns.CriteriaPrescribingTextId, CriteriaParameterRelationshipRow.Columns.CriteriaPrescribingTextId),
        ColumnMapping(RawCriteriaParameterRelationshipRow.Columns.ParameterPrescribingTextId, CriteriaParameterRelationshipRow.Columns.ParameterPrescribingTextId),
        ColumnMapping(RawCriteriaParameterRelationshipRow.Columns.Position, CriteriaParameterRelationshipRow.Columns.ParameterPosition)
    )

    override protected def encode(rawDs: DataFrame): Dataset[CriteriaParameterRelationshipRow] = {
        import sparkSession.implicits._
        rawDs.as[CriteriaParameterRelationshipRow]
    }

}
