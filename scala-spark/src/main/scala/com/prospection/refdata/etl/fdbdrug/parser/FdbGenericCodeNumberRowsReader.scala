package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbGenericCodeNumberRow, RawFdbGenericCodeNumberRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbGenericCodeNumberRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbGenericCodeNumberRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbGenericCodeNumberRow.Columns.GcnSeqno, FdbGenericCodeNumberRow.Columns.GcnSeqno),
        ColumnMapping(RawFdbGenericCodeNumberRow.Columns.Gcdf, FdbGenericCodeNumberRow.Columns.Gcdf),
        ColumnMapping(RawFdbGenericCodeNumberRow.Columns.Gcrt, FdbGenericCodeNumberRow.Columns.Gcrt),
        ColumnMapping(RawFdbGenericCodeNumberRow.Columns.Hic3Seqn, FdbGenericCodeNumberRow.Columns.Hic3Seqn),
        ColumnMapping(RawFdbGenericCodeNumberRow.Columns.HiclSeqno, FdbGenericCodeNumberRow.Columns.HiclSeqno),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbGenericCodeNumberRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbGenericCodeNumberRow]
    }
}
