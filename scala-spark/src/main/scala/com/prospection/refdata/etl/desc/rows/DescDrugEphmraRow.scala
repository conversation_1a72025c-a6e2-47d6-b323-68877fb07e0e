package com.prospection.refdata.etl.desc.rows

import com.prospection.refdata.etl.common.StandardColumns

case class DescDrugEphmraRow(
                                    code: String,
                                    atc_ephmra: String
                            )

object DescDrugEphmraRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val AtcEphmra: String = StandardColumns.AtcEphmra
    }
}

object RawDescDrugEphmraRow {
    object Columns {
        val DrugCode = "drug_code"
        val AtcCode = "atc_code"
    }
}