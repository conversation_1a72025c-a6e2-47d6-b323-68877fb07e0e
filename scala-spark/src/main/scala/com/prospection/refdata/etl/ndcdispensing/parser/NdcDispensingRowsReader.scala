package com.prospection.refdata.etl.ndcdispensing.parser

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions, StandardColumns}
import com.prospection.refdata.etl.ndcdispensing.rows.{NdcDispensingRow, RawNdcDispensingRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class NdcDispensingRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[NdcDispensingRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.TAB)
) {

    override val colMappings = List(
        ColumnMapping(RawNdcDispensingRow.Columns.ElevenDigitNDCCode, StandardColumns.Code),
        ColumnMapping(RawNdcDispensingRow.Columns.ProprietaryName, StandardColumns.BrandName),
        ColumnMapping(RawNdcDispensingRow.Columns.NonProprietaryName, StandardColumns.DrugName),
        ColumnMapping(RawNdcDispensingRow.Columns.SubstanceName, StandardColumns.IngredientName),
        ColumnMapping(RawNdcDispensingRow.Columns.LabelerName, StandardColumns.ManufacturerName),
        ColumnMapping(RawNdcDispensingRow.Columns.DosageFormName, StandardColumns.DoseForm),
        ColumnMapping(RawNdcDispensingRow.Columns.RouteName, StandardColumns.RouteOfAdministration),
        ColumnMapping(RawNdcDispensingRow.Columns.PackageDescription, StandardColumns.PackageDescription),
        ColumnMapping(RawNdcDispensingRow.Columns.StrengthNumber, StandardColumns.StrengthNumber),
        ColumnMapping(RawNdcDispensingRow.Columns.StrengthUnit, StandardColumns.StrengthUnit),
        ColumnMapping(RawNdcDispensingRow.Columns.PharmaceuticalClasses, StandardColumns.PharmaceuticalClasses),
        ColumnMapping(RawNdcDispensingRow.Columns.IndicationAndUsage, StandardColumns.IndicationDescription),
        ColumnMapping(RawNdcDispensingRow.Columns.MarketingCategoryName, StandardColumns.MarketingCategory),
        ColumnMapping(RawNdcDispensingRow.Columns.ProductTypeName, StandardColumns.ProductTypeName),
    )

    override protected def encode(rawDs: DataFrame): Dataset[NdcDispensingRow] = {
        import sparkSession.implicits._
        rawDs.as[NdcDispensingRow]
    }
}
