package com.prospection.refdata.etl.mdvitem.transformer

import com.prospection.refdata.etl.common.{StandardColumns, StandardValues, Validator}
import com.prospection.refdata.etl.mdvitem.rows.OutputRow
import org.apache.spark.sql.functions.{col, lit, when}
import org.apache.spark.sql.{Dataset, SparkSession}

class MdvItemTransformer(spark: SparkSession) {
    def transform(input: MdvItemTransformerInput): Dataset[OutputRow] = {
        import spark.implicits._

        val drugs = input.drugs.getDataset
            .select(col("*"),
                lit(null).as(StandardColumns.KubunCode),
                lit(null).as(StandardColumns.ProcedureName),
            )
            .withColumn(StandardColumns.DrugUsage,
                when(col(StandardColumns.DrugUsage) === "1", StandardValues.DrugUsage.OralMedication)
                    .when(col(StandardColumns.DrugUsage) === "3", StandardValues.DrugUsage.Others)
                    .when(col(StandardColumns.DrugUsage) === "4", StandardValues.DrugUsage.Injection)
                    .when(col(StandardColumns.DrugUsage) === "6", StandardValues.DrugUsage.ExternalMedication)
                    .when(col(StandardColumns.DrugUsage) === "8", StandardValues.DrugUsage.DentalMedication)
                    .otherwise(StandardValues.Unknown)
            )
            .withColumn(StandardColumns.GenericFlag,
                when(col(StandardColumns.GenericFlag) === "0", StandardValues.GenericFlag.NonGeneric)
                    .when(col(StandardColumns.GenericFlag) === "1", StandardValues.GenericFlag.Generic)
                    .otherwise(StandardValues.Unknown)
            )
            .as[OutputRow]

        val procedures = input.procedures.getDataset
            .select(col("*"),
                lit(null).as(StandardColumns.AtcEphmra),
                lit(null).as(StandardColumns.DrugNameJp),
                lit(null).as(StandardColumns.DrugName),
                lit(null).as(StandardColumns.DrugUsage),
                lit(null).as(StandardColumns.GenericFlag),
                lit(null).as(StandardColumns.DescriptionJp),
            )
            .as[OutputRow]

        drugs.unionByName(procedures)
    }

    // TODO it makes more sense to perform validation while parsing. Move this function to a better place as we add more code.
    def validate(input: MdvItemTransformerInput): Unit = {
        Validator.validate(
            input.drugs,
            input.procedures
        )
    }
}
