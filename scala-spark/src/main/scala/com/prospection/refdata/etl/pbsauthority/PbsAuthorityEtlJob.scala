package com.prospection.refdata.etl.pbsauthority

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams, WareHouseExporter}
import com.prospection.refdata.etl.pbsauthority.datasets.PbsAuthorityRestrictionDataset
import com.prospection.refdata.etl.pbsauthority.parser.writer.{PbsAuthorityHistoricalWriter, PbsAuthorityHistoricalWriterInput}
import com.prospection.refdata.etl.pbsauthority.parser.{PbsAuthorityParser, PbsAuthorityParserOutput}
import com.prospection.refdata.etl.pbsauthority.transformer.{PbsAuthorityTransformer, PbsAuthorityTransformerInput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class PbsAuthorityEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends Etl<PERSON>ob(spark, params) with WareHouseExporter {

    override val name: String = "PBS Authority Etl Job"
    private final val PBS_AUTHORITY_KEY = "PBS Authority"

    override def parse(): ParserOutput = {
        new PbsAuthorityParser(spark, getInputFilePath(PBS_AUTHORITY_KEY)).parse()
    }

    override def transform(parserOutput: ParserOutput): DataFrame = {
        val parseResult = parserOutput.asInstanceOf[PbsAuthorityParserOutput]
        val transformInput = PbsAuthorityTransformerInput(
            new PbsAuthorityRestrictionDataset(parseResult.restrictionDataset)
        )
        new PbsAuthorityTransformer(spark).transform(transformInput).toDF()
    }


    override def storeInWarehouse(parseResult: ParserOutput): Unit = {
        val parserOutput = parseResult.asInstanceOf[PbsAuthorityParserOutput]
        val writerInput = PbsAuthorityHistoricalWriterInput(
            parserOutput.restrictionRawDataset,
        )
        PbsAuthorityHistoricalWriter(writerInput).write(params.warehousePath, getMonthVersion(getVersion))
    }
}
