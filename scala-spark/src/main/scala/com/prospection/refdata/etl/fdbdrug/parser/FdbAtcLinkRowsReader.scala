package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbAtcLinkRow, RawAtcLinkRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbAtcLinkRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbAtcLinkRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawAtcLinkRow.Columns.GcnSeqno, FdbAtcLinkRow.Columns.GcnSeqno),
        ColumnMapping(RawAtcLinkRow.Columns.Atc, FdbAtcLinkRow.Columns.Atc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbAtcLinkRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbAtcLinkRow]
    }
}
