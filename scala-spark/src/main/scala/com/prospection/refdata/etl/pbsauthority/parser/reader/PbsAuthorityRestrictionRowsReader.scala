package com.prospection.refdata.etl.pbsauthority.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, ExcelReader}
import com.prospection.refdata.etl.common.{SparkExcelOptions, StandardColumns}
import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityRestrictionRow
import com.prospection.refdata.etl.pbsauthority.rows.RawPbsAuthorityRestrictionRow.Columns.{RestrictionNumber, RestrictionText}
import org.apache.spark.sql.types._
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class PbsAuthorityRestrictionRowsReader(sparkSession: SparkSession, path: String) extends ExcelReader[PbsAuthorityRestrictionRow](
    sparkSession,
    path,
    SparkExcelOptions(dataAddress = "'Restriction Description'!A5").toMap,
    StructType(Array(
        StructField(RestrictionNumber, StringType),
        StructField(RestrictionText, StringType)))
) {

    override val colMappings = List(
        ColumnMapping(RestrictionNumber, StandardColumns.Code),
        ColumnMapping(RestrictionText, StandardColumns.Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[PbsAuthorityRestrictionRow] = {
        import sparkSession.implicits._
        rawDs.as[PbsAuthorityRestrictionRow]
    }
}
