package com.prospection.refdata.etl.pbsitem.snapshot

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.pbsitem.domain.{OutputRow, SnapshotRow}
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.functions.{coalesce, col, lit, sort_array, when}
import org.apache.spark.sql.types.{ArrayType, DataType, StringType}
import org.apache.spark.sql.{Dataset, SparkSession, functions}

case class PbsItemSnapshotGenerator(
                                       spark: SparkSession,
                                       currentDataset: Dataset[OutputRow],
                                       versionDateStr: String,
                                       previousSnapshotPath: String,
                                   ) {

    private val logger = Logger(classOf[PbsItemSnapshotGenerator])

    private val JOIN_COLUMNS = List(
        JoinColumn(SnapshotRow.Columns.Code, StringType),
        Join<PERSON><PERSON>um<PERSON>(SnapshotRow.Columns.AtcCode, StringType),
        Join<PERSON><PERSON>um<PERSON>(SnapshotRow.Columns.Description, StringType),
        Jo<PERSON><PERSON><PERSON><PERSON><PERSON>(SnapshotRow.Columns.DrugName, StringType),
        Jo<PERSON><PERSON><PERSON><PERSON><PERSON>(SnapshotRow.Columns.BrandName, StringType),
        JoinColumn(SnapshotRow.Columns.IndicationId, StringType),
        JoinColumn(SnapshotRow.Columns.ManufacturerName, StringType),
        JoinColumn(SnapshotRow.Columns.RestrictionFlag, StringType),
        JoinColumn(SnapshotRow.Columns.IndicationDescription, StringType),
        JoinColumn(SnapshotRow.Columns.TreatmentPhase, StringType),
        JoinColumn(SnapshotRow.Columns.LiDrugName, StringType),
        JoinColumn(SnapshotRow.Columns.Formulary, StringType),
        JoinColumn(SnapshotRow.Columns.IndicationCondition, ArrayType(StringType)),
        JoinColumn(SnapshotRow.Columns.IndicationEpisodicity, ArrayType(StringType)),
        JoinColumn(SnapshotRow.Columns.IndicationSeverity, ArrayType(StringType))
    )

    import spark.implicits._

    def generate(): Dataset[SnapshotRow] = {
        logger.info(s"Generating snapshot with previous snapshot path: $previousSnapshotPath")
        val previousSnapshotDs = readPreviousSnapshot()

        val joinExp = JOIN_COLUMNS
            .map(joinColumn => {
                if (joinColumn.dataType == ArrayType(StringType)) {
                    // Special logic for array fields. We want to compare arrays regardless or ordering of the elements. So we sort the arrays before comparing them.
                    sort_array(previousSnapshotDs.col(joinColumn.name)).eqNullSafe(sort_array(currentDataset.col(joinColumn.name)))
                } else {
                    previousSnapshotDs.col(joinColumn.name).eqNullSafe(currentDataset.col(joinColumn.name))
                }
            }).reduce((exp1, exp2) => exp1 && exp2)

        val joinedDs = previousSnapshotDs.join(currentDataset, joinExp, "full_outer")

        val transformDs = joinedDs
            .withColumn(
                SnapshotRow.Columns.DateLastListed,
                when(currentDataset.col(SnapshotRow.Columns.Code).isNotNull, versionDateStr)
                    otherwise col(SnapshotRow.Columns.DateLastListed)
            )
            .withColumn(
                SnapshotRow.Columns.DateFirstListed,
                when(col(SnapshotRow.Columns.DateFirstListed).isNull, versionDateStr)
                    otherwise col(SnapshotRow.Columns.DateFirstListed)
            )

        transformDs
            .where(previousSnapshotDs(SnapshotRow.Columns.Code).isNotNull)
            .select(getSelectCondition(previousSnapshotDs): _*)
            .union(
                transformDs
                    .where(previousSnapshotDs(SnapshotRow.Columns.Code).isNull)
                    .select(getSelectCondition(currentDataset): _*)
            )
            .as[SnapshotRow]
    }

    private def getSelectCondition[T](df: Dataset[T]) = {
        val result = JOIN_COLUMNS.map(column => df.col(column.name)).toBuffer
        result += col(SnapshotRow.Columns.DateFirstListed)
        result += col(SnapshotRow.Columns.DateLastListed)
        result.toList
    }

    private def readPreviousSnapshot(): Dataset[SnapshotRow] = {
        val previousSnapshotDF = spark.read.options(SparkOptions().toMap).parquet(previousSnapshotPath)


        /**
         * Read previous snapshot adding missing columns and converting strings to array for array fields.
         *
         * We've changed field types of the indication_condition, indication_episodicity and indication_severity fields from String to Array[String].
         * But it means that previous snapshots schema is now incompatible with the new schema.
         * So we put some special logic to handle conversion of string fields to array fields.
         * The assumption is that previous snapshots had their arrays represented as strings with "|" as separator.
         *
         * Also, previous snapshots may not have all the fields that are present in the current snapshot. So we need to add missing columns with correct data types.
         */
        JOIN_COLUMNS.foldLeft(previousSnapshotDF) { (currentDF, column) =>
            if (!currentDF.columns.contains(column.name)) {
                currentDF.withColumn(column.name, lit(null).cast(column.dataType))
            } else {
                if (column.dataType == ArrayType(StringType) && currentDF.schema(column.name).dataType != ArrayType(StringType)) {
                    currentDF.withColumn(column.name, functions.split(currentDF.col(column.name), "\\|").cast(column.dataType))
                } else {
                    currentDF
                }
            }
        }.as[SnapshotRow]

    }

    private case class JoinColumn(name: String, dataType: DataType)
}
