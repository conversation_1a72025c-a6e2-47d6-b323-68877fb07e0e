package com.prospection.refdata.etl.fdbdrug

import com.prospection.refdata.etl.common.CodingSystems.FDB_DRUG
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.fdbdrug.parser.{FdbDrugParser, FdbDrugParserOutput}
import com.prospection.refdata.etl.fdbdrug.transformer.{FdbDrugTransformer, FdbDrugTransformerInput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class FdbDrugEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) {

    override val name: String = "FDB ETL job"

    override def parse(): ParserOutput = {
        new FdbDrugParser(spark, getInputFilePath(FDB_DRUG)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {
        val parseResult = parseOutput.asInstanceOf[FdbDrugParserOutput]
        val transformInput = FdbDrugTransformerInput(
            ndcDataset = parseResult.ndcDataset,
            ndcDescDataset = parseResult.ndcDescDataset,
            ndcDeletionReasonDataset = parseResult.ndcDeletionReasonDataset,
            doseDescDataset = parseResult.doseDescDataset,
            genericCodeNumberDataset = parseResult.genericCodeNumberDataset,
            ingredientStrengthDataset = parseResult.ingredientStrengthDataset,
            strengthUomDataset = parseResult.strengthUomDataset,
            routeDescDataset = parseResult.routeDescDataset,
            hierarchicalIngredientListDataset = parseResult.hierarchicalIngredientListDataset,
            hicDescDataset = parseResult.hicDescDataset,
            hicLinkDataset = parseResult.hicLinkDataset,
            atcLinkDataset = parseResult.atcLinkDataset,
            hierarchicalIngredientDataset = parseResult.hierarchicalIngredientDataset,
            ahfsLinkDataset = parseResult.ahfsLinkDataset,
            ahfsDescDataset = parseResult.ahfsDescDataset,
            indicationLinkDataset = parseResult.indicationLinkDataset,
            indicationDataset = parseResult.indicationDataset,
            icdSearchDataset = parseResult.icdSearchDataset,
            diseaseIdentifierDataset = parseResult.diseaseIdentifierDataset,
            medicareDataset = parseResult.medicareDataset,
            medicareReferenceDescDataset = parseResult.medicareReferenceDescDataset,
            etcDataset = parseResult.etcDataset,
            etcIdDataset = parseResult.etcIdDataset,
            uscLinkDataset = parseResult.uscLinkDataset,
            uscDescDataset = parseResult.uscDescDataset,
        )
        new FdbDrugTransformer(spark).transform(transformInput).toDF()
    }
}