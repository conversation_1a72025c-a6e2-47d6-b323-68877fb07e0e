package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RETCTBL0_ETC_ID file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbEtcIdRow(
                        etc_classification: String,
                        etc_description: String,
                    )

object FdbEtcIdRow {
    object Columns {
        val EtcId = StandardColumns.EtcId
        val EtcName = StandardColumns.EtcName
    }
}

object RawFdbEtcIdRow {
    object Columns {
        val EtcId = "_c0"
        val EtcName = "_c1"

    }
}