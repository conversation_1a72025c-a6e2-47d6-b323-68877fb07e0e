package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbDoseDescRow, RawFdbDoseDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbDoseDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbDoseDescRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbDoseDescRow.Columns.Gcdf, FdbDoseDescRow.Columns.Gcdf),
        ColumnMapping(RawFdbDoseDescRow.Columns.GcdfDesc, FdbDoseDescRow.Columns.GcdfDesc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbDoseDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbDoseDescRow]
    }
}
