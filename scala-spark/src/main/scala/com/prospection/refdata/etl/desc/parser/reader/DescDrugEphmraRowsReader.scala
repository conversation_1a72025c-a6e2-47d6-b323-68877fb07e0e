package com.prospection.refdata.etl.desc.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.desc.rows.{DescDrugEphmraRow, RawDescDrugEphmraRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class DescDrugEphmraRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[DescDrugEphmraRow](sparkSession, path, SparkOptions(delimiter = CommonDelimiters.TAB)) {

    override val colMappings: List[ColumnMapping] = List(
        ColumnMapping(RawDescDrugEphmraRow.Columns.DrugCode, DescDrugEphmraRow.Columns.Code),
        ColumnMapping(RawDescDrugEphmraRow.Columns.AtcCode, DescDrugEphmraRow.Columns.AtcEphmra),
    )

    override protected def encode(rawDs: DataFrame): Dataset[DescDrugEphmraRow] = {
        import sparkSession.implicits._
        rawDs.as[DescDrugEphmraRow]
    }
}
