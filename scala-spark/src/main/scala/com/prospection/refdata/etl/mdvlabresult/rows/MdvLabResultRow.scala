package com.prospection.refdata.etl.mdvlabresult.rows

// Represent a row in M_Labo.txt file
case class MdvLabResultRow(
                              code: String,
                              lab_test_name_jp: String,
                              lab_test_name: String,
                              unit: String,
                          )

object MdvLabResultRow {
    object Columns {
        val LabTestNameJP = "lab_test_name_jp"
        val LabTestName = "lab_test_name"
        val Unit = "unit"
    }
}

object RawMdvLabResultRow {
    object Columns {
        val LaboCode = "labocode"
        val LaboName = "laboname"
        val LaboNameEng = "laboname_eng"
        val Unit = "unit"
    }
}