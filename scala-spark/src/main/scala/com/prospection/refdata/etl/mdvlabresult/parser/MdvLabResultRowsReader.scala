package com.prospection.refdata.etl.mdvlabresult.parser

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions, StandardColumns}
import com.prospection.refdata.etl.mdvlabresult.rows.{MdvLabResultRow, RawMdvLabResultRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class MdvLabResultRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[MdvLabResultRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.TAB)
) {

    override val colMappings = List(
        ColumnMapping(RawMdvLabResultRow.Columns.LaboCode, StandardColumns.Code),
        ColumnMapping(RawMdvLabResultRow.Columns.LaboName, MdvLabResultRow.Columns.LabTestNameJP),
        ColumnMapping(RawMdvLabResultRow.Columns.LaboNameEng, MdvLabResultRow.Columns.LabTestName),
        ColumnMapping(RawMdvLabResultRow.Columns.Unit, MdvLabResultRow.Columns.Unit),
    )

    override protected def encode(rawDs: DataFrame): Dataset[MdvLabResultRow] = {
        import sparkSession.implicits._
        rawDs.as[MdvLabResultRow]
    }
}
