package com.prospection.refdata.etl.jmdc.parser.reader

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}

import com.prospection.refdata.etl.jmdc.row.{JmdcDiagnosisRow, RawJmdcDiagnosisRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class JmdcDiagnosisRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[JmdcDiagnosisRow](
    sparkSession,
    path,
    SparkOptions()
) {

    override val colMappings = List(
        ColumnMapping(RawJmdcDiagnosisRow.Columns.StandardDiseaseCode, JmdcDiagnosisRow.Columns.Code),
        ColumnMapping(RawJmdcDiagnosisRow.Columns.StandardDiseaseName, JmdcDiagnosisRow.Columns.DiagnosisName),
        ColumnMapping(RawJmdcDiagnosisRow.Columns.Icd10Level4Code, JmdcDiagnosisRow.Columns.Icd10Code),
    )

    override protected def encode(rawDs: DataFrame): Dataset[JmdcDiagnosisRow] = {
        import sparkSession.implicits._
        rawDs.as[JmdcDiagnosisRow]
    }
}


