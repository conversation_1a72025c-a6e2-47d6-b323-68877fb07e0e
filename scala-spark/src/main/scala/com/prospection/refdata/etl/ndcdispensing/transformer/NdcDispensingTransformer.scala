package com.prospection.refdata.etl.ndcdispensing.transformer

import com.prospection.refdata.etl.common.StandardColumns._
import com.prospection.refdata.etl.common.Validator
import com.prospection.refdata.etl.ndcdispensing.rows.OutputRow
import com.prospection.refdata.etl.ndcdispensing.transformer.NdcDispensingTransformer.ProductTypeBulkIngredient
import org.apache.spark.sql.functions.{col, regexp_replace}
import org.apache.spark.sql.{Dataset, SparkSession}

class NdcDispensingTransformer(spark: SparkSession) {
    def transform(input: NdcDispensingTransformerInput): Dataset[OutputRow] = {
        import spark.implicits._

        input.dispensing.getDataset
            .withColumn(Code, regexp_replace(col(Code), "-", ""))
            .where(col(ProductTypeName) =!= ProductTypeBulkIngredient)
            .drop(ProductTypeName)
            .as[OutputRow]
    }

    // TODO it makes more sense to perform validation while parsing. Move this function to a better place as we add more code.
    def validate(input: NdcDispensingTransformerInput): Unit = {
        Validator.validate(
            input.dispensing,
        )
    }
}

object NdcDispensingTransformer {
    val ProductTypeBulkIngredient = "BULK INGREDIENT"
}
