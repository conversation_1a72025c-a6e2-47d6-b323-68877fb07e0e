package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbAhfsDescRow, FdbAhfsLinkRow, RawFdbAhfsDescRow, RawFdbAhfsLinkRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbAhfsLinkRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbAhfsLinkRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbAhfsLinkRow.Columns.GcnSeqno, FdbAhfsLinkRow.Columns.GcnSeqno),
        ColumnMapping(RawFdbAhfsLinkRow.Columns.Ahfs8, FdbAhfsLinkRow.Columns.Ahfs8),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbAhfsLinkRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbAhfsLinkRow]
    }
}
