package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in item-restriction-relationships_{date}.csv file
case class ItemRestrictionRelationshipRow(
                      code: String, // pbs_code
                      res_code: String // res_code
                  )

object ItemRestrictionRelationshipRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val RestrictionCode: String = PbsApiColumns.RestrictionCode
    }
}

object RawItemRestrictionRelationshipRow {
    object Columns {
        val ItemCode = "pbs_code"
        val ResCode = "res_code"
    }
}