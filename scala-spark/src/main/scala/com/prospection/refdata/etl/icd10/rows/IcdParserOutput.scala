package com.prospection.refdata.etl.icd10.rows

import com.prospection.refdata.etl.common.ParserOutput
import org.apache.spark.sql.Dataset


case class IcdParserOutput(
                                       icd10Dataset: Map[Int, Dataset[Icd10OrderRow]],
                                       icd9LongDescDataset: Dataset[Icd9Row],
                                       icd9ShortDescDataset: Dataset[Icd9Row],
                                       ccsrDataset: Dataset[CcsrRow],
                                       icd9GemDataset: Dataset[IcdGemRow]
                                   ) extends ParserOutput