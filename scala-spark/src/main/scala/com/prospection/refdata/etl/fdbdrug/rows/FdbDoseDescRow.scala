package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RDOSED2_DOSE_DESC file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbDoseDescRow(
                             gcdf: String,
                             dose_form: String,
                         )

object FdbDoseDescRow {
    object Columns {
        val Gcdf = "gcdf"
        val GcdfDesc = StandardColumns.DoseForm
    }
}

object RawFdbDoseDescRow {
    object Columns {
        val Gcdf = "_c0"
        val GcdfDesc = "_c2"
    }
}