package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.CsvReader
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import org.apache.spark.sql.SparkSession

abstract class FdbDrugReader[T](spark: SparkSession, path: String) extends CsvReader[T](
    spark,
    path,
    //There is not header in Fdb data. The column will be _c1, _c2, ..., _cn
    SparkOptions(delimiter = CommonDelimiters.PIPE, header = false)
)