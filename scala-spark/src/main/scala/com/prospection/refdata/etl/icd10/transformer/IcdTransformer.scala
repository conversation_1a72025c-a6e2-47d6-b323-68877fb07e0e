package com.prospection.refdata.etl.icd10.transformer

import com.prospection.refdata.etl.icd10.rows._
import org.apache.spark.sql.functions._
import org.apache.spark.sql._

class IcdTransformer(spark: SparkSession) {
    private val SPACE = " "
    private val SINGLE_QUOTE = "'"
    private val COLUMN_YEAR = "year"

    def transform(input: IcdTransformerInput, icdType10: String, icdType9: String): Dataset[IcdOutputRow] = {
        import spark.implicits._

        val ccsrDs = getCcsrDataset(input)

        val icd9CastedToIcd10 = getIcd9CastedToIcd10(input)

        val icdDataSorted = (input.icd10s + (0 -> icd9CastedToIcd10)).toList
            .sortWith((e1, e2) => e1._1 > e2._1)
            .map(f => f._2
                .withColumn(COLUMN_YEAR, lit(f._1))
                .withColumn(IcdOutputRow.Columns.IcdType, if (f._1 > 0) lit(icdType10) else lit(icdType9))
            )

        val mergeIcd10Ds = icdDataSorted
            .reduce((acc, dataset) => acc.unionByName(dataset))
            //Sort be code and year descending to group
            .orderBy(col(Icd10OrderRow.Columns.IcdCode), col(COLUMN_YEAR).desc)
            .groupBy(Icd10OrderRow.Columns.IcdCode)
            .agg(
                first(Icd10OrderRow.Columns.ShortDescription).as(IcdOutputRow.Columns.ShortDescription),
                first(Icd10OrderRow.Columns.LongDescription).as(IcdOutputRow.Columns.Description),
                first(IcdOutputRow.Columns.IcdType).as(IcdOutputRow.Columns.IcdType),
                first(COLUMN_YEAR).as(COLUMN_YEAR),

            )
            .withColumnRenamed(Icd10OrderRow.Columns.IcdCode, IcdOutputRow.Columns.Code)
            .drop(COLUMN_YEAR)

        mergeIcd10Ds
            .join(ccsrDs, Seq(IcdOutputRow.Columns.Code), "left")
            .withColumn(IcdOutputRow.Columns.AllCcsrCodes, convertNullToEmpty(IcdOutputRow.Columns.AllCcsrCodes))
            .withColumn(IcdOutputRow.Columns.AllCcsrDescriptions, convertNullToEmpty(IcdOutputRow.Columns.AllCcsrDescriptions))
            .as[IcdOutputRow]
    }

    private def convertNullToEmpty(colName: String): Column = {
        when(col(colName).isNull, expr(s"cast(array() as array<string>)")).otherwise(col(colName))
    }

    private def getIcd9CastedToIcd10(input: IcdTransformerInput): DataFrame = {
        val icd9LongDescRenameDs = input.icd9LongDesc
            .withColumnRenamed(Icd9Row.Columns.Description, Icd10OrderRow.Columns.LongDescription)
        val icd9CMShortDescRenameDs = input.icd9ShortDesc
            .withColumnRenamed(Icd9Row.Columns.Description, Icd10OrderRow.Columns.ShortDescription)

        icd9LongDescRenameDs
            .join(icd9CMShortDescRenameDs, Seq(Icd9Row.Columns.IcdCode), "fullouter")
    }

    private def getCcsrDataset(input: IcdTransformerInput): DataFrame = {
        val exceptValues = array(lit(null), lit(SPACE))

        val processedCcsrDataset = removeSingleQuote(input.ccsrDataset).cache()

        val icd9CcsrDataset = getIcd9CcsrDataset(input.icd9Gems, processedCcsrDataset)

        val icd10CcsrDataset = processedCcsrDataset
            .withColumnRenamed(CcsrRow.Columns.IcdCode, IcdOutputRow.Columns.Code)
            .withColumn(IcdOutputRow.Columns.DefaultCcsrCode, lit(col(CcsrRow.Columns.CcsrCategory1)))
            .withColumn(IcdOutputRow.Columns.DefaultCcsrDescription, lit(col(CcsrRow.Columns.CcsrCategory1Description)))

            .withColumn(CcsrRow.Columns.CcsrCategory1, array(CcsrRow.Columns.CcsrCategory1))
            .withColumn(CcsrRow.Columns.CcsrCategory2, array(CcsrRow.Columns.CcsrCategory2))
            .withColumn(CcsrRow.Columns.CcsrCategory3, array(CcsrRow.Columns.CcsrCategory3))
            .withColumn(CcsrRow.Columns.CcsrCategory4, array(CcsrRow.Columns.CcsrCategory4))
            .withColumn(CcsrRow.Columns.CcsrCategory5, array(CcsrRow.Columns.CcsrCategory5))
            .withColumn(CcsrRow.Columns.CcsrCategory6, array(CcsrRow.Columns.CcsrCategory6))

            .withColumn(CcsrRow.Columns.CcsrCategory1Description, array(CcsrRow.Columns.CcsrCategory1Description))
            .withColumn(CcsrRow.Columns.CcsrCategory2Description, array(CcsrRow.Columns.CcsrCategory2Description))
            .withColumn(CcsrRow.Columns.CcsrCategory3Description, array(CcsrRow.Columns.CcsrCategory3Description))
            .withColumn(CcsrRow.Columns.CcsrCategory4Description, array(CcsrRow.Columns.CcsrCategory4Description))
            .withColumn(CcsrRow.Columns.CcsrCategory5Description, array(CcsrRow.Columns.CcsrCategory5Description))
            .withColumn(CcsrRow.Columns.CcsrCategory6Description, array(CcsrRow.Columns.CcsrCategory6Description))

        icd10CcsrDataset.unionByName(icd9CcsrDataset)
            .withColumn(IcdOutputRow.Columns.AllCcsrCodes,
                array_except(
                    array_distinct(flatten(array(
                        CcsrRow.Columns.CcsrCategory1,
                        CcsrRow.Columns.CcsrCategory2,
                        CcsrRow.Columns.CcsrCategory3,
                        CcsrRow.Columns.CcsrCategory4,
                        CcsrRow.Columns.CcsrCategory5,
                        CcsrRow.Columns.CcsrCategory6
                    ))),
                    exceptValues
                )
            )
            .withColumn(IcdOutputRow.Columns.AllCcsrDescriptions,
                array_except(
                    array_distinct(flatten(array(
                        CcsrRow.Columns.CcsrCategory1Description,
                        CcsrRow.Columns.CcsrCategory2Description,
                        CcsrRow.Columns.CcsrCategory3Description,
                        CcsrRow.Columns.CcsrCategory4Description,
                        CcsrRow.Columns.CcsrCategory5Description,
                        CcsrRow.Columns.CcsrCategory6Description
                    ))),
                    exceptValues
                )
            )
            .drop(
                CcsrRow.Columns.CcsrCategory1,
                CcsrRow.Columns.CcsrCategory2,
                CcsrRow.Columns.CcsrCategory3,
                CcsrRow.Columns.CcsrCategory4,
                CcsrRow.Columns.CcsrCategory5,
                CcsrRow.Columns.CcsrCategory6,
                CcsrRow.Columns.CcsrCategory1Description,
                CcsrRow.Columns.CcsrCategory2Description,
                CcsrRow.Columns.CcsrCategory3Description,
                CcsrRow.Columns.CcsrCategory4Description,
                CcsrRow.Columns.CcsrCategory5Description,
                CcsrRow.Columns.CcsrCategory6Description)
    }

    private def getIcd9CcsrDataset(icd9Gems: Dataset[IcdGemRow], ccsrDataset: Dataset[Row]): DataFrame = {
        icd9Gems
            //Only get row which icd9 is not exist in icd10 (icd9 is replaced by icd10 E8021, E8331, E8381, E8411, E8849, E8889, E896
            .join(ccsrDataset, icd9Gems(IcdGemRow.Columns.Icd9Code) ===  ccsrDataset(CcsrRow.Columns.IcdCode), "leftanti")
            .join(ccsrDataset, Seq(IcdGemRow.Columns.IcdCode), "inner")
            .drop(IcdGemRow.Columns.IcdCode)
            //Rename icd9 column to code
            .withColumnRenamed(IcdGemRow.Columns.Icd9Code, CcsrRow.Columns.IcdCode)
            .groupBy(CcsrRow.Columns.IcdCode)
            .agg(
                first(CcsrRow.Columns.CcsrCategory1).name(IcdOutputRow.Columns.DefaultCcsrCode),
                first(CcsrRow.Columns.CcsrCategory1Description).name(IcdOutputRow.Columns.DefaultCcsrDescription),

                collect_set(CcsrRow.Columns.CcsrCategory1).name(CcsrRow.Columns.CcsrCategory1),
                collect_set(CcsrRow.Columns.CcsrCategory2).name(CcsrRow.Columns.CcsrCategory2),
                collect_set(CcsrRow.Columns.CcsrCategory3).name(CcsrRow.Columns.CcsrCategory3),
                collect_set(CcsrRow.Columns.CcsrCategory4).name(CcsrRow.Columns.CcsrCategory4),
                collect_set(CcsrRow.Columns.CcsrCategory5).name(CcsrRow.Columns.CcsrCategory5),
                collect_set(CcsrRow.Columns.CcsrCategory6).name(CcsrRow.Columns.CcsrCategory6),

                collect_set(CcsrRow.Columns.CcsrCategory1Description).name(CcsrRow.Columns.CcsrCategory1Description),
                collect_set(CcsrRow.Columns.CcsrCategory2Description).name(CcsrRow.Columns.CcsrCategory2Description),
                collect_set(CcsrRow.Columns.CcsrCategory3Description).name(CcsrRow.Columns.CcsrCategory3Description),
                collect_set(CcsrRow.Columns.CcsrCategory4Description).name(CcsrRow.Columns.CcsrCategory4Description),
                collect_set(CcsrRow.Columns.CcsrCategory5Description).name(CcsrRow.Columns.CcsrCategory5Description),
                collect_set(CcsrRow.Columns.CcsrCategory6Description).name(CcsrRow.Columns.CcsrCategory6Description),
            )
    }

    private def removeSingleQuote(ds: Dataset[CcsrRow]): DataFrame = {
        //'ICD-10-CM CODE'
        //'A000'
        ds.select(
            ds.columns.map(colName => {
                lit(when(col(colName).startsWith(SINGLE_QUOTE) && col(colName).endsWith(SINGLE_QUOTE), expr(s"substring($colName, 2, length($colName)-2)"))
                    .otherwise(col(colName))).name(colName)
            }).toIndexedSeq: _*
        )
    }

}
