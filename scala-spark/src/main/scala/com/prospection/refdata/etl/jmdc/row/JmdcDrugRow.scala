package com.prospection.refdata.etl.jmdc.row

import com.prospection.refdata.etl.common.StandardColumns

case class JmdcDrugRow(
                           code: String,
                           brand_name: String,
                           description: String,
                           atc_code: String,
                           atc_ephmra: String,
                           nhi_code: String,
                           strength_unit: String,
                           strength_number: String,
                           generic_flag: String,
                           drug_name: String,
                           drug_usage: String,
                           dose_form_sml: String,
                           dose_form_med: String,
                          )

object JmdcDrugRow {
    object Columns {
        val Code = StandardColumns.Code
        val BranchName = StandardColumns.BrandName
        val Description = StandardColumns.Description
        val AtcCode = StandardColumns.AtcCode
        val AtcEpharm = StandardColumns.AtcEphmra
        val NhiCode = StandardColumns.NhiCode
        val StrengthUnit = StandardColumns.StrengthUnit
        val StrengthNumber = StandardColumns.StrengthNumber
        val GenericFlag = StandardColumns.GenericFlag
        val DrugName = StandardColumns.DrugName
        val DrugUsage = StandardColumns.DrugUsage
        val DoseFormSml = StandardColumns.DoseFormSml
        val DoseFormMed = StandardColumns.DoseFormMed
    }
}

object RawJmdcDrugRow{
    object Columns {
        val JmdcDrugCode = "jmdc_drug_code"
        val DrugName = "drug_name"
        val AtcLevel4Code = "atc_level4_code"
        val WhoAtcCode = "who_atc_code"
        val DrugCode = "drug_code"
        val GeneralName = "general_name"
        val BrandName = "brand_name"
        val GenericDrugFlag = "generic_drug_flag"
        val FormulationLargeClassificationName = "formulation_large_classification_name"
        val FormulationMediumClassificationName = "formulation_medium_classification_name"
        val FormulationSmallClassificationName = "formulation_small_classification_name"
        val Titer = "titer"
        val TiterUnit = "titer_unit"
    }
}