package com.prospection.refdata.etl.jmdc.row

import com.prospection.refdata.etl.common.StandardColumns

case class JmdcProcedureRow(
                           code: String,
                           description: String,
                           kubuncode: String,
                           procedure_cat_med: String,
                           procedure_cat_sml: String,
                           procedure_cat_subclass: String,
                           procedure_version: String,

                          )

object JmdcProcedureRow {
    object Columns {
        val Code = StandardColumns.Code
        val Description = StandardColumns.Description
        val KubunCode = StandardColumns.KubunCode
        val ProcedureCatMed = StandardColumns.ProcedureCatMed
        val ProcedureCatSml = StandardColumns.ProcedureCatSml
        val ProcedureCatSubclass =StandardColumns.ProcedureCatSubclass
        val ProcedureVersion =StandardColumns.ProcedureVersion
    }
}

object RawJmdcProcedureRow{
    object Columns {
        val StandardDiseaseCode = "standardized_procedure_code"
        val StandardizedProcedureName = "standardized_procedure_name"
        val ProcedureCode = "procedure_code"
        val ProcedureCategoryMediumClassificationName = "procedure_category_medium_classification_name"
        val ProcedureCategorySmallClassificationName = "procedure_category_small_classification_name"
        val ProcedureCategorySubclassificationName = "procedure_category_subclassification_name"
        val StandardizedProcedureVersion = "standardized_procedure_version"

    }
}