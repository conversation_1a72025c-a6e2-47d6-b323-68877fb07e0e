package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.ItemRow
import com.prospection.refdata.etl.pbsitem.domain.ItemRow.Columns
import org.apache.spark.sql.Dataset

class ItemDataset(dataset: Dataset[ItemRow]) extends EtlDataset[ItemRow](dataset, "items") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Code,
        Columns.DrugName,
        Columns.BrandName,
        Columns.RestrictionFlag,
        Columns.ManufacturerId,
        Columns.LiDrugName,
        Columns.Formulary
    )
}
