package com.prospection.refdata.etl.icd10.parser

import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.icd10.parser.reader.{DxCcsrRowReader, Icd10Order<PERSON>owsReader, Icd9CMRowsReader, Icd9GemRowsReader}
import com.prospection.refdata.etl.icd10.rows.{Icd10OrderRow, IcdParserOutput}
import com.typesafe.scalalogging.Logger
import org.apache.spark.sql.{Dataset, SparkSession}

import java.time.LocalDate

class IcdDiagnosisParser(
                            sparkSession: SparkSession,
                            pathPrefix: String,
                            versionDate: LocalDate,
                        ) {

    private val ICD10CM_MIN_YEAR = 2014
    private final val TransformedPath = s"$pathPrefix/transformed"

    private val logger = Logger(classOf[IcdDiagnosisParser])

    def parse(): IcdParserOutput = {

        val icd10CMDs = getIcd10CMOrderDataset(versionDate)

        //Only CMS32_DESC_LONG_DX is stored as ISO-8859
        val icd9CMLongDescData = Icd9CMRowsReader(
            sparkSession,
            s"$TransformedPath/CMS32_DESC_LONG_DX.txt",
            SparkOptions(header = false, delimiter = CommonDelimiters.BREAK_LINE, encoding = "ISO-8859-1")
        ).read()

        val icd9CMShortDescData = Icd9CMRowsReader(sparkSession, s"$TransformedPath/CMS32_DESC_SHORT_DX.txt").read()

        val dxCcsrData = DxCcsrRowReader(sparkSession, s"$TransformedPath/DXCCSR*.csv").read()

        val icd9GemData =  Icd9GemRowsReader(sparkSession, s"$TransformedPath/2018_I9gem.txt").read()

        IcdParserOutput(
            icd10CMDs,
            icd9CMLongDescData.dataset,
            icd9CMShortDescData.dataset,
            dxCcsrData.dataset,
            icd9GemData.dataset
        )
    }

    private def getIcd10CMOrderDataset (versionDate: LocalDate): Map[Int, Dataset[Icd10OrderRow]] = {

        var icd10CMDs = Map[Int, Dataset[Icd10OrderRow]]()
        val currentYear = versionDate.getYear

        val datasetOfNextYear = getIcd10CMOrderDatasetOfNextYear(currentYear)

        if (datasetOfNextYear != null) {
            icd10CMDs += (currentYear + 1 -> datasetOfNextYear)
        }

        for (year <- ICD10CM_MIN_YEAR to versionDate.getYear) {
            val dataSetByYear = Icd10OrderRowsReader(sparkSession, s"$TransformedPath/icd10cm_order_$year.txt").read()
            icd10CMDs += (year -> dataSetByYear.dataset)
        }

        icd10CMDs
    }

    //We need get the order on next year because it is provided at current year
    // Example: ICD10CM Order 2023 is provided at 2022-07-27.
    private def getIcd10CMOrderDatasetOfNextYear(currentYear: Int): Dataset[Icd10OrderRow] = {
        val nextYear = currentYear + 1
        try {
            Icd10OrderRowsReader(sparkSession, s"$TransformedPath/icd10cm_order_$nextYear.txt").read().dataset
        } catch {
            case _: Throwable => logger.info(s"Icd10 CM Order file of $nextYear is not exits")
        }

        null
    }
}