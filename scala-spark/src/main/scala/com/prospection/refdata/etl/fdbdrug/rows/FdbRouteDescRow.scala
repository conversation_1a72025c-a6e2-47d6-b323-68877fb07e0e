package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RROUTED3_ROUTE_DESC file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbRouteDescRow(
                              gcrt: String,
                              route_of_administration: String,
                          )

object FdbRouteDescRow {
    object Columns {
        val Gcrt = "gcrt"
        val GcrtDesc = StandardColumns.RouteOfAdministration
    }
}

object RawFdbRouteDescRow {
    object Columns {
        val Gcrt = "_c0"
        val GcrtDesc = "_c3"
    }
}