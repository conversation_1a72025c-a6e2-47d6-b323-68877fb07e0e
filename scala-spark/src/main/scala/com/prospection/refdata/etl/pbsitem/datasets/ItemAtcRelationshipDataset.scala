package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.ItemAtcRelationshipRow.Columns
import com.prospection.refdata.etl.pbsitem.domain.ItemAtcRelationshipRow
import org.apache.spark.sql.Dataset

class ItemAtcRelationshipDataset(dataset: Dataset[ItemAtcRelationshipRow]) extends EtlDataset[ItemAtcRelationshipRow](dataset, "item-atc-relationships") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Code,
        Columns.AtcCode
    )
}
