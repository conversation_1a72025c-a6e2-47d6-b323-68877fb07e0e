package com.prospection.refdata.etl.fdbdrug.transformer

import com.prospection.refdata.etl.common.StandardColumns
import com.prospection.refdata.etl.fdbdrug.rows._
import org.apache.spark.sql.functions._
import org.apache.spark.sql.{Dataset, Encoders, Row, SparkSession}

class FdbDrugTransformer(spark: SparkSession) {
    def transform(input: FdbDrugTransformerInput): Dataset[FdbDrugOutputRow] = {
        import spark.implicits._

        val fieldNames = Encoders.product[FdbDrugOutputRow].schema.fieldNames

        //Cache to improve performance
        val ndcSelectedDs = getNdcSelectedDataset(input).cache()

        val ndcInformationDs = getNdcInformationDataset(input, ndcSelectedDs)

        val ndcAdditionInformationDs = getNdcAdditionInformationDataset(input, ndcSelectedDs)

        val ndcStrengthDs = getNdcStrengthDataset(input, ndcSelectedDs)

        val indicationLinkDs = getIndicationLinkDataset(input, ndcSelectedDs)

        val etcLinkDs = getEtcLinkDataset(input, ndcSelectedDs)

        val ahfsLinkDs = getAhfsLinkDataset(input, ndcSelectedDs)

        // Relationship between FdbNdcRow.Columns.Ndc to FdbNdcRow.Columns.GcnSeqno is many to one. So no need groupBy
        ndcInformationDs
            .join(ndcAdditionInformationDs, Seq(FdbGenericCodeNumberRow.Columns.GcnSeqno), "left")
            .join(ndcStrengthDs, Seq(FdbIngredientStrengthRow.Columns.GcnSeqno), "left")
            .join(indicationLinkDs, Seq(FdbIndicationLinkRow.Columns.GcnSeqno), "left")
            .join(etcLinkDs, Seq(FdbEtcRow.Columns.GcnSeqno), "left")
            .join(ahfsLinkDs, Seq(FdbAhfsLinkRow.Columns.GcnSeqno), "left")

            //Only keep the needed fields
            .select(fieldNames.head, fieldNames.tail.toIndexedSeq: _*)
            .as[FdbDrugOutputRow]
    }

    private def getNdcSelectedDataset(input: FdbDrugTransformerInput): Dataset[Row] = {
        input.ndcDataset
            .where(col(FdbNdcRow.Columns.Obsdtec) >= "20150101"
                || col(FdbNdcRow.Columns.Obsdtec) === "00000000"
                || col(FdbNdcRow.Columns.Obsdtec).isNull)
            .select(FdbNdcRow.Columns.Ndc, FdbNdcRow.Columns.GcnSeqno)
    }

    private def getNdcInformationDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        val ndcDeletionReason = getNdcDeletionReasonDataset(input, ndcSelectedDs)

        val medicareDs = getMedicareDataset(input, ndcSelectedDs)

        val uscLinkDs = getUscLinkDataset(input, ndcSelectedDs)

        input.ndcDataset
            .join(ndcSelectedDs, Seq(FdbNdcRow.Columns.Ndc), "leftsemi")
            .join(input.atcLinkDataset, Seq(FdbAtcLinkRow.Columns.GcnSeqno), "left")
            .join(input.ndcDescDataset, Seq(FdbNdcDescRow.Columns.Lblrid), "left")
            .join(ndcDeletionReason, Seq(FdbNdcDeletionReasonRow.Columns.Ndc), "left")
            .join(medicareDs, Seq(FdbMedicareRow.Columns.Ndc), "left")
            .join(uscLinkDs, Seq(FdbUscNdcLinkRow.Columns.Ndc), "left")
            .groupBy(FdbNdcRow.Columns.Ndc, FdbNdcRow.Columns.GcnSeqno)
            .agg(
                first(FdbNdcRow.Columns.Bn).as(FdbNdcRow.Columns.Bn),
                first(FdbNdcRow.Columns.Ln).as(FdbNdcRow.Columns.Ln),
                first(FdbNdcRow.Columns.Ps).cast("double").as(FdbNdcRow.Columns.Ps),
                first(FdbNdcRow.Columns.Pd).as(FdbNdcRow.Columns.Pd),
                first(FdbNdcRow.Columns.Obsdtec).as(FdbNdcRow.Columns.Obsdtec),
                first(FdbAtcLinkRow.Columns.Atc).as(FdbAtcLinkRow.Columns.Atc),
                first(FdbNdcDescRow.Columns.Mfg).as(FdbNdcDescRow.Columns.Mfg),
                first(FdbNdcDeletionReasonRow.Columns.NdcDeleteDate).as(FdbNdcDeletionReasonRow.Columns.NdcDeleteDate),
                first(FdbMedicareReferenceDescRow.Columns.McrBc).as(FdbMedicareReferenceDescRow.Columns.McrBc),
                first(FdbMedicareReferenceDescRow.Columns.McrBcdesc).as(FdbMedicareReferenceDescRow.Columns.McrBcdesc),
                first(FdbUscNdcLinkRow.Columns.Usc).as(FdbUscNdcLinkRow.Columns.Usc),
                first(FdbUscDescRow.Columns.UscDesc40).as(FdbUscDescRow.Columns.UscDesc40),
            )
            .withColumn(FdbNdcRow.Columns.Ps, col(FdbNdcRow.Columns.Ps).cast("string"))
    }

    private def getNdcAdditionInformationDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        val hicDs = input.hicLinkDataset
            .join(input.hicDescDataset, Seq(FdbHicDescRow.Columns.HicSeqn), "left")
            .groupBy(FdbHicLinkRow.Columns.HiclSeqno)
            .agg(
                collect_list(FdbHicDescRow.Columns.HicDesc).as(FdbHicDescRow.Columns.HicDesc),
            )

        input.genericCodeNumberDataset
            .join(ndcSelectedDs, Seq(FdbNdcRow.Columns.GcnSeqno), "leftsemi")
            .join(input.doseDescDataset, Seq(FdbDoseDescRow.Columns.Gcdf), "left")
            .join(input.routeDescDataset, Seq(FdbRouteDescRow.Columns.Gcrt), "left")
            .join(input.hierarchicalIngredientDataset, Seq(FdbHierarchicalIngredientRow.Columns.Hic3Seqn), "left")
            .join(input.hierarchicalIngredientListDataset, Seq(FdbHierarchicalIngredientListRow.Columns.HiclSeqno), "left")
            .join(hicDs, Seq(FdbHicLinkRow.Columns.HiclSeqno), "left")
            .groupBy(FdbGenericCodeNumberRow.Columns.GcnSeqno)
            .agg(
                first(FdbDoseDescRow.Columns.GcdfDesc).as(FdbDoseDescRow.Columns.GcdfDesc),
                first(FdbRouteDescRow.Columns.GcrtDesc).as(FdbRouteDescRow.Columns.GcrtDesc),
                first(FdbHierarchicalIngredientRow.Columns.Hic3).as(FdbHierarchicalIngredientRow.Columns.Hic3),
                first(FdbHierarchicalIngredientRow.Columns.Hic3Desc).as(FdbHierarchicalIngredientRow.Columns.Hic3Desc),
                first(FdbHierarchicalIngredientListRow.Columns.Gnn60).as(FdbHierarchicalIngredientListRow.Columns.Gnn60),
                collect_list(FdbHicDescRow.Columns.HicDesc).as(FdbHicDescRow.Columns.HicDesc),
            )
            //[[x],[y]] => [x,y]
            .withColumn(FdbHicDescRow.Columns.HicDesc, flatten(col(FdbHicDescRow.Columns.HicDesc)))
    }

    private def getNdcDeletionReasonDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        ndcSelectedDs
            .join(input.ndcDeletionReasonDataset, Seq(FdbNdcDeletionReasonRow.Columns.Ndc), "left")
            .groupBy(FdbNdcDeletionReasonRow.Columns.Ndc)
            .agg(
                collect_list(FdbNdcDeletionReasonRow.Columns.NdcDeleteDate).as(FdbNdcDeletionReasonRow.Columns.NdcDeleteDate)
            )
    }

    private def getMedicareDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        input.medicareDataset
            .join(ndcSelectedDs, Seq(FdbMedicareRow.Columns.Ndc), "leftsemi")
            .join(input.medicareReferenceDescDataset, Seq(FdbMedicareReferenceDescRow.Columns.McrRef), "left")
            .groupBy(FdbMedicareRow.Columns.Ndc)
            .agg(
                collect_list(FdbMedicareReferenceDescRow.Columns.McrBc).as(FdbMedicareReferenceDescRow.Columns.McrBc),
                collect_list(FdbMedicareReferenceDescRow.Columns.McrBcdesc).as(FdbMedicareReferenceDescRow.Columns.McrBcdesc)
            )
    }

    private def getUscLinkDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        input.uscLinkDataset
            .join(ndcSelectedDs, Seq(FdbUscNdcLinkRow.Columns.Ndc), "leftsemi")
            .join(input.uscDescDataset, Seq(FdbUscDescRow.Columns.Usc), "left")
            .groupBy(FdbUscNdcLinkRow.Columns.Ndc)
            .agg(
                first(FdbUscNdcLinkRow.Columns.Usc).as(FdbUscNdcLinkRow.Columns.Usc),
                first(FdbUscDescRow.Columns.UscDesc40).as(FdbUscDescRow.Columns.UscDesc40)
            )
    }

    private def getNdcStrengthDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        val ingredientStrengthDs = input.ingredientStrengthDataset
        val strengthUomDs = input.strengthUomDataset

        ingredientStrengthDs
            .join(ndcSelectedDs, Seq(FdbNdcRow.Columns.GcnSeqno), "leftsemi")
            .join(strengthUomDs, ingredientStrengthDs(FdbIngredientStrengthRow.Columns.StrengthUomId) === strengthUomDs(FdbStrengthUomRow.Columns.UomId), "left")
            // Set empty string for strength_unit_description_abbreviation if it is null
            .na.fill("", Seq(FdbStrengthUomRow.Columns.UomPreferredDesc))
            .groupBy(FdbIngredientStrengthRow.Columns.GcnSeqno)
            .agg(
                collect_list(FdbIngredientStrengthRow.Columns.Strength).cast("array<double>").as(FdbIngredientStrengthRow.Columns.Strength),
                collect_list(FdbStrengthUomRow.Columns.UomPreferredDesc).as(FdbStrengthUomRow.Columns.UomPreferredDesc)
            )
            .withColumn(FdbIngredientStrengthRow.Columns.Strength, col(FdbIngredientStrengthRow.Columns.Strength).cast("array<string>"))
    }

    private def getIndicationLinkDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        val indicationDs = input.indicationDataset
            .join(input.diseaseIdentifierDataset, Seq(FdbDiseaseIdentifierRow.Columns.Dxid), "left")
            .withColumn(StandardColumns.OnLabelIndication, when(
                col(FdbIndicationRow.Columns.IndctsLbl) === "L",
                input.diseaseIdentifierDataset(FdbDiseaseIdentifierRow.Columns.DxidDesc100)
            ).otherwise(null))
            .withColumn(StandardColumns.OffLabelIndication, when(
                col(FdbIndicationRow.Columns.IndctsLbl) === "U",
                input.diseaseIdentifierDataset(FdbDiseaseIdentifierRow.Columns.DxidDesc100)
            ).otherwise(null))

        val icdSearchesDs = input.icdSearchDataset
            .filter(col(FdbIcdSearchRow.Columns.FmlNavCode) === "01")
        input.indicationLinkDataset
            .join(ndcSelectedDs, Seq(FdbNdcRow.Columns.GcnSeqno), "leftsemi")
            .join(indicationDs, Seq(FdbIndicationRow.Columns.Indcts), "left")
            .join(icdSearchesDs, icdSearchesDs(FdbIcdSearchRow.Columns.RelatedDxid) === indicationDs(FdbIndicationRow.Columns.Dxid), "left")
            .groupBy(FdbIndicationLinkRow.Columns.GcnSeqno)
            .agg(
                //Using set to remove duplicate icd_code
                collect_set(FdbIcdSearchRow.Columns.SearchIcdCd).as(FdbIcdSearchRow.Columns.SearchIcdCd),
                collect_set(StandardColumns.OnLabelIndication).as(StandardColumns.OnLabelIndication),
                collect_set(StandardColumns.OffLabelIndication).as(StandardColumns.OffLabelIndication),
            )
    }

    private def getEtcLinkDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        input.etcDataset
            .join(ndcSelectedDs, Seq(FdbNdcRow.Columns.GcnSeqno), "leftsemi")
            .join(input.etcIdDataset, Seq(FdbEtcIdRow.Columns.EtcId), "left")
            .groupBy(FdbEtcRow.Columns.GcnSeqno)
            .agg(
                collect_list(FdbEtcIdRow.Columns.EtcId).as(FdbEtcIdRow.Columns.EtcId),
                collect_list(FdbEtcIdRow.Columns.EtcName).as(FdbEtcIdRow.Columns.EtcName)
            )
    }

    private def getAhfsLinkDataset(input: FdbDrugTransformerInput, ndcSelectedDs: Dataset[Row]): Dataset[Row] = {
        input.ahfsLinkDataset
            .join(ndcSelectedDs, Seq(FdbNdcRow.Columns.GcnSeqno), "leftsemi")
            .join(input.ahfsDescDataset, Seq(FdbAhfsDescRow.Columns.Ahfs8), "left")
            .groupBy(FdbAhfsLinkRow.Columns.GcnSeqno)
            .agg(
                collect_list(FdbAhfsDescRow.Columns.Ahfs8).as(FdbAhfsDescRow.Columns.Ahfs8),
                collect_list(FdbAhfsDescRow.Columns.AhfsDesc).as(FdbAhfsDescRow.Columns.AhfsDesc),
            )
    }
}
