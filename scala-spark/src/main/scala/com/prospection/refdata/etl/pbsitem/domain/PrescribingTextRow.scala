package com.prospection.refdata.etl.pbsitem.domain

// Represent a row in prescribing-texts file
case class PrescribingTextRow(
                                 prescribing_text_id: String, // prescribing_text_id
                                 prescribing_type: String, // prescribing_type
                                 prescribing_txt: String // prescribing_txt
                                                    )

object PrescribingTextRow {
    object Columns {
        val PrescribingTextId: String = PbsApiColumns.PrescribingTextId
        val PrescribingType: String = PbsApiColumns.PrescribingType
        val PrescribingText: String = PbsApiColumns.PrescribingText
    }
}

object RawPrescribingTextRow {
    object Columns {
        val PrescribingTextId = "prescribing_txt_id"
        val PrescribingType = "prescribing_type"
        val PrescribingText = "prescribing_txt"
    }
}