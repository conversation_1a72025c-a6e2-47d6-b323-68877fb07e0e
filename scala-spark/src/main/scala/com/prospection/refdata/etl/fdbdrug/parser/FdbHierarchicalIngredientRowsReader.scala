package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbHierarchicalIngredientRow, RawFdbHierarchicalIngredientRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbHierarchicalIngredientRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbHierarchicalIngredientRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbHierarchicalIngredientRow.Columns.Hic3Seqn, FdbHierarchicalIngredientRow.Columns.Hic3Seqn),
        ColumnMapping(RawFdbHierarchicalIngredientRow.Columns.Hic3, FdbHierarchicalIngredientRow.Columns.Hic3),
        ColumnMapping(RawFdbHierarchicalIngredientRow.Columns.Hic3Desc, FdbHierarchicalIngredientRow.Columns.Hic3Desc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbHierarchicalIngredientRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbHierarchicalIngredientRow]
    }
}
