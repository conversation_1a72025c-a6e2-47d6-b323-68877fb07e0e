package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns

case class SnapshotRow(
                          code: String,
                          description: String,
                          atc_code: String,
                          drug_name: String,
                          brand_name: String,
                          restriction_flag: String,
                          manufacturer_name: String,
                          indication_id: String,
                          indication_description: String,
                          date_last_listed: String,
                          date_first_listed: String,
                          treatment_phase: String,
                          li_drug_name: String,
                          formulary: String,
                          indication_condition: Seq[String],
                          indication_episodicity: Seq[String],
                          indication_severity: Seq[String]
                      )

object SnapshotRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val Description: String = StandardColumns.Description
        val AtcCode: String = StandardColumns.AtcCode
        val DrugName: String = StandardColumns.DrugName
        val BrandName: String = StandardColumns.BrandName
        val RestrictionFlag: String = StandardColumns.RestrictionFlag
        val ManufacturerName: String = StandardColumns.ManufacturerName
        val IndicationId: String = StandardColumns.IndicationId
        val IndicationDescription: String = StandardColumns.IndicationDescription
        val DateLastListed = "date_last_listed"
        val DateFirstListed = "date_first_listed"
        val TreatmentPhase: String = PbsApiColumns.TreatmentPhase
        val LiDrugName: String = PbsApiColumns.LiDrugName
        val Formulary: String = PbsApiColumns.Formulary
        val IndicationCondition: String = StandardColumns.IndicationCondition
        val IndicationEpisodicity: String = StandardColumns.IndicationEpisodicity
        val IndicationSeverity: String = StandardColumns.IndicationSeverity
    }
}