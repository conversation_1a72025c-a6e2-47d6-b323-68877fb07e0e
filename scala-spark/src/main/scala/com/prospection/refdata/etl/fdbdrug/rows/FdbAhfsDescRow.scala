package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RAHFSD1_DESC file (Enhanced Therapeutic Classification)
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbAhfsDescRow(
                             ahfs_classification: String,
                             ahfs_description: String,
                         )

object FdbAhfsDescRow {
    object Columns {
        val Ahfs8 = StandardColumns.Ahfs8
        val AhfsDesc = StandardColumns.AhfsDescription

    }
}

object RawFdbAhfsDescRow {
    object Columns {
        val Ahfs8 = "_c0"
        val AhfsDesc = "_c1"
    }
}