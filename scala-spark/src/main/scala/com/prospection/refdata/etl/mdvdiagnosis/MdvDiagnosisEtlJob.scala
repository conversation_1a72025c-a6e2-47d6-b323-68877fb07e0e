package com.prospection.refdata.etl.mdvdiagnosis

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.mdvdiagnosis.parser.MdvDiagnosisParser
import com.prospection.refdata.etl.mdvdiagnosis.rows.OutputRow
import com.prospection.refdata.etl.mdvdiagnosis.transformer.MdvDiagnosisTransformer
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

class MdvDiagnosisEtlJob(val spark: SparkSession, val params: EtlJobParams) extends
    EtlJob(spark, params) {

    override val name: String = "MDV Diagnosis Etl Job"
    final val MDV_DISEASE_KEY = "MDV Disease"
    final val MDV_HIA_DISEASE_KEY = "MDV HIA Disease"

    override def parse(): ParserOutput = null

    override def transform(parseOutput: ParserOutput): DataFrame = {
        val transformer = new MdvDiagnosisTransformer(spark)
        val transformedMdvDisease = transformByCategory(transformer, MDV_DISEASE_KEY)
        val transformedMdvHiaDisease = transformByCategory(transformer, MDV_HIA_DISEASE_KEY)

        transformedMdvDisease.unionByName(transformedMdvHiaDisease)
            .distinct() // Some duplicated rows after merging two of them need to remove
            .toDF()
    }

    private def transformByCategory(transformer: MdvDiagnosisTransformer, category: String): Dataset[OutputRow] = {
        val parser = new MdvDiagnosisParser(spark, getInputFilePath(category))
        val parseResult = parser.parse()
        transformer.transform(parseResult)
    }
}
