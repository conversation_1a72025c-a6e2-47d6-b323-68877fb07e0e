package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{CriteriaRow, RawCriteriaRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class CriteriaReader(sparkSession: SparkSession, path: String) extends CsvReader[CriteriaRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawCriteriaRow.Columns.CriteriaPrescriptionTextId, CriteriaRow.Columns.CriteriaPrescriptionTextId),
        ColumnMapping(RawCriteriaRow.Columns.CriteriaType, CriteriaRow.Columns.CriteriaType),
        ColumnMapping(RawCriteriaRow.Columns.ParameterRelationship, CriteriaRow.Columns.ParameterRelationship),
    )

    override protected def encode(rawDs: DataFrame): Dataset[CriteriaRow] = {
        import sparkSession.implicits._
        rawDs.as[CriteriaRow]
    }

}
