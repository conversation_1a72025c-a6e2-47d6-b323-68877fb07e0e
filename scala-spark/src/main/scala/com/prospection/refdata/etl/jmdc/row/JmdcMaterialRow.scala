package com.prospection.refdata.etl.jmdc.row

import com.prospection.refdata.etl.common.StandardColumns

case class JmdcMaterialRow(
                              code: String,
                              material_code: String,
                              material_name: String,
                              material_cat_med: String,
                              material_cat_large: String,
                              material_version: String,
                          )

object JmdcMaterialRow {
    object Columns {
        val Code = StandardColumns.Code
        val MaterialCode = StandardColumns.MaterialCode
        val MaterialName = StandardColumns.MaterialName
        val MaterialCatMed = StandardColumns.MaterialCatMed
        val MaterialCatLarge = StandardColumns.MaterialCatLarge
        val MaterialVersion = StandardColumns.MaterialVersion
    }
}

object RawJmdcMaterialRow {
    object Columns {
        val StandardizedMaterialCode = "standardized_material_code"
        val StandardizedMaterialVersion = "standardized_material_version"
        val StandardizedMaterialName = "standardized_material_name"
        val MaterialCategoryLargeClassificationName = "material_category_large_classification_name"
        val MaterialCategoryMediumClassificationName = "material_category_medium_classification_name"
        val MaterialCode = "material_code"
    }
}