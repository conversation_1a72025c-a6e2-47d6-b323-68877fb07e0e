package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbEtcIdRow, RawFdbEtcIdRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbEtcIdRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbEtcIdRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbEtcIdRow.Columns.EtcId, FdbEtcIdRow.Columns.EtcId),
        ColumnMapping(RawFdbEtcIdRow.Columns.EtcName, FdbEtcIdRow.Columns.EtcName),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbEtcIdRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbEtcIdRow]
    }
}
