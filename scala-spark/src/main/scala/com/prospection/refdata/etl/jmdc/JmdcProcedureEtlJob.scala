package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_PROCEDURE
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.jmdc.parser.{JmdcProcedureParser, JmdcProcedureParserOutput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class JmdcProcedureEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) {

    override val name: String = "JMDC Procedure Etl Job"

    override def parse(): JmdcProcedureParserOutput = {
        new JmdcProcedureParser(spark, getInputFilePath(JMDC_PROCEDURE)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {
        parseOutput.asInstanceOf[JmdcProcedureParserOutput].dataset.toDF()
    }
}
