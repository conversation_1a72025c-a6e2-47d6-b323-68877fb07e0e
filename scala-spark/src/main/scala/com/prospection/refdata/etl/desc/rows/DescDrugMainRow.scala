package com.prospection.refdata.etl.desc.rows

import com.prospection.refdata.etl.common.StandardColumns

case class DescDrugMainRow(
                                  drug_name_jp: String,
                                  drug_name: String,
                                  code: String,
                                  description: String
                          )

object DescDrugMainRow {
    object Columns {
        val DrugNameJp: String = StandardColumns.DrugNameJp
        val DrugName: String = StandardColumns.DrugName
        val Code: String = StandardColumns.Code
        val Description: String = StandardColumns.Description
    }
}

object RawDescDrugMainRow {
    object Columns {
        val GenericNameJp = "generic_name_jp"
        val GenericNameEn = "generic_name_en"
        val DrugCode = "drug_code"
        val DrugNameEn = "drug_name_en"
    }
}