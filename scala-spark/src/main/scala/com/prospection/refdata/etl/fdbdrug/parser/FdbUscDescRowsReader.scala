package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbUscDescRow, RawFdbUscDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbUscDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbUscDescRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbUscDescRow.Columns.Usc, FdbUscDescRow.Columns.Usc),
        ColumnMapping(RawFdbUscDescRow.Columns.UscDesc40, FdbUscDescRow.Columns.UscDesc40),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbUscDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbUscDescRow]
    }
}
