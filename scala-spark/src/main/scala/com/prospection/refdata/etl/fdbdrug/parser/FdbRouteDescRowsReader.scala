package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbRouteDescRow, RawFdbRouteDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbRouteDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbRouteDescRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbRouteDescRow.Columns.Gcrt, FdbRouteDescRow.Columns.Gcrt),
        ColumnMapping(RawFdbRouteDescRow.Columns.GcrtDesc, FdbRouteDescRow.Columns.GcrtDesc),
    )


    override protected def encode(rawDs: DataFrame): Dataset[FdbRouteDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbRouteDescRow]
    }
}
