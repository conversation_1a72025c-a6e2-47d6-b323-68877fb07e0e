package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.jmdc.parser.reader.JmdcDiagnosisRowsReader
import com.prospection.refdata.etl.jmdc.row.JmdcDiagnosisRow
import org.apache.spark.sql.{Dataset, SparkSession}

class JmdcDiagnosisParser(
                             sparkSession: SparkSession,
                             pathPrefix: String,
                         ) {

    def parse(): JmdcDiagnosisParserOutput = {

        val jmdcDiagnosisDataSet = JmdcDiagnosisRowsReader(sparkSession, s"$pathPrefix/transformed/split/diagnosis_master").read()

        JmdcDiagnosisParserOutput(
            jmdcDiagnosisDataSet.dataset
        )
    }
}

case class JmdcDiagnosisParserOutput(dataset: Dataset[JmdcDiagnosisRow]) extends ParserOutput