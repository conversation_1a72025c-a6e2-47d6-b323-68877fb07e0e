package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.etl.common.CodingSystems.JMDC_DIAGNOSIS
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.jmdc.parser.{JmdcDiagnosisParser, JmdcDiagnosisParserOutput}
import org.apache.spark.sql.{DataFrame, SparkSession}

class JmdcDiagnosisEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) {

    override val name: String = "JMDC Diagnosis Etl Job"

    override def parse(): ParserOutput = {
        new JmdcDiagnosisParser(spark, getInputFilePath(JMDC_DIAGNOSIS)).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {
        parseOutput.asInstanceOf[JmdcDiagnosisParserOutput].dataset.toDF()
    }
}
