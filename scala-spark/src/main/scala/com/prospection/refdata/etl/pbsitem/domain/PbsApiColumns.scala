package com.prospection.refdata.etl.pbsitem.domain

object PbsApiColumns {

    val RestrictionCode = "res_code"
    val CriteriaPrescriptionTextId = "criteria_prescribing_txt_id"
    val CriteriaType = "criteria_type"
    val ParameterRelationship = "parameter_relationship"
    val TreatmentPhase = "treatment_phase"
    val CriteriaRelationship = "criteria_relationship"
    val PrescribingTextId = "prescribing_text_id"
    val CriteriaPosition = "criteria_position"
    val PrescribingType = "prescribing_type"
    val PrescribingText = "prescribing_txt"
    val ParameterPosition = "parameter_position"
    val LiDrugName = "li_drug_name"
    val Formulary = "formulary"
    val IndicationPrescribingTextId = "indication_prescribing_txt_id"
    val IndicationCondition = "condition"
    val IndicationEpisodicity = "episodicity"
    val Severity = "severity"
}
