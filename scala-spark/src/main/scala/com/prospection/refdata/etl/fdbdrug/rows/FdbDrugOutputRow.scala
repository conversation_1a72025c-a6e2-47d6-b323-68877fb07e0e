package com.prospection.refdata.etl.fdbdrug.rows

case class FdbDrugOutputRow(
                               code: String,
                               label_name: String,
                               brand_name: String,
                               drug_name: String,
                               ingredient_name: Array[String],
                               strength_number: Array[String],
                               strength_unit_description_abbreviation: Array[String],
                               package_size: String,
                               dose_form: String,
                               package_description: String,
                               route_of_administration: String,
                               labeller_name: String,
                               jcode: Array[String],
                               jcode_desc: Array[String],
                               hic3_classification: String,
                               hic3_description: String,
                               etc_classification: Array[String],
                               etc_description: Array[String],
                               usc_classification: String,
                               usc_description: String,
                               ahfs_classification: Array[String],
                               ahfs_description: Array[String],
                               atc_code: String,
                               on_label_indication: Array[String],
                               off_label_indication: Array[String],
                               icd_code: Array[String],
                               obsolete_date: String,
                               previous_version_delete_date: Array[String]
                           )