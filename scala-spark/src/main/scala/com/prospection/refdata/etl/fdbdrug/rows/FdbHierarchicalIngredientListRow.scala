package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RHICLSQ2_HICLSEQNO_MSTR file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbHierarchicalIngredientListRow(
                                               drug_name: String,
                                               hicl_seqno: String,
                                           )

object FdbHierarchicalIngredientListRow {
    object Columns {
        val Gnn60 = StandardColumns.DrugName
        val HiclSeqno = "hicl_seqno"
    }
}

object RawFdbHierarchicalIngredientListRow {
    object Columns {
        val HiclSeqno = "_c0"
        val Gnn60 = "_c2"

    }
}