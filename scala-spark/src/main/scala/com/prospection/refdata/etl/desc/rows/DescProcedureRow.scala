package com.prospection.refdata.etl.desc.rows

import com.prospection.refdata.etl.common.StandardColumns

case class DescProcedureRow(
                                   code: String,
                                   procedure_name: String,
                                   description: String
                           )

object DescProcedureRow {
    object Columns {
        val Code: String = StandardColumns.Code
        val ProcedureName: String = StandardColumns.ProcedureName
        val Description: String = StandardColumns.Description
        val VersionCode = "version_code"
    }
}

object RawDescProcedureRow {
    object Columns {
        val MedicalProcedureCode = "medical_procedure_code"
        val MedicalProcedureName = "medical_procedure_name"
        val MedicalProcedureNameEn = "medical_procedure_name_en"
        val VersionCode = "version_code"
    }
}