package com.prospection.refdata.etl.jmdc.parser.reader

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.jmdc.row.{JmdcProcedureRow, RawJmdcDiagnosisRow, RawJmdcProcedureRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class JmdcProcedureRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[JmdcProcedureRow](
    sparkSession,
    path,
    SparkOptions()
) {

    override val colMappings = List(
        ColumnMapping(RawJmdcProcedureRow.Columns.StandardDiseaseCode, JmdcProcedureRow.Columns.Code),
        ColumnMapping(RawJmdcProcedureRow.Columns.StandardizedProcedureName, JmdcProcedureRow.Columns.Description),
        ColumnMapping(RawJmdcProcedureRow.Columns.ProcedureCode, JmdcProcedureRow.Columns.KubunCode),

        ColumnMapping(RawJmdcProcedureRow.Columns.ProcedureCategoryMediumClassificationName, JmdcProcedureRow.Columns.ProcedureCatMed),
        ColumnMapping(RawJmdcProcedureRow.Columns.ProcedureCategorySmallClassificationName, JmdcProcedureRow.Columns.ProcedureCatSml),
        ColumnMapping(RawJmdcProcedureRow.Columns.ProcedureCategorySubclassificationName, JmdcProcedureRow.Columns.ProcedureCatSubclass),
        ColumnMapping(RawJmdcProcedureRow.Columns.StandardizedProcedureVersion, JmdcProcedureRow.Columns.ProcedureVersion),
    )

    override protected def encode(rawDs: DataFrame): Dataset[JmdcProcedureRow] = {
        import sparkSession.implicits._
        rawDs.as[JmdcProcedureRow]
    }
}


