package com.prospection.refdata.etl.common

case class SparkOptions(
                           header: Boolean = true,
                           delimiter: String = CommonDelimiters.COMMA,
                           quote: String = "\"",
                           escape: String = "\"",
                           encoding: String = "UTF-8"
                       ) {

    def toMap: Map[String, String] = Map(
        "header" -> header.toString,
        "sep" -> delimiter,
        "quote" -> quote,
        "escape" -> escape,
        "encoding" -> encoding
    )
}