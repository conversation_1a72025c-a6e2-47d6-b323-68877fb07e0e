package com.prospection.refdata.etl

import com.prospection.refdata.etl.common.job.{EtlJob, WareHouseExporter}

case class EtlJobExecutor(etlJob: EtlJob) {

    def execute(): String = {

        //Do common step for any coding system
        val parseResult = etlJob.parse()

        val transformResult = etlJob.transform(parseResult)

        etlJob.storeSnapshot(transformResult)

        //Avoid write direct from transformerDS to improve performance
        val rawDf = etlJob.readSnapshotData()

        etlJob.storeRawItem(rawDf)

        //Do special step for some coding system
        etlJob match {
            case wareHouseExporter:  WareHouseExporter =>
                wareHouseExporter.storeInWarehouse(parseResult)
            case _ =>
        }

        null
    }

    def getJobName = etlJob.name
}
