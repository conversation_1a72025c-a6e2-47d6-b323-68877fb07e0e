package com.prospection.refdata.etl.mdvitem.parser

import com.prospection.refdata.etl.mdvitem.datasets.{MdvItemDataset, MdvProcedureDataset}
import com.prospection.refdata.etl.mdvitem.transformer.MdvItemTransformerInput
import org.apache.spark.sql.SparkSession

class MdvItemParser(
                       sparkSession: SparkSession,
                       pathPrefix: String,
                   ) {

    def parse(drugFileName: String = "M_Drug.txt.gz", procedureFileName: String = "M_Act.txt.gz"): MdvItemTransformerInput = {
        val drugs = MdvItemRowsReader(sparkSession, s"$pathPrefix/$drugFileName").read()
        val procedures = MdvProcedureRowsReader(sparkSession, s"$pathPrefix/$procedureFileName").read()

        MdvItemTransformerInput(
            new MdvItemDataset(drugs.dataset),
            new MdvProcedureDataset(procedures.dataset),
        )
    }
}
