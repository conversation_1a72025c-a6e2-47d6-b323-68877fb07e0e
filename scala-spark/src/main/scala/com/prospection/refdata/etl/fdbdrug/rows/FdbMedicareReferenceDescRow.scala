package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RMCRRD1_MEDICARE_REG_REF_DESC file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbMedicareReferenceDescRow(
                                          jcode: String,
                                          jcode_desc: String,
                                      )

object FdbMedicareReferenceDescRow {
    object Columns {
        val McrRef = "mcr_ref"
        val McrBc = StandardColumns.Jcode
        val McrBcdesc = StandardColumns.JcodeDesc
    }
}

object RawFdbMedicareReferenceDescRow {
    object Columns {
        val McrRef = "_c0"
        val McrBc = "_c2"
        val McrBcdesc = "_c3"
    }
}