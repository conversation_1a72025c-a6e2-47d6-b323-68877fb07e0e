package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.CriteriaRow
import com.prospection.refdata.etl.pbsitem.domain.CriteriaRow.Columns
import org.apache.spark.sql.Dataset

class CriteriaDataset(dataset: Dataset[CriteriaRow]) extends EtlDataset[CriteriaRow](dataset, "criteria") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.CriteriaPrescriptionTextId,
        Columns.CriteriaType,
        Columns.ParameterRelationship
    )
}
