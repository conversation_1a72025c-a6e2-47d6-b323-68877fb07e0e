package com.prospection.refdata.etl.pbsitem.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.pbsitem.domain._
import com.prospection.refdata.etl.pbsitem.parser.reader._
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

class PbsItemParser(
                       sparkSession: SparkSession,
                       pathPrefix: String,
                       version: String,
                   ) {

    def parse(): PbsItemParserOutput = {

        val itemOutput = ItemReader(sparkSession, getPath("items")).read()
        val restrictionOutput = RestrictionReader(sparkSession, getPath("restrictions")).read()
        val itemRestrictionRelationshipOutput = ItemRestrictionRelationshipReader(sparkSession, getPath("item-restriction-relationships")).read()
        val itemAtcRelationshipOutput = ItemAtcRelationshipReader(sparkSession, getPath("item-atc-relationships")).read()
        val manufacturerOutput = OrganisationReader(sparkSession, getPath("organisations")).read()
        val criteriaOutput = CriteriaReader(sparkSession, getPath("criteria")).read()
        val restrictionPrescribingTextRelationshipOutput = RestrictionPrescribingTextRelationshipReader(sparkSession, getPath("restriction-prescribing-text-relationships")).read()
        val prescribingTextOutput = PrescribingTextReader(sparkSession, getPath("prescribing-texts")).read()
        val criteriaParameterRelationshipOutput = CriteriaParameterRelationshipReader(sparkSession, getPath("criteria-parameter-relationships")).read()
        val indicationOutput = IndicationReader(sparkSession, getPath("indications")).read()

        PbsItemParserOutput(
            itemDataSet = itemOutput.dataset,
            itemRawDataset = itemOutput.rawDataset,
            restrictionDataSet = restrictionOutput.dataset,
            restrictionRawDataset = restrictionOutput.rawDataset,
            itemRestrictionRelationshipDataSet = itemRestrictionRelationshipOutput.dataset,
            itemRestrictionRelationshipRawDataset = itemRestrictionRelationshipOutput.rawDataset,
            itemAtcRelationshipDataSet = itemAtcRelationshipOutput.dataset,
            itemAtcRelationshipRawDataset = itemAtcRelationshipOutput.rawDataset,
            manufacturerDataSet = manufacturerOutput.dataset,
            manufacturerRawDataset = manufacturerOutput.rawDataset,
            criteriaDataSet = criteriaOutput.dataset,
            criteriaRawDataset = criteriaOutput.rawDataset,
            restrictionPrescribingTextRelationshipDataSet = restrictionPrescribingTextRelationshipOutput.dataset,
            restrictionPrescribingTextRelationshipRawDataset = restrictionPrescribingTextRelationshipOutput.rawDataset,
            prescribingTextDataSet = prescribingTextOutput.dataset,
            prescribingTextRawDataset = prescribingTextOutput.rawDataset,
            criteriaParameterRelationshipDataset = criteriaParameterRelationshipOutput.dataset,
            criteriaParameterRelationshipRawDataset = criteriaParameterRelationshipOutput.rawDataset,
            indicationDataset = indicationOutput.dataset,
            indicationRawDataset = indicationOutput.rawDataset
        )
    }

    private def getPath(name: String): String = s"$pathPrefix/$name/published=$version/"
}

case class PbsItemParserOutput(
                                  itemDataSet: Dataset[ItemRow],
                                  itemRawDataset: DataFrame,
                                  restrictionDataSet: Dataset[RestrictionRow],
                                  restrictionRawDataset: DataFrame,
                                  itemRestrictionRelationshipDataSet: Dataset[ItemRestrictionRelationshipRow],
                                  itemRestrictionRelationshipRawDataset: DataFrame,
                                  itemAtcRelationshipDataSet: Dataset[ItemAtcRelationshipRow],
                                  itemAtcRelationshipRawDataset: DataFrame,
                                  manufacturerDataSet: Dataset[OrganisationRow],
                                  manufacturerRawDataset: DataFrame,
                                  criteriaDataSet: Dataset[CriteriaRow],
                                  criteriaRawDataset: DataFrame,
                                  restrictionPrescribingTextRelationshipDataSet: Dataset[RestrictionPrescribingTextRelationshipRow],
                                  restrictionPrescribingTextRelationshipRawDataset: DataFrame,
                                  prescribingTextDataSet: Dataset[PrescribingTextRow],
                                  prescribingTextRawDataset: DataFrame,
                                  criteriaParameterRelationshipDataset: Dataset[CriteriaParameterRelationshipRow],
                                  criteriaParameterRelationshipRawDataset: DataFrame,
                                  indicationDataset: Dataset[IndicationRow],
                                  indicationRawDataset: DataFrame
                              ) extends ParserOutput
