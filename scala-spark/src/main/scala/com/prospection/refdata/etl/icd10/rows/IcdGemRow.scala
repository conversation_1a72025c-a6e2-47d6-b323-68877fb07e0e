package com.prospection.refdata.etl.icd10.rows

import com.prospection.refdata.etl.common.StandardColumns

case class IcdGemRow(
                           code: String,
                           icd9_code: String,
                          )

object IcdGemRow {
    object Columns {
        val IcdCode = StandardColumns.Code
        val Icd9Code = "icd9_code"
    }
}

object RawIcdGemRow{
    object Columns {
        val Icd9Code = "icd9_code"
        val IcdCode = "icd10_code"
        val ApproximateFlag = "approximate_flag"
        val NoMapFlag = "no_map_flag"
        val CombinationFlag = "combination_flag"
        val ScenarioFlag = "scenario_flag"
        val ChoiceListFlag = "choice_list_flag"
    }
}