package com.prospection.refdata.etl.pbsitem.domain

// Represent a row in restriction-prescribing-text-relationships file
case class RestrictionPrescribingTextRelationshipRow(
                                                        res_code: String, // res_code
                                                        prescribing_text_id: String, // prescribing_text_id
                                                        criteria_position: String // criteria_position
                                                    )

object RestrictionPrescribingTextRelationshipRow {
    object Columns {
        val RestrictionCode: String = PbsApiColumns.RestrictionCode
        val PrescribingTextId: String = PbsApiColumns.PrescribingTextId
        val CriteriaPosition: String = PbsApiColumns.CriteriaPosition
    }
}

object RawRestrictionPrescribingTextRelationshipRow {
    object Columns {
        val ResCode = "res_code"
        val PrescribingTextId = "prescribing_text_id"
        val Position = "pt_position"
    }
}