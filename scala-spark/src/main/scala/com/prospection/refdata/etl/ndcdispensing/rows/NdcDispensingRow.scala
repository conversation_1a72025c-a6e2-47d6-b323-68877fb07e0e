package com.prospection.refdata.etl.ndcdispensing.rows

// Represent a row in the ndc.tsv file
case class NdcDispensingRow(
                               code: String,
                               brand_name: String,
                               drug_name: String,
                               ingredient_name: String,
                               manufacturer_name: String,
                               dose_form: String,
                               route_of_administration: String,
                               package_description: String,
                               strength_number: String,
                               strength_unit: String,
                               pharmaceutical_classes: String,
                               indication_description: String,
                               marketing_category: String,
                               product_type_name: String,
                           )

object RawNdcDispensingRow {
    object Columns {
        val PackageDescription = "Package Description"
        val ElevenDigitNDCCode = "11 Digit NDC Code"
        val ProprietaryName = "Proprietary Name"
        val NonProprietaryName = "Non Proprietary Name"
        val DosageFormName = "Dosage Form Name"
        val RouteName = "Route Name"
        val MarketingCategoryName = "Marketing Category Name"
        val LabelerName = "Labeler Name"
        val SubstanceName = "Substance Name"
        val StrengthNumber = "Strength Number"
        val StrengthUnit = "Strength Unit"
        val PharmaceuticalClasses = "Pharmaceutical Classes"
        val IndicationAndUsage = "Indication And Usage"
        val ProductTypeName = "Product Type Name"
    }
}