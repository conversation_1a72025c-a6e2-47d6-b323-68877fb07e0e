package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RNDCDR0_NDC_DELETION_REASON file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbNdcDeletionReasonRow(
                                 code: String,
                                 previous_version_delete_date: String,
                             )

object FdbNdcDeletionReasonRow {
    object Columns {
        val Ndc = StandardColumns.Code
        val NdcDeleteDate = StandardColumns.NdcDeleteDate
    }
}

object RawFdbNdcDeletionReasonRow {
    object Columns {
        val Ndc = "_c1"
        val NdcDeleteDate = "_c6"
    }
}