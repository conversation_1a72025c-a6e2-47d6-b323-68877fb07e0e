package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.icd10.rows.{Icd9Row, RawIcd9Row}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class Icd9PCSRowsReader(sparkSession: SparkSession, path: String) extends IcdReader[Icd9Row](
    sparkSession,
    path,
) {

    private val MAX_CHARACTERS = 400 //Define in icd10 order file

    override val tableStructs = List(
        IcdOrderStruct(colName = RawIcd9Row.Columns.IcdCode, position =  1, length = 4),
        IcdOrderStruct(colName = RawIcd9Row.Columns.Description, position =  6, length = MAX_CHARACTERS - 7)

    )
    override val colMappings = List(
        ColumnMapping(RawIcd9Row.Columns.IcdCode, Icd9Row.Columns.IcdCode),
        ColumnMapping(RawIcd9Row.Columns.Description, Icd9Row.Columns.Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[Icd9Row] = {
        import sparkSession.implicits._
        rawDs.as[Icd9Row]
    }
}


