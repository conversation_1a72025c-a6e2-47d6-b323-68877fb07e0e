package com.prospection.refdata.etl.icd10

import com.prospection.refdata.etl.common.CodingSystems.ICD_PROCEDURE
import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams}
import com.prospection.refdata.etl.icd10.parser.IcdProcedureParser
import com.prospection.refdata.etl.icd10.rows.IcdParserOutput
import com.prospection.refdata.etl.icd10.transformer.IcdTransformer
import org.apache.spark.sql.{DataFrame, SparkSession}

class IcdProcedureEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) {

    override val name: String = "ICD Procedure Etl Job"
    private val ICD_10PSC_TYPE = "ICD-10-PCS"
    private val ICD_9PSC_TYPE = "ICD-9-CM Px"

    override def parse(): ParserOutput = {
        new IcdProcedureParser(
            spark,
            getInputFilePath(ICD_PROCEDURE),
            getVersion
        ).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {
        val parseResult = parseOutput.asInstanceOf[IcdParserOutput]
        val transformInput = transformer.IcdTransformerInput(
            parseResult.icd10Dataset,
            parseResult.icd9LongDescDataset,
            parseResult.icd9ShortDescDataset,
            parseResult.ccsrDataset,
            parseResult.icd9GemDataset
        )
        new IcdTransformer(spark).transform(transformInput, ICD_10PSC_TYPE, ICD_9PSC_TYPE).toDF()
    }
}
