package com.prospection.refdata.etl.desc.transformer

import com.prospection.refdata.etl.common.{StandardColumns, StandardValues}
import com.prospection.refdata.etl.desc.rows.{DescDrugEphmraRow, DescDrugMainRow, DescDrugReceRow, DescItemOutputRow}
import org.apache.spark.sql.functions.{col, lit, when}
import org.apache.spark.sql.{Dataset, SparkSession}

class DescItemTransformer(spark: SparkSession) {
    def transform(input: DescItemTransformerInput): Dataset[DescItemOutputRow] = {
        import spark.implicits._

        val drugs = transformDrug(input)

        val procedures = input.descProcedureDataset.getDataset
                .select(col("*"),
                    lit(null).as(StandardColumns.AtcEphmra),
                    lit(null).as(StandardColumns.DrugNameJp),
                    lit(null).as(StandardColumns.DrugName),
                    lit(null).as(StandardColumns.DrugUsage),
                    lit(null).as(StandardColumns.GenericFlag),
                )
                .as[DescItemOutputRow]

        drugs.unionByName(procedures)

    }

    private def transformDrug(input: DescItemTransformerInput): Dataset[DescItemOutputRow] = {
        import spark.implicits._

        val drugMainDf = input.descDrugMainDataset.getDataset
        val drugReceDf = input.descDrugReceDataset.getDataset
        val drugEphmraDf = input.descDrugEphmraDataset.getDataset

        drugReceDf.join(drugMainDf, drugReceDf(DescDrugReceRow.Columns.Code) === drugMainDf(DescDrugMainRow.Columns.Code))
                .join(drugEphmraDf, drugReceDf(DescDrugReceRow.Columns.Code) === drugEphmraDf(DescDrugEphmraRow.Columns.Code))
                .select(
                    drugReceDf(DescDrugReceRow.Columns.Code),
                    drugEphmraDf(DescDrugEphmraRow.Columns.AtcEphmra),
                    drugMainDf(DescDrugMainRow.Columns.DrugNameJp),
                    drugMainDf(DescDrugMainRow.Columns.DrugName),
                    drugMainDf(DescDrugMainRow.Columns.Description),
                    drugReceDf(DescDrugReceRow.Columns.DrugUsage),
                    drugReceDf(DescDrugReceRow.Columns.GenericFlag),
                    lit(null).as(StandardColumns.ProcedureName),
                )
                .withColumn(StandardColumns.DrugUsage,
                    when(col(StandardColumns.DrugUsage) === "1", StandardValues.DrugUsage.OralMedication)
                            .when(col(StandardColumns.DrugUsage) === "3", StandardValues.DrugUsage.Others)
                            .when(col(StandardColumns.DrugUsage) === "4", StandardValues.DrugUsage.Injection)
                            .when(col(StandardColumns.DrugUsage) === "6", StandardValues.DrugUsage.ExternalMedication)
                            .when(col(StandardColumns.DrugUsage) === "8", StandardValues.DrugUsage.DentalMedication)
                            .otherwise(StandardValues.Unknown)
                )
                .withColumn(StandardColumns.GenericFlag,
                    when(col(StandardColumns.GenericFlag) === "0", StandardValues.GenericFlag.NonGeneric)
                            .when(col(StandardColumns.GenericFlag) === "1", StandardValues.GenericFlag.Generic)
                            .otherwise(StandardValues.Unknown)
                ).as[DescItemOutputRow]
    }

}
