package com.prospection.refdata.etl.pbsauthority.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.pbsauthority.parser.reader.{PbsAuthorityRestrictionRowsReader, PbsAuthorityRestrictionRowsXmlReader}
import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityRestrictionRow
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

class PbsAuthorityParser(
                            sparkSession: SparkSession,
                            excelFilePath: String,
                        ) {

    def parse(): PbsAuthorityParserOutput = {
        val authorityOutput = if (isExcelExtension(excelFilePath)) {
            PbsAuthorityRestrictionRowsReader(sparkSession, s"$excelFilePath").read()
        } else {
            PbsAuthorityRestrictionRowsXmlReader(sparkSession, s"$excelFilePath").read()
        }

        PbsAuthorityParserOutput(
            authorityOutput.dataset,
            authorityOutput.rawDataset
        )
    }

    def isExcelExtension(excelFilePath: String): Boolean = {
        if ("xlsx".equals(excelFilePath.split("\\.").last)) {
            return true
        }

        //In this case: xls file from S3 is a plain xml file
        //Discuss with Devops or Owner to make sure that it is always .xlsx
        false
    }
}

case class PbsAuthorityParserOutput(
                                       restrictionDataset: Dataset[PbsAuthorityRestrictionRow],
                                       restrictionRawDataset: DataFrame
                                   ) extends ParserOutput