package com.prospection.refdata.etl.mdvitem.datasets

import com.prospection.refdata.etl.common.{EtlDataset, StandardColumns}
import com.prospection.refdata.etl.mdvitem.rows.MdvProcedureRow
import org.apache.spark.sql.Dataset

class MdvProcedureDataset(dataset: Dataset[MdvProcedureRow]) extends EtlDataset[MdvProcedureRow](dataset, "procedures") {
    override def getNonNullableColumns: Set[String] = Set(
        StandardColumns.Code,
        StandardColumns.KubunCode,
    )
}
