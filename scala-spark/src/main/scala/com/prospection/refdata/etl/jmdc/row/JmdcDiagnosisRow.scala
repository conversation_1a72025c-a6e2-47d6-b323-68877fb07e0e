package com.prospection.refdata.etl.jmdc.row

import com.prospection.refdata.etl.common.StandardColumns

case class JmdcDiagnosisRow(
                           code: String,
                           diagnosis_name: String,
                           icd10_code: String,
                          )

object JmdcDiagnosisRow {
    object Columns {
        val Code = StandardColumns.Code
        val Icd10Code = StandardColumns.Icd10Code
        val DiagnosisName = StandardColumns.DiagnosisName
    }
}

object RawJmdcDiagnosisRow{
    object Columns {
        val StandardDiseaseCode = "standard_disease_code"
        val StandardDiseaseName = "standard_disease_name"
        val Icd10Level4Code = "icd10_level4_code"
    }
}