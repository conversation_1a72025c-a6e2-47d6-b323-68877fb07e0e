package com.prospection.refdata.etl.desc.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.desc.rows.{DescDrugReceRow, RawDescDrugReceRow}
import org.apache.spark.sql.functions.max
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class DescDrugReceRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[DescDrugReceRow](sparkSession, path, SparkOptions(delimiter = CommonDelimiters.TAB)) {

    override val colMappings: List[ColumnMapping] = List(
        ColumnMapping(RawDescDrugReceRow.Columns.DrugCode, DescDrugReceRow.Columns.Code),
        ColumnMapping(RawDescDrugReceRow.Columns.FormulationCode, DescDrugReceRow.Columns.DrugUsage),
        ColumnMapping(RawDescDrugReceRow.Columns.GenericCode, DescDrugReceRow.Columns.GenericFlag),
        ColumnMapping(RawDescDrugReceRow.Columns.VersionCode, DescDrugReceRow.Columns.VersionCode)
    )

    override protected def encode(rawDs: DataFrame): Dataset[DescDrugReceRow] = {
        import sparkSession.implicits._
        // Get the latest version for each DrugCode
        val latestVersions = rawDs
                .groupBy(DescDrugReceRow.Columns.Code)
                .agg(max(DescDrugReceRow.Columns.VersionCode).as(DescDrugReceRow.Columns.VersionCode))

        // Join with the original DataFrame to filter only the latest versions
        val filteredDs = rawDs
                .join(latestVersions, Seq(DescDrugReceRow.Columns.Code, DescDrugReceRow.Columns.VersionCode))
                .drop(DescDrugReceRow.Columns.VersionCode)
                .distinct()

        filteredDs.as[DescDrugReceRow]
    }
}
