package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{RawRestrictionPrescribingTextRelationshipRow, RestrictionPrescribingTextRelationshipRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class RestrictionPrescribingTextRelationshipReader(sparkSession: SparkSession, path: String) extends CsvReader[RestrictionPrescribingTextRelationshipRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawRestrictionPrescribingTextRelationshipRow.Columns.ResCode, RestrictionPrescribingTextRelationshipRow.Columns.RestrictionCode),
        ColumnMapping(RawRestrictionPrescribingTextRelationshipRow.Columns.PrescribingTextId, RestrictionPrescribingTextRelationshipRow.Columns.PrescribingTextId),
        ColumnMapping(RawRestrictionPrescribingTextRelationshipRow.Columns.Position, RestrictionPrescribingTextRelationshipRow.Columns.CriteriaPosition)
    )

    override protected def encode(rawDs: DataFrame): Dataset[RestrictionPrescribingTextRelationshipRow] = {
        import sparkSession.implicits._
        rawDs.as[RestrictionPrescribingTextRelationshipRow]
    }

}
