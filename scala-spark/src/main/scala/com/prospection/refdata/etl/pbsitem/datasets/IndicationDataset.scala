package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.IndicationRow
import com.prospection.refdata.etl.pbsitem.domain.IndicationRow.Columns
import org.apache.spark.sql.Dataset

class IndicationDataset(dataset: Dataset[IndicationRow]) extends EtlDataset[IndicationRow](dataset, "indications") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.IndicationPrescribingTextId
    )
}
