package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.icd10.rows.{Icd9Row, RawIcd9Row}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class Icd9CMRowsReader(
                               sparkSession: SparkSession,
                               path: String,
                               options: SparkOptions = SparkOptions(header = false, delimiter = CommonDelimiters.BREAK_LINE)
                           ) extends IcdReader[Icd9Row](
    sparkSession,
    path,
    options
) {

    private val MAX_CHARACTERS = 400 //Define in icd10 order file

    override val tableStructs = List(
        IcdOrderStruct(colName = RawIcd9Row.Columns.IcdCode, position = 1, length = 5),
        IcdOrderStruct(colName = RawIcd9Row.Columns.Description, position = 7, length = MAX_CHARACTERS - 7)

    )
    override val colMappings = List(
        ColumnMapping(RawIcd9Row.Columns.IcdCode, Icd9Row.Columns.IcdCode),
        ColumnMapping(RawIcd9Row.Columns.Description, Icd9Row.Columns.Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[Icd9Row] = {
        import sparkSession.implicits._
        rawDs.as[Icd9Row]
    }
}


