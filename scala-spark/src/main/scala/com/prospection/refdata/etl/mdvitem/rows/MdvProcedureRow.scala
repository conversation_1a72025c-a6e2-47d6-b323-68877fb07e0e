package com.prospection.refdata.etl.mdvitem.rows

// Represent a row in M_Act.txt file
case class MdvProcedureRow(
                              kubuncode: String,
                              code: String,
                              procedure_name: String,
                              description: String,
                          )

object RawMdvProcedureRow {
    object Columns {
        val KubunCode = "kubuncode"
        val ReceiptCode = "receiptcode"
        val ReceiptName = "receiptname"
        val ReceiptNameEng = "receiptname_eng"
    }
}