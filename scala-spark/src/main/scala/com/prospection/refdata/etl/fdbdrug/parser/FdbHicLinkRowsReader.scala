package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbHicLinkRow, RawFdbHicLinkRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbHicLinkRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbHicLinkRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbHicLinkRow.Columns.HiclSeqno, FdbHicLinkRow.Columns.HiclSeqno),
        ColumnMapping(RawFdbHicLinkRow.Columns.HicSeqn, FdbHicLinkRow.Columns.HicSeqn),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbHicLinkRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbHicLinkRow]
    }
}
