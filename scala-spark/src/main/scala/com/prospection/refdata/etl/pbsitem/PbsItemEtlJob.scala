package com.prospection.refdata.etl.pbsitem

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.common.job.{EtlJob, EtlJobParams, WareHouseExporter}
import com.prospection.refdata.etl.pbsitem.datasets._
import com.prospection.refdata.etl.pbsitem.domain.{OutputRow, SnapshotRow}
import com.prospection.refdata.etl.pbsitem.parser.writer.{PbsItemHistoricalWriter, PbsItemHistoricalWriterInput}
import com.prospection.refdata.etl.pbsitem.parser.{PbsItemParser, PbsItemParserOutput}
import com.prospection.refdata.etl.pbsitem.snapshot.PbsItemSnapshotGenerator
import com.prospection.refdata.etl.pbsitem.transformer.{PbsItemTransformer, PbsItemTransformerInput}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

import java.time.LocalDate
import java.time.format.DateTimeFormatter

class PbsItemEtlJob(val spark: SparkSession, val params: EtlJobParams)
    extends EtlJob(spark, params) with WareHouseExporter {

    override final val name: String = "PBS Drug Etl Job V2"
    private final val PBS_DRUG_KEY = "PBS_Drug_API"

    override def getVersion: LocalDate = LocalDate.parse(params.version, DateTimeFormatter.ofPattern("yyyy-MM-dd"))

    override def parse(): ParserOutput = {
        new PbsItemParser(spark, getInputFilePath(PBS_DRUG_KEY), params.version).parse()
    }

    override def transform(parseOutput: ParserOutput): DataFrame = {

        val parseResult = parseOutput.asInstanceOf[PbsItemParserOutput]
        val transformInput = PbsItemTransformerInput(
            new ItemDataset(parseResult.itemDataSet),
            new RestrictionDataset(parseResult.restrictionDataSet),
            new ManufacturerDataset(parseResult.manufacturerDataSet),
            new ItemAtcRelationshipDataset(parseResult.itemAtcRelationshipDataSet),
            new ItemRestrictionRelationshipDataset(parseResult.itemRestrictionRelationshipDataSet),
            new CriteriaDataset(parseResult.criteriaDataSet),
            new RestrictionPrescribingTextRelationshipDataset(parseResult.restrictionPrescribingTextRelationshipDataSet),
            new PrescribingTextDataset(parseResult.prescribingTextDataSet),
            new CriteriaParameterRelationshipDataset(parseResult.criteriaParameterRelationshipDataset),
            new IndicationDataset(parseResult.indicationDataset)
        )

        val transformDs = new PbsItemTransformer(spark).transform(transformInput)

        generateSnapshot(spark, transformDs, params, getVersion).toDF()
    }

    override def storeInWarehouse(parserOutput: ParserOutput): Unit = {
        val parseResult = parserOutput.asInstanceOf[PbsItemParserOutput]
        val writerInput = PbsItemHistoricalWriterInput(
            parseResult.itemRawDataset,
            parseResult.restrictionRawDataset,
            parseResult.manufacturerRawDataset,
            parseResult.itemAtcRelationshipRawDataset,
            parseResult.itemRestrictionRelationshipRawDataset,
            parseResult.criteriaRawDataset,
            parseResult.restrictionPrescribingTextRelationshipRawDataset,
            parseResult.prescribingTextRawDataset,
            parseResult.criteriaParameterRelationshipRawDataset
        )
        PbsItemHistoricalWriter(writerInput)
            .write(params.warehousePath, getMonthVersion(getVersion))
    }

    override def storeRawItem(df: DataFrame): Unit = {
        val dropColumns = List(SnapshotRow.Columns.DateFirstListed, SnapshotRow.Columns.DateLastListed)
        val rawItemsDs = df.drop(dropColumns: _*)
        super.storeRawItem(rawItemsDs)
    }

    override def getSnapshotTimestamp: String = getVersion.format(DateTimeFormatter.ofPattern("yyyyMM"))

    private def generateSnapshot(spark: SparkSession, transformDs: Dataset[OutputRow], params: EtlJobParams, versionDate: LocalDate): Dataset[SnapshotRow] = {
        val previousSnapshotPath = s"${params.snapshotPath}/timestamp=${
            getMonthVersion(versionDate.minusMonths(1))
        }"

        // params.version is a string representing a date in the format "yyyy-MM-dd". For the snapshot timestamp, the format is "yyyyMMdd".
        val dateStr = versionDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"))

        PbsItemSnapshotGenerator(spark, transformDs, dateStr, previousSnapshotPath)
            .generate()
    }


}
