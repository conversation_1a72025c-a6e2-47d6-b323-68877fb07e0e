package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.RestrictionPrescribingTextRelationshipRow
import com.prospection.refdata.etl.pbsitem.domain.RestrictionPrescribingTextRelationshipRow.Columns
import org.apache.spark.sql.Dataset

class RestrictionPrescribingTextRelationshipDataset(dataset: Dataset[RestrictionPrescribingTextRelationshipRow]) extends EtlDataset[RestrictionPrescribingTextRelationshipRow](dataset, "restriction-prescribing-text-relationships") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.RestrictionCode,
        Columns.PrescribingTextId,
        Columns.CriteriaPosition
    )
}
