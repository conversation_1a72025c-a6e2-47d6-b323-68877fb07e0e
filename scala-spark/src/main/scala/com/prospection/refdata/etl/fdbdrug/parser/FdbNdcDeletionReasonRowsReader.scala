package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbNdcDeletionReasonRow, RawFdbNdcDeletionReasonRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbNdcDeletionReasonRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbNdcDeletionReasonRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbNdcDeletionReasonRow.Columns.Ndc, FdbNdcDeletionReasonRow.Columns.Ndc),
        ColumnMapping(RawFdbNdcDeletionReasonRow.Columns.NdcDeleteDate, FdbNdcDeletionReasonRow.Columns.NdcDeleteDate),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbNdcDeletionReasonRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbNdcDeletionReasonRow]
    }
}
