package com.prospection.refdata.etl.jmdc.parser.reader

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.jmdc.row.{JmdcMaterialRow, RawJmdcMaterialRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class JmdcMaterialRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[JmdcMaterialRow](
    sparkSession,
    path,
    SparkOptions()
) {

    override val colMappings = List(
        ColumnMapping(RawJmdcMaterialRow.Columns.StandardizedMaterialCode, JmdcMaterialRow.Columns.Code),
        ColumnMapping(RawJmdcMaterialRow.Columns.StandardizedMaterialName, JmdcMaterialRow.Columns.MaterialName),
        ColumnMapping(RawJmdcMaterialRow.Columns.MaterialCode, JmdcMaterialRow.Columns.MaterialCode),
        ColumnMapping(RawJmdcMaterialRow.Columns.StandardizedMaterialVersion, JmdcMaterialRow.Columns.MaterialVersion),

        ColumnMapping(RawJmdcMaterialRow.Columns.MaterialCategoryLargeClassificationName, JmdcMaterialRow.Columns.MaterialCatLarge),
        ColumnMapping(RawJmdcMaterialRow.Columns.MaterialCategoryMediumClassificationName, JmdcMaterialRow.Columns.MaterialCatMed),
    )

    override protected def encode(rawDs: DataFrame): Dataset[JmdcMaterialRow] = {
        import sparkSession.implicits._
        rawDs.as[JmdcMaterialRow]
    }
}


