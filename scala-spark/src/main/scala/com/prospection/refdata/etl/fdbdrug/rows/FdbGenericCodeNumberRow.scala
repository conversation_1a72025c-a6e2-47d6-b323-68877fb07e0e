package com.prospection.refdata.etl.fdbdrug.rows

// Represent a row in RGCNSEQ4_GCNSEQNO_MSTR file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbGenericCodeNumberRow(
                                      gcn_seqno: String,
                                      gcdf: String,
                                      gcrt: String,
                                      hic3_seqn: String,
                                      hicl_seqno: String,
                                  )


object FdbGenericCodeNumberRow {
    object Columns {
        val GcnSeqno = "gcn_seqno"
        val Gcdf = "gcdf"
        val Gcrt = "gcrt"
        val Hic3Seqn = "hic3_seqn"
        val HiclSeqno = "hicl_seqno"

    }
}

object RawFdbGenericCodeNumberRow {
    object Columns {
        val GcnSeqno = "_c0"
        val HiclSeqno = "_c2"
        val Gcdf = "_c3"
        val Gcrt = "_c4"
        val Hic3Seqn = "_c11"
    }
}