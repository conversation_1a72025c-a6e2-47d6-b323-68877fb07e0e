package com.prospection.refdata.etl.common.reader

import org.apache.spark.sql.functions.col
import org.apache.spark.sql.{DataFrame, Dataset}

abstract class Reader[T]() {

    protected val colMappings: List[ColumnMapping]

    protected def encode(rawDs: DataFrame): Dataset[T]

    protected def sanitize(ds: Dataset[T]): Dataset[T] = ds

    protected def read(): ReaderOutput[T]

    protected def preProcessRawData(rawDs: DataFrame): DataFrame = rawDs

    protected final def toReaderOutput(rawDs: DataFrame) = {
        var toEncodeDs = rawDs.select(colMappings.map(it => col(it.srcColumn)): _*)
        colMappings.foreach(it => toEncodeDs = toEncodeDs.withColumnRenamed(it.srcColumn, it.destColumn))
        ReaderOutput(rawDs, sanitize(encode(toEncodeDs)))
    }
}

case class ColumnMapping(
                            srcColumn: String,
                            destColumn: String
                        )