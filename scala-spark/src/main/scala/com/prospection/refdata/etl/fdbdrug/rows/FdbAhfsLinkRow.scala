package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RAHFSGC1_GCNSEQNO_LINK file (Enhanced Therapeutic Classification)
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbAhfsLinkRow(
                             gcn_seqno: String,
                             ahfs_classification: String,
                         )

object FdbAhfsLinkRow {
    object Columns {
        val GcnSeqno = "gcn_seqno"
        val Ahfs8 = StandardColumns.Ahfs8
    }
}

object RawFdbAhfsLinkRow {
    object Columns {
        val GcnSeqno = "_c0"
        val Ahfs8 = "_c1"
    }
}