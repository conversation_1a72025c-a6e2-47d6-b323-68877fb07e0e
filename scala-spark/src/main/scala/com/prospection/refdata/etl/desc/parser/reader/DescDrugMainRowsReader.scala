package com.prospection.refdata.etl.desc.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.desc.rows.{DescDrugMainRow, RawDescDrugMainRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class DescDrugMainRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[DescDrugMainRow](sparkSession, path, SparkOptions(delimiter = CommonDelimiters.TAB)) {

    override val colMappings: List[ColumnMapping] = List(
        ColumnMapping(RawDescDrugMainRow.Columns.GenericNameJp, DescDrugMainRow.Columns.DrugNameJp),
        ColumnMapping(RawDescDrugMainRow.Columns.GenericNameEn, DescDrugMainRow.Columns.DrugName),
        ColumnMapping(RawDescDrugMainRow.Columns.DrugCode, DescDrugMainRow.Columns.Code),
        ColumnMapping(RawDescDrugMainRow.Columns.DrugNameEn, DescDrugMainRow.Columns.Description)
    )

    override protected def encode(rawDs: DataFrame): Dataset[DescDrugMainRow] = {
        import sparkSession.implicits._
        rawDs.as[DescDrugMainRow]
    }
}
