package com.prospection.refdata.etl.mdvdiagnosis.rows

// Represent a row in M_Disease.txt file
case class MdvDiagnosisRow(
                              code: String,
                              icd10_code: String,
                              diagnosis_name_jp: String,
                              diagnosis_name: String,
                          )

object MdvDiagnosisRow {
    object Columns {
        val Icd10Code = "icd10_code"
        val DiagnosisNameJp = "diagnosis_name_jp"
        val DiagnosisName = "diagnosis_name"
    }
}

object RawMdvDiagnosisRow {
    object Columns {
        val DiseaseCode = "diseasecode"
        val Icd10Code = "icd10code"
        val DiseaseName = "diseasename"
        val DiseaseNameEng = "diseasename_eng"
    }
}