package com.prospection.refdata.etl.pbsitem.domain

// Represent a row in indications file
case class IndicationRow(
                            indication_prescribing_txt_id: String,
                            condition: String,
                            episodicity: String,
                            severity: String
                        )

object IndicationRow {
    object Columns {
        val IndicationPrescribingTextId: String = PbsApiColumns.IndicationPrescribingTextId
        val Condition: String = PbsApiColumns.IndicationCondition
        val Episodicity: String = PbsApiColumns.IndicationEpisodicity
        val Severity: String = PbsApiColumns.Severity
    }
}

object RawIndicationRow {
    object Columns {
        val IndicationPrescribingTextId = "indication_prescribing_txt_id"
        val Condition = "condition"
        val Episodicity = "episodicity"
        val Severity: String = "severity"
    }
}