package com.prospection.refdata.etl.pbsitem.transformer

import com.prospection.refdata.etl.common.Validator
import com.prospection.refdata.etl.pbsitem.domain.{IndicationDescriptionRow, ItemRow, OutputRow}
import org.apache.spark.sql.functions.{col, lit, when}
import org.apache.spark.sql.{Dataset, SparkSession}

class PbsItemTransformer(spark: SparkSession) {
    def transform(input: PbsItemTransformerInput): Dataset[OutputRow] = {
        import spark.implicits._

        // get Indication descriptions for each item and restriction code
        val indicationDescriptionTransformer = new IndicationDescriptionTransformer(spark)
        val indicationDS = indicationDescriptionTransformer.transform(input)

        val columnNames = Array(
            col(OutputRow.Columns.code),
            col(OutputRow.Columns.description),
            col(OutputRow.Columns.atc_code),
            col(OutputRow.Columns.drug_name),
            col(OutputRow.Columns.brand_name),
            when(col(OutputRow.Columns.restriction_flag) === "S", lit("A")).otherwise(col(OutputRow.Columns.restriction_flag)).as(OutputRow.Columns.restriction_flag),
            col(OutputRow.Columns.manufacturer_name),
            col(OutputRow.Columns.indication_id),
            col(OutputRow.Columns.indication_description),
            col(OutputRow.Columns.route_of_administration),
            col(OutputRow.Columns.li_drug_name),
            col(OutputRow.Columns.formulary),
            col(OutputRow.Columns.treatment_phase),
            col(OutputRow.Columns.indication_condition),
            col(OutputRow.Columns.indication_episodicity),
            col(OutputRow.Columns.indication_severity)
        )

        input.items.getDataset.join(input.itemAtcRelationships.getDataset, Seq(ItemRow.Columns.Code), "left")
            .join(input.manufacturers.getDataset, Seq(ItemRow.Columns.ManufacturerId), "left")
            .join(indicationDS, Seq(IndicationDescriptionRow.Columns.Code), "left")
            .withColumn("route_of_administration", lit(null).cast("string"))
            .select(columnNames.toIndexedSeq: _*)
            .as[OutputRow]
    }

    def validate(input: PbsItemTransformerInput): Unit = {
        Validator.validate(
            input.items,
            input.restrictions,
            input.manufacturers,
            input.itemAtcRelationships,
            input.itemRestrictionRelationships,
            input.criteria,
            input.restrictionPrescribingTextRelationships,
            input.prescribingTexts,
            input.criteriaParameterRelationships
        )
    }
}
