package com.prospection.refdata.etl.fdbdrug.rows

// Represent a row in RHICL1_HIC_HICLSEQNO_LINK file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbHicLinkRow(
                            hicl_seqno: String,
                            hic_seqn: String,
                        )

object FdbHicLinkRow {
    object Columns {
        val HiclSeqno = "hicl_seqno"
        val HicSeqn = "hic_seqn"
    }
}

object RawFdbHicLinkRow {
    object Columns {
        val HiclSeqno = "_c0"
        val HicSeqn = "_c1"
    }
}