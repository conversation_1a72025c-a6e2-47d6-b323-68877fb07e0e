package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RUSCD0_USC_DESC file (Universal_System_of_Classification)
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbUscDescRow(
                            usc_classification: String,
                            usc_description: String,
                        )

object FdbUscDescRow {
    object Columns {
        val Usc = StandardColumns.Usc
        val UscDesc40 = StandardColumns.UscDescription
    }
}

object RawFdbUscDescRow {
    object Columns {
        val Usc = "_c0"
        val UscDesc40 = "_c1"
    }
}