package com.prospection.refdata.etl.mdvlabresult.parser

import com.prospection.refdata.etl.mdvlabresult.datasets.MdvLabResultDataset
import com.prospection.refdata.etl.mdvlabresult.transformer.MdvLabResultTransformerInput
import org.apache.spark.sql.SparkSession

class MdvLabResultParser(sparkSession: SparkSession, pathPrefix: String) {
    def parse(fileName: String = "M_Labo.txt.gz"): MdvLabResultTransformerInput = {
        val labos = MdvLabResultRowsReader(sparkSession, s"$pathPrefix/$fileName").read()
        MdvLabResultTransformerInput(new MdvLabResultDataset(labos.dataset))
    }
}
