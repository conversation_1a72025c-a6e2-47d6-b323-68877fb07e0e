package com.prospection.refdata.etl.icd10.parser.reader

import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.icd10.rows.{CcsrRow, RawPrCcsrRow}
import org.apache.spark.sql.functions.lit
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class PrCcsrRowReader(sparkSession: SparkSession, path: String) extends CsvReader[CcsrRow](
    sparkSession,
    path,
    SparkOptions()
) {

    override val colMappings = List(
        ColumnMapping(RawPrCcsrRow.Columns.Icd10PCSCode, CcsrRow.Columns.IcdCode),
        ColumnMapping(RawPrCcsrRow.Columns.PrCcsr, CcsrRow.Columns.CcsrCategory1),
        ColumnMapping(RawPrCcsrRow.Columns.PrCcsrDescription, CcsrRow.Columns.CcsrCategory1Description),
    )

    override protected def encode(rawDs: DataFrame): Dataset[CcsrRow] = {
        import sparkSession.implicits._
        rawDs
            .withColumn(CcsrRow.Columns.CcsrCategory2, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory3, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory4, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory5, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory6, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory2Description, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory3Description, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory4Description, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory5Description, lit(null: String))
            .withColumn(CcsrRow.Columns.CcsrCategory6Description, lit(null: String))
            .as[CcsrRow]
    }
}