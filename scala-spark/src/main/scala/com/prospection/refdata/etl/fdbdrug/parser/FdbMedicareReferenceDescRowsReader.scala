package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbMedicareReferenceDescRow, RawFdbMedicareReferenceDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbMedicareReferenceDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbMedicareReferenceDescRow](sparkSession, path) {

    override val colMappings = List(
        ColumnMapping(RawFdbMedicareReferenceDescRow.Columns.McrRef, FdbMedicareReferenceDescRow.Columns.McrRef),
        ColumnMapping(RawFdbMedicareReferenceDescRow.Columns.McrBc, FdbMedicareReferenceDescRow.Columns.McrBc),
        ColumnMapping(RawFdbMedicareReferenceDescRow.Columns.McrBcdesc, FdbMedicareReferenceDescRow.Columns.McrBcdesc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbMedicareReferenceDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbMedicareReferenceDescRow]
    }
}
