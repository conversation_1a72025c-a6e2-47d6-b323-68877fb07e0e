package com.prospection.refdata.etl.desc.rows

import com.prospection.refdata.etl.common.StandardColumns

case class DescDiagnosisRow(
                                   icd10_code: String,
                                   code: String,
                                   diagnosis_name_jp: String,
                                   diagnosis_name: String
                           )

object DescDiagnosisRow {
    object Columns {
        val Icd10Code = StandardColumns.Icd10Code
        val Code = StandardColumns.Code
        val DiagnosisNameJapan = StandardColumns.DiagnosisNameJp
        val DiagnosisName = StandardColumns.DiagnosisName
    }
}

object RawDescDiagnosisRow {
    object Columns {
        val Icd10Code = "icd10_code"
        val DiseaseCode = "diseases_code"
        val Icd10Name = "icd10_name"
        val icd10NameEn = "icd10_name_en"
    }
}