package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{OrganisationRow, RawOrganisationRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class OrganisationReader(sparkSession: SparkSession, path: String) extends CsvReader[OrganisationRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {
    override val colMappings = List(
        ColumnMapping(RawOrganisationRow.Columns.OrganisationId, OrganisationRow.Columns.ManufacturerCode),
        ColumnMapping(RawOrganisationRow.Columns.Name, OrganisationRow.Columns.ManufacturerName),
    )

    override def encode(rawDs: DataFrame): Dataset[OrganisationRow] = {
        import sparkSession.implicits._
        rawDs.as[OrganisationRow]
    }

}