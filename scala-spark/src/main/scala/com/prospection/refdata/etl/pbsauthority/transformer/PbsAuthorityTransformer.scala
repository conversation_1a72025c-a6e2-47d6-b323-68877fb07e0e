package com.prospection.refdata.etl.pbsauthority.transformer

import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityOutputRow
import org.apache.spark.sql.{Dataset, SparkSession}

class PbsAuthorityTransformer(spark: SparkSession) {
    def transform(input: PbsAuthorityTransformerInput): Dataset[PbsAuthorityOutputRow] = {
        import spark.implicits._

        input.restrictions.getDataset
            .as[PbsAuthorityOutputRow]
    }
}
