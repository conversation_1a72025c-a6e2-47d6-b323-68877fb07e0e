package com.prospection.refdata.etl.mdvitem.parser

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions, StandardColumns}
import com.prospection.refdata.etl.mdvitem.rows.{MdvItemRow, RawMdvItemRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class MdvItemRowsReader(sparkSession: SparkSession, path: String) extends CsvReader[MdvItemRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.TAB)
) {

    override val colMappings = List(
        ColumnMapping(RawMdvItemRow.Columns.AtcCodeEphmra, StandardColumns.AtcEphmra),
        ColumnMapping(RawMdvItemRow.Columns.DrugGeneralName, StandardColumns.DrugNameJp),
        ColumnMapping(RawMdvItemRow.Columns.DrugGeneralNameEng, StandardColumns.DrugName),
        ColumnMapping(RawMdvItemRow.Columns.ReceiptCode, StandardColumns.Code),
        ColumnMapping(RawMdvItemRow.Columns.ReceiptName, StandardColumns.DescriptionJp),
        ColumnMapping(RawMdvItemRow.Columns.ReceiptNameEng, StandardColumns.Description),
        ColumnMapping(RawMdvItemRow.Columns.DrugUsageCode, StandardColumns.DrugUsage),
        ColumnMapping(RawMdvItemRow.Columns.GenericCode, StandardColumns.GenericFlag),
    )

    override protected def encode(rawDs: DataFrame): Dataset[MdvItemRow] = {
        import sparkSession.implicits._
        rawDs.as[MdvItemRow]
    }
}
