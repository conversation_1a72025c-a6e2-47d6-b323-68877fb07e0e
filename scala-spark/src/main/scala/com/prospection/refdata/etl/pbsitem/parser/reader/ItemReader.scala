package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{ItemRow, RawItemRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class ItemReader(sparkSession: SparkSession, path: String) extends CsvReader[ItemRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawItemRow.Columns.ItemCode, ItemRow.Columns.Code),
        ColumnMapping(RawItemRow.Columns.DrugName, ItemRow.Columns.DrugName),
        ColumnMapping(RawItemRow.Columns.BrandName, ItemRow.Columns.BrandName),
        ColumnMapping(RawItemRow.Columns.RestrictionFlag, ItemRow.Columns.RestrictionFlag),
        ColumnMapping(RawItemRow.Columns.ManufacturerId, ItemRow.Columns.ManufacturerId),
        ColumnMapping(RawItemRow.Columns.Description, ItemRow.Columns.Description),
        ColumnMapping(RawItemRow.Columns.LiDrugName, ItemRow.Columns.LiDrugName),
        ColumnMapping(RawItemRow.Columns.Formulary, ItemRow.Columns.Formulary),
    )

    override protected def encode(rawDs: DataFrame): Dataset[ItemRow] = {
        import sparkSession.implicits._
        rawDs.as[ItemRow]
    }

    override protected def sanitize(ds: Dataset[ItemRow]): Dataset[ItemRow] = ds.distinct()
}
