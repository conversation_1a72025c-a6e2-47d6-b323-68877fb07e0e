package com.prospection.refdata.etl.pbsitem.domain

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in restrictions_{date}.csv file
case class RestrictionRow(
                             res_code: String, // res_code
                             indication_id: String, // treatment_of_code
                             treatment_phase: String, // treatment_phase
                             indication_description: String, // schedule_html_text
                             criteria_relationship: String // criteria_relationship
                         )

object RestrictionRow {
    object Columns {
        val RestrictionCode: String = PbsApiColumns.RestrictionCode
        val IndicationId: String = StandardColumns.IndicationId
        val TreatmentPhase: String = PbsApiColumns.TreatmentPhase
        val IndicationDescription: String = StandardColumns.IndicationDescription
        val CriteriaRelationship: String = PbsApiColumns.CriteriaRelationship
    }
}

object RawRestrictionRow {
    object Columns {
        val ResCode = "res_code"
        val IndicationId = "treatment_of_code"
        val TreatmentPhase = "treatment_phase"
        val RestrictionIndicationText = "schedule_html_text"
        val CriteriaRelationship = "criteria_relationship"
    }
}