package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RHICD5_HIC_DESC file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbHicDescRow(
                            hic_seqn: String,
                            ingredient_name: String,
                        )

object FdbHicDescRow {
    object Columns {
        val HicSeqn = "hic_seqn"
        val HicDesc = StandardColumns.IngredientName
    }
}

object RawFdbHicDescRow {
    object Columns {
        val HicSeqn = "_c0"
        val HicDesc = "_c2"
    }
}