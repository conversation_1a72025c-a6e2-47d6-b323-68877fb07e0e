package com.prospection.refdata.etl.jmdc.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.jmdc.parser.reader.JmdcMaterialRowsReader
import com.prospection.refdata.etl.jmdc.row.JmdcMaterialRow
import org.apache.spark.sql.{Dataset, SparkSession}

class JmdcMaterialParser(
                            sparkSession: SparkSession,
                            pathPrefix: String,
                        ) {

    def parse(): JmdcMaterialParserOutput = {

        val jmdcMaterialDataSet = JmdcMaterialRowsReader(sparkSession, s"$pathPrefix/transformed/split/material_master").read()

        JmdcMaterialParserOutput(
            jmdcMaterialDataSet.dataset
        )
    }
}

case class JmdcMaterialParserOutput(dataset: Dataset[JmdcMaterialRow]) extends ParserOutput