package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.OrganisationRow
import com.prospection.refdata.etl.pbsitem.domain.OrganisationRow.Columns
import org.apache.spark.sql.Dataset

class ManufacturerDataset(dataset: Dataset[OrganisationRow]) extends EtlDataset[OrganisationRow](dataset, "manufacturers") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.ManufacturerCode,
        Columns.ManufacturerName
    )

    override def getUniqueColumn: Option[String] = Some(Columns.ManufacturerCode)
}
