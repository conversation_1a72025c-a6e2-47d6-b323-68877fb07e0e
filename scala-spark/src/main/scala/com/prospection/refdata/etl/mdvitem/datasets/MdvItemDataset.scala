package com.prospection.refdata.etl.mdvitem.datasets

import com.prospection.refdata.etl.common.{EtlDataset, StandardColumns}
import com.prospection.refdata.etl.mdvitem.rows.MdvItemRow
import org.apache.spark.sql.Dataset

class MdvItemDataset(dataset: Dataset[MdvItemRow]) extends EtlDataset[MdvItemRow](dataset, "drugs") {
    override def getNonNullableColumns: Set[String] = Set(
        StandardColumns.Code,
        StandardColumns.AtcEphmra,
    )
}
