package com.prospection.refdata.etl.common.job

case class EtlJobParams(
                           // YYYYMMDD
                           version: String,

                           // { '{Identifying Key}' : "{Full S3/File Path}" }
                           inputPaths: scala.collection.Map[String, String],

                           // s3://pd-au-{env}-ref-data-v2/items/draft/raw-items/{classification}
                           outputPath: String,

                           // s3://pd-au-{env}-ref-data-v2/warehouse/{classification}
                           warehousePath: String,

                           // s3://pd-au-{env}-ref-data-v2/snapshots/{classification}
                           snapshotPath: String,
                       )