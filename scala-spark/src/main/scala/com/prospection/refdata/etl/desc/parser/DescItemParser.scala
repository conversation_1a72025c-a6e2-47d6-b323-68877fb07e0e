package com.prospection.refdata.etl.desc.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.desc.parser.reader.{DescDrugEphmraRowsReader, DescDrugMainRowsReader, DescDrugReceRowsReader, DescProcedureRowsReader}
import com.prospection.refdata.etl.desc.rows.{DescDrugEphmraRow, DescDrugMainRow, DescDrugReceRow, DescProcedureRow}
import org.apache.spark.sql.{Dataset, SparkSession}

class DescItemParser(
                            sparkSession: SparkSession,
                            pathPrefix: String,
                    ) {

    def parse(): DescItemParserOutput = {
        val descDrugEphmraOutput = DescDrugEphmraRowsReader(sparkSession, s"$pathPrefix/m_drug_ephmra_atc").read()
        val descDrugMainOutput = DescDrugMainRowsReader(sparkSession, s"$pathPrefix/m_drug_main").read()
        val descDrugReceOutput = DescDrugReceRowsReader(sparkSession, s"$pathPrefix/m_drug_rece_all").read()
        val descProcedureOutput = DescProcedureRowsReader(sparkSession, s"$pathPrefix/m_med_treat_all").read()

        DescItemParserOutput(
            descDrugEphmra = descDrugEphmraOutput.dataset,
            descDrugMain = descDrugMainOutput.dataset,
            descDrugRece = descDrugReceOutput.dataset,
            descProcedure = descProcedureOutput.dataset
        )
    }
}

case class DescItemParserOutput(
                                       descDrugEphmra: Dataset[DescDrugEphmraRow],
                                       descDrugMain: Dataset[DescDrugMainRow],
                                       descDrugRece: Dataset[DescDrugReceRow],
                                       descProcedure: Dataset[DescProcedureRow]

                               ) extends ParserOutput