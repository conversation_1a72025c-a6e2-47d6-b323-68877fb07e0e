package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbIndicationLinkRow, RawFdbIndicationLinkRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbIndicationLinkRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbIndicationLinkRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbIndicationLinkRow.Columns.Indcts, FdbIndicationLinkRow.Columns.Indcts),
        ColumnMapping(RawFdbIndicationLinkRow.Columns.GcnSeqno, FdbIndicationLinkRow.Columns.GcnSeqno),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbIndicationLinkRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbIndicationLinkRow]
    }
}
