package com.prospection.refdata.etl.ndcdispensing.datasets

import com.prospection.refdata.etl.common.{EtlDataset, StandardColumns}
import com.prospection.refdata.etl.ndcdispensing.rows.NdcDispensingRow
import org.apache.spark.sql.Dataset

class NdcDispensingDataset(dataset: Dataset[NdcDispensingRow]) extends EtlDataset[NdcDispensingRow](dataset, "dispensing") {
    override def getNonNullableColumns: Set[String] = Set(
        StandardColumns.Code,
    )
}
