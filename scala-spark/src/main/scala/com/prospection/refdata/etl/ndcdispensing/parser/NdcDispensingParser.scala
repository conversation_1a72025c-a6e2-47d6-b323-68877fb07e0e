package com.prospection.refdata.etl.ndcdispensing.parser

import com.prospection.refdata.etl.ndcdispensing.datasets.NdcDispensingDataset
import com.prospection.refdata.etl.ndcdispensing.transformer.NdcDispensingTransformerInput
import org.apache.spark.sql.SparkSession

class NdcDispensingParser(
                             sparkSession: SparkSession,
                             pathPrefix: String,
                             version: String,
                         ) {

    def parse(): NdcDispensingTransformerInput = {
        val dispensing = NdcDispensingRowsReader(sparkSession, s"$pathPrefix/ndc.tsv").read()

        NdcDispensingTransformerInput(
            new NdcDispensingDataset(dispensing.dataset),
        )
    }
}
