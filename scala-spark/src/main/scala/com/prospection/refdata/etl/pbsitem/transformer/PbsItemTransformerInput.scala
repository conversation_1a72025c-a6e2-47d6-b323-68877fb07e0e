package com.prospection.refdata.etl.pbsitem.transformer

import com.prospection.refdata.etl.pbsitem.datasets.{CriteriaDataset, CriteriaParameterRelationshipDataset, IndicationDataset, ItemAtcRelationshipDataset, ItemDataset, ItemRestrictionRelationshipDataset, ManufacturerDataset, PrescribingTextDataset, RestrictionDataset, RestrictionPrescribingTextRelationshipDataset}


case class PbsItemTransformerInput(
                                      items: ItemDataset,
                                      restrictions: RestrictionDataset,
                                      manufacturers: ManufacturerDataset,
                                      itemAtcRelationships: ItemAtcRelationshipDataset,
                                      itemRestrictionRelationships: ItemRestrictionRelationshipDataset,
                                      criteria: CriteriaDataset,
                                      restrictionPrescribingTextRelationships: RestrictionPrescribingTextRelationshipDataset,
                                      prescribingTexts: PrescribingTextDataset,
                                      criteriaParameterRelationships: CriteriaParameterRelationshipDataset,
                                      indication: IndicationDataset
                                  )
