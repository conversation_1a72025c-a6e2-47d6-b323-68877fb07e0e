package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbNdcDescRow, RawFdbNdcDescRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbNdcDescRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbNdcDescRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbNdcDescRow.Columns.Mfg, FdbNdcDescRow.Columns.Mfg),
        ColumnMapping(RawFdbNdcDescRow.Columns.Lblrid, FdbNdcDescRow.Columns.Lblrid),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbNdcDescRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbNdcDescRow]
    }
}
