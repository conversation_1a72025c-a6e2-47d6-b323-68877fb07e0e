package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.reader.ColumnMapping
import com.prospection.refdata.etl.fdbdrug.rows.{FdbStrengthUomRow, RawFdbStrengthUomRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class FdbStrengthUomRowsReader(sparkSession: SparkSession, path: String) extends FdbDrugReader[FdbStrengthUomRow](
    sparkSession,
    path
) {

    override val colMappings = List(
        ColumnMapping(RawFdbStrengthUomRow.Columns.UomId, FdbStrengthUomRow.Columns.UomId),
        ColumnMapping(RawFdbStrengthUomRow.Columns.UomPreferredDesc, FdbStrengthUomRow.Columns.UomPreferredDesc),
    )

    override protected def encode(rawDs: DataFrame): Dataset[FdbStrengthUomRow] = {
        import sparkSession.implicits._
        rawDs.as[FdbStrengthUomRow]
    }
}
