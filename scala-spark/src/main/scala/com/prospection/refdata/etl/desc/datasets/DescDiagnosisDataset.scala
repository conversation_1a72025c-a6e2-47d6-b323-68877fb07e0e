package com.prospection.refdata.etl.desc.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.desc.rows.DescDiagnosisRow
import com.prospection.refdata.etl.desc.rows.DescDiagnosisRow.Columns
import org.apache.spark.sql.Dataset

class DescDiagnosisDataset(dataset: Dataset[DescDiagnosisRow]) extends EtlDataset[DescDiagnosisRow](dataset, "diagnosis") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.Icd10Code,
        Columns.Code,
        Columns.DiagnosisNameJapan,
        Columns.DiagnosisName
    )
}
