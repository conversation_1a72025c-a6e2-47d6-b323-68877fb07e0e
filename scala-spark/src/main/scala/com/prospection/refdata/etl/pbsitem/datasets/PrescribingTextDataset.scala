package com.prospection.refdata.etl.pbsitem.datasets

import com.prospection.refdata.etl.common.EtlDataset
import com.prospection.refdata.etl.pbsitem.domain.PrescribingTextRow
import com.prospection.refdata.etl.pbsitem.domain.PrescribingTextRow.Columns
import org.apache.spark.sql.Dataset

class PrescribingTextDataset(dataset: Dataset[PrescribingTextRow]) extends EtlDataset[PrescribingTextRow](dataset, "prescribing-texts") {
    override def getNonNullableColumns: Set[String] = Set(
        Columns.PrescribingTextId,
        Columns.PrescribingType
    )
}
