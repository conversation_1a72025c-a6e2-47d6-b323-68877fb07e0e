package com.prospection.refdata.etl.mdvitem.rows

// Represent a row in M_Drug.txt file
case class MdvItemRow(
                         atc_ephmra: String,
                         drug_name_jp: String,
                         drug_name: String,
                         code: String,
                         description_jp: String,
                         description: String,
                         drug_usage: String,
                         generic_flag: String,
                     )

object RawMdvItemRow {
    object Columns {
        val AtcCodeEphmra = "atccode_ephmra"
        val DrugGeneralName = "druggeneralname"
        val DrugGeneralNameEng = "druggeneralname_eng"
        val ReceiptCode = "receiptcode"
        val ReceiptName = "receiptname"
        val ReceiptNameEng = "receiptname_eng"
        val DrugUsageCode = "drugusagecode"
        val GenericCode = "genericcode"
    }
}