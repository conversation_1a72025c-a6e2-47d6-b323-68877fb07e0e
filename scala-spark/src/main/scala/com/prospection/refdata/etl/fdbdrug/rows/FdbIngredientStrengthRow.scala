package com.prospection.refdata.etl.fdbdrug.rows

import com.prospection.refdata.etl.common.StandardColumns

// Represent a row in RGCNSTR0_INGREDIENT_STRENGTH file
// Column name (_c0, _c1,...) base on index when read file with header = false. The index find in nddf_plus_ddl
case class FdbIngredientStrengthRow(
                                       gcn_seqno: String,
                                       strength_uom_id: String,
                                       strength_number: String
                                   )

object FdbIngredientStrengthRow {
    object Columns {
        val GcnSeqno = "gcn_seqno"
        val StrengthUomId = "strength_uom_id"
        val Strength = StandardColumns.StrengthNumber
    }
}

object RawIngredientStrengthRow {
    object Columns {
        val GcnSeqno = "_c0"
        val Strength = "_c3"
        val StrengthUomId = "_c4"
    }
}