package com.prospection.refdata.etl.fdbdrug.parser

import com.prospection.refdata.etl.common.ParserOutput
import com.prospection.refdata.etl.fdbdrug.rows._
import org.apache.spark.sql.{Dataset, SparkSession}

class FdbDrugParser(
                       sparkSession: SparkSession,
                       pathPrefix: String,
                   ) {

    private final val NddfPlusDb = s"$pathPrefix/transformed/unpacked/nddf_plus_db"
    private final val PackagedProduct = s"$NddfPlusDb/NDDF Descriptive and Pricing/NDDF BASICS 3.0/Packaged Product"
    private final val GenericFormulationIngredient = s"$NddfPlusDb/NDDF Descriptive and Pricing/NDDF BASICS 3.0/Generic Formulation and Ingredient"
    private final val MiscellaneousTherapeuticClass = s"$NddfPlusDb/NDDF Descriptive and Pricing/NDDF BASICS 3.0/Miscellaneous Therapeutic Class"
    private final val NddfFml20 = s"$NddfPlusDb/NDDF Descriptive and Pricing/NDDF FML 2.0"
    private final val NddfEtc10 = s"$NddfPlusDb/NDDF Descriptive and Pricing/NDDF ETC 1.0"
    private final val Indm20 = s"$NddfPlusDb/Indications/INDM 2.0"
    private final val Medicare30 = s"$NddfPlusDb/Medicare/MEDICARE 3.0"
    private final val UniversalSystemClassification10 = s"$NddfPlusDb/Universal System of Classification/USC 1.0"

    def parse(): FdbDrugParserOutput = {
        val nationDrugCodes = FdbNdcRowsReader(sparkSession, s"$PackagedProduct/RNDC14_NDC_MSTR").read()
        val nationDrugCodeDescriptions = FdbNdcDescRowsReader(sparkSession, s"$PackagedProduct/RLBLRID3_LBLR_DESC").read()
        val nationDrugDeletionReasons = FdbNdcDeletionReasonRowsReader(sparkSession, s"$PackagedProduct/RNDCDR0_NDC_DELETION_REASON").read()

        val doseDescriptions = FdbDoseDescRowsReader(sparkSession, s"$GenericFormulationIngredient/RDOSED2_DOSE_DESC").read()
        val genericCodeNumbers = FdbGenericCodeNumberRowsReader(sparkSession, s"$GenericFormulationIngredient/RGCNSEQ4_GCNSEQNO_MSTR").read()
        val ingredientStrengths = FdbIngredientStrengthRowsReader(sparkSession, s"$GenericFormulationIngredient/RGCNSTR0_INGREDIENT_STRENGTH").read()
        val strengthUoms = FdbStrengthUomRowsReader(sparkSession, s"$GenericFormulationIngredient/RSTRUOM0_STRENGTH_UOM").read()
        val routeDescriptions = FdbRouteDescRowsReader(sparkSession, s"$GenericFormulationIngredient/RROUTED3_ROUTE_DESC").read()
        val hierarchicalIngredientList = FdbHierarchicalIngredientListRowsReader(sparkSession, s"$GenericFormulationIngredient/RHICLSQ2_HICLSEQNO_MSTR").read()
        val hicDescriptions = FdbHicDescRowsReader(sparkSession, s"$GenericFormulationIngredient/RHICD5_HIC_DESC").read()
        val hicLinks = FdbHicLinkRowsReader(sparkSession, s"$GenericFormulationIngredient/RHICL1_HIC_HICLSEQNO_LINK").read()

        val atcLinks = FdbAtcLinkRowsReader(sparkSession, s"$MiscellaneousTherapeuticClass/RATCGC0_ATC_GCNSEQNO_LINK").read()
        val hierarchicalIngredients = FdbHierarchicalIngredientRowsReader(sparkSession, s"$MiscellaneousTherapeuticClass/RHIC3D3_HIC_THERAP_CLASS_DESC").read()
        val ahfsLinks = FdbAhfsLinkRowsReader(sparkSession, s"$MiscellaneousTherapeuticClass/RAHFSGC1_GCNSEQNO_LINK").read()
        val ahfsDescriptions = FdbAhfsDescRowsReader(sparkSession, s"$MiscellaneousTherapeuticClass/RAHFSD1_DESC").read()

        val indicationLinks = FdbIndicationLinkRowsReader(sparkSession, s"$Indm20/RINDMGC0_INDCTS_GCNSEQNO_LINK").read()
        val indications = FdbIndicationRowsReader(sparkSession, s"$Indm20/RINDMMA2_INDCTS_MSTR").read()

        val icdSearches = FdbIcdSearchRowsReader(sparkSession, s"$NddfFml20/RFMLISR1_ICD_SEARCH").read()
        val diseaseIdentifiers = FdbDiseaseIdentifierRowsReader(sparkSession, s"$NddfFml20/RFMLDX0_DXID").read()

        val medicares = FdbMedicareRowsReader(sparkSession, s"$Medicare30/RMCRMA1_MEDICARE_MSTR").read()
        val medicareReferenceDescriptions = FdbMedicareReferenceDescRowsReader(sparkSession, s"$Medicare30/RMCRRD1_MEDICARE_REG_REF_DESC").read()

        val etcs = FdbEtcRowsReader(sparkSession, s"$NddfEtc10/RETCGC0_ETC_GCNSEQNO").read()
        val etcIds = FdbEtcIdRowsReader(sparkSession, s"$NddfEtc10/RETCTBL0_ETC_ID").read()

        val uscLinks = FdbUscNdcLinkRowsReader(sparkSession, s"$UniversalSystemClassification10/RUSCNDC0_USC_NDC_LINK").read()
        val uscDescriptions = FdbUscDescRowsReader(sparkSession, s"$UniversalSystemClassification10/RUSCD0_USC_DESC").read()

        FdbDrugParserOutput(
            ndcDataset = nationDrugCodes.dataset,
            ndcDescDataset = nationDrugCodeDescriptions.dataset,
            ndcDeletionReasonDataset = nationDrugDeletionReasons.dataset,
            doseDescDataset = doseDescriptions.dataset,
            genericCodeNumberDataset = genericCodeNumbers.dataset,
            ingredientStrengthDataset = ingredientStrengths.dataset,
            strengthUomDataset = strengthUoms.dataset,
            routeDescDataset = routeDescriptions.dataset,
            hierarchicalIngredientListDataset = hierarchicalIngredientList.dataset,
            hicDescDataset = hicDescriptions.dataset,
            hicLinkDataset = hicLinks.dataset,
            atcLinkDataset = atcLinks.dataset,
            hierarchicalIngredientDataset = hierarchicalIngredients.dataset,
            ahfsLinkDataset = ahfsLinks.dataset,
            ahfsDescDataset = ahfsDescriptions.dataset,
            indicationLinkDataset = indicationLinks.dataset,
            indicationDataset = indications.dataset,
            icdSearchDataset = icdSearches.dataset,
            diseaseIdentifierDataset = diseaseIdentifiers.dataset,
            medicareDataset = medicares.dataset,
            medicareReferenceDescDataset = medicareReferenceDescriptions.dataset,
            etcDataset = etcs.dataset,
            etcIdDataset = etcIds.dataset,
            uscLinkDataset = uscLinks.dataset,
            uscDescDataset = uscDescriptions.dataset,
        )
    }
}

case class FdbDrugParserOutput(
                                  ndcDataset: Dataset[FdbNdcRow],
                                  ndcDescDataset: Dataset[FdbNdcDescRow],
                                  ndcDeletionReasonDataset: Dataset[FdbNdcDeletionReasonRow],
                                  doseDescDataset: Dataset[FdbDoseDescRow],
                                  genericCodeNumberDataset: Dataset[FdbGenericCodeNumberRow],
                                  ingredientStrengthDataset: Dataset[FdbIngredientStrengthRow],
                                  strengthUomDataset: Dataset[FdbStrengthUomRow],
                                  routeDescDataset: Dataset[FdbRouteDescRow],
                                  hierarchicalIngredientListDataset: Dataset[FdbHierarchicalIngredientListRow],
                                  hicDescDataset: Dataset[FdbHicDescRow],
                                  hicLinkDataset: Dataset[FdbHicLinkRow],
                                  atcLinkDataset: Dataset[FdbAtcLinkRow],
                                  hierarchicalIngredientDataset: Dataset[FdbHierarchicalIngredientRow],
                                  ahfsLinkDataset: Dataset[FdbAhfsLinkRow],
                                  ahfsDescDataset: Dataset[FdbAhfsDescRow],
                                  indicationLinkDataset: Dataset[FdbIndicationLinkRow],
                                  indicationDataset: Dataset[FdbIndicationRow],
                                  icdSearchDataset: Dataset[FdbIcdSearchRow],
                                  diseaseIdentifierDataset: Dataset[FdbDiseaseIdentifierRow],
                                  medicareDataset: Dataset[FdbMedicareRow],
                                  medicareReferenceDescDataset: Dataset[FdbMedicareReferenceDescRow],
                                  etcDataset: Dataset[FdbEtcRow],
                                  etcIdDataset: Dataset[FdbEtcIdRow],
                                  uscLinkDataset: Dataset[FdbUscNdcLinkRow],
                                  uscDescDataset: Dataset[FdbUscDescRow],
                              ) extends ParserOutput