package com.prospection.refdata.etl.icd10.transformer

import com.prospection.refdata.etl.icd10.rows.{CcsrRow, Icd10OrderRow, Icd9Row, IcdGemRow}
import org.apache.spark.sql.Dataset

case class IcdTransformerInput(
                                           icd10s: Map[Int, Dataset[Icd10OrderRow]],
                                           icd9LongDesc: Dataset[Icd9Row],
                                           icd9ShortDesc: Dataset[Icd9Row],
                                           ccsrDataset: Dataset[CcsrRow],
                                           icd9Gems: Dataset[IcdGemRow]
                                       )
