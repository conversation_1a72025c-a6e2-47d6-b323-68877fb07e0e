package com.prospection.refdata.etl.pbsitem.parser.reader

import com.prospection.refdata.etl.common.reader.{ColumnMapping, CsvReader}
import com.prospection.refdata.etl.common.{CommonDelimiters, SparkOptions}
import com.prospection.refdata.etl.pbsitem.domain.{IndicationRow, RawIndicationRow}
import org.apache.spark.sql.{DataFrame, Dataset, SparkSession}

case class IndicationReader(sparkSession: SparkSession, path: String) extends CsvReader[IndicationRow](
    sparkSession,
    path,
    SparkOptions(delimiter = CommonDelimiters.COMMA)
) {

    override val colMappings = List(
        ColumnMapping(RawIndicationRow.Columns.IndicationPrescribingTextId, IndicationRow.Columns.IndicationPrescribingTextId),
        ColumnMapping(RawIndicationRow.Columns.Condition, IndicationRow.Columns.Condition),
        ColumnMapping(RawIndicationRow.Columns.Episodicity, IndicationRow.Columns.Episodicity),
        ColumnMapping(RawIndicationRow.Columns.Severity, IndicationRow.Columns.Severity),
    )

    override protected def encode(rawDs: DataFrame): Dataset[IndicationRow] = {
        import sparkSession.implicits._
        rawDs.as[IndicationRow]
    }

}
