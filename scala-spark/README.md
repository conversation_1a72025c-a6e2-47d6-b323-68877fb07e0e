# scala-spark

## Why do we need this module?
Native spark functions are not available in kotlin because only scala/python are the main languages for spark.
This means we can't use any syntactic sugar and have to use native Java syntax in Kotlin, which results in much more/dirtier Java code than expected.
Also, it's easier to find useful resources and examples in Scala.

With this module, we'll compile Scala code first, and the main Kotlin module (on the root of this project) will import this module and call the functions written in Scala.