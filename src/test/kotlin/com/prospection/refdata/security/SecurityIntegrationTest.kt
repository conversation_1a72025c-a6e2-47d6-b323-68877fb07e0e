package com.prospection.refdata.security

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.web.security.AuthTokenFilter.Companion.AUTH_HEADER
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.test.context.support.WithAnonymousUser
import org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.test.web.servlet.setup.DefaultMockMvcBuilder
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.context.WebApplicationContext
import java.time.LocalDate
import java.util.*

/**
 * Integration tests for security configuration.
 */
class SecurityIntegrationTest : AbstractIntegrationTest() {

    @Value("\${application.security.secret}")
    private lateinit var securitySecret: String

    @Autowired
    private lateinit var context: WebApplicationContext

    @Autowired
    private lateinit var codingSystemJpaRepository: CodingSystemJpaRepository

    private val mvc by lazy {
        MockMvcBuilders
            .webAppContextSetup(context)
            .apply<DefaultMockMvcBuilder>(springSecurity())
            .build()
    }

    @Test
    fun `should return success status when calling a public endpoint`() {
        mvc.perform(get("/actuator/health"))
            .andExpect(MockMvcResultMatchers.status().isOk)
    }

    @Test
    fun `should return forbidden status when user is not admin`() {
        val authToken = authToken(isAdmin = false)
        mvc.perform(get("/api/ref-data-v2/job/latest").header(AUTH_HEADER, authToken))
            .andExpect(MockMvcResultMatchers.status().isForbidden)
    }

    @Test
    fun `should return success status when user is admin`() {
        val authToken = authToken(isAdmin = true)
        mvc.perform(get("/api/ref-data-v2/job/latest").header(AUTH_HEADER, authToken))
            .andExpect(MockMvcResultMatchers.status().isOk)
    }

    @Test
    @WithAnonymousUser
    fun `should allow specific apis for both admin and non-admin users`() {
        val routes = listOf("/api/ref-data-v2/versions/testPublishVersion/item-groups")
        val tokens = listOf(authToken(isAdmin = true), authToken(isAdmin = false))
        routes.forEach { route ->
            mvc.perform(get(route))
                .andExpect(MockMvcResultMatchers.status().isForbidden)
            tokens.forEach { token ->
                mvc.perform(get(route).header(AUTH_HEADER, token))
                    .andExpect(MockMvcResultMatchers.status().isOk)
            }
        }
    }

    @Test
    @WithAnonymousUser
    fun `should allow specific apis for anonymous users`() {
        val classification = codingSystemJpaRepository.save(CodingSystemEntity(name = "classification-1"))
        mvc.perform(
            get("/api/ref-data-v2/coding-systems/check-etl-job-possibility")
                .param("classification", classification.name)
                .param("pullVersion", LocalDate.of(2022, 4, 6).toString())
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
    }

    private fun authToken(isAdmin: Boolean): String {
        val signingKey = Keys.hmacShaKeyFor(Base64.getDecoder().decode(securitySecret))
        return Jwts.builder()
            .subject("<EMAIL>")
            .claim("admin", isAdmin)
            .signWith(signingKey)
            .compact()
    }
}
