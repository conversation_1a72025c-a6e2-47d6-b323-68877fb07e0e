package com.prospection.refdata.rules.application.rest

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.items.integration.EnrichedItemsMetadataEntity
import com.prospection.refdata.items.integration.EnrichedItemsMetadataJpaRepository
import com.prospection.refdata.items.integration.RawItemsMetadataEntity
import com.prospection.refdata.items.integration.RawItemsMetadataJpaRepository
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeDto
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeValueDto
import com.prospection.refdata.rules.domain.Type
import com.prospection.refdata.rules.integration.EnrichedAttributeEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeJpaRepository
import com.prospection.refdata.rules.integration.EnrichedAttributeValueEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeValueJpaRepository
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime

internal class AttributeControllerTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var enrichedAttributeRepository: EnrichedAttributeJpaRepository

    @Autowired
    private lateinit var enrichedAttributeValueRepository: EnrichedAttributeValueJpaRepository

    @Autowired
    private lateinit var classificationRepository: CodingSystemJpaRepository

    @Autowired
    private lateinit var rawItemsMetadataJpaRepository: RawItemsMetadataJpaRepository

    @Autowired
    private lateinit var enrichedItemsMetadataJpaRepository: EnrichedItemsMetadataJpaRepository

    @Autowired
    private lateinit var controller: AttributeController

    private lateinit var classificationTest1: CodingSystemEntity
    private lateinit var classificationTest2: CodingSystemEntity

    @BeforeEach
    override fun setUp() {
        super.setUp()
        val enrichedAttribute = enrichedAttributeRepository.save(
            EnrichedAttributeEntity(
                uuid = "enriched_attribute_uuid1",
                name = "Therapy Area",
            )
        )

        enrichedAttributeValueRepository.save(
            EnrichedAttributeValueEntity(
                uuid = "enriched_attribute_value_uuid1",
                value = "MM",
                enrichedAttribute = enrichedAttribute
            )
        )

        enrichedAttributeValueRepository.save(
            EnrichedAttributeValueEntity(
                uuid = "enriched_attribute_value_uuid2",
                value = "ADHD",
                enrichedAttribute = enrichedAttribute
            )
        )

        classificationTest1 = classificationRepository.save(CodingSystemEntity(name = "DRG Diagnosis"))
        classificationTest2 = classificationRepository.save(CodingSystemEntity(name = "DRG Dispensing"))
    }

    @AfterEach
    fun clear() {
        rawItemsMetadataJpaRepository.deleteAll()
        enrichedItemsMetadataJpaRepository.deleteAll()
        classificationRepository.deleteAll()
    }

    @Test
    fun `List enriched attributes`() {
        val enrichedAttributes = controller.listEnrichedAttributes()

        assertEquals(1, enrichedAttributes.size)

        assertEquals(
            EnrichedAttributeDto(
                id = "enriched_attribute_uuid1",
                name = "Therapy Area",
            ), enrichedAttributes[0]
        )
    }

    @Test
    fun `Find attribute values by attribute UUID`() {
        val attributeValues = controller.findEnrichedAttributeValuesByAttributeUuid("enriched_attribute_uuid1")

        assertEquals(
            listOf(
                EnrichedAttributeValueDto(
                    id = "enriched_attribute_value_uuid2",
                    value = "ADHD",
                    enrichedAttribute = EnrichedAttributeDto(
                        id = "enriched_attribute_uuid1",
                        name = "Therapy Area",
                    ),
                ),
                EnrichedAttributeValueDto(
                    id = "enriched_attribute_value_uuid1",
                    value = "MM",
                    enrichedAttribute = EnrichedAttributeDto(
                        id = "enriched_attribute_uuid1",
                        name = "Therapy Area",
                    ),
                ),
            ), attributeValues
        )

        assertEquals(
            emptyList<EnrichedAttributeValueDto>(),
            controller.findEnrichedAttributeValuesByAttributeUuid("not_existing_attribute_uuid")
        )
    }

    @Test
    fun `List attribute types and attributes per type`() {
        val metaDataEntity1 = RawItemsMetadataEntity(
            sourceAttributes = listOf("source_code", "a", "b"),
            publishedItemVersion = null,
            totalItem = 1,
            codingSystem = classificationTest1,
            newItem = 1,
            deletedItem = 0,
            createdBy = "thomas",
            createdAt = LocalDateTime.now()
        )

        val metaDataEntity2 = RawItemsMetadataEntity(
            sourceAttributes = listOf("source_code", "a", "c"),
            publishedItemVersion = null,
            totalItem = 1,
            codingSystem = classificationTest2,
            newItem = 1,
            deletedItem = 0,
            createdBy = "thomas",
            createdAt = LocalDateTime.now()
        )

        rawItemsMetadataJpaRepository.saveAll(listOf(metaDataEntity1, metaDataEntity2))

        val attributeTypes = controller.listAttributeTypes()

        // classification
        assertEquals(2, attributeTypes.size)
        attributeTypes.find { it.type == Type.CODING_SYSTEM }!!.let {
            assertEquals(1, it.attributes.size)

            val attribute = it.attributes.first()

            assertEquals("coding_system", attribute.name)

            val attributeValues = attribute.values

            assertEquals(2, attributeValues.size)
            assertEquals("DRG Diagnosis", attributeValues[0])
            assertEquals("DRG Dispensing", attributeValues[1])
        }

        assertThat(attributeTypes.find { it.type == Type.SOURCE_ATTRIBUTE }!!.attributes)
            .extracting("name")
            .containsExactly("a", "b", "c", "source_code")
    }

    @Test
    fun `list item group rule attributes including source attributes, enriched attributes and classification`() {
        // Mock enriched metadata
        val metaDataEntity = EnrichedItemsMetadataEntity(
            codingSystem = classificationTest1,
            createdBy = "markus",
            latestEnrichmentRuleRevisionId = 10,
            sourceAttributes = listOf(
                "source_code",
                "source_attribute_2",
                "source_attribute_1",
                "source_attribute_3",
            ),
            enrichedAttributes = listOf(
                "therapy_area"
            ),
            createdAt = LocalDateTime.now()
        )

        enrichedItemsMetadataJpaRepository.save(metaDataEntity)

        // When
        val attributeTypes = controller.listItemGroupRuleAttributes()

        // Then
        assertEquals(3, attributeTypes.size)

        // Verify classification
        attributeTypes.find { it.type == Type.CODING_SYSTEM }!!.let {
            assertEquals(1, it.attributes.size)

            val attribute = it.attributes.first()
            assertEquals("coding_system", attribute.name)

            val attributeValues = attribute.values
            assertEquals(2, attributeValues.size)
            assertEquals("DRG Diagnosis", attributeValues[0])
            assertEquals("DRG Dispensing", attributeValues[1])
        }

        // Verify enriched attribute
        attributeTypes.find { it.type == Type.ENRICHED_ATTRIBUTE }!!.let {
            assertEquals(1, it.attributes.size)

            val attribute = it.attributes.first()
            assertEquals("therapy_area", attribute.name)

            val attributeValues = attribute.values
            assertEquals(2, attributeValues.size)
            assertEquals("ADHD", attributeValues[0])
            assertEquals("MM", attributeValues[1])
        }

        assertThat(attributeTypes.find { it.type == Type.SOURCE_ATTRIBUTE }!!.attributes)
            .extracting("name")
            .containsExactly("source_attribute_1", "source_attribute_2", "source_attribute_3", "source_code")
    }

}
