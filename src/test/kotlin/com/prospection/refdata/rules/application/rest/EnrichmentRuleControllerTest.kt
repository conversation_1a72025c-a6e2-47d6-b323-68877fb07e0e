package com.prospection.refdata.rules.application.rest

import com.prospection.refdata.AbstractControllerIntegrationTest
import com.fasterxml.jackson.core.type.TypeReference
import com.prospection.refdata.mock.MockDateTime
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeValueDtoMapper
import com.prospection.refdata.rules.application.rest.dto.EnrichmentRuleDto
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.integration.EnrichedAttributeEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeJpaRepository
import com.prospection.refdata.rules.integration.EnrichedAttributeValueEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeValueEntityMapper
import com.prospection.refdata.rules.integration.EnrichedAttributeValueJpaRepository
import com.prospection.refdata.rules.integration.EnrichmentRuleEntity
import com.prospection.refdata.rules.integration.EnrichmentRuleEntityMapper
import com.prospection.refdata.rules.integration.EnrichmentRuleJpaRepository
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.util.*

/**
 * Test class for EnrichmentRuleController.
 */
internal class EnrichmentRuleControllerTest : AbstractControllerIntegrationTest() {
    companion object {
        lateinit var enrichedAttributeEntity: EnrichedAttributeEntity
        lateinit var enrichedAttributeValueEntity: EnrichedAttributeValueEntity
        const val BASE_URL = "/api/ref-data-v2/enrichment-rules"
    }

    @Autowired
    private lateinit var enrichmentRuleRepository: EnrichmentRuleJpaRepository

    @Autowired
    private lateinit var enrichedAttributeJpaRepository: EnrichedAttributeJpaRepository

    @Autowired
    private lateinit var enrichedAttributeValueJpaRepository: EnrichedAttributeValueJpaRepository

    @Autowired
    private lateinit var enrichedAttributeValueEntityMapper: EnrichedAttributeValueEntityMapper

    @Autowired
    private lateinit var enrichedAttributeValueDtoMapper: EnrichedAttributeValueDtoMapper

    @Autowired
    private lateinit var enrichmentRuleEntityMapper: EnrichmentRuleEntityMapper

    @Autowired
    private lateinit var dateTime: MockDateTime

    private lateinit var enrichmentRule: EnrichmentRule

    private lateinit var deletedEnrichmentRule: EnrichmentRule

    @BeforeEach
    override fun setUp() {
        super.setUp()
        enrichedAttributeValueEntity = createEnrichedAttributeValue(
            attr = "Therapy Area",
            value = "MM"
        )
        enrichedAttributeEntity = enrichedAttributeValueEntity.enrichedAttribute

        val newErEntity = EnrichmentRuleEntity(
            uuid = UUID.randomUUID().toString(),
            enrichedAttributeValue = enrichedAttributeValueEntity,
            rule = """{"field":"rule ABC"}""",
            goal = "goal1",
            lastModifiedAt = dateTime.past(),
            lastModifiedBy = "<EMAIL>",
            version = 5
        )

        enrichmentRule = enrichmentRuleEntityMapper.toDomain(enrichmentRuleRepository.save(newErEntity))


        val deletedEnrichedAttributeValue = createEnrichedAttributeValue(
            persistedAttr = enrichedAttributeEntity,
            value = "ADHD"
        )

        val deletedEnrichmentRuleEntity = EnrichmentRuleEntity(
            uuid = UUID.randomUUID().toString(),
            rule = """{"field":"rule DEF"}""",
            lastModifiedBy = "<EMAIL>",
            lastModifiedAt = dateTime.now(),
            enrichedAttributeValue = deletedEnrichedAttributeValue,
            deleted = 1,
            goal = "goal2"
        )
        deletedEnrichmentRule = enrichmentRuleEntityMapper.toDomain(enrichmentRuleRepository.save(deletedEnrichmentRuleEntity))
    }

    @AfterEach
    fun clear() {
        enrichmentRuleRepository.deleteAll()
        enrichedAttributeValueJpaRepository.deleteAll()
        enrichedAttributeJpaRepository.deleteAll()
    }

    @Test
    fun `List enrichment rules`() {

        val response = performGet("$BASE_URL?shouldDisplayArchived=true")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val enrichmentRuleDtos = parseResponse(response, object : TypeReference<List<EnrichmentRuleDto>>() {})

        assertEquals(2, enrichmentRuleDtos.size)

        val validRuleDto = EnrichmentRuleDto(
            id = enrichmentRule.id,
            rule = enrichmentRule.rule,
            enrichedAttributeValue = getEnrichedAttrValueDto(enrichmentRule.enrichedAttributeValue),
            goal = enrichmentRule.goal,
            version = enrichmentRule.version,
            lastModifiedBy = enrichmentRule.lastModifiedBy,
            lastModifiedAt = enrichmentRule.lastModifiedAt,
            deleted = enrichmentRule.deleted
        )

        val deletedRuleDto = EnrichmentRuleDto(
            id = deletedEnrichmentRule.id,
            rule = deletedEnrichmentRule.rule,
            enrichedAttributeValue = getEnrichedAttrValueDto(deletedEnrichmentRule.enrichedAttributeValue),
            goal = deletedEnrichmentRule.goal,
            version = deletedEnrichmentRule.version,
            lastModifiedBy = deletedEnrichmentRule.lastModifiedBy,
            lastModifiedAt = deletedEnrichmentRule.lastModifiedAt,
            deleted = deletedEnrichmentRule.deleted
        )

        assertThat(enrichmentRuleDtos).containsExactly(validRuleDto, deletedRuleDto)

    }

    @Test
    fun `list active enrichment rules`() {
        val response = performGet("$BASE_URL?shouldDisplayArchived=false")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val activeEnrichmentRuleDos = parseResponse(response, object : TypeReference<List<EnrichmentRuleDto>>() {})

        assertEquals(1, activeEnrichmentRuleDos.size)
    }

    @Test
    fun `Test Read EnrichmentRule`() {

        val response = performGet("$BASE_URL/${enrichmentRule.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val enrichmentRuleDto = parseResponse(response, object : TypeReference<EnrichmentRuleDto>() {})

        assertEquals(enrichmentRule.id!!, enrichmentRuleDto.id)
        assertEquals(enrichmentRule.rule, enrichmentRuleDto.rule)
        assertEquals(enrichmentRule.goal, enrichmentRuleDto.goal)
        assertEquals(enrichedAttributeValueEntity.uuid, enrichmentRuleDto.enrichedAttributeValue.id)
        assertEquals(enrichedAttributeValueEntity.enrichedAttribute.uuid, enrichmentRuleDto.enrichedAttributeValue.enrichedAttribute.id)
        assertEquals(dateTime.past(), enrichmentRuleDto.lastModifiedAt)
        assertThat(enrichmentRuleDto.lastModifiedBy).contains("<EMAIL>")
    }

    @Test
    fun `Test Create EnrichmentRule`() {
        enrichedAttributeValueEntity = createEnrichedAttributeValue(
            attr = "Therapy Area Test",
            value = "MM Test"
        )

        val newEnrichmentRuleDto = EnrichmentRuleDto(
            enrichedAttributeValue = getEnrichedAttrValueDto(enrichedAttributeValueEntity),
            rule = """{"field":"rule ABCD"}""",
            goal = "goal1"
        )

        val response = performPost(BASE_URL, newEnrichmentRuleDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val created = parseResponse(response, object : TypeReference<EnrichmentRuleDto>() {})
        val enrichmentRuleUuid = created.id

        assertEquals(getEnrichedAttrValueDto(),created.enrichedAttributeValue)
        assertEquals("""{"field":"rule ABCD"}""", created.rule)
        assertEquals("goal1", created.goal)
        assertThat(created.lastModifiedBy).contains("<EMAIL>")
        assertEquals(dateTime.now(), created.lastModifiedAt)
        assertEquals(0, created.version)
        assertNotNull(enrichmentRuleUuid)
        assertNotNull(created.lastModifiedAt)
    }

    @Test
    fun `Test Update EnrichmentRule`() {

        val anotherEnrichedAttributeValueEntity = createEnrichedAttributeValue(
            attr = "Days Supply",
            value = "30"
        )

        val toBeUpdatedEnrichmentRuleDto = EnrichmentRuleDto(
            id = enrichmentRule.id,
            rule = """{"field":"rule DEF"}""",
            goal = "goal2",
            enrichedAttributeValue = getEnrichedAttrValueDto(anotherEnrichedAttributeValueEntity),
            version = enrichmentRule.version
        )

        val response = performPut("$BASE_URL/${enrichmentRule.id}", toBeUpdatedEnrichmentRuleDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val updated = parseResponse(response, object : TypeReference<EnrichmentRuleDto>() {})


        assertEquals(
            toBeUpdatedEnrichmentRuleDto.enrichedAttributeValue,
            updated.enrichedAttributeValue
        )
        assertEquals("""{"field":"rule DEF"}""", updated.rule)
        assertEquals("goal2", updated.goal)
        assertEquals(enrichmentRule.id, updated.id)
        assertEquals(dateTime.now(), updated.lastModifiedAt)
        assertEquals(toBeUpdatedEnrichmentRuleDto.version!! + 1, updated.version)
        assertThat(updated.lastModifiedBy).contains("<EMAIL>")

        assertEquals(enrichmentRule.version + 1 , updated.version)
    }

    @Test
    fun `Should not update with the same value`() {

        val toBeUpdatedEnrichmentRuleDto = EnrichmentRuleDto(
            id = enrichmentRule.id,
            rule = enrichmentRule.rule,
            goal = enrichmentRule.goal,
            enrichedAttributeValue = getEnrichedAttrValueDto(enrichmentRule.enrichedAttributeValue),
            version = enrichmentRule.version
        )

        val response = performPut("$BASE_URL/${enrichmentRule.id}", toBeUpdatedEnrichmentRuleDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val updated = parseResponse(response, object : TypeReference<EnrichmentRuleDto>() {})

        assertEquals(toBeUpdatedEnrichmentRuleDto.enrichedAttributeValue, updated.enrichedAttributeValue)
        assertEquals(toBeUpdatedEnrichmentRuleDto.rule, updated.rule)
        assertEquals(toBeUpdatedEnrichmentRuleDto.goal, updated.goal)
        assertEquals(toBeUpdatedEnrichmentRuleDto.id, updated.id)
        assertEquals(enrichmentRule.lastModifiedAt, updated.lastModifiedAt)
        assertThat(updated.lastModifiedBy).contains("<EMAIL>")

        assertEquals(enrichmentRule.version, updated.version)
    }

    @Test
    fun `update EnrichmentRule should return 409 CONFLICT if optimistic lock fails`() {

        val toBeUpdatedEnrichmentRuleDto = EnrichmentRuleDto(
            id = enrichmentRule.id,
            rule = """{"field":"rule DEF"}""",
            enrichedAttributeValue = getEnrichedAttrValueDto(),
            version = 0
        )

        performPut("$BASE_URL/${enrichmentRule.id}", toBeUpdatedEnrichmentRuleDto)
            .andExpect(MockMvcResultMatchers.status().isConflict)
    }

    @Test
    fun `delete EnrichmentRule should return 404 if enrichment rule not found`() {
        val notFoundEntityId = "not-found-id"

        performDelete("$BASE_URL/$notFoundEntityId")
            .andExpect(MockMvcResultMatchers.status().isNotFound)
    }

    @Test
    fun `delete EnrichmentRule success`() {

        val response = performDelete("$BASE_URL/${enrichmentRule.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn().response

        val responseDto = parseResponse(response, EnrichmentRuleDto::class.java)
        assertEquals(responseDto.deleted, 1)
        SecurityContextHolder.getContext().authentication?.principal?.toString().let {
            assertEquals(it, responseDto.lastModifiedBy)
        }
        assertEquals(dateTime.now(), responseDto.lastModifiedAt)
    }

    @Test
    fun `test create new EnrichmentRule with the same attribute value after the old one is deleted`() {

        performDelete("$BASE_URL/${enrichmentRule.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn()

        val newEnrichmentRuleDto = EnrichmentRuleDto(
            enrichedAttributeValue = getEnrichedAttrValueDto(enrichmentRule.enrichedAttributeValue),
            rule = """{"field":"rule ABC"}""",
            goal = "goal1"
        )

        performPost(BASE_URL, newEnrichmentRuleDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
    }

    @Test
    fun `should unarchive enrichment rule success`() {

        val response = performPost("$BASE_URL/unarchive/${deletedEnrichmentRule.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn().response

        val responseDto = parseResponse(response, EnrichmentRuleDto::class.java)
        assertEquals(responseDto.id, deletedEnrichmentRule.id)
        assertEquals(responseDto.deleted, 0)
        val actualEntity = enrichmentRuleRepository.findByUuid(deletedEnrichmentRule.id!!)
        assertEquals(actualEntity?.deleted, 0)
    }

    @Test
    fun `should unarchive enrichment rule fail when there is a duplicated active enrichment rule`() {

        val archivedEnrichmentRuleEntity =  EnrichmentRuleEntity(
            uuid = UUID.randomUUID().toString(),
            enrichedAttributeValue = enrichedAttributeValueEntity,
            rule = deletedEnrichmentRule.rule,
            lastModifiedAt = dateTime.past(),
            lastModifiedBy = "<EMAIL>",
            deleted = 1
        )

        enrichmentRuleRepository.saveAllAndFlush(listOf(archivedEnrichmentRuleEntity))

        performPost("$BASE_URL/unarchive/${archivedEnrichmentRuleEntity.uuid}")
            .andExpect(MockMvcResultMatchers.status().is4xxClientError)
    }

    private fun getEnrichedAttrValueDto(domain: EnrichedAttributeValue) = domain.let(enrichedAttributeValueDtoMapper::toDto)

    private fun getEnrichedAttrValueDto(entity: EnrichedAttributeValueEntity = enrichedAttributeValueEntity) = entity
        .let(enrichedAttributeValueEntityMapper::toDomain)
        .let(enrichedAttributeValueDtoMapper::toDto)

    private fun createEnrichedAttribute(name: String): EnrichedAttributeEntity {
        return EnrichedAttributeEntity(
            uuid = "enriched_attribute_uuid_$name",
            name = name,
        ).let { enrichedAttributeJpaRepository.save(it) }
    }

    private fun createEnrichedAttributeValue(attr: String, value: String): EnrichedAttributeValueEntity {
        enrichedAttributeEntity = createEnrichedAttribute(attr)

        return EnrichedAttributeValueEntity(
            uuid = "enriched_attribute_value_uuid_$value",
            value = value,
            enrichedAttribute = enrichedAttributeEntity,
        ).let { enrichedAttributeValueJpaRepository.save(it) }
    }

    private fun createEnrichedAttributeValue(persistedAttr: EnrichedAttributeEntity, value: String): EnrichedAttributeValueEntity {
        return EnrichedAttributeValueEntity(
            uuid = "enriched_attribute_value_uuid_$value",
            value = value,
            enrichedAttribute = persistedAttr,
        ).let { enrichedAttributeValueJpaRepository.save(it) }
    }
}
