package com.prospection.refdata.rules.domain

import TestSpark.spark
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.FileNamePort
import com.prospection.refdata.common.domain.GenerateIdPort
import com.prospection.refdata.common.domain.GeneratePublicUrlPort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.items.domain.ItemsPort
import com.prospection.refdata.job.domain.JobPort
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

internal class EnrichmentRuleServiceTest {
    private val mockEnrichmentRulePort: EnrichmentRulePort = mock()
    private val mockDateTimePort: DateTimePort = mock()
    private val mockUserPort: UserPort = mock()
    private val mockItemsPort: ItemsPort = mock()
    private val mockCodingSystemPort: CodingSystemPort = mock()
    private val mockJobPort: JobPort = mock()
    private val mockGenerateIdPort: GenerateIdPort = mock()
    private val mockImportExportHelper: ImportExportHelper<ResponseInputStream<GetObjectResponse>> = mock()
    private val mockGeneratePublicUrlPort: GeneratePublicUrlPort = mock()
    private val mockFileNamePort: FileNamePort = mock()

    private val enrichmentRuleService = EnrichmentRuleService(
        mockEnrichmentRulePort,
        mockDateTimePort,
        mockUserPort,
        mockItemsPort,
        mockCodingSystemPort,
        mockJobPort,
        mockGenerateIdPort,
        mockImportExportHelper,
        mockGeneratePublicUrlPort,
        mockFileNamePort
    )

    @AfterEach
    fun cleanUp() {
        reset(mockEnrichmentRulePort)
        reset(mockDateTimePort)
        reset(mockUserPort)
        reset(mockItemsPort)
        reset(mockCodingSystemPort)
        reset(mockJobPort)
        reset(mockGenerateIdPort)
        reset(mockImportExportHelper)
        reset(mockGeneratePublicUrlPort)
        reset(mockFileNamePort)
    }

    @Test
    fun `should filter out irrelevant classifications when preview an enrichment rule has pre-defined classification`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        doReturn(setOf(codingSystemA, codingSystemB)).whenever(mockCodingSystemPort).findAll()

        doReturn(true).whenever(mockItemsPort).doesRawItemsExist(eq(codingSystemA))

        val enrichmentRule = EnrichmentRule(
            enrichedAttributeValue = previewedEnrichedAttributeValue,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = "A", operator = "="),
                        Rule(field = "source_name", value = "abc", operator = "=")
                    ),
                    combinator = "and"
                )
            ),
            deleted = 0,
        )
        val enrichedItems = spark.emptyDataFrame().alias(codingSystemA)
        doReturn(enrichedItems).whenever(mockItemsPort).applyRules(eq(codingSystemA), eq(listOf(enrichmentRule)))

        val filteredEnrichedItems = createDataFrame(listOf(TestRow("1", "abc")))
        doReturn(filteredEnrichedItems).whenever(mockItemsPort)
            .filterMatchedRowsOnly(eq(enrichedItems), eq(enrichmentRule))

        doReturn("preview_link").whenever(mockItemsPort).writePreview(eq(codingSystemA), eq(filteredEnrichedItems))

        doReturn("zip_path").whenever(mockImportExportHelper).writeZip(any(), any(), any())

        doReturn(LocalDateTime.now()).whenever(mockDateTimePort).now()

        enrichmentRuleService.getPreviewFileUrl(enrichmentRule)

        verify(mockItemsPort, times(1)).applyRules(eq(codingSystemA), eq(listOf(enrichmentRule)))
        verify(mockItemsPort, never()).applyRules(eq(codingSystemB), eq(listOf(enrichmentRule)))
    }

    @Test
    fun `should run all classifications when preview an enrichment rule does not have pre-defined classification`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        doReturn(setOf(codingSystemA, codingSystemB)).whenever(mockCodingSystemPort).findAll()

        doReturn(true).whenever(mockItemsPort).doesRawItemsExist(eq(codingSystemA))
        doReturn(true).whenever(mockItemsPort).doesRawItemsExist(eq(codingSystemB))

        val enrichmentRule = EnrichmentRule(
            enrichedAttributeValue = previewedEnrichedAttributeValue,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "source_name", value = "abc", operator = "=")
                    ),
                    combinator = "and"
                )
            ),
            deleted = 0,
        )
        val enrichedItems = spark.emptyDataFrame().alias(codingSystemA)
        doReturn(enrichedItems).whenever(mockItemsPort).applyRules(eq(codingSystemA), eq(listOf(enrichmentRule)))
        doReturn(enrichedItems).whenever(mockItemsPort).applyRules(eq(codingSystemB), eq(listOf(enrichmentRule)))

        val filteredEnrichedItems = createDataFrame(listOf(TestRow("1", "abc")))
        doReturn(filteredEnrichedItems).whenever(mockItemsPort)
            .filterMatchedRowsOnly(eq(enrichedItems), eq(enrichmentRule))

        doReturn("preview_link").whenever(mockItemsPort).writePreview(eq(codingSystemA), eq(filteredEnrichedItems))
        doReturn("preview_link").whenever(mockItemsPort).writePreview(eq(codingSystemB), eq(filteredEnrichedItems))

        doReturn("zip_path").whenever(mockImportExportHelper).writeZip(any(), any(), any())

        doReturn(LocalDateTime.now()).whenever(mockDateTimePort).now()

        enrichmentRuleService.getPreviewFileUrl(enrichmentRule)

        verify(mockItemsPort, times(1)).applyRules(eq(codingSystemA), eq(listOf(enrichmentRule)))
        verify(mockItemsPort, times(1)).applyRules(eq(codingSystemB), eq(listOf(enrichmentRule)))
    }

    private val previewedEnrichedAttributeValue = EnrichedAttributeValue(
        value = "enriched_value",
        id = "enriched_value",
        enrichedAttribute = EnrichedAttribute(
            name = "enriched_attribute",
            id = "enriched_attribute"
        )
    )

    private fun createDataFrame(rows: List<TestRow>): Dataset<Row> {
        return spark.createDataFrame(rows, TestRow::class.java)
    }

    data class TestRow(
        val source_code: String,
        val source_name: String,
    )
}