package com.prospection.refdata.rules.domain

import com.amazonaws.util.IOUtils
import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.domain.InvalidModelException
import com.prospection.domain.ModelDoesNotExistException
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.common.integration.FileNameAdapter
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.job.domain.JobStatus
import com.prospection.refdata.rules.integration.EnrichedAttributeEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeJpaRepository
import com.prospection.refdata.rules.integration.EnrichedAttributeValueEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeValueJpaRepository
import com.prospection.refdata.rules.integration.EnrichmentRuleEntity
import com.prospection.refdata.rules.integration.EnrichmentRuleJpaRepository
import org.apache.spark.sql.SaveMode
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertLinesMatch
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import java.net.URI
import java.nio.file.Paths
import java.time.LocalDateTime
import java.util.zip.ZipInputStream
import kotlin.io.path.name

internal class EnrichmentRuleServiceIntegrationTest : AbstractIntegrationTest() {
    companion object {
        lateinit var enrichedAttributeEntity: EnrichedAttributeEntity
        lateinit var enrichedAttributeValueEntity: EnrichedAttributeValueEntity
        lateinit var enrichedAttributeValue: EnrichedAttributeValue
    }

    @Autowired
    private lateinit var enrichedAttributeJpaRepository: EnrichedAttributeJpaRepository

    @Autowired
    private lateinit var enrichedAttributeValueJpaRepository: EnrichedAttributeValueJpaRepository

    @Autowired
    private lateinit var service: EnrichmentRuleService

    @MockitoSpyBean
    private lateinit var dateTime: DateTimePort

    @Autowired
    private lateinit var enrichmentRuleRepository: EnrichmentRuleJpaRepository

    @Autowired
    private lateinit var codingSystemRepository: CodingSystemJpaRepository

    @BeforeEach
    override fun setUp() {
        super.setUp()
        enrichedAttributeEntity = EnrichedAttributeEntity(
            uuid = "attr_uuid1",
            name = "Therapy Area",
        ).let { enrichedAttributeJpaRepository.save(it) }

        enrichedAttributeValueEntity = EnrichedAttributeValueEntity(
            uuid = "value_uuid1",
            value = "MM",
            enrichedAttribute = enrichedAttributeEntity,
        ).let { enrichedAttributeValueJpaRepository.save(it) }

        enrichedAttributeValue = EnrichedAttributeValue(
            id = "value_uuid1",
            value = "MM",
            enrichedAttribute = EnrichedAttribute(
                id = "attr_uuid1",
                name = "Therapy Area"
            )
        )

        doReturn(LocalDateTime.now()).whenever(dateTime).now()
    }

    @Test
    fun `Should throw DataIntegrityViolationException with clear error message if duplicate enriched attribute and value exist`() {
        service.create(
            EnrichmentRule(
                rule = """{"field":"rule ABC"}""",
                enrichedAttributeValue = enrichedAttributeValue
            )
        )

        val exception = assertThrows(InvalidModelException::class.java) {
            service.create(
                EnrichmentRule(
                    rule = """{"field":"rule DEF"}""",
                    enrichedAttributeValue = enrichedAttributeValue
                )
            )
        }

        assertThat(exception.message).contains("Duplicate rule found")
    }

    @Test
    fun `should throw ModelDoesNotExistException when delete enrichment rule that does not exist`() {
        assertThrows(ModelDoesNotExistException::class.java) {
            service.delete("not-found-enrichment-rule-id")
        }
    }

    @Test
    fun `test delete enrichment rule success`() {
        val enrichmentRule = service.create(
            EnrichmentRule(
                rule = """{"field":"rule ABC"}""",
                enrichedAttributeValue = enrichedAttributeValue
            )
        )
        assertNotNull(enrichmentRule.id)
        enrichmentRule.id?.let { enrichmentRuleId ->
            {
                val deletedEnrichmentRule = service.delete(enrichmentRuleId)
                assertEquals(enrichmentRule.id, deletedEnrichmentRule.id)
                assertEquals(1, deletedEnrichmentRule.deleted)
                SecurityContextHolder.getContext().authentication?.principal?.toString().let {
                    assertEquals(it, enrichmentRule.lastModifiedBy)
                }
                assertEquals(dateTime.now(), deletedEnrichmentRule.lastModifiedAt)
            }
        }
    }

    @Test
    fun `test create new enrichment rule with the same info after deleting the old one success`() {
        val enrichmentRuleEntity = EnrichmentRuleEntity(
            uuid = "test-uuid",
            enrichedAttributeValue = enrichedAttributeValueEntity,
            rule = """{"field":"rule ABC"}""",
            lastModifiedAt = dateTime.now(),
            lastModifiedBy = SecurityContextHolder.getContext().authentication?.principal?.toString() ?: "test-user"
        )
        val savedEnrichmentRuleEntity = enrichmentRuleRepository.saveAndFlush(enrichmentRuleEntity)

        service.delete(savedEnrichmentRuleEntity.uuid)

        val toCreateEnrichmentRule = EnrichmentRule(
            rule = """{"field":"rule ABC"}""",
            enrichedAttributeValue = enrichedAttributeValue
        )
        service.create(toCreateEnrichmentRule)
    }

    @Test
    fun `Should create a zip filename with an enrichment rule’s enriched attribute`() {
        val draftItems = spark.createDataFrame(
            listOf(
                TestRow("1", "abc"),
                TestRow("2", "def")
            ), TestRow::class.java
        )

        val classification = "Test Classification"

        draftItems.write().mode(SaveMode.Overwrite)
            .parquet("s3a://${applicationProperties.s3Bucket}/${Items.Draft.RAW_ITEMS}/${classification}")

        codingSystemRepository.save(CodingSystemEntity(name = classification))

        val zipFileUrl = service.getPreviewFileUrl(previewedEnrichmentRule)

        assertEquals(
            "whatever filename_2022-06-10 8888.zip",
            getFileNameFromUrl(zipFileUrl)
        )
    }

    @Test
    fun `Should create a preview zip file for an enrichment rule`() {
        val draftItems = spark.createDataFrame(
            listOf(
                TestRow("1", "abc"),
                TestRow("2", "def")
            ), TestRow::class.java
        )

        val codingSystem = "Test Coding System"

        draftItems.write().mode(SaveMode.Overwrite)
            .parquet("s3a://${applicationProperties.s3Bucket}/${Items.Draft.RAW_ITEMS}/${codingSystem}")

        codingSystemRepository.save(CodingSystemEntity(name = codingSystem))

        service.getPreviewFileUrl(previewedEnrichmentRule)

        val response = amazonS3.getObject { builder ->
            builder.bucket(applicationProperties.s3Bucket)
                   .key("${Items.PREVIEW}/whatever filename_2022-06-10 8888.zip")
        }

        ZipInputStream(response).use {
            val zipEntry = it.nextEntry

            assertEquals("${codingSystem}.csv", zipEntry?.name)

            assertLinesMatch(
                "source_code,source_name,coding_system,enriched_attribute\n1,abc,${codingSystem},[enriched_value]\n".lines(),
                IOUtils.toString(it).lines()
            )

            print(zipEntry)

            assertNull(it.nextEntry)
        }
    }

    @Test
    fun `Should throw an error if no rows matched the previewed enrichment rule at all`() {
        val draftItems = spark.createDataFrame(
            listOf(
                TestRow("1", "def"), // doesn't match abc in previewedEnrichmentRule
            ), TestRow::class.java
        )

        draftItems.write().mode(SaveMode.Overwrite)
            .parquet("s3a://${applicationProperties.s3Bucket}/${Items.Draft.RAW_ITEMS}/whatever")

        Assertions.assertThatThrownBy {
            service.getPreviewFileUrl(previewedEnrichmentRule)
        }.isInstanceOf(RuntimeException::class.java)
            .hasMessageContaining("There were no items matching the given condition")
    }

    @Test
    fun `Should run a job if downloading a preview file of an enrichment rule`() {
        val enrichmentRule = EnrichmentRule(
            id = "test-uuid",
            enrichedAttributeValue = enrichedAttributeValue,
            rule = """{"field":"rule ABC"}""",
        )

        val job = service.queuePreviewFileDownload(enrichmentRule)

        assertThat(job)
            .extracting("name", "status")
            .containsExactly(
                "Generating the preview file of enrichment rule - ${enrichmentRule.toSimpleName()}",
                JobStatus.RUNNING
            )
    }

    private val previewedEnrichmentRule = EnrichmentRule(
        enrichedAttributeValue = EnrichedAttributeValue(
            value = "enriched_value",
            id = "enriched_value",
            enrichedAttribute = EnrichedAttribute(
                name = "enriched_attribute",
                id = "enriched_attribute"
            )
        ),
        rule = HasRule.objectMapper.writeValueAsString(
            Rule(
                rules = listOf(
                    Rule(field = "source_name", value = "abc", operator = "="),
                ),
                combinator = "and"
            )
        ),
        deleted = 0,
    )

    private fun getFileNameFromUrl(url: String?): String? {
        return url?.let { Paths.get(URI(url).path).fileName?.name }
    }

    data class TestRow(val source_code: String, val source_name: String)
}