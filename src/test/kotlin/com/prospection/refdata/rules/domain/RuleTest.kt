package com.prospection.refdata.rules.domain

import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class RuleTest {

    @Test
    fun `Can resolve to either specific coding system when top level OR specifies different coding systems`() {
        val rule = Rule(
            combinator = "or",
            rules = listOf(
                Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = "a"),
                Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = "b")
            )
        )
        assertTrue(rule.canResolveToCodingSystem("a"))
        assertTrue(rule.canResolveToCodingSystem("b"))
        assertFalse(rule.canResolveToCodingSystem("c"))
    }

    @Test
    fun `Can resolve to any coding system when top level OR specifies contains a rule that does not specify a coding system`() {
        val rule = Rule(
            combinator = "or",
            rules = listOf(
                Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = "a"),
                Rule(field = "OtherAttribute", value = "OtherValue")
            )
        )
        assertTrue(rule.canResolveToCodingSystem("a"))
        assertTrue(rule.canResolveToCodingSystem("b"))
    }

    @Test
    fun `Can resolve to any coding system when no coding system is specified`() {
        val rule = Rule(
            combinator = "or",
            rules = listOf(
                Rule(field = "OtherAttribute", value = "OtherValue")
            )
        )
        assertTrue(rule.canResolveToCodingSystem("a"))
        assertTrue(rule.canResolveToCodingSystem("b"))
    }

    @Test
    fun `Can resolve to a specific coding system when coding system is specified at top level rule`() {
        val rule = Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = "a")
        assertTrue(rule.canResolveToCodingSystem("a"))
        assertFalse(rule.canResolveToCodingSystem("b"))
    }

    @Test
    fun `Can resolve to specific coding systems when coding systems are specified in nested rules under OR`() {
        val rule = Rule(
            combinator = "and",
            rules = listOf(
                Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = "a"),
                Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = "b"),
            )
        )
        assertTrue(rule.canResolveToCodingSystem("a"))
        assertTrue(rule.canResolveToCodingSystem("b"))
    }

}