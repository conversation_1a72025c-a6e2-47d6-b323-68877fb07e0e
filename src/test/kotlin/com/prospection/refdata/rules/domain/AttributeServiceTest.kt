package com.prospection.refdata.rules.domain

import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.items.RuleBuilderOperators.BeginsWith
import com.prospection.refdata.items.RuleBuilderOperators.Contains
import com.prospection.refdata.items.RuleBuilderOperators.DoesNotBeginWith
import com.prospection.refdata.items.RuleBuilderOperators.DoesNotContain
import com.prospection.refdata.items.RuleBuilderOperators.EqualsTo
import com.prospection.refdata.items.RuleBuilderOperators.IsNotEqualTo
import com.prospection.refdata.items.domain.EnrichedItemsMetadata
import com.prospection.refdata.items.domain.EnrichedItemsMetadataPort
import com.prospection.refdata.items.domain.RawItemsMetadata
import com.prospection.refdata.items.domain.RawItemsMetadataPort
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.timeout
import org.mockito.Mockito.times
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.reset
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

internal class AttributeServiceTest {
    private val attributeRepository = mock(AttributeRepository::class.java)
    private val codingSystemPort = mock(CodingSystemPort::class.java)
    private val rawItemsMetadataPort = mock(RawItemsMetadataPort::class.java)
    private val enrichedItemsMetadataPort = mock(EnrichedItemsMetadataPort::class.java)

    private val attributeService =
        AttributeService(
            attributeRepository,
            codingSystemPort,
            rawItemsMetadataPort,
            enrichedItemsMetadataPort
        )

    private val enrichmentRuleOpratorOfSourceAttribute =
        listOf(EqualsTo(), IsNotEqualTo(), Contains(), DoesNotContain(), BeginsWith(), DoesNotBeginWith())

    @AfterEach
    fun setup() {
        reset(attributeRepository)
        reset(codingSystemPort)
        reset(rawItemsMetadataPort)
        reset(enrichedItemsMetadataPort)
    }

    @Test
    fun `Source attributes are tagged with classifications for enrichment rule`() {
        doReturn(
            listOf(
                RawItemsMetadata(
                    codingSystem = CodingSystem(name = "classification a"),
                    sourceAttributes = listOf("source_brand_name", "source_code"),
                    totalItem = 0,
                    newItem = 0,
                    deletedItem = 0,
                    createdBy = "whoever",
                    createdAt = LocalDateTime.now()
                ),
                RawItemsMetadata(
                    codingSystem = CodingSystem(name = "classification b"),
                    sourceAttributes = listOf("source_code", "source_atc_code"),
                    totalItem = 0,
                    newItem = 0,
                    deletedItem = 0,
                    createdBy = "whoever",
                    createdAt = LocalDateTime.now()
                ),
            )
        ).whenever(rawItemsMetadataPort).getAllDraftMetadata()

        doReturn(emptySet<String>()).whenever(codingSystemPort).findAll()

        val listEnrichmentRuleAttributes = attributeService.listEnrichmentRuleAttributes()

        assertThat(getAttributesByType(listEnrichmentRuleAttributes, Type.CODING_SYSTEM))
            .containsExactly(
                Attribute(
                    name = "coding_system",
                    codingSystems = null,
                    hasAllCodingSystems = null,
                    values = emptyList(),
                    operators = listOf(EqualsTo())
                )
            )

        assertThat(getAttributesByType(listEnrichmentRuleAttributes, Type.SOURCE_ATTRIBUTE))
            .containsExactly(
                Attribute(
                    name = "source_atc_code",
                    codingSystems = listOf("classification b"),
                    hasAllCodingSystems = false,
                    values = emptyList(),
                    operators = enrichmentRuleOpratorOfSourceAttribute

                ),
                Attribute(
                    name = "source_brand_name",
                    codingSystems = listOf("classification a"),
                    hasAllCodingSystems = false,
                    values = emptyList(),
                    operators = enrichmentRuleOpratorOfSourceAttribute
                ),
                Attribute(
                    name = "source_code",
                    codingSystems = listOf("classification a", "classification b"),
                    hasAllCodingSystems = true,
                    values = emptyList(),
                    operators = enrichmentRuleOpratorOfSourceAttribute
                ),
            )

        verify(rawItemsMetadataPort, times(1)).getAllDraftMetadata()
        verify(codingSystemPort, times(1)).findAll()
    }

    @Test
    fun `Source attributes are tagged with classifications for item group rule`() {
        doReturn(
            listOf(
                EnrichedItemsMetadata(
                    codingSystem = CodingSystem(name = "classification a"),
                    sourceAttributes = listOf("source_brand_name", "source_code"),
                    enrichedAttributes = listOf("diagnosis"),
                    createdBy = "whoever",
                    createdAt = LocalDateTime.now(),
                    latestEnrichmentRuleRevisionId = 0,
                ),
                EnrichedItemsMetadata(
                    codingSystem = CodingSystem(name = "classification b"),
                    sourceAttributes = listOf("source_code", "source_drug_name"),
                    enrichedAttributes = emptyList(),
                    createdBy = "whoever",
                    createdAt = LocalDateTime.now(),
                    latestEnrichmentRuleRevisionId = 0,
                ),
            )
        ).whenever(enrichedItemsMetadataPort).getAllDraftMetadata()

        doReturn(emptySet<String>()).whenever(codingSystemPort).findAll()
        doReturn(
            listOf(
                EnrichedAttributeValue(
                    id = "6fe0b35d-9596-c766-c543-2f6a69ccc6fd",
                    value = "A00 - Cholera",
                    enrichedAttribute = EnrichedAttribute(
                        id = "b4e082c3-8812-508a-c0bc-34b75aaf0db6",
                        name = "diagnosis"
                    )
                )
            )
        ).whenever(attributeRepository).listEnrichedAttributeValues()

        val listItemGroupRuleAttributes = attributeService.listItemGroupRuleAttributes()


        assertThat(getAttributesByType(listItemGroupRuleAttributes, Type.CODING_SYSTEM))
            .containsExactly(
                Attribute(
                    name = "coding_system",
                    codingSystems = null,
                    hasAllCodingSystems = null,
                    values = emptyList(),
                    operators = listOf(EqualsTo()),
                )
            )

        assertThat(getAttributesByType(listItemGroupRuleAttributes, Type.ENRICHED_ATTRIBUTE))
            .containsExactly(
                Attribute(
                    name = "diagnosis",
                    codingSystems = null,
                    hasAllCodingSystems = null,
                    values = listOf("A00 - Cholera"),
                    operators = listOf(EqualsTo(), IsNotEqualTo()),
                )
            )

        assertThat(getAttributesByType(listItemGroupRuleAttributes, Type.SOURCE_ATTRIBUTE))
            .containsExactly(
                Attribute(
                    name = "source_brand_name",
                    codingSystems = listOf("classification a"),
                    hasAllCodingSystems = false,
                    values = emptyList(),
                    operators = listOf(EqualsTo(), IsNotEqualTo()),
                ),
                Attribute(
                    name = "source_code",
                    codingSystems = listOf("classification a", "classification b"),
                    hasAllCodingSystems = true,
                    values = emptyList(),
                    operators = listOf(EqualsTo(), IsNotEqualTo()),
                ),
                Attribute(
                    name = "source_drug_name",
                    codingSystems = listOf("classification b"),
                    hasAllCodingSystems = false,
                    values = emptyList(),
                    operators = listOf(EqualsTo(), IsNotEqualTo()),
                ),
            )
    }

    @Test
    fun `Source attributes atc_code, atc_ephmra, icd10_code and kubuncode get additional operators - beginsWith and doesNotBeginWith at item group level`() {
        val fieldWithAdditionalOperators =
            listOf("source_atc_code", "source_atc_ephmra", "source_icd10_code", "source_kubuncode")
        val fieldWithoutAdditionalOperators = listOf("source_code", "source_brand_name")

        doReturn(
            listOf(
                EnrichedItemsMetadata(
                    codingSystem = CodingSystem(name = "coding_system"),
                    sourceAttributes = listOf(fieldWithAdditionalOperators, fieldWithoutAdditionalOperators).flatten(),
                    enrichedAttributes = emptyList(),
                    createdBy = "whoever",
                    createdAt = LocalDateTime.now(),
                    latestEnrichmentRuleRevisionId = 0,
                ),
            )
        ).whenever(enrichedItemsMetadataPort).getAllDraftMetadata()

        doReturn(emptySet<String>()).whenever(codingSystemPort).findAll()
        doReturn(emptyList<EnrichedAttributeValue>()).whenever(attributeRepository).listEnrichedAttributeValues()

        val listItemGroupRuleAttributes = attributeService.listItemGroupRuleAttributes()

        val sourceAttributes = getAttributesByType(listItemGroupRuleAttributes, Type.SOURCE_ATTRIBUTE)

        fieldWithAdditionalOperators.forEach {
            assertThat(getAdditionalOperators(sourceAttributes, it))
                .containsExactly(EqualsTo(), IsNotEqualTo(), BeginsWith(), DoesNotBeginWith())
        }

        fieldWithoutAdditionalOperators.forEach {
            assertThat(getAdditionalOperators(sourceAttributes, it))
                .containsExactly(EqualsTo(), IsNotEqualTo())
        }

        verify(enrichedItemsMetadataPort, timeout(1)).getAllDraftMetadata()
        verify(codingSystemPort, timeout(1)).findAll()
        verify(attributeRepository, timeout(1)).listEnrichedAttributeValues()
    }

    private fun getAdditionalOperators(sourceAttributes: List<Attribute>, name: String) =
        sourceAttributes.find { it.name === name }!!.operators

    private fun getAttributesByType(attributeTypes: List<AttributeType>, type: Type): List<Attribute> {
        return attributeTypes.find { it.type == type }!!.attributes
    }
}
