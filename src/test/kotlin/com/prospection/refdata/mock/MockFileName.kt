package com.prospection.refdata.mock

import com.prospection.refdata.common.domain.FileNamePort
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Profile("test")
@Component
@Primary
class MockFileName : FileNamePort {
    override fun createFileNameWithDateTime(filename: String, dateTime: LocalDateTime): String {
        return "whatever filename_2022-06-10 8888"
    }
}
