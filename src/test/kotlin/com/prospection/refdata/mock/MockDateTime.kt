package com.prospection.refdata.mock

import com.prospection.refdata.common.domain.DateTimePort
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.LocalDateTime

@Profile("test")
@Component
@Primary
class MockDateTime : DateTimePort {
    override fun now(): LocalDateTime = LocalDate.of(2021, 1, 1).atStartOfDay()

    fun past(): LocalDateTime = now().minusMonths(1)
}
