package com.prospection.refdata.itemgroups.domain

import TestSpark.spark
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestTopic
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestWorkflow
import com.prospection.refdata.items.domain.ItemsPort
import com.prospection.refdata.items.domain.PublishItemVersionPort
import com.prospection.refdata.items.domain.PublishedItemVersion
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.rules.domain.Rule
import com.prospection.refdata.subscription.domain.CompanySubscription
import com.prospection.refdata.subscription.domain.CompanySubscriptionPort
import com.prospection.refdata.topic.domain.EnrichedTopic
import com.prospection.refdata.topic.domain.TopicPort
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.reset
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

internal class ItemGroupsServiceTest {
    private val mockUserPort: UserPort = mock()
    private val mockItemGroupPort: ItemGroupPort = mock()
    private val mockPublishItemGroupPort: PublishItemGroupPort = mock()
    private val mockDateTimePort: DateTimePort = mock()
    private val mockCodingSystemPort: CodingSystemPort = mock()
    private val mockItemsPort: ItemsPort = mock()
    private val mockItemGroupsSparkPort: ItemGroupsSparkPort = mock()
    private val mockApplyItemGroupRulePort: ApplyItemGroupRulePort = mock()
    private val mockChangeSummaryItemGroupPort: ChangeSummaryItemGroupPort = mock()
    private val mockItemGroupMetadataPort: ItemGroupMetadataPort = mock()
    private val mockExportItemsToItemGroupsPort: ExportItemsToItemGroupsPort = mock()
    private val mockJobPort: JobPort = mock()
    private val mockPublishItemGroupVersionPort: PublishItemGroupVersionPort = mock()
    private val mockPublishItemVersionPort: PublishItemVersionPort = mock()
    private val mockTopicPort: TopicPort = mock()
    private val mockCompanySubscriptionPort: CompanySubscriptionPort = mock()

    private val itemGroupsService = ItemGroupsService(
        mockUserPort,
        mockItemGroupPort,
        mockDateTimePort,
        mockPublishItemGroupPort,
        mockCodingSystemPort,
        mockItemsPort,
        mockItemGroupsSparkPort,
        mockApplyItemGroupRulePort,
        mockItemGroupMetadataPort,
        mockExportItemsToItemGroupsPort,
        mockChangeSummaryItemGroupPort,
        mockJobPort,
        mockPublishItemGroupVersionPort,
        mockPublishItemVersionPort,
        mockTopicPort,
        mockCompanySubscriptionPort
    )

    @AfterEach
    fun cleanUp() {
        reset(mockItemGroupPort)
        reset(mockPublishItemGroupPort)
        reset(mockDateTimePort)
        reset(mockCodingSystemPort)
        reset(mockItemsPort)
        reset(mockApplyItemGroupRulePort)
        reset(mockChangeSummaryItemGroupPort)
        reset(mockItemGroupMetadataPort)
        reset(mockExportItemsToItemGroupsPort)
        reset(mockJobPort)
        reset(mockTopicPort)
        reset(mockCompanySubscriptionPort)
    }

    @Test
    fun `Should merge multiple classifications into 1`() {
        val codingSystemNameA = "A"
        val codingSystemNameB = "B"
        val classificationA = "$codingSystemNameA classification"
        val classificationB = "$codingSystemNameB classification"
        val codingSystemToClassifications = mapOf(
            codingSystemNameA to listOf(createCodingSystemToClassification(codingSystemNameA, classificationA)),
            codingSystemNameB to listOf(createCodingSystemToClassification(codingSystemNameB, classificationB))
        )

        doReturn(linkedSetOf(codingSystemNameA, codingSystemNameB)).whenever(mockCodingSystemPort).findAll()
        doReturn(
            PublishedItemVersion(
                publishedVersion = "2021-12-20",
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val mockItemGroups = listOf(
            ItemGroup(
                name = "Apply to all",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        combinator = "or", rules = listOf(
                            Rule(field = "coding_system", value = codingSystemNameA),
                            Rule(field = "coding_system", value = codingSystemNameB)
                        )
                    )
                )
            )
        )

        val publishedItemsA = spark.emptyDataFrame().alias("A")

        doReturn(publishedItemsA).whenever(mockItemsPort).getPublishedItems(eq(codingSystemNameA), eq("2021-12-20"))

        val itemsToItemGroupsA = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("A1", "g1", classificationA, "Group 1"),
                ItemsToItemGroupsRow("A2", "g2", classificationA, "Group 2"),
            )
        )

        doReturn(itemsToItemGroupsA).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemsGroupsWithoutSourceAttributeColumns(eq(publishedItemsA), eq(mockItemGroups), any())

        val publishedItemsB = spark.emptyDataFrame().alias("B")

        doReturn(publishedItemsB).whenever(mockItemsPort).getPublishedItems(eq(codingSystemNameB), eq("2021-12-20"))

        val itemsToItemGroupsB = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("B1", "g1", classificationB, "Group 1"),
                ItemsToItemGroupsRow("B2", "g2", classificationB, "Group 2"),
            )
        )

        doReturn(itemsToItemGroupsB).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemsGroupsWithoutSourceAttributeColumns(eq(publishedItemsB), eq(mockItemGroups), any())

        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()

        val itemsToItemGroups =
            itemGroupsService.getItemsToItemGroupsWithoutSourceAttributeColumns(mockItemGroups).collectAsList()

        assertThat(itemsToItemGroups).hasSize(4)

        val firstRow = itemsToItemGroups[0]
        assertThat(firstRow.getAs<String>("item_code")).isEqualTo("A1")
        assertThat(firstRow.getAs<String>("item_group")).isEqualTo("g1")
        assertThat(firstRow.getAs<String>("classification")).isEqualTo(classificationA)
        assertThat(firstRow.getAs<String>("item_group_name")).isEqualTo("Group 1")

        val secondRow = itemsToItemGroups[1]
        assertThat(secondRow.getAs<String>("item_code")).isEqualTo("A2")
        assertThat(secondRow.getAs<String>("item_group")).isEqualTo("g2")
        assertThat(secondRow.getAs<String>("classification")).isEqualTo(classificationA)
        assertThat(secondRow.getAs<String>("item_group_name")).isEqualTo("Group 2")

        val thirdRow = itemsToItemGroups[2]
        assertThat(thirdRow.getAs<String>("item_code")).isEqualTo("B1")
        assertThat(thirdRow.getAs<String>("item_group")).isEqualTo("g1")
        assertThat(thirdRow.getAs<String>("classification")).isEqualTo(classificationB)
        assertThat(thirdRow.getAs<String>("item_group_name")).isEqualTo("Group 1")

        val fourthRow = itemsToItemGroups[3]
        assertThat(fourthRow.getAs<String>("item_code")).isEqualTo("B2")
        assertThat(fourthRow.getAs<String>("item_group")).isEqualTo("g2")
        assertThat(fourthRow.getAs<String>("classification")).isEqualTo(classificationB)
        assertThat(fourthRow.getAs<String>("item_group_name")).isEqualTo("Group 2")
    }

    @Test
    fun `Should apply item groups again if the latest published version of items in metadata is obsolete`() {
        val oldPublishedItemsVersion = PublishedItemVersion(
            publishedVersion = "20210101000000Z",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )
        val newPublishedItemsVersion = PublishedItemVersion(
            publishedVersion = "20220101000000Z",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )

        val sameItemGroupVersion = 1

        doReturn(
            ItemGroupsMetadata(
                createdBy = "whoever",
                createdAt = LocalDateTime.now(),
                publishedItemVersion = oldPublishedItemsVersion,
                itemGroupRevisionId = sameItemGroupVersion,
            )
        ).whenever(mockItemGroupMetadataPort).getDraftMetadata()

        doReturn(sameItemGroupVersion).whenever(mockItemGroupPort).getLatestItemGroupRevisionId()
        doReturn(newPublishedItemsVersion).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val spyService = spy(itemGroupsService)
        doNothing().whenever(spyService).applyItemGroupsToItems("test user")

        spyService.applyItemGroupsIfObsolete("test user")

        verify(spyService, times(1)).applyItemGroupsToItems("test user")
    }

    @Test
    fun `Should apply item groups again if the latest item group version in metadata is obsolete`() {
        val existingItemGroupVersion = 1
        val updatedItemGroupVersion = 2

        val samePublishedItemsVersion = PublishedItemVersion(
            publishedVersion = "20210101000000Z",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )

        doReturn(
            ItemGroupsMetadata(
                itemGroupRevisionId = existingItemGroupVersion,
                publishedItemVersion = samePublishedItemsVersion,
                createdBy = "whoever",
                createdAt = LocalDateTime.now()
            )
        ).whenever(mockItemGroupMetadataPort).getDraftMetadata()

        doReturn(updatedItemGroupVersion).whenever(mockItemGroupPort).getLatestItemGroupRevisionId()
        doReturn(samePublishedItemsVersion).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val spyService = spy(itemGroupsService)
        doNothing().whenever(spyService).applyItemGroupsToItems("test user")

        spyService.applyItemGroupsIfObsolete("test user")

        verify(spyService, times(1)).applyItemGroupsToItems("test user")
    }

    @Test
    fun `Should apply to all coding systems during publish when coding system is not specified in item group`() {
        val codingSystemNameA = "A"
        val codingSystemNameB = "B"
        val classificationA = "$codingSystemNameA classification"
        val classificationB = "$codingSystemNameB classification"
        val codingSystemToClassifications = mapOf(
            codingSystemNameA to listOf(createCodingSystemToClassification(codingSystemNameA, classificationA)),
            codingSystemNameB to listOf(createCodingSystemToClassification(codingSystemNameB, classificationB))
        )

        doReturn(linkedSetOf(codingSystemNameA, codingSystemNameB)).whenever(mockCodingSystemPort).findAll()
        doReturn(
            PublishedItemVersion(
                publishedVersion = "2021-12-20",
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val mockItemGroups = listOf(
            ItemGroup(
                name = "Apply to all",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        combinator = "or", rules = listOf(
                            Rule(field = "source_atc_code", value = "123")
                        )
                    )
                )
            )
        )

        val publishedItemsA = spark.emptyDataFrame().alias("A")

        doReturn(publishedItemsA).whenever(mockItemsPort).getPublishedItems(eq(codingSystemNameA), eq("2021-12-20"))

        val itemsToItemGroupsA = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("A1", "g1", classificationA, "Group 1"),
                ItemsToItemGroupsRow("A2", "g2", classificationA, "Group 2"),
            )
        )

        doReturn(itemsToItemGroupsA).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemsGroupsWithoutSourceAttributeColumns(eq(publishedItemsA), eq(mockItemGroups), any())

        val publishedItemsB = spark.emptyDataFrame().alias("B")

        doReturn(publishedItemsB).whenever(mockItemsPort).getPublishedItems(eq(codingSystemNameB), eq("2021-12-20"))

        val itemsToItemGroupsB = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("B1", "g1", classificationB, "Group 1"),
                ItemsToItemGroupsRow("B2", "g2", classificationB, "Group 2"),
            )
        )

        doReturn(itemsToItemGroupsB).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemsGroupsWithoutSourceAttributeColumns(eq(publishedItemsB), eq(mockItemGroups), any())

        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()

        val itemsToItemGroups =
            itemGroupsService.getItemsToItemGroupsWithoutSourceAttributeColumns(mockItemGroups).collectAsList()

        assertThat(itemsToItemGroups).hasSize(4)

        val firstRow = itemsToItemGroups[0]
        assertThat(firstRow.getAs<String>("item_code")).isEqualTo("A1")
        assertThat(firstRow.getAs<String>("item_group")).isEqualTo("g1")
        assertThat(firstRow.getAs<String>("classification")).isEqualTo(classificationA)
        assertThat(firstRow.getAs<String>("item_group_name")).isEqualTo("Group 1")

        val secondRow = itemsToItemGroups[1]
        assertThat(secondRow.getAs<String>("item_code")).isEqualTo("A2")
        assertThat(secondRow.getAs<String>("item_group")).isEqualTo("g2")
        assertThat(secondRow.getAs<String>("classification")).isEqualTo(classificationA)
        assertThat(secondRow.getAs<String>("item_group_name")).isEqualTo("Group 2")

        val thirdRow = itemsToItemGroups[2]
        assertThat(thirdRow.getAs<String>("item_code")).isEqualTo("B1")
        assertThat(thirdRow.getAs<String>("item_group")).isEqualTo("g1")
        assertThat(thirdRow.getAs<String>("classification")).isEqualTo(classificationB)
        assertThat(thirdRow.getAs<String>("item_group_name")).isEqualTo("Group 1")

        val fourthRow = itemsToItemGroups[3]
        assertThat(fourthRow.getAs<String>("item_code")).isEqualTo("B2")
        assertThat(fourthRow.getAs<String>("item_group")).isEqualTo("g2")
        assertThat(fourthRow.getAs<String>("classification")).isEqualTo(classificationB)
        assertThat(fourthRow.getAs<String>("item_group_name")).isEqualTo("Group 2")
    }

    @Test
    fun `Should not apply item groups again if the latest versions are the same`() {
        val samePublishedItemsVersion = PublishedItemVersion(
            publishedVersion = "20210101000000Z",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )
        val sameItemGroupVersion = 1

        doReturn(
            ItemGroupsMetadata(
                publishedItemVersion = samePublishedItemsVersion,
                itemGroupRevisionId = sameItemGroupVersion,
                createdBy = "whoever",
                createdAt = LocalDateTime.now()
            )
        ).whenever(mockItemGroupMetadataPort).getDraftMetadata()

        doReturn(sameItemGroupVersion).whenever(mockItemGroupPort).getLatestItemGroupRevisionId()
        doReturn(samePublishedItemsVersion).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val spyService = spy(itemGroupsService)
        doNothing().whenever(spyService).applyItemGroupsToItems("test user")

        spyService.applyItemGroupsIfObsolete("test user")

        verify(spyService, never()).applyItemGroupsToItems("test user")
    }

    @Test
    fun `Should generate outputs including items to item groups mapping and item group audit while publishing item groups`() {
        val spyService = spy(itemGroupsService)
        val emptyList = emptyList<ItemGroup>()
        val whateverVersion = "version"

        doNothing().whenever(spyService).applyItemGroupsIfObsolete("test user")
        doReturn(emptyList).whenever(mockItemGroupPort).listActiveItemGroups()
        doReturn(LocalDateTime.now()).whenever(mockDateTimePort).now()
        doReturn(whateverVersion).whenever(mockDateTimePort).toPublishedVersionString(any())

        spyService.publish("test user", "comment")

        verify(mockPublishItemGroupPort, times(1)).publishItemsToItemGroups(eq(whateverVersion))
        verify(mockPublishItemGroupPort, times(1)).exportItemGroupsForAudit(eq(emptyList), eq(whateverVersion))
    }

    @Test
    fun `should populate item group preview with source attribute and classification columns`() {
        val codingSystemNameA = "A"
        val classificationA = "$codingSystemNameA classification"
        val codingSystemToClassifications = mapOf(
            codingSystemNameA to listOf(createCodingSystemToClassification(codingSystemNameA, classificationA))
        )

        doReturn(
            PublishedItemVersion(
                publishedVersion = "2021-12-20",
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val publishedItemsA = spark.emptyDataFrame().alias("A")
        doReturn(publishedItemsA).whenever(mockItemsPort).getPublishedItems(eq(codingSystemNameA), eq("2021-12-20"))

        val itemsToItemGroupsA = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("A1", "g1", classificationA, "Group 1"),
                ItemsToItemGroupsRow("A2", "g2", classificationA, "Group 2"),
            )
        )

        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""")
        doReturn(itemsToItemGroupsA).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemGroupWithSourceAttributeColumns(eq(publishedItemsA), eq(itemGroup), any())
        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()

        val itemCountsByClassification = itemGroupsService.exportPreviewFileAndCountByClassification(itemGroup)
        assertEquals(2, itemCountsByClassification.values.sum())

        verify(mockExportItemsToItemGroupsPort, times(1)).writeToPreviewItemGroupTemp(any(), eq("A"))
        verify(mockCodingSystemPort, times(1)).findAllCodingSystemToClassifications()
    }

    @Test
    fun `Should throw an error if no items matched the previewed item group at all`() {
        doReturn(linkedSetOf("A", "B")).whenever(mockCodingSystemPort).findAll()
        doReturn(
            PublishedItemVersion(
                publishedVersion = "2021-12-20",
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val publishedItemsA = spark.emptyDataFrame().alias("A")
        doReturn(publishedItemsA).whenever(mockItemsPort)
            .getPublishedItems(eq("A"), eq("2021-12-20"))

        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""")
        doReturn(spark.emptyDataFrame()).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemGroupWithSourceAttributeColumns(eq(publishedItemsA), eq(itemGroup), any())

        Assertions.assertThatThrownBy {
            itemGroupsService.exportPreviewFileAndCountByClassification(itemGroup)
        }.isInstanceOf(RuntimeException::class.java)
            .hasMessageContaining("There were no items matching the given condition")
    }

    @Test
    fun `should persist a published item group version while publishing item groups`() {
        val spyService = spy(itemGroupsService)
        val emptyList = emptyList<ItemGroup>()
        val whateverVersion = "version"
        val userId = "test user"
        val comment = "comment"

        doNothing().whenever(spyService).applyItemGroupsIfObsolete(userId)
        doReturn(emptyList).whenever(mockItemGroupPort).listActiveItemGroups()

        val now: LocalDateTime = LocalDateTime.now()
        doReturn(now).whenever(mockDateTimePort).now()
        doReturn(whateverVersion).whenever(mockDateTimePort).toPublishedVersionString(eq(now))

        spyService.publish(userId, comment)

        val publishedVersion = PublishedItemGroupVersion(
            publishedBy = userId,
            publishedVersion = whateverVersion,
            comment = comment,
            publishedAt = now
        )

        verify(mockPublishItemGroupVersionPort, times(1)).savePublishedVersion(argThat {
            assertEquals(publishedVersion.publishedBy, this.publishedBy)
            assertEquals(publishedVersion.publishedVersion, this.publishedVersion)
            assertEquals(publishedVersion.comment, this.comment)
            assertEquals(publishedVersion.publishedAt, this.publishedAt)
            true
        })
    }

    @Test
    fun `should persist item to item group mappings while publishing item groups`() {
        val spyService = spy(itemGroupsService)
        val emptyList = emptyList<ItemGroup>()
        val whateverVersion = "version"
        val userId = "test user"
        val comment = "comment"

        doNothing().whenever(spyService).applyItemGroupsIfObsolete(userId)
        doReturn(emptyList).whenever(mockItemGroupPort).listActiveItemGroups()

        val now: LocalDateTime = LocalDateTime.now()
        doReturn(now).whenever(mockDateTimePort).now()
        doReturn(whateverVersion).whenever(mockDateTimePort).toPublishedVersionString(eq(now))

        val samplePublishedItemToItemGroupList = listOf(
            PublishedItemToItemGroup(
                "testItemCode", "testBusinessKey", "testClassification", "testGroupName"
            )
        )
        doReturn(samplePublishedItemToItemGroupList)
            .whenever(mockPublishItemGroupPort)
            .publishItemsToItemGroups(eq(whateverVersion))

        spyService.publish(userId, comment)

        verify(mockPublishItemGroupPort, times(1)).savePublishedItemToItemGroups(
            eq(whateverVersion),
            eq(samplePublishedItemToItemGroupList),
        )
    }

    @Test
    fun `Should apply item groups while get the mapping result between items and item groups`() {
        val spyService = spy(itemGroupsService)
        val userId = "test user"
        val samePublishedItemsVersion = PublishedItemVersion(
            publishedVersion = "20210101000000Z",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )
        val sameItemGroupVersion = 1

        doReturn(
            ItemGroupsMetadata(
                publishedItemVersion = samePublishedItemsVersion,
                itemGroupRevisionId = sameItemGroupVersion,
                createdBy = "tester",
                createdAt = LocalDateTime.now()
            )
        ).whenever(mockItemGroupMetadataPort).getDraftMetadata()

        doReturn(sameItemGroupVersion).whenever(mockItemGroupPort).getLatestItemGroupRevisionId()
        doReturn(samePublishedItemsVersion).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()
        doReturn(LocalDateTime.now()).whenever(mockDateTimePort).now()

        spyService.getMappingResultUrl(userId)

        verify(spyService, times(1)).applyItemGroupsIfObsolete(userId)
    }

    @Test
    fun `should generate zip by classification`() {
        val spyService = spy(itemGroupsService)
        val itemGroupName = "PBS Item"
        val now = LocalDateTime.now()
        val classificationExported = mapOf(itemGroupName to 5000L)
        val url = "http://localhost:8080/preview.zip"

        doReturn(now).whenever(mockDateTimePort).now()
        doReturn(url).whenever(mockExportItemsToItemGroupsPort).generateZipFileAndGetUrl(eq(itemGroupName), eq(now))

        val zipFileUrl = spyService.getPreviewFileUrl(itemGroupName, classificationExported)
        assertEquals(url, zipFileUrl)

        verify(mockExportItemsToItemGroupsPort, times(1)).generateZipFileAndGetUrl(eq(itemGroupName), eq(now))
    }

    @Test
    fun `should generate excel sheet by classification`() {
        val spyService = spy(itemGroupsService)
        val itemGroupName = "PBS Item"
        val now = LocalDateTime.now()
        val classificationExported = mapOf(itemGroupName to 2000L)
        val url = "http://localhost:8080/${itemGroupName}.xlsx"

        doReturn(now).whenever(mockDateTimePort).now()
        doReturn(url).whenever(mockExportItemsToItemGroupsPort)
            .generateExcelAndGetUrl(eq(classificationExported.keys), eq(itemGroupName), eq(now))

        val excelFileUrl = spyService.getPreviewFileUrl(itemGroupName, classificationExported)
        assertEquals(url, excelFileUrl)

        verify(mockExportItemsToItemGroupsPort, times(1))
            .generateExcelAndGetUrl(eq(classificationExported.keys), eq(itemGroupName), eq(now))
    }

    @Test
    fun `should filter out irrelevant classifications when preview an item group has pre-defined classification`() {
        val latestPublishedItemVersion = "2021-12-20"
        doReturn(
            PublishedItemVersion(
                publishedVersion = latestPublishedItemVersion,
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val codingSystemNameA = "A"
        val classificationA = "$codingSystemNameA classification"
        val codingSystemToClassifications = mapOf(
            codingSystemNameA to listOf(createCodingSystemToClassification(codingSystemNameA, classificationA))
        )

        val publishedItemsA = spark.emptyDataFrame().alias(codingSystemNameA)
        doReturn(publishedItemsA).whenever(mockItemsPort)
            .getPublishedItems(eq(codingSystemNameA), eq(latestPublishedItemVersion))

        val itemGroup =
            ItemGroup(name = "whatever", rule = """{"field":"Classification", "value":"A", "operator":"="}""")
        val itemsToItemGroupsA = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("A1", "gA1", classificationA, "Group A1"),
                ItemsToItemGroupsRow("A2", "gA2", classificationA, "Group A2"),
            )
        )
        doReturn(itemsToItemGroupsA).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemGroupWithSourceAttributeColumns(eq(publishedItemsA), eq(itemGroup), any())
        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()

        itemGroupsService.exportPreviewFileAndCountByClassification(itemGroup)

        verify(mockItemsPort, times(1)).getPublishedItems(eq(codingSystemNameA), eq(latestPublishedItemVersion))
        verify(mockCodingSystemPort, times(1)).findAllCodingSystemToClassifications()
    }

    @Test
    fun `should run all classifications when preview an item group does not have pre-defined classification`() {
        val latestPublishedItemVersion = "2021-12-20"
        doReturn(
            PublishedItemVersion(
                publishedVersion = latestPublishedItemVersion,
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val codingSystemNameA = "A"
        val codingSystemNameB = "B"
        val classificationA = "$codingSystemNameA classification"
        val classificationB = "$codingSystemNameB classification"
        val codingSystemToClassifications = mapOf(
            codingSystemNameA to listOf(createCodingSystemToClassification(codingSystemNameA, classificationA)),
            codingSystemNameB to listOf(createCodingSystemToClassification(codingSystemNameB, classificationB))
        )

        doReturn(setOf(codingSystemNameA, codingSystemNameB)).whenever(mockCodingSystemPort).findAll()

        val publishedItemsA = spark.emptyDataFrame().alias(codingSystemNameA)
        doReturn(publishedItemsA).whenever(mockItemsPort).getPublishedItems(
            eq(codingSystemNameA), eq(
                latestPublishedItemVersion
            )
        )

        val publishedItemsB = spark.emptyDataFrame().alias(codingSystemNameB)
        doReturn(publishedItemsB).whenever(mockItemsPort).getPublishedItems(
            eq(codingSystemNameB), eq(
                latestPublishedItemVersion
            )
        )

        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"ABC"}""")

        val itemsToItemGroupsA = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("A1", "gA1", classificationA, "Group A1"),
                ItemsToItemGroupsRow("A2", "gA2", classificationA, "Group A2"),
            )
        )
        doReturn(itemsToItemGroupsA).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemGroupWithSourceAttributeColumns(eq(publishedItemsA), eq(itemGroup), any())

        val itemsToItemGroupsB = createDataFrame(
            listOf(
                ItemsToItemGroupsRow("B1", "gB1", classificationB, "Group B1"),
                ItemsToItemGroupsRow("B2", "gB2", classificationB, "Group B2"),
            )
        )
        doReturn(itemsToItemGroupsB).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemGroupWithSourceAttributeColumns(eq(publishedItemsB), eq(itemGroup), any())

        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()

        itemGroupsService.exportPreviewFileAndCountByClassification(itemGroup)

        verify(mockItemsPort, times(1)).getPublishedItems(eq(codingSystemNameA), eq(latestPublishedItemVersion))
        verify(mockItemsPort, times(1)).getPublishedItems(eq(codingSystemNameA), eq(latestPublishedItemVersion))
    }

    @Test
    fun `Item group should be applied to all coding systems when none are specified `() {
        mockApplyItemGroupsToItemsDependencies()
        mockCodingSystems(setOf("a", "b"))

        val itemGroup = ItemGroup(
            name = "Apply to all",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(field = "other", value = "other", operator = "=")
            )
        )

        itemGroupsService.getItemsToItemGroupsWithoutSourceAttributeColumns(listOf(itemGroup))

        verify(mockApplyItemGroupRulePort).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            eq(listOf(itemGroup)),
            eq(listOf(createCodingSystemToClassification("a")))
        )
        verify(mockApplyItemGroupRulePort).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            eq(listOf(itemGroup)),
            eq(listOf(createCodingSystemToClassification("b")))
        )
        verify(mockApplyItemGroupRulePort, times(2)).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            any(),
            any()
        )
    }

    @Test
    fun `Item group should be applied to all coding systems when it specifies a coding system but can resolve to any `() {
        mockApplyItemGroupsToItemsDependencies()
        mockCodingSystems(setOf("a", "b"))
        val itemGroup = ItemGroup(
            name = "Apply to all",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    combinator = "or", rules = listOf(
                        Rule(field = "other", value = "other", operator = "="),
                        Rule(field = "coding_system", value = "a", operator = "=")
                    )
                )
            )
        )

        itemGroupsService.getItemsToItemGroupsWithoutSourceAttributeColumns(listOf(itemGroup))

        verify(mockApplyItemGroupRulePort).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            eq(listOf(itemGroup)),
            eq(listOf(createCodingSystemToClassification("a")))
        )
        verify(mockApplyItemGroupRulePort).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            eq(listOf(itemGroup)),
            eq(listOf(createCodingSystemToClassification("b")))
        )
        verify(mockApplyItemGroupRulePort, times(2)).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            any(),
            any()
        )
    }

    @Test
    fun `Item group should be applied to a specific coding systems it can resolve to`() {
        mockApplyItemGroupsToItemsDependencies()
        mockCodingSystems(setOf("a", "b", "c"))
        val itemGroup = ItemGroup(
            name = "Specific",
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    combinator = "or", rules = listOf(
                        Rule(field = "coding_system", value = "a", operator = "="),
                        Rule(field = "coding_system", value = "b", operator = "=")
                    )
                )
            )
        )

        itemGroupsService.getItemsToItemGroupsWithoutSourceAttributeColumns(listOf(itemGroup))

        verify(mockApplyItemGroupRulePort).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            eq(listOf(itemGroup)),
            eq(listOf(createCodingSystemToClassification("a")))
        )
        verify(mockApplyItemGroupRulePort).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            eq(listOf(itemGroup)),
            eq(listOf(createCodingSystemToClassification("b")))
        )
        verify(mockApplyItemGroupRulePort, times(2)).getItemsToItemsGroupsWithoutSourceAttributeColumns(
            any(),
            any(),
            any()
        )
    }

    @Test
    fun `preview item group should return two coding systems`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        val codingSystemC = "C"
        val codingSystemToClassifications = mapOf(
            codingSystemA to listOf(createCodingSystemToClassification(codingSystemA)),
            codingSystemB to listOf(createCodingSystemToClassification(codingSystemB)),
            codingSystemB to listOf(createCodingSystemToClassification(codingSystemC))
        )

        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()
        doReturn(
            PublishedItemVersion(
                publishedVersion = "2021-12-20",
                publishedBy = "tester",
                publishedAt = LocalDateTime.now()
            )
        ).whenever(mockPublishItemVersionPort).getLatestPublishedItemVersion()

        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""")
        val previewResult = listOf(
            ItemGroupPreviewResult(
                codingSystemA,
                10,
                listOf("code", "name"),
                listOf(mapOf("code" to "1", "name" to "a"),)
            )
        )
        doReturn(previewResult).whenever(mockItemGroupsSparkPort).getItemGroupPreview(any(), any(), any())

        val actualResult = itemGroupsService.getItemGroupPreview(itemGroup)

        assertEquals(previewResult, actualResult)

        verify(mockCodingSystemPort, times(1)).findAllCodingSystemToClassifications()
        verify(mockPublishItemVersionPort, times(1)).getLatestPublishedItemVersion()
        verify(mockItemGroupsSparkPort, times(1)).getItemGroupPreview(
            eq(itemGroup),
            eq(codingSystemToClassifications),
            eq("2021-12-20")
        )
    }

    @Test
    fun `should return mapping item group to topics`() {
        // given
        val businessKey = "test-item-group"
        val subscriptionRecordId1 = "subscription-record-id-1"
        val subscriptionRecordId2 = "subscription-record-id-2"
        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""", businessKey = businessKey)

        val workflow1 = createTestWorkflow(
            id = 1L,
            status = "GENERATED",
            relatedItemGroups = listOf(businessKey),
        )

        val workflow2 = createTestWorkflow(
            id = 2L,
            status = "PUBLISHED",
            relatedItemGroups = listOf(businessKey),
        )

        val workflow3 = createTestWorkflow(
            id = 3L,
            status = "GENERATED",
            relatedItemGroups = listOf(businessKey),
        )

        val topics = listOf(
            createTestTopic(
                id = 1L,
                relatedWorkflows = listOf(workflow1),
                subscriptionId = subscriptionRecordId1,
                name = "topic1",
                conditionName = "condition-1",
                therapyAreaName = "therapy-area-1"
            ),
            createTestTopic(
                id = 2L,
                relatedWorkflows = listOf(workflow2, workflow3),
                subscriptionId = subscriptionRecordId2,
                name = "topic1",
                conditionName = "condition-1",
                therapyAreaName = "therapy-area-1"
            ),
        )

        doReturn(topics).whenever(mockTopicPort).listTopicsByItemGroups(any())

        val subscriptions = listOf(
            CompanySubscription(
                id = "subscription-id-1",
                name = "subscription-name-1",
                status = "Active",
                recordId = subscriptionRecordId1
            ),
            CompanySubscription(
                id = "subscription-id-2",
                name = "subscription-name-2",
                status = "Expired",
                recordId = subscriptionRecordId2
            )
        )

        doReturn(
            listOf(
                EnrichedTopic(
                    topic = topics[0],
                    subscriptionStatus = subscriptions[0].status,
                    subscriptionName = subscriptions[0].name,
                    companyName = "Company 1"
                ),
                EnrichedTopic(
                    topic = topics[1],
                    subscriptionStatus = subscriptions[1].status,
                    subscriptionName = subscriptions[1].name,
                    companyName = "Company 2"
                )
            )
        ).whenever(mockCompanySubscriptionPort).enrichTopicsWithCompanyAndSubscription(topics)

        // when
        val itemGroupToTopics = itemGroupsService.getItemGroupToTopics(itemGroup)

        // then
        assertEquals(itemGroupToTopics[0].topicId, topics[0].id)
        assertEquals(itemGroupToTopics[0].companyId, topics[0].companyId)
        assertEquals(itemGroupToTopics[0].companyName, "Company 1")
        assertEquals(itemGroupToTopics[0].condition, topics[0].conditionName)
        assertEquals(itemGroupToTopics[0].workflowId, topics[0].relatedWorkflows[0].id)
        assertEquals(itemGroupToTopics[0].workflowStatus, topics[0].relatedWorkflows[0].status)
        assertEquals(itemGroupToTopics[0].therapyArea, topics[0].therapyAreaName)
        assertEquals(itemGroupToTopics[0].subscriptionName, subscriptions[0].name)
        assertEquals(itemGroupToTopics[0].subscriptionStatus, subscriptions[0].status)
        assertEquals(itemGroupToTopics[0].subscriptionRecordId, subscriptions[0].recordId)

        assertEquals(itemGroupToTopics[1].topicId, topics[1].id)
        assertEquals(itemGroupToTopics[1].companyId, topics[1].companyId)
        assertEquals(itemGroupToTopics[1].companyName, "Company 2")
        assertEquals(itemGroupToTopics[1].condition, topics[1].conditionName)
        assertEquals(itemGroupToTopics[1].workflowId, topics[1].relatedWorkflows[0].id)
        assertEquals(itemGroupToTopics[1].workflowStatus, topics[1].relatedWorkflows[0].status)
        assertEquals(itemGroupToTopics[1].therapyArea, topics[1].therapyAreaName)
        assertEquals(itemGroupToTopics[1].subscriptionName, subscriptions[1].name)
        assertEquals(itemGroupToTopics[1].subscriptionStatus, subscriptions[1].status)
        assertEquals(itemGroupToTopics[1].subscriptionRecordId, subscriptions[1].recordId)

        assertEquals(itemGroupToTopics[2].topicId, topics[1].id)
        assertEquals(itemGroupToTopics[2].companyId, topics[1].companyId)
        assertEquals(itemGroupToTopics[1].companyName, "Company 2")
        assertEquals(itemGroupToTopics[2].condition, topics[1].conditionName)
        assertEquals(itemGroupToTopics[2].workflowId, topics[1].relatedWorkflows[1].id)
        assertEquals(itemGroupToTopics[2].workflowStatus, topics[1].relatedWorkflows[1].status)
        assertEquals(itemGroupToTopics[2].therapyArea, topics[1].therapyAreaName)
        assertEquals(itemGroupToTopics[2].subscriptionName, subscriptions[1].name)
        assertEquals(itemGroupToTopics[2].subscriptionStatus, subscriptions[1].status)
        assertEquals(itemGroupToTopics[2].subscriptionRecordId, subscriptions[1].recordId)
    }

    @Test
    fun `should set company name to unknown if topic does not have company name`() {
        // given
        val businessKey = "test-item-group"
        val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"rule ABC"}""", businessKey = businessKey)

        val workflow1 = createTestWorkflow(
            id = 1L,
            status = "GENERATED",
            relatedItemGroups = listOf(businessKey),
        )

        val topics = listOf(
            createTestTopic(
                id = 1L,
                relatedWorkflows = listOf(workflow1),
                name = "topic1",
            )
        )

        doReturn(topics).whenever(mockTopicPort).listTopicsByItemGroups(any())

        doReturn(
            listOf(
                EnrichedTopic(
                    topic = topics[0],
                )
            )
        ).whenever(mockCompanySubscriptionPort).enrichTopicsWithCompanyAndSubscription(topics)

        // when
        val itemGroupToTopics = itemGroupsService.getItemGroupToTopics(itemGroup)

        // then
        assertEquals(itemGroupToTopics[0].companyName, "Unknown")
    }

    @Test
    fun `should publish item groups to topics`() {
        // given
        val itemsToItemGroups = listOf(
            PublishedItemToItemGroup("i1", "g1", "pbs", "group 1"),
            PublishedItemToItemGroup("i2", "g1", "pbs", "group 1"),
            PublishedItemToItemGroup("i3", "g2", "pbs", "group 2")
        )

        val mockTopics = listOf(
            createTestTopic(
                id = 1L,
                name = "topic1",
                relatedWorkflows = listOf(
                    createTestWorkflow(
                        id = 100L,
                        status = "GENERATED",
                        relatedItemGroups = listOf("g1")
                    ),
                    createTestWorkflow(
                        id = 200L,
                        status = "PUBLISHED",
                        relatedItemGroups = listOf("g1", "g2")
                    )
                )
            ),
            createTestTopic(
                id = 2L,
                name = "topic2",
                relatedWorkflows = listOf(
                    createTestWorkflow(
                        id = 300L,
                        status = "GENERATED",
                        relatedItemGroups = listOf("g2")
                    )
                )
            )
        )

        doReturn(mockTopics).whenever(mockTopicPort).listTopicsByItemGroups(eq(setOf("g1", "g2")))

        val mockEnrichedTopics = listOf(
            EnrichedTopic(
                topic = mockTopics[0],
                subscriptionStatus = "Active",
                subscriptionName = "subscription-name-1",
                companyName = "Company 1"
            ),
            EnrichedTopic(
                topic = mockTopics[1],
                subscriptionStatus = "Expired",
                subscriptionName = "subscription-name-2",
                companyName = "Company 2"
            )
        )

        doReturn(mockEnrichedTopics).whenever(mockCompanySubscriptionPort)
            .enrichTopicsWithCompanyAndSubscription(eq(mockTopics))

        // when
        itemGroupsService.publishItemGroupsToTopics(itemsToItemGroups, "v1")

        // then
        verify(mockPublishItemGroupPort).publishItemGroupsToTopics(
            eq("v1"),
            eq(mockEnrichedTopics),
            eq(mapOf("g1" to "group 1", "g2" to "group 2"))
        )
    }

    data class ItemsToItemGroupsRow(
        val item_code: String,
        val item_group: String,
        val classification: String,
        val item_group_name: String
    )

    private fun createDataFrame(rows: List<ItemsToItemGroupsRow>): Dataset<Row> {
        return spark.createDataFrame(rows, ItemsToItemGroupsRow::class.java)
    }

    private fun mockApplyItemGroupsToItemsDependencies() {
        val mockDataset = spark.emptyDataFrame()
        doReturn(mockDataset).whenever(mockItemsPort).getPublishedItems(any(), any())
        doReturn(
            PublishedItemVersion(
                publishedVersion = "whatever-version",
                publishedBy = "whoever",
                publishedAt = LocalDateTime.of(2022, 9, 1, 0, 0)
            )
        ).whenever(mockPublishItemVersionPort)
            .getLatestPublishedItemVersion()

        doReturn(spark.emptyDataFrame()).whenever(mockApplyItemGroupRulePort)
            .getItemsToItemsGroupsWithoutSourceAttributeColumns(any(), any(), any())
    }

    private fun mockCodingSystems(codingSystems: Set<String>) {
        val codingSystemToClassifications =
            codingSystems.associateWith { listOf(createCodingSystemToClassification(it)) }

        doReturn(codingSystemToClassifications).whenever(mockCodingSystemPort).findAllCodingSystemToClassifications()
        doReturn(codingSystems).whenever(mockCodingSystemPort).findAll()
    }

    private fun createCodingSystemToClassification(
        codingSystemName: String,
        classification: String = codingSystemName,
        codingSystemColumnToExport: String = "source_code"
    ): CodingSystemToClassification {
        return CodingSystemToClassification(
            codingSystem = codingSystemName,
            classification = classification,
            codingSystemColumnToExport = codingSystemColumnToExport
        )
    }
}