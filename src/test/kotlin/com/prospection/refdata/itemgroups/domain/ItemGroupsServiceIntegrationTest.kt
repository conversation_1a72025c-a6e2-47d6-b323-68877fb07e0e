package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.codingsystem.integration.CodingSystemToClassificationEntity
import com.prospection.refdata.common.consts.CodingSystems
import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestTopic
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestWorkflow
import com.prospection.refdata.config.S3Path.Items.Published.ENRICHED_ITEMS_PARQUET
import com.prospection.refdata.itemgroups.application.rest.dto.CustomGroupDto
import com.prospection.refdata.items.domain.ItemsPort
import com.prospection.refdata.items.domain.PublishItemVersionPort
import com.prospection.refdata.items.domain.PublishedItemVersion
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.job.domain.JobStatus
import com.prospection.refdata.rules.domain.Rule
import com.prospection.refdata.subscription.domain.CompanySubscriptionPort
import com.prospection.refdata.topic.domain.EnrichedTopic
import com.prospection.refdata.topic.domain.TopicPort
import org.apache.spark.sql.RowFactory
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.types.DataTypes
import org.apache.spark.sql.types.Metadata
import org.apache.spark.sql.types.StructField
import org.apache.spark.sql.types.StructType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.spy
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.doThrow
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import software.amazon.awssdk.core.exception.SdkClientException
import java.time.LocalDateTime

internal class ItemGroupsServiceIntegrationTest : AbstractIntegrationTest() {
    companion object {
        private const val VERSION = "version"
        private const val CLASSIFICATION = "Test Classification"
        private const val FORIAN_DISPENSING_CLASSIFICATION = "Forian Dispensing"
        private const val CODING_SYSTEM = "Test Coding System"
        private val PUBLISHED_ITEM_VERSION = PublishedItemVersion(VERSION, LocalDateTime.now(), "thomas")
    }

    @Autowired
    private lateinit var userPort: UserPort

    @Autowired
    private lateinit var itemsPort: ItemsPort

    @Autowired
    private lateinit var itemGroupsSparkPort: ItemGroupsSparkPort

    @Autowired
    private lateinit var applyItemGroupRulePort: ApplyItemGroupRulePort

    @MockitoSpyBean
    private lateinit var itemGroupMetadataPort: ItemGroupMetadataPort

    @Autowired
    private lateinit var exportItemsToItemGroupsPort: ExportItemsToItemGroupsPort

    private val itemGroupPort: ItemGroupPort = mock()

    private val dateTimePort: DateTimePort = mock()

    @Autowired
    private lateinit var jobPort: JobPort

    @Autowired
    private lateinit var publishItemGroupVersionPort: PublishItemGroupVersionPort

    @Autowired
    private lateinit var publishItemVersionPort: PublishItemVersionPort

    @MockitoSpyBean
    private lateinit var publishItemGroupPort: PublishItemGroupPort

    private lateinit var itemGroupsService: ItemGroupsService

    @Autowired
    private lateinit var publishedItemGroupsService: PublishedItemGroupsService

    @Autowired
    private lateinit var codingSystemPort: CodingSystemPort

    @Autowired
    private lateinit var codingSystemRepository: CodingSystemJpaRepository

    @MockitoSpyBean
    private lateinit var topicPort: TopicPort

    @MockitoSpyBean
    private lateinit var companySubscriptionPort: CompanySubscriptionPort

    @BeforeEach
    fun init() {
        itemGroupsService = spy(
            ItemGroupsService(
                userPort,
                itemGroupPort,
                dateTimePort,
                publishItemGroupPort,
                codingSystemPort,
                itemsPort,
                itemGroupsSparkPort,
                applyItemGroupRulePort,
                itemGroupMetadataPort,
                exportItemsToItemGroupsPort,
                mock(ChangeSummaryItemGroupPort::class.java),
                jobPort,
                publishItemGroupVersionPort,
                publishItemVersionPort,
                topicPort,
                companySubscriptionPort
            )
        )

        mockPublishedItems()
        mockListItemGroups()
        mockListCodingSystem()
        mockTopics()
    }

    @AfterEach
    fun clear() {
        reset(dateTimePort)
        reset(itemGroupPort)
        codingSystemRepository.deleteAll()
    }

    @Test
    fun `should generate the metadata after applying item groups to items`() {
        doReturn(1).whenever(itemGroupPort).getLatestItemGroupRevisionId()
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        insertTestPublishedItemVersion()
        insertTestPublishedItemGroupVersion()

        itemGroupsService.applyItemGroupsToItems("test user")

        val metadata = itemGroupMetadataPort.getDraftMetadata()

        assertThat(metadata).isNotNull
        metadata!!.let {
            assertThat(it.createdBy).contains("test user")
            assertThat(it.itemGroupRevisionId).isEqualTo(1L)
        }
    }

    @Test
    fun `should re-generate the metadata if item groups revision is incremented`() {
        doReturn(1, 2).whenever(itemGroupPort).getLatestItemGroupRevisionId()
        doReturn(LocalDateTime.now(), LocalDateTime.now().plusHours(1)).whenever(dateTimePort).now()

        insertTestPublishedItemVersion()
        insertTestPublishedItemGroupVersion()

        itemGroupsService.applyItemGroupsIfObsolete("test user")

        assertThat(itemGroupMetadataPort.getDraftMetadata()!!.itemGroupRevisionId).isEqualTo(1)

        itemGroupsService.applyItemGroupsIfObsolete("test user")

        assertThat(itemGroupMetadataPort.getDraftMetadata()!!.itemGroupRevisionId).isEqualTo(2)
    }

    @Test
    fun `should not re-generate the metadata if item groups revision is the same`() {
        doReturn(1).whenever(itemGroupPort).getLatestItemGroupRevisionId()
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        insertTestPublishedItemVersion()
        insertTestPublishedItemGroupVersion()

        itemGroupsService.applyItemGroupsIfObsolete("test user")
        val firstMetadata = itemGroupMetadataPort.getDraftMetadata()

        itemGroupsService.applyItemGroupsIfObsolete("test user")
        val secondMetadata = itemGroupMetadataPort.getDraftMetadata()

        // second call shouldn't trigger applyItemGroupsToItems
        verify(itemGroupsService, times(1)).applyItemGroupsToItems("test user")
        assertEquals(firstMetadata, secondMetadata)
    }

    @Test
    fun `should throw SdkClientException if publishing item groups failed by SdkClientException`() {
        val publishedTime = LocalDateTime.now()
        doReturn(publishedTime).whenever(dateTimePort).now()

        val publishedVersion = "version"
        doReturn(publishedVersion)
            .whenever(dateTimePort).toPublishedVersionString(publishedTime)

        doThrow(SdkClientException::class)
            .whenever(publishItemGroupPort).publishItemsToItemGroups(publishedVersion)

        insertTestPublishedItemVersion()

        Assertions.assertThrows(SdkClientException::class.java) {
            itemGroupsService.publish(
                "test user",
                "test-comment"
            )
        }
    }

    @Test
    fun `Should run a job if publishing item groups`() {
        val job = itemGroupsService.queuePublishingItemsAndItemGroups("test-comment")

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Publishing item groups", JobStatus.RUNNING)
    }

    @Test
    fun `Should run a job if downloading a preview file of an item group`() {
        val itemGroup = ItemGroup(
            name = "test name",
            rule = """{"field":"rule ABC"}""",
        )

        val job = itemGroupsService.queuePreviewFileDownload(itemGroup)

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Generating the preview file of item group - ${itemGroup.name}", JobStatus.RUNNING)
    }

    @Test
    fun `Should run a job if downloading the mapping results between items and item groups`() {
        val job = itemGroupsService.queueDownloadingMappingResult()

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Generating the mapping result between items and item groups", JobStatus.RUNNING)
    }

    @Test
    fun `Should increment the published item group version after publishing item groups`() {
        insertTestPublishedItemVersion()

        val yesterday = LocalDateTime.now().minusDays(1)
        doReturn(yesterday).whenever(dateTimePort).now()
        doReturn("version-yesterday").whenever(dateTimePort).toPublishedVersionString(yesterday)

        itemGroupsService.publish("whoever", "whatever")

        assertEquals("version-yesterday", publishItemGroupVersionPort.getLatestPublishedVersionString())

        val today = LocalDateTime.now()
        doReturn(today).whenever(dateTimePort).now()
        doReturn("version-today").whenever(dateTimePort).toPublishedVersionString(today)

        itemGroupsService.publish("whoever", "whatever")

        assertEquals("version-today", publishItemGroupVersionPort.getLatestPublishedVersionString())
    }

    @Test
    fun `Should insert the published item group version with the correct values`() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(VERSION).whenever(dateTimePort).toPublishedVersionString(now)
        insertTestPublishedItemVersion()

        val testUser = "test user"
        val testComment = "test comment"

        itemGroupsService.publish(testUser, testComment)

        assertThat(publishedItemGroupsService.getLatestPublishedVersion())
            .extracting("publishedVersion", "publishedBy", "publishedAt", "comment")
            .containsExactly(VERSION, testUser, now, testComment)
    }

    @Test
    fun `Should insert the published item groups`() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(VERSION).whenever(dateTimePort).toPublishedVersionString(now)
        insertTestPublishedItemVersion()

        itemGroupsService.publish("thomas", "test comment")

        assertThat(publishedItemGroupsService.findAllItemGroups(VERSION))
            .hasSize(1)
            .first()
            .extracting("businessKey", "name")
            .containsExactly("group_1", "Group 1")
    }

    @Test
    fun `should call method to save publish metadata`() {
        doReturn(1).whenever(itemGroupPort).getLatestItemGroupRevisionId()

        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(VERSION).whenever(dateTimePort).toPublishedVersionString(now)

        insertTestPublishedItemVersion()
        insertTestDraftItemGroupsMetadata()

        itemGroupsService.publish("thomas", "test comment")

        verify(itemGroupMetadataPort).savePublishMetadata(any(), eq("thomas"), eq(now))
    }

    @Test
    fun `custom groups should be created based on condition with one group attribute`() {
        val publishedItemsSchema = StructType(
            listOf(
                StructField("source_code", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_drug_name", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_icd_code", DataTypes.StringType, false, Metadata.empty()),
            ).toTypedArray()
        )
        val publishedItemsSchemaDs = spark.createDataFrame(
            listOf(
                RowFactory.create("1", "Drug 1", "icd_code_1;"),
                RowFactory.create("2", "Drug 2", "icd_code_2;"),
                RowFactory.create("3", "Drug 3", "icd_code_3;"),
                RowFactory.create("4", "Drug 1", "icd_code_2;icd_code_4;"),
            ), publishedItemsSchema
        )

        publishedItemsSchemaDs.write().mode(SaveMode.Overwrite)
            .parquet(
                "s3a://${applicationProperties.s3Bucket}/${ENRICHED_ITEMS_PARQUET}/${CodingSystems.FDB_DRUG}/${
                    createVersionPath(
                        VERSION
                    )
                }"
            )

        insertTestPublishedItemVersion()

        val customGroupCondition = CustomGroupCondition(
            FORIAN_DISPENSING_CLASSIFICATION,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    combinator = "or", rules = listOf(
                        Rule(field = "source_icd_code", value = "icd_code_1", operator = "contains"),
                        Rule(field = "source_icd_code", value = "icd_code_2", operator = "contains")
                    )
                )
            ),
            groupingAttributes = listOf("source_drug_name")
        )

        val customGroups = itemGroupsService.createCustomGroups(customGroupCondition)

        assertNotNull(customGroups)
        assertEquals(VERSION, customGroups?.publishedItemsVersion)

        assertThat(customGroups?.customGroups)
            .containsExactlyInAnyOrder(
                CustomGroupDto(
                    group = "Drug 1",
                    values = listOf("4", "1"),
                    target = "event.code",
                    FORIAN_DISPENSING_CLASSIFICATION,
                ),
                CustomGroupDto(
                    group = "Drug 2",
                    values = listOf("2"),
                    target = "event.code",
                    FORIAN_DISPENSING_CLASSIFICATION,
                )
            )
    }

    @Test
    fun `custom groups should be created based on condition with two group attributes`() {
        val publishedItemsSchema = StructType(
            listOf(
                StructField("source_code", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_drug_name", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_brand_name", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_icd_code", DataTypes.StringType, false, Metadata.empty()),
            ).toTypedArray()
        )
        val publishedItemsSchemaDs = spark.createDataFrame(
            listOf(
                RowFactory.create("1", "Drug 1", "Brand 1", "icd_code_1;"),
                RowFactory.create("2", "Drug 2", "Brand 2", "icd_code_2;"),
                RowFactory.create("3", "Drug 3", "Brand 3", "icd_code_3;"),
                RowFactory.create("4", "Drug 1", "Brand 4", "icd_code_2;icd_code_4;"),
            ), publishedItemsSchema
        )

        publishedItemsSchemaDs.write().mode(SaveMode.Overwrite)
            .parquet(
                "s3a://${applicationProperties.s3Bucket}/${ENRICHED_ITEMS_PARQUET}/${CodingSystems.FDB_DRUG}/${
                    createVersionPath(
                        VERSION
                    )
                }"
            )

        insertTestPublishedItemVersion()

        val customGroupCondition = CustomGroupCondition(
            FORIAN_DISPENSING_CLASSIFICATION,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    combinator = "or", rules = listOf(
                        Rule(field = "source_icd_code", value = "icd_code_1", operator = "contains"),
                        Rule(field = "source_icd_code", value = "icd_code_2", operator = "contains")
                    )
                )
            ),
            groupingAttributes = listOf("source_drug_name", "source_brand_name")
        )

        val customGroups = itemGroupsService.createCustomGroups(customGroupCondition)

        assertNotNull(customGroups)
        assertEquals(VERSION, customGroups?.publishedItemsVersion)

        assertThat(customGroups?.customGroups)
            .containsExactlyInAnyOrder(
                CustomGroupDto(
                    group = "Drug 1_Brand 4",
                    values = listOf("4"),
                    target = "event.code",
                    FORIAN_DISPENSING_CLASSIFICATION,
                ),
                CustomGroupDto(
                    group = "Drug 2_Brand 2",
                    values = listOf("2"),
                    target = "event.code",
                    FORIAN_DISPENSING_CLASSIFICATION,
                ),
                CustomGroupDto(
                    group = "Drug 1_Brand 1",
                    values = listOf("1"),
                    target = "event.code",
                    FORIAN_DISPENSING_CLASSIFICATION,
                )
            )
    }

    private fun mockPublishedItems() {
        val publishedItems = spark.createDataFrame(
            listOf(
                TestRow("1", "abc", arrayOf("A1", "B1"), CODING_SYSTEM),
                TestRow("2", "def", arrayOf("A2", "B2"), CODING_SYSTEM),
            ), TestRow::class.java
        )

        publishedItems.write().mode(SaveMode.Overwrite)
            .parquet(
                "s3a://${applicationProperties.s3Bucket}/${ENRICHED_ITEMS_PARQUET}/${CODING_SYSTEM}/${
                    createVersionPath(
                        VERSION
                    )
                }"
            )
    }

    private fun mockListItemGroups() {
        val mockItemGroups = listOf(
            ItemGroup(
                businessKey = "group_1",
                name = "Group 1",
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "abc", operator = "="),
                            Rule(field = "source_array", value = "1", operator = "contains"),
                            Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = CODING_SYSTEM, operator = "="),
                        ),
                        combinator = "and",
                    )
                ),
                lastModifiedAt = LocalDateTime.now(),
                lastModifiedBy = "markus"
            )
        )

        doReturn(mockItemGroups).whenever(itemGroupPort).listActiveItemGroups()
    }

    private fun mockListCodingSystem() {
        val codingSystemEntity = createCodingSystemEntity(name = CODING_SYSTEM, classification = CLASSIFICATION)
        val fdbDrugCodingSystemEntity =
            createCodingSystemEntity(name = CodingSystems.FDB_DRUG, classification = FORIAN_DISPENSING_CLASSIFICATION)

        codingSystemRepository.saveAll(listOf(codingSystemEntity, fdbDrugCodingSystemEntity))
    }

    private fun mockTopics() {
        val topic = createTestTopic(
            id = 1,
            name = "Test Topic",
            relatedWorkflows = listOf(
                createTestWorkflow(
                    id = 100,
                    status = "PUBLISHED",
                    relatedItemGroups = listOf("group_1")
                )
            )
        )

        doReturn(listOf(topic)).whenever(topicPort).listTopicsByItemGroups(any())

        doReturn(
            listOf(
                EnrichedTopic(
                    topic = topic,
                    companyName = "Company 1",
                    subscriptionName = "Prospection",
                    subscriptionStatus = "Active"
                )
            )
        ).whenever(companySubscriptionPort).enrichTopicsWithCompanyAndSubscription(listOf(topic))
    }

    private fun insertTestPublishedItemVersion() {
        publishItemVersionPort.savePublishedVersion(PUBLISHED_ITEM_VERSION)
    }

    private fun insertTestPublishedItemGroupVersion() {
        publishItemGroupVersionPort.savePublishedVersion(
            PublishedItemGroupVersion(
                VERSION, LocalDateTime.now(), "thomas", "test-comment"
            )
        )
    }

    private fun insertTestDraftItemGroupsMetadata() {
        itemGroupMetadataPort.saveDraftMetadata(
            ItemGroupsMetadata(
                createdBy = "tester",
                createdAt = LocalDateTime.now(),
                itemGroupRevisionId = 0,
                publishedItemVersion = PUBLISHED_ITEM_VERSION
            )
        )
    }

    private fun createCodingSystemEntity(
        name: String = CODING_SYSTEM,
        classification: String = CLASSIFICATION,
        codingSystemColumnToExport: String = "source_code"
    ): CodingSystemEntity {
        val codingSystemEntity = CodingSystemEntity(name = name)

        val codingSystemToClassificationEntity = CodingSystemToClassificationEntity(
            classification = classification,
            codingSystem = codingSystemEntity,
            codingSystemColumnToExport = codingSystemColumnToExport
        )

        codingSystemEntity.codingSystemToClassifications = listOf(codingSystemToClassificationEntity)

        return codingSystemEntity
    }

    data class TestRow(
        val source_code: String,
        val source_name: String,
        val source_array: Array<String>,
        val coding_system: String
    )
}