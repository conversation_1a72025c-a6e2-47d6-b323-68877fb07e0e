package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.itemgroups.domain.ConditionBasedItemGroupService.Companion.GROUPING_ENRICHED_ATTRIBUTE
import com.prospection.refdata.rules.domain.AttributeRepository
import com.prospection.refdata.rules.domain.EnrichedAttribute
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.EnrichmentRulePort
import com.prospection.refdata.rules.domain.Rule
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.whenever

internal class ConditionBasedItemGroupServiceTest {
    companion object {
        private const val TEST_CODING_SYSTEM = "A"
        private const val TEST_CLASSIFICATION = "Classification A1"
    }

    private val mockEnrichmentRulePort: EnrichmentRulePort = mock()
    private val mockCodingSystemPort: CodingSystemPort = mock()
    private val mockAttributeRepository: AttributeRepository = mock()

    private val conditionBasedItemGroupService = ConditionBasedItemGroupService(
        mockEnrichmentRulePort,
        mockCodingSystemPort,
        mockAttributeRepository,
    )

    @AfterEach
    fun cleanUp() {
        reset(mockEnrichmentRulePort)
        reset(mockCodingSystemPort)
        reset(mockAttributeRepository)
    }

    @Test
    fun `return list all relevant classifications that are configured with Grouping`() {
        val enrichmentRules = listOf(
            EnrichmentRule(
                enrichedAttributeValue = EnrichedAttributeValue(
                    value = "enriched_value",
                    id = "enriched_value",
                    enrichedAttribute = EnrichedAttribute(
                        name = GROUPING_ENRICHED_ATTRIBUTE,
                        id = "enriched_attribute"
                    )
                ),
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "abc", operator = "="),
                            Rule(field = "coding_system", value = TEST_CODING_SYSTEM),
                        ),
                        combinator = "and"
                    )
                ),
                deleted = 0,
            )
        )

        doReturn(enrichmentRules).whenever(mockEnrichmentRulePort).listActiveEnrichmentRules()

        doReturn(setOf(TEST_CLASSIFICATION)).whenever(mockCodingSystemPort)
            .findClassificationsByCodingSystem(TEST_CODING_SYSTEM)

        val classifications = conditionBasedItemGroupService.listClassifications()

        assertThat(classifications).containsExactlyInAnyOrder(TEST_CLASSIFICATION)
    }

    @Test
    fun `return an empty list if no coding system configured in rule`() {
        val enrichmentRules = listOf(
            EnrichmentRule(
                enrichedAttributeValue = EnrichedAttributeValue(
                    value = "enriched_value",
                    id = "enriched_value",
                    enrichedAttribute = EnrichedAttribute(
                        name = GROUPING_ENRICHED_ATTRIBUTE,
                        id = "enriched_attribute"
                    )
                ),
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = "source_name", value = "abc", operator = "="),
                        ),
                        combinator = "and"
                    )
                ),
                deleted = 0,
            )
        )

        doReturn(enrichmentRules).whenever(mockEnrichmentRulePort).listActiveEnrichmentRules()

        val classifications = conditionBasedItemGroupService.listClassifications()

        assertThat(classifications).isEmpty()
    }

    @Test
    fun `return default condition groupings`() {
        val groupingId = "grouping_id"
        val groupingEnrichedAttribute = EnrichedAttribute(name = GROUPING_ENRICHED_ATTRIBUTE, id = groupingId)
        val enrichedAttributes = listOf(
            groupingEnrichedAttribute,
            EnrichedAttribute(name = "Diagnosis", id = "diagnosis_id")
        )

        doReturn(enrichedAttributes).whenever(mockAttributeRepository).listEnrichedAttributes()

        val enrichedAttributeValues = listOf(
            EnrichedAttributeValue(
                value = "enriched_value_1",
                id = "enriched_value_1",
                enrichedAttribute = groupingEnrichedAttribute
            ),
            EnrichedAttributeValue(
                value = "enriched_value_2",
                id = "enriched_value_2",
                enrichedAttribute = groupingEnrichedAttribute
            )
        )

        doReturn(enrichedAttributeValues).whenever(mockAttributeRepository)
            .findEnrichedAttributeValuesByAttributeUuid(groupingId)

        val conditionGroupings = conditionBasedItemGroupService.listConditionGroupings()

        assertThat(conditionGroupings).isEqualTo(enrichedAttributeValues.map { it.value }.toSet())
    }

}