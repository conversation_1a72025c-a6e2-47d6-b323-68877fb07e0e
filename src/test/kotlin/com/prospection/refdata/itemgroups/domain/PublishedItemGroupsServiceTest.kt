package com.prospection.refdata.itemgroups.domain

import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions

internal class PublishedItemGroupsServiceTest {

    private val publishItemGroupPort: PublishItemGroupPort = mock()
    private val publishItemGroupVersionPort: PublishItemGroupVersionPort = mock()
    private val exportItemsToItemGroupsPort: ExportItemsToItemGroupsPort = mock()

    private val publishedItemGroupsService = PublishedItemGroupsService(
        publishItemGroupPort,
        publishItemGroupVersionPort,
        exportItemsToItemGroupsPort,
    )

    @AfterEach
    fun tearDown() {
        reset(publishItemGroupPort, publishItemGroupVersionPort, exportItemsToItemGroupsPort)
    }

    @Test
    fun archiveUnusedHistoricalData() {
        // when
        publishedItemGroupsService.archiveUnusedHistoricalData(listOf("version1", "version2"))

        // then
        verify(publishItemGroupVersionPort, times(1)).archivePublishedVersions(listOf("version1", "version2"))
        verify(publishItemGroupPort, times(1)).archivePublishedItemGroups(listOf("version1", "version2"))
        verify(publishItemGroupPort, times(1)).archivePublishedItemsToItemGroups(listOf("version1", "version2"))
        verifyNoMoreInteractions(
            publishItemGroupPort,
            publishItemGroupVersionPort,
            exportItemsToItemGroupsPort
        )
    }

    @Test
    fun finaliseArchiving() {
        // when
        publishedItemGroupsService.finaliseArchiving()

        // then
        verify(publishItemGroupPort, times(1)).finalizeArchiving()
        verifyNoMoreInteractions(
            publishItemGroupPort,
            publishItemGroupVersionPort,
            exportItemsToItemGroupsPort
        )
    }
}