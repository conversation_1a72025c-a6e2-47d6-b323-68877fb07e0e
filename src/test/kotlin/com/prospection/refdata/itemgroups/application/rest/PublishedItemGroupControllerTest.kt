package com.prospection.refdata.itemgroups.application.rest

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.config.S3Path.ItemGroups
import com.prospection.refdata.itemgroups.application.rest.dto.DuplicateItemsByItemGroupsDto
import com.prospection.refdata.itemgroups.application.rest.dto.DuplicatedItemDto
import com.prospection.refdata.itemgroups.application.rest.dto.DuplicatedItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.ItemToItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.PublishedItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.PublishedItemGroupVersionDto
import com.prospection.refdata.itemgroups.domain.PublishItemGroupPort
import com.prospection.refdata.itemgroups.domain.PublishItemGroupVersionPort
import com.prospection.refdata.itemgroups.domain.PublishedItemGroup
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupVersion
import com.prospection.refdata.itemgroups.domain.PublishedItemToItemGroup
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.time.LocalDateTime
import java.time.ZoneOffset

class PublishedItemGroupControllerTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var controller: PublishedItemGroupController

    @Autowired
    private lateinit var publishItemGroupVersionPort: PublishItemGroupVersionPort

    @Autowired
    private lateinit var publishItemGroupPort: PublishItemGroupPort

    @Test
    fun `Should return a list of published versions of item groups in descending order by published time`() {
        val yesterday = LocalDateTime.now().minusDays(1)
        publishItemGroupVersionPort.savePublishedVersion(
            PublishedItemGroupVersion(
                "v1",
                yesterday,
                "person 1",
                "comment 1",
            )
        )

        val today = LocalDateTime.now()
        publishItemGroupVersionPort.savePublishedVersion(
            PublishedItemGroupVersion(
                "v2",
                today,
                "person 2",
                "comment 2",
            )
        )

        val publishedVersions = controller.findAllPublishedVersions()

        assertThat(publishedVersions)
            .containsExactly(
                PublishedItemGroupVersionDto(
                    id = "v2",
                    publishedDate = today.toInstant(ZoneOffset.UTC),
                    publishedBy = "person 2",
                    comment = "comment 2",
                ),
                PublishedItemGroupVersionDto(
                    id = "v1",
                    publishedDate = yesterday.toInstant(ZoneOffset.UTC),
                    publishedBy = "person 1",
                    comment = "comment 1",
                )
            )
    }

    @Test
    fun `Should return the latest published version`() {
        val aDayBeforeYesterday = LocalDateTime.now().minusDays(1)
        publishItemGroupVersionPort.savePublishedVersion(
            PublishedItemGroupVersion(
                "v3", // intentionally using the max version string here so we can validate if version's ordered by publishedAt
                aDayBeforeYesterday,
                "person 3",
                "comment 3",
            )
        )

        val yesterday = LocalDateTime.now().minusDays(1)
        publishItemGroupVersionPort.savePublishedVersion(
            PublishedItemGroupVersion(
                "v1",
                yesterday,
                "person 1",
                "comment 1",
            )
        )

        val today = LocalDateTime.now()
        publishItemGroupVersionPort.savePublishedVersion(
            PublishedItemGroupVersion(
                "v2",
                today,
                "person 2",
                "comment 2",
            )
        )

        val latestPublishedVersion = controller.getLatestPublishedVersion()

        assertEquals(
            PublishedItemGroupVersionDto(
                id = "v2",
                publishedDate = today.toInstant(ZoneOffset.UTC),
                publishedBy = "person 2",
                comment = "comment 2"
            ), latestPublishedVersion
        )
    }

    @Test
    fun `Should return a list of published item to item group mappings by version and item group business keys`() {
        publishItemGroupPort.savePublishedItemToItemGroups(
            "v1", listOf(
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i2",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c2",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i3",
                    itemGroupBusinessKey = "g3",
                    itemGroupName = "Group 3",
                    classification = "c3",
                ),
            )
        )

        publishItemGroupPort.savePublishedItemToItemGroups(
            "v2", listOf(
                PublishedItemToItemGroup(
                    itemCode = "i4",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c4",
                ),
            )
        )

        val itemToItemGroups = controller.findPublishedItemToItemGroups("v1", setOf("g1", "g3"))

        assertThat(itemToItemGroups)
            .containsExactlyInAnyOrder(
                ItemToItemGroupDto(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                ItemToItemGroupDto(
                    itemCode = "i3",
                    itemGroupBusinessKey = "g3",
                    itemGroupName = "Group 3",
                    classification = "c3",
                ),
            )
    }

    @Test
    fun `Should return item groups by a version and business keys`() {
        publishItemGroupPort.savePublishedItemGroups(
            "v1",
            setOf(
                PublishedItemGroup("g1", "v1 Group 1"),
                PublishedItemGroup("g2", "v1 Group 2"),
                PublishedItemGroup("g3", "v1 Group 3"),
            )
        )

        publishItemGroupPort.savePublishedItemGroups(
            "v2",
            setOf(
                PublishedItemGroup("g1", "v2 Group 1"),
                PublishedItemGroup("g2", "v2 Group 2"),
                PublishedItemGroup("g3", "v2 Group 3"),
            )
        )

        assertThat(
            controller.findItemGroupsByBusinessKeys(
                "v1",
                setOf("g1", "g2")
            )
        )
            .containsExactlyInAnyOrder(
                PublishedItemGroupDto("g1", "v1 Group 1"),
                PublishedItemGroupDto("g2", "v1 Group 2"),
            )
    }

    @Test
    fun `Should return item groups by a (version) and (case insensitive name or case insensitive business key)`() {
        val searchString = "SearchString"
        publishItemGroupPort.savePublishedItemGroups(
            "v1",
            setOf(
                PublishedItemGroup("g1 $searchString", "v1 Group 1"),
                PublishedItemGroup("g2", "v1 Group 2 $searchString"),
                PublishedItemGroup("g3", "v1 Group 3"),
            )
        )

        publishItemGroupPort.savePublishedItemGroups(
            "v2",
            setOf(
                PublishedItemGroup("g1 $searchString", "v2 Group 1"),
                PublishedItemGroup("g2", "v2 Group 2 $searchString"),
                PublishedItemGroup("g3", "v2 Group 3"),
            )
        )

        assertThat(
            controller.findItemGroupsByNameOrBusinessKey(
                "v1",
                searchString,
                Pageable.unpaged(),
            )
        )
            .containsExactly(
                PublishedItemGroupDto("g1 $searchString", "v1 Group 1"),
                PublishedItemGroupDto("g2", "v1 Group 2 $searchString"),
            )
    }

    @Test
    fun `Should return classifications by a version and business keys`() {
        publishItemGroupPort.savePublishedItemToItemGroups(
            "v1", listOf(
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i2",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c2",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i3",
                    itemGroupBusinessKey = "g3",
                    itemGroupName = "Group 3",
                    classification = "c3",
                ),
            )
        )

        publishItemGroupPort.savePublishedItemToItemGroups(
            "v", listOf(
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i2",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c2",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i3",
                    itemGroupBusinessKey = "g3",
                    itemGroupName = "Group 3",
                    classification = "c3",
                ),
            )
        )

        publishItemGroupPort.savePublishedItemToItemGroups(
            "v2", listOf(
                PublishedItemToItemGroup(
                    itemCode = "i3",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c4",
                ),
            )
        )

        assertThat(controller.findClassificationsByBusinessKeys("v1", setOf("g1", "g2")))
            .containsExactly("c1", "c2")
    }

    @Test
    fun `Should return all item groups of a version`() {
        publishItemGroupPort.savePublishedItemGroups(
            "v1",
            setOf(
                PublishedItemGroup("g1", "v1 Group 1"),
                PublishedItemGroup("g2", "v1 Group 2"),
                PublishedItemGroup("g3", "v1 Group 3"),
            )
        )

        publishItemGroupPort.savePublishedItemGroups(
            "v2",
            setOf(
                PublishedItemGroup("g1", "v2 Group 1"),
                PublishedItemGroup("g2", "v2 Group 2"),
                PublishedItemGroup("g3", "v2 Group 3"),
            )
        )

        assertThat(controller.findAllItemGroups("v2"))
            .containsExactlyInAnyOrder(
                PublishedItemGroupDto("g1", "v2 Group 1"),
                PublishedItemGroupDto("g2", "v2 Group 2"),
                PublishedItemGroupDto("g3", "v2 Group 3"),
            )
    }

    @Test
    fun `Should return the correct file path of item to item group mappings`() {
        val versionString = "test-version"

        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(versionString)}/test.csv")
                .build(),
            RequestBody.fromString("")
        )

        assertThat(controller.getPublishedItemToItemGroupsFilePath(versionString))
            .isEqualTo("s3a://test-bucket/${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/version=test-version/test.csv")
    }

    @Test
    fun `Should return duplicate items by item group business keys`() {
        val versionString = "test-version"
        publishItemGroupPort.savePublishedItemToItemGroups(
            versionString,
            listOf(
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i2",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c2",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i2",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c2",
                ),
            )
        )

        val duplicateItems = controller.findDuplicateItemsByItemGroups(versionString, setOf("g1", "g2"))

        assertThat(duplicateItems)
            .containsExactly(
                DuplicateItemsByItemGroupsDto(
                    duplicateItemGroups = listOf(
                        DuplicatedItemGroupDto(
                            name = "Group 1",
                            itemGroupId = "g1",
                        ),
                        DuplicatedItemGroupDto(
                            name = "Group 2",
                            itemGroupId = "g2",
                        )
                    ),
                    duplicateItems = listOf(
                        DuplicatedItemDto(
                            code = "i1",
                            classification = "c1",
                        ),
                        DuplicatedItemDto(
                            code = "i2",
                            classification = "c2",
                        )
                    )
                )
            )
    }

    @Test
    fun `Should not return duplicate items if no duplicate item code`() {
        val versionString = "test-version"
        publishItemGroupPort.savePublishedItemToItemGroups(
            versionString,
            listOf(
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i2",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c1",
                ),
            )
        )

        val duplicateItems = controller.findDuplicateItemsByItemGroups(versionString, setOf("g1", "g2"))

        assertThat(duplicateItems).isEmpty()
    }

    @Test
    fun `Should not return duplicate items if no duplicate classification`() {
        val versionString = "test-version"
        publishItemGroupPort.savePublishedItemToItemGroups(
            versionString,
            listOf(
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g1",
                    itemGroupName = "Group 1",
                    classification = "c1",
                ),
                PublishedItemToItemGroup(
                    itemCode = "i1",
                    itemGroupBusinessKey = "g2",
                    itemGroupName = "Group 2",
                    classification = "c2",
                ),
            )
        )

        val duplicateItems = controller.findDuplicateItemsByItemGroups(versionString, setOf("g1", "g2"))

        assertThat(duplicateItems).isEmpty()
    }
}