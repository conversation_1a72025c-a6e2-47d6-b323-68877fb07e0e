package com.prospection.refdata.itemgroups.application.job

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupVersion
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupsService
import com.prospection.refdata.itemgroups.integration.ArchivedItemGroupEntity
import com.prospection.refdata.itemgroups.integration.ArchivedItemGroupJpaRepository
import com.prospection.refdata.itemgroups.integration.ArchivedItemToItemGroupEntity
import com.prospection.refdata.itemgroups.integration.ArchivedItemToItemGroupJpaRepository
import com.prospection.refdata.itemgroups.integration.PublishedItemGroupEntity
import com.prospection.refdata.itemgroups.integration.PublishedItemGroupJpaRepository
import com.prospection.refdata.itemgroups.integration.PublishedItemGroupVersionEntity
import com.prospection.refdata.itemgroups.integration.PublishedItemGroupVersionJpaRepository
import com.prospection.refdata.itemgroups.integration.PublishedItemToItemGroupEntity
import com.prospection.refdata.itemgroups.integration.PublishedItemToItemGroupJpaRepository
import com.prospection.refdata.topic.domain.TopicPort
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.groups.Tuple
import org.jooq.DSLContext
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.atLeast
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.verifyNoMoreInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.doCallRealMethod
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.doThrow
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.autoconfigure.orm.jpa.AutoConfigureTestEntityManager
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@AutoConfigureTestEntityManager
internal class UnusedHistoricalDataArchivingJobIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var unusedHistoricalDataArchivingJob: UnusedHistoricalDataArchivingJob

    @MockitoSpyBean
    private lateinit var publishedItemGroupsService: PublishedItemGroupsService

    @MockitoSpyBean
    private lateinit var topicPort: TopicPort

    @MockitoSpyBean
    private lateinit var dslContext: DSLContext

    @Autowired
    private lateinit var publishedItemGroupVersionJpaRepository: PublishedItemGroupVersionJpaRepository
    @Autowired
    private lateinit var publishedItemGroupJpaRepository: PublishedItemGroupJpaRepository
    @Autowired
    private lateinit var publishedItemToItemGroupJpaRepository: PublishedItemToItemGroupJpaRepository
    @Autowired
    private lateinit var archivedItemGroupJpaRepository: ArchivedItemGroupJpaRepository
    @Autowired
    private lateinit var archivedItemToItemGroupJpaRepository: ArchivedItemToItemGroupJpaRepository
    @Autowired
    private lateinit var entityManager: TestEntityManager
    @Autowired
    @Value("\${application.job.archiveUnusedData.cutOffMonths}")
    private var cutOffMonth: Long = 0

    private lateinit var cutOffDate: LocalDateTime

    @BeforeEach
    override fun setUp() {
        super.setUp()
        cutOffDate = truncateToMicroseconds(LocalDateTime.now().minusMonths(cutOffMonth))
        dslContext.execute("UPDATE archive_unused_historical_data_lock SET locked = false")
        reset(topicPort, dslContext)
        publishedItemGroupVersionJpaRepository.deleteAll()
        publishedItemGroupJpaRepository.deleteAll()
        publishedItemToItemGroupJpaRepository.deleteAll()
        archivedItemGroupJpaRepository.deleteAll()
        archivedItemToItemGroupJpaRepository.deleteAll()
        entityManager.flush()

        // Ensure S3 bucket is clean before each test
        clearS3BucketContents()
    }

    @AfterEach
    fun tearDown() {
        // Clean S3 bucket after each test to prevent interference
        clearS3BucketContents()
    }

    @Test
    fun `start should do nothing when no version at all`() {
        // Given
        doReturn(listOf("unknown")).whenever(topicPort).listInUseReferenceDataVersions()

        // When
        unusedHistoricalDataArchivingJob.start()

        // Then
        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verify(publishedItemGroupsService, times(1)).findAllPublishedVersions()
        verifyNoMoreInteractions(publishedItemGroupsService)

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).isEmpty()
        assertThat(publishedItemGroupVersionJpaRepository.findAll()).isEmpty()
        assertThat(publishedItemGroupJpaRepository.findAll()).isEmpty()
        assertThat(archivedItemGroupJpaRepository.findAll()).isEmpty()
        assertThat(publishedItemToItemGroupJpaRepository.findAll()).isEmpty()
        assertThat(archivedItemToItemGroupJpaRepository.findAll()).isEmpty()

        assertAcquireAndReleaseLock()

        assertNumFilesInBucket(0)
    }

    @Test
    fun `start should do nothing when no in used ref version at all`() {
        // Given
        preparePublishedData("v1", -1)
        preparePublishedData("v2", 1)
        doReturn(listOf<String>()).whenever(topicPort).listInUseReferenceDataVersions()

        // When
        unusedHistoricalDataArchivingJob.start()

        // Then
        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verifyNoInteractions(publishedItemGroupsService)

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v2", cutOffDate.plusDays(1), "", ""),
            PublishedItemGroupVersion("v1", cutOffDate.plusDays(-1), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v1", cutOffDate.plusDays(-1), false),
                Tuple("v2", cutOffDate.plusDays(1), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
                Tuple("v2", "itemGroup1", "Item Group 1"),
                Tuple("v2", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll()).isEmpty()

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v2", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v2", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll()).isEmpty()

        assertAcquireAndReleaseLock()

        assertPublishedItemToItemGroupInS3("v1", "v2")
        assertNumFilesInBucket(2)
    }

    @Test
    fun `start should do nothing when no version suitable to archive`() {
        // Given
        preparePublishedData("v1", -1)
        preparePublishedData("v2", 1)
        doReturn(listOf("v1")).`when`(topicPort).listInUseReferenceDataVersions()

        // When
        unusedHistoricalDataArchivingJob.start()

        // Then
        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verify(publishedItemGroupsService, times(1)).findAllPublishedVersions()
        verifyNoMoreInteractions(publishedItemGroupsService)

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v2", cutOffDate.plusDays(1), "", ""),
            PublishedItemGroupVersion("v1", cutOffDate.plusDays(-1), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v1", cutOffDate.plusDays(-1), false),
                Tuple("v2", cutOffDate.plusDays(1), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v2", "itemGroup1", "Item Group 1"),
                Tuple("v2", "itemGroup2", "Item Group 2"),
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll()).isEmpty()

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v2", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v2", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll()).isEmpty()

        assertAcquireAndReleaseLock()

        assertPublishedItemToItemGroupInS3("v1", "v2")
        assertNumFilesInBucket(2)
    }

    @Test
    fun `start should archive unused version before cut off months`() {
        // Given
        prepareArchivedData("v0", -4) // should not be affected
        preparePublishedData("v1", -3)
        preparePublishedData("v2", -2) // should not be affected
        preparePublishedData("v3", -1)
        preparePublishedData("v4", 1) // should not be affected
        preparePublishedData("v5", 2) // should not be affected

        doReturn(listOf("unknown", "v0", "v2", "v4")).whenever(topicPort).listInUseReferenceDataVersions()

        // When
        unusedHistoricalDataArchivingJob.start()

        // Then
        val versionsToArchiveCaptor = argumentCaptor<List<String>>()
        verify(publishedItemGroupsService, times(1)).archiveUnusedHistoricalData(versionsToArchiveCaptor.capture())
        assertThat(versionsToArchiveCaptor.firstValue).containsExactly("v1", "v3")

        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verify(publishedItemGroupsService, times(1)).findAllPublishedVersions()
        verify(publishedItemGroupsService, times(1)).finaliseArchiving()

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v5", cutOffDate.plusDays(2), "", ""),
            PublishedItemGroupVersion("v4", cutOffDate.plusDays(1), "", ""),
            PublishedItemGroupVersion("v2", cutOffDate.plusDays(-2), "", "")
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v0", cutOffDate.plusDays(-4), true),
                Tuple("v1", cutOffDate.plusDays(-3), true),
                Tuple("v2", cutOffDate.plusDays(-2), false),
                Tuple("v3", cutOffDate.plusDays(-1), true),
                Tuple("v4", cutOffDate.plusDays(1), false),
                Tuple("v5", cutOffDate.plusDays(2), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v2", "itemGroup1", "Item Group 1"),
                Tuple("v2", "itemGroup2", "Item Group 2"),
                Tuple("v4", "itemGroup1", "Item Group 1"),
                Tuple("v4", "itemGroup2", "Item Group 2"),
                Tuple("v5", "itemGroup1", "Item Group 1"),
                Tuple("v5", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
                Tuple("v3", "itemGroup1", "Item Group 1"),
                Tuple("v3", "itemGroup2", "Item Group 2"),
            )

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v2", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v2", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v4", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v4", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v5", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v5", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemCode1", "itemGroup1", "class2", "Item Group 1"),
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v3", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v3", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertAcquireAndReleaseLock()

        assertArchivedItemToItemGroupInS3("v0", "v1", "v3")
        assertPublishedItemToItemGroupInS3("v2", "v4", "v5")
        assertNumFilesInBucket(6)
    }

    @Test
    fun `start should split versions to archive in chunks`() {
        // Given
        prepareArchivedData("v0", -4) // should not affect
        preparePublishedData("v1", -3)
        preparePublishedData("v2", -2)
        preparePublishedData("v3", -1)
        preparePublishedData("v4", 1) // should not be affected

        doReturn(listOf("unknown")).whenever(topicPort).listInUseReferenceDataVersions()

        // When
        unusedHistoricalDataArchivingJob.start()

        // Then
        val versionsToArchiveCaptor = argumentCaptor<List<String>>()
        verify(publishedItemGroupsService, times(2)).archiveUnusedHistoricalData(versionsToArchiveCaptor.capture())
        assertThat(versionsToArchiveCaptor.firstValue).containsExactly("v1", "v2")
        assertThat(versionsToArchiveCaptor.secondValue).containsExactly("v3")

        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verify(publishedItemGroupsService, times(1)).findAllPublishedVersions()
        verify(publishedItemGroupsService, times(1)).finaliseArchiving()

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v4", cutOffDate.plusDays(1), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v0", cutOffDate.plusDays(-4), true),
                Tuple("v1", cutOffDate.plusDays(-3), true),
                Tuple("v2", cutOffDate.plusDays(-2), true),
                Tuple("v3", cutOffDate.plusDays(-1), true),
                Tuple("v4", cutOffDate.plusDays(1), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v4", "itemGroup1", "Item Group 1"),
                Tuple("v4", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
                Tuple("v2", "itemGroup1", "Item Group 1"),
                Tuple("v2", "itemGroup2", "Item Group 2"),
                Tuple("v3", "itemGroup1", "Item Group 1"),
                Tuple("v3", "itemGroup2", "Item Group 2"),
            )

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v4", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v4", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemCode1", "itemGroup1", "class2", "Item Group 1"),
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v2", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v2", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v3", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v3", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertAcquireAndReleaseLock()

        assertArchivedItemToItemGroupInS3("v0", "v2", "v1", "v3")
        assertPublishedItemToItemGroupInS3("v4")
        assertNumFilesInBucket(5)
    }

    @Test
    fun `start should call finalise and save chunk 1 to db when chunk 1 finished and chunk 2 throw exception`() {
        // Given
        prepareArchivedData("v0", -4) // should not be affected
        preparePublishedData("v1", -3)
        preparePublishedData("v2", -2)
        preparePublishedData("v3", -1)
        preparePublishedData("v4", 1) // should not be affected

        doReturn(listOf("unknown")).whenever(topicPort).listInUseReferenceDataVersions()

        doCallRealMethod()
            .doThrow(RuntimeException("Test exception"))
            .whenever(publishedItemGroupsService).archiveUnusedHistoricalData(any())

        // When
        assertThrows<Exception> { unusedHistoricalDataArchivingJob.start() }

        // Then
        val versionsToArchiveCaptor = argumentCaptor<List<String>>()
        verify(publishedItemGroupsService, times(2)).archiveUnusedHistoricalData(versionsToArchiveCaptor.capture())
        assertThat(versionsToArchiveCaptor.firstValue).containsExactly("v1", "v2")
        assertThat(versionsToArchiveCaptor.secondValue).containsExactly("v3")

        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verify(publishedItemGroupsService, times(1)).findAllPublishedVersions()
        verify(publishedItemGroupsService, times(1)).finaliseArchiving()

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v4", cutOffDate.plusDays(1), "", ""),
            PublishedItemGroupVersion("v3", cutOffDate.plusDays(-1), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v0", cutOffDate.plusDays(-4), true),
                Tuple("v1", cutOffDate.plusDays(-3), true),
                Tuple("v2", cutOffDate.plusDays(-2), true),
                Tuple("v3", cutOffDate.plusDays(-1), false),
                Tuple("v4", cutOffDate.plusDays(1), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v3", "itemGroup1", "Item Group 1"),
                Tuple("v3", "itemGroup2", "Item Group 2"),
                Tuple("v4", "itemGroup1", "Item Group 1"),
                Tuple("v4", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
                Tuple("v2", "itemGroup1", "Item Group 1"),
                Tuple("v2", "itemGroup2", "Item Group 2"),
            )

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v3", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v3", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v4", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v4", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemCode1", "itemGroup1", "class2", "Item Group 1"),
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
                Tuple("v2", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v2", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertAcquireAndReleaseLock()

        assertArchivedItemToItemGroupInS3("v0", "v2", "v1")
        assertPublishedItemToItemGroupInS3("v3", "v4")
        assertNumFilesInBucket(5)
    }

    @Test
    fun `start should not call finalise when there is exception and no chunk processed`() {
        // Given
        prepareArchivedData("v0", -4) // should not affect archive data
        preparePublishedData("v1", -3)

        doReturn(listOf("unknown")).whenever(topicPort).listInUseReferenceDataVersions()
        doThrow(RuntimeException("Test exception"))
            .whenever(publishedItemGroupsService).archiveUnusedHistoricalData(any())

        // When
        assertThrows<Exception> { unusedHistoricalDataArchivingJob.start() }

        // Then
        val versionsToArchiveCaptor = argumentCaptor<List<String>>()
        verify(publishedItemGroupsService, times(1))
            .archiveUnusedHistoricalData(versionsToArchiveCaptor.capture())
        assertThat(versionsToArchiveCaptor.firstValue).containsExactly("v1")

        verify(topicPort, times(1)).listInUseReferenceDataVersions()
        verify(publishedItemGroupsService, times(1)).findAllPublishedVersions()
        verifyNoMoreInteractions(publishedItemGroupsService)

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v1", cutOffDate.plusDays(-3), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v0", cutOffDate.plusDays(-4), true),
                Tuple("v1", cutOffDate.plusDays(-3), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemGroup1", "Item Group 1"),
            )

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v0", "itemCode1", "itemGroup1", "class2", "Item Group 1"),
            )

        assertAcquireAndReleaseLock()

        assertArchivedItemToItemGroupInS3("v0")
        assertPublishedItemToItemGroupInS3("v1")
        assertNumFilesInBucket(2)
    }

    @Test
    fun `start should throw and do nothing if can not acquire lock`() {
        // Given
        dslContext.execute("UPDATE archive_unused_historical_data_lock SET locked = true")
        reset(dslContext)

        preparePublishedData("v1", -1)
        doReturn(listOf<String>()).`when`(topicPort).listInUseReferenceDataVersions()

        // When
        assertThrows<Exception> { unusedHistoricalDataArchivingJob.start() }

        // Then
        verifyNoInteractions(topicPort)
        verifyNoInteractions(publishedItemGroupsService)

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v1", cutOffDate.plusDays(-1), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v1", cutOffDate.plusDays(-1), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll()).isEmpty()

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll()).isEmpty()

        val sqlCaptor = argumentCaptor<String>()
        verify(dslContext, times(1)).execute(sqlCaptor.capture())
        assertThat(sqlCaptor.firstValue).isEqualTo("UPDATE archive_unused_historical_data_lock SET locked = true WHERE locked = false")

        assertPublishedItemToItemGroupInS3("v1")
        assertNumFilesInBucket(1)
    }

    @Test
    fun `start should throw and do nothing if in used ref version is empty`() {
        // Given
        dslContext.execute("UPDATE archive_unused_historical_data_lock SET locked = true")
        reset(dslContext)

        preparePublishedData("v1", -1)
        doReturn(listOf<String>()).`when`(topicPort).listInUseReferenceDataVersions()

        // When
        assertThrows<Exception> { unusedHistoricalDataArchivingJob.start() }

        // Then
        verifyNoInteractions(topicPort)
        verifyNoInteractions(publishedItemGroupsService)

        assertThat(publishedItemGroupsService.findAllPublishedVersions()).containsExactly(
            PublishedItemGroupVersion("v1", cutOffDate.plusDays(-1), "", ""),
        )

        assertThat(publishedItemGroupVersionJpaRepository.findAll())
            .extracting("publishedVersion", "publishedAt", "archived")
            .containsExactlyInAnyOrder(
                Tuple("v1", cutOffDate.plusDays(-1), false),
            )

        assertThat(publishedItemGroupJpaRepository.findAll())
            .extracting("version", "businessKey", "name")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemGroup1", "Item Group 1"),
                Tuple("v1", "itemGroup2", "Item Group 2"),
            )

        assertThat(archivedItemGroupJpaRepository.findAll()).isEmpty()

        assertThat(publishedItemToItemGroupJpaRepository.findAll())
            .extracting("version", "itemCode", "itemGroupBusinessKey", "classification", "itemGroupName")
            .containsExactlyInAnyOrder(
                Tuple("v1", "itemCode1", "itemGroup1", "class1", "Item Group 1"),
                Tuple("v1", "itemCode2", "itemGroup2", "class2", "Item Group 2"),
            )

        assertThat(archivedItemToItemGroupJpaRepository.findAll()).isEmpty()

        val sqlCaptor = argumentCaptor<String>()
        verify(dslContext, times(1)).execute(sqlCaptor.capture())
        assertThat(sqlCaptor.firstValue).isEqualTo("UPDATE archive_unused_historical_data_lock SET locked = true WHERE locked = false")

        assertPublishedItemToItemGroupInS3("v1")
        assertNumFilesInBucket(1)
    }

    private fun preparePublishedData(version: String, daySinceCutOff: Long) {
        publishedItemGroupVersionJpaRepository.save(
            PublishedItemGroupVersionEntity(version, "", truncateToMicroseconds(cutOffDate.plusDays(daySinceCutOff)), ""),
        )
        publishedItemGroupJpaRepository.saveAll(listOf(
            PublishedItemGroupEntity(null, version, "itemGroup1", "Item Group 1"),
            PublishedItemGroupEntity(null, version, "itemGroup2", "Item Group 2"),
        ))
        publishedItemToItemGroupJpaRepository.saveAll(listOf(
            PublishedItemToItemGroupEntity(null, version, "itemCode1", "itemGroup1", "class1", "Item Group 1"),
            PublishedItemToItemGroupEntity(null, version, "itemCode2", "itemGroup2", "class2", "Item Group 2"),
        ))
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("item-groups/published/item-to-item-group/version=$version/file.csv")
                .build(),
            RequestBody.fromString(version)
        )
    }

    private fun prepareArchivedData(version: String, daySinceCutOff: Long) {
        publishedItemGroupVersionJpaRepository.save(
            PublishedItemGroupVersionEntity(version, "", truncateToMicroseconds(cutOffDate.plusDays(daySinceCutOff)), "", archived = true),
        )
        archivedItemGroupJpaRepository.saveAll(listOf(
            ArchivedItemGroupEntity(version, "itemGroup1", "Item Group 1"),
        ))
        archivedItemToItemGroupJpaRepository.saveAll(listOf(
            ArchivedItemToItemGroupEntity(version, "itemCode1", "itemGroup1", "class2", "Item Group 1"),
        ))
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("item-groups/archived/item-to-item-group/version=$version/file.csv")
                .build(),
            RequestBody.fromString(version)
        )
    }

    private fun assertAcquireAndReleaseLock() {
        val sqlCaptor = argumentCaptor<String>()
        verify(dslContext, atLeast(2)).execute(sqlCaptor.capture())
        assertThat(sqlCaptor.firstValue).isEqualTo("UPDATE archive_unused_historical_data_lock SET locked = true WHERE locked = false")
        assertThat(sqlCaptor.lastValue).isEqualTo("UPDATE archive_unused_historical_data_lock SET locked = false WHERE locked = true")
    }

    private fun assertPublishedItemToItemGroupInS3(vararg versions: String) {
        versions.forEach {
            val response = amazonS3.getObject { builder ->
                builder.bucket(applicationProperties.s3Bucket)
                       .key("item-groups/published/item-to-item-group/version=$it/file.csv")
            }
            assertThat(response.readAllBytes().isNotEmpty())
        }
    }

    private fun assertArchivedItemToItemGroupInS3(vararg versions: String) {
        versions.forEach {
            val response = amazonS3.getObject { builder ->
                builder.bucket(applicationProperties.s3Bucket)
                       .key("item-groups/archived/item-to-item-group/version=$it/file.csv")
            }
            assertThat(response.readAllBytes().isNotEmpty())
        }
    }

    private fun assertNumFilesInBucket(numFiles: Int) {
        val listResponse = amazonS3.listObjectsV2(
            ListObjectsV2Request.builder()
                .bucket(applicationProperties.s3Bucket)
                .build()
        )
        assertThat(listResponse.contents().size).isEqualTo(numFiles)
    }

    private fun clearS3BucketContents() {
        try {
            val listRequest = ListObjectsV2Request.builder()
                .bucket(applicationProperties.s3Bucket)
                .build()

            val listResponse = amazonS3.listObjectsV2(listRequest)
            listResponse.contents().forEach { s3Object ->
                val deleteRequest = DeleteObjectRequest.builder()
                    .bucket(applicationProperties.s3Bucket)
                    .key(s3Object.key())
                    .build()
                amazonS3.deleteObject(deleteRequest)
            }
        } catch (e: Exception) {
            // Log but don't fail the test if S3 cleanup fails
            println("Warning: Could not clear S3 bucket contents: ${e.message}")
        }
    }

    /**
     * Truncates LocalDateTime to microsecond precision (6 digits) to match PostgreSQL TIMESTAMP precision.
     * This prevents test failures due to nanosecond precision differences between Java and PostgreSQL.
     */
    private fun truncateToMicroseconds(dateTime: LocalDateTime): LocalDateTime {
        return dateTime.truncatedTo(ChronoUnit.MICROS)
    }
}