package com.prospection.refdata.itemgroups.application.rest

import com.prospection.refdata.AbstractControllerIntegrationTest
import com.prospection.refdata.common.consts.EntityStatus
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.itemgroups.application.rest.dto.ItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.PublishItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.mapper.ItemGroupDtoMapper
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.integration.ItemGroupEntity
import com.prospection.refdata.itemgroups.integration.ItemGroupJpaRepository
import com.prospection.refdata.itemgroups.integration.mapper.ItemGroupEntityMapper
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import java.util.*

class ItemGroupControllerTest : AbstractControllerIntegrationTest() {
    companion object {
        const val BASE_URL = "/api/ref-data-v2/item-groups"
    }

    @Autowired
    private lateinit var itemGroupController: ItemGroupController

    @Autowired
    private lateinit var itemGroupJpaRepository: ItemGroupJpaRepository

    @Autowired
    private lateinit var itemGroupDtoMapper: ItemGroupDtoMapper

    @Autowired
    private lateinit var itemGroupEntityMapper: ItemGroupEntityMapper

    @Autowired
    private lateinit var dateTimePort: DateTimePort

    private lateinit var itemGroup: ItemGroup

    private lateinit var deletedItemGroup: ItemGroup

    @BeforeEach
    fun init() {
        itemGroup = itemGroupJpaRepository.save(ItemGroupEntity(
            businessKey = "business-key",
            lastModifiedBy = "Peter",
            rule = """{"field":"rule ABC"}""",
            lastModifiedAt = dateTimePort.now().minusMinutes(1),
            name = "name",
            uuid = UUID.randomUUID().toString(),
            goal = "goal"
        )).let { itemGroupEntityMapper.toDomain(it) }

        deletedItemGroup = itemGroupJpaRepository.save(ItemGroupEntity(
            deleted = 1,
            name = "deleted-name",
            lastModifiedAt = dateTimePort.now().minusMinutes(1),
            rule = """{"field":"rule ABC"}""",
            lastModifiedBy = "Peter",
            businessKey = "deleted-business-key",
            uuid = UUID.randomUUID().toString(),
        )).let { itemGroupEntityMapper.toDomain(it) }
    }

    @AfterEach
    fun clear() {
        itemGroupJpaRepository.deleteAll()
    }

    @Test
    fun `should return list of active item groups`() {
        val itemGroups = itemGroupController.listItemGroups(false)
        assertEquals(1, itemGroups.size)
        assertEquals(itemGroup, itemGroupDtoMapper.toDomain(itemGroups[0])
        )
    }

    @Test
    fun `should return list of all item groups`() {
        val itemGroups = itemGroupController.listItemGroups(true)
        assertEquals(2, itemGroups.size)
        assertEquals(itemGroup, itemGroupDtoMapper.toDomain(itemGroups[0]))
        assertEquals(deletedItemGroup, itemGroupDtoMapper.toDomain(itemGroups[1])
        )
    }

    @Test
    fun `should create item group fail with invalid body`() {
        val invalidBody = ItemGroupDto(
            name = "",
            rule = ""
        )

        performPost(BASE_URL, invalidBody)
            .andExpect(MockMvcResultMatchers.status().is4xxClientError)
    }

    @Test
    fun `should create item group success`() {
        val itemGroupDto = ItemGroupDto(
            name = "Test   Name", // Intended
            rule = """{"field":"rule"}""",
            goal = "Test goal"
        )

        val response = performPost(BASE_URL, itemGroupDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val itemGroupDtoResponse = parseResponse(response, ItemGroupDto::class.java)
        Assertions.assertNotNull(itemGroupDtoResponse.id)
        assertEquals(itemGroupDto.name, itemGroupDtoResponse.name)
        assertEquals(itemGroupDto.rule, itemGroupDtoResponse.rule)
        assertEquals("test_name", itemGroupDtoResponse.businessKey)
        assertEquals(EntityStatus.ACTIVE.value, itemGroupDtoResponse.deleted)
        assertEquals(itemGroupDto.goal, itemGroupDtoResponse.goal)
    }

    @Test
    fun `should update item group fail with invalid body`() {
        val invalidBody = ItemGroupDto(
            name = "",
            rule = ""
        )

        performPut("$BASE_URL/test-business-key", invalidBody)
            .andExpect(MockMvcResultMatchers.status().is4xxClientError)
    }

    @Test
    fun `should update item group success`() {
        val updateItemGroupDto = ItemGroupDto(
            businessKey = "try-to-override-key",
            name = "updated-name",
            rule = """{"field":"rule 2"}""",
            goal = "updated-goal",
            version = itemGroup.version,
        )

        val response = performPut("$BASE_URL/${itemGroup.id}", updateItemGroupDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val itemGroupDtoResponse = parseResponse(response, ItemGroupDto::class.java)
        assertEquals(itemGroup.businessKey, itemGroupDtoResponse.businessKey)
        assertEquals(updateItemGroupDto.name, itemGroupDtoResponse.name)
        assertEquals(updateItemGroupDto.rule, itemGroupDtoResponse.rule)
        assertEquals(itemGroup.id, itemGroupDtoResponse.id)
        assertEquals(updateItemGroupDto.goal, itemGroupDtoResponse.goal)
        Assertions.assertTrue(itemGroupDtoResponse.lastModifiedAt!!.isAfter(itemGroup.lastModifiedAt))
    }

    @Test
    fun `should not update item group when value is the same`() {
        val updateItemGroupDto = ItemGroupDto(
            businessKey = itemGroup.businessKey,
            name = itemGroup.name,
            rule = itemGroup.rule,
            goal = itemGroup.goal,
            version = itemGroup.version,
        )

        val response = performPut("$BASE_URL/${itemGroup.id}", updateItemGroupDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful)
            .andReturn().response

        val itemGroupDtoResponse = parseResponse(response, ItemGroupDto::class.java)
        assertEquals(itemGroup.lastModifiedAt!!, itemGroupDtoResponse.lastModifiedAt!!)
        assertEquals(updateItemGroupDto.name, itemGroupDtoResponse.name)
        assertEquals(updateItemGroupDto.rule, itemGroupDtoResponse.rule)
    }

    @Test
    fun `should return 404 when get non-existing item group`() {
        performGet("$BASE_URL/not-found-business-key")
            .andExpect(MockMvcResultMatchers.status().isNotFound)
    }

    @Test
    fun `should get item group success`() {
        val response = performGet("$BASE_URL/${itemGroup.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn().response

        val itemGroupDtoResponse = parseResponse(response, ItemGroupDto::class.java)
        assertEquals(itemGroup.businessKey, itemGroupDtoResponse.businessKey)
        assertEquals(itemGroup.name, itemGroupDtoResponse.name)
        assertEquals(itemGroup.rule, itemGroupDtoResponse.rule)
        assertEquals(itemGroup.deleted, itemGroupDtoResponse.deleted)
        assertEquals(itemGroup.version, itemGroupDtoResponse.version)
        assertEquals(itemGroup.lastModifiedAt, itemGroupDtoResponse.lastModifiedAt)
        assertEquals(itemGroup.lastModifiedBy, itemGroupDtoResponse.lastModifiedBy)
        assertEquals(itemGroup.id, itemGroupDtoResponse.id)
        assertEquals(itemGroup.goal, itemGroupDtoResponse.goal)
    }

    @Test
    fun `should return 404 when delete non-existing item group`() {
        performDelete("$BASE_URL/not-found-item-group-id")
            .andExpect(MockMvcResultMatchers.status().isNotFound)
    }

    @Test
    fun `should delete item group success`() {
        val response = performDelete("$BASE_URL/${itemGroup.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn().response

        val itemGroupDtoResponse = parseResponse(response, ItemGroupDto::class.java)
        assertEquals(EntityStatus.DELETED.value, itemGroupDtoResponse.deleted)
    }

    @Test
    fun `should unarchive item group success`() {
        val response = performPost("$BASE_URL/unarchive/${deletedItemGroup.id}")
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn().response

        val itemGroupDtoResponse = parseResponse(response, ItemGroupDto::class.java)
        assertEquals(EntityStatus.ACTIVE.value, itemGroupDtoResponse.deleted)
    }

    @Test
    fun `should return 400 when publish item groups without comment`() {
        val publishItemGroupDto = PublishItemGroupDto(
            ""
        )
        performPost("$BASE_URL/publish", publishItemGroupDto)
            .andExpect(MockMvcResultMatchers.status().is4xxClientError)
    }

    @Test
    fun `should publish item groups success`() {
        val publishItemGroupDto = PublishItemGroupDto("test")
        performPost("$BASE_URL/publish", publishItemGroupDto)
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn()
    }
}