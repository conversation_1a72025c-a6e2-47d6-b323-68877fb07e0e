package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.common.integration.S3ImportExportHelper
import com.prospection.refdata.common.integration.S3SparkImportExportHelper
import com.prospection.refdata.itemgroups.integration.datasetrow.ItemGroupToTopicRow
import com.prospection.refdata.itemgroups.integration.mapper.ItemToItemGroupCsvRowMapper
import com.prospection.refdata.itemgroups.integration.mapper.PublishedItemGroupEntityMapper
import com.prospection.refdata.itemgroups.integration.mapper.PublishedItemToItemGroupEntityMapper
import com.prospection.refdata.topic.domain.EnrichedTopic
import com.prospection.refdata.topic.domain.Topic
import com.prospection.refdata.topic.domain.Workflow
import org.apache.spark.sql.Encoders
import org.jooq.DSLContext
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime


class PublishItemGroupAdapterTest {
    private val importExportPort: S3ImportExportHelper = mock()
    private val sparkImportExportPort: S3SparkImportExportHelper = mock()
    private val itemToItemGroupCsvRowMapper: ItemToItemGroupCsvRowMapper = mock()
    private val dslContext: DSLContext = mock()
    private val publishedItemToItemGroupJpaRepository: PublishedItemToItemGroupJpaRepository = mock()
    private val publishedItemToItemGroupEntityMapper: PublishedItemToItemGroupEntityMapper = mock()
    private val publishedItemGroupJpaRepository: PublishedItemGroupJpaRepository = mock()
    private val publishedItemGroupEntityMapper: PublishedItemGroupEntityMapper = mock()
    private val archivedItemGroupJpaRepository: ArchivedItemGroupJpaRepository = mock()
    private val archivedItemToItemGroupJpaRepository: ArchivedItemToItemGroupJpaRepository = mock()

    private val publishItemGroupAdapter = PublishItemGroupAdapter(
        importExportPort,
        sparkImportExportPort,
        itemToItemGroupCsvRowMapper,
        dslContext,
        publishedItemToItemGroupJpaRepository,
        publishedItemToItemGroupEntityMapper,
        publishedItemGroupJpaRepository,
        publishedItemGroupEntityMapper,
        archivedItemGroupJpaRepository,
        archivedItemToItemGroupJpaRepository,
    )

    @BeforeEach
    fun setup() {
        reset(
            importExportPort,
            itemToItemGroupCsvRowMapper,
            dslContext,
            publishedItemToItemGroupJpaRepository,
            publishedItemToItemGroupEntityMapper,
            publishedItemGroupJpaRepository,
            publishedItemGroupEntityMapper,
        )
    }

    @Test
    fun `should publish items to item groups`() {
        // given
        val sydneyDateTime = ZonedDateTime.of(
            LocalDateTime.of(2023, 1, 1, 12, 0), ZoneId.of("Australia/Sydney")
        ).toInstant()

        val enrichedTopics = listOf(
            EnrichedTopic(
                topic = Topic(
                    id = 1,
                    name = "Topic 1",
                    companyId = "c1",
                    conditionName = "Condition 1",
                    subscriptionId = "s1",
                    bridge = false,
                    adhoc = false,
                    therapyAreaName = "Therapy Area 1",
                    subscriptionTherapyAreaId = "ta1",
                    standalone = false,
                    relatedWorkflows = listOf(
                        Workflow(
                            id = 100,
                            status = "GENERATED",
                            relatedItemGroups = listOf("itemGroup1", "itemGroup2"),
                            lastModifiedBy = "user1",
                            lastModifiedDate = sydneyDateTime,
                            generatedTime = sydneyDateTime,
                            country = "AU"
                        ),
                        Workflow(
                            id = 200,
                            status = "PUBLISHED",
                            relatedItemGroups = listOf("itemGroup2", "itemGroup3"),
                            lastModifiedBy = "user2",
                            lastModifiedDate = sydneyDateTime,
                            generatedTime = null,
                            country = "JP"
                        )
                    )
                ),
                companyName = "Company 1",
                subscriptionName = "Subscription 1",
                subscriptionStatus = "active"
            ),
            EnrichedTopic(
                topic = Topic(
                    id = 2,
                    name = "Topic 2",
                    companyId = "c2",
                    conditionName = "Condition 2",
                    subscriptionId = "s2",
                    bridge = true,
                    adhoc = true,
                    therapyAreaName = "Therapy Area 2",
                    subscriptionTherapyAreaId = "ta2",
                    standalone = true,
                    relatedWorkflows = listOf(
                        Workflow(
                            id = 300,
                            status = "GENERATED",
                            relatedItemGroups = listOf("itemGroup3", "itemGroup4"),
                            lastModifiedBy = "user3",
                            lastModifiedDate = sydneyDateTime,
                            generatedTime = null,
                            country = "US"
                        )
                    )
                ),
                companyName = "Company 2",
                subscriptionName = "Subscription 2",
                subscriptionStatus = "expired"
            ),
        )

        val itemGroupKeysToNames = mapOf(
            "itemGroup1" to "Item Group 1",
            "itemGroup2" to "Item Group 2",
            "itemGroup3" to "Item Group 3",
            "itemGroup4" to "Item Group 4"
        )

        // when
        publishItemGroupAdapter.publishItemGroupsToTopics(
            "v1",
            enrichedTopics,
            itemGroupKeysToNames
        )

        verify(sparkImportExportPort).writeParquet(
            eq("item-groups/published/item-groups-to-topics/version=v1"),
            eq(listOf(
                ItemGroupToTopicRow(
                    itemGroupKey = "itemGroup1",
                    itemGroupName = "Item Group 1",
                    topicId = "1",
                    topicName = "Topic 1",
                    company = "Company 1",
                    condition = "Condition 1",
                    therapyArea = "Therapy Area 1",
                    country = "AU",
                    subscriptionId = "s1",
                    subscriptionName = "Subscription 1",
                    subscriptionStatus = "active",
                    workflowStatus = "GENERATED",
                    lastModifiedAt = "2023-01-01 12:00:00",
                    lastModifiedBy = "user1",
                    generatedAt = "2023-01-01 12:00:00"
                ),
                ItemGroupToTopicRow(
                    itemGroupKey = "itemGroup2",
                    itemGroupName = "Item Group 2",
                    topicId = "1",
                    topicName = "Topic 1",
                    company = "Company 1",
                    condition = "Condition 1",
                    therapyArea = "Therapy Area 1",
                    country = "AU",
                    subscriptionId = "s1",
                    subscriptionName = "Subscription 1",
                    subscriptionStatus = "active",
                    workflowStatus = "GENERATED",
                    lastModifiedAt = "2023-01-01 12:00:00",
                    lastModifiedBy = "user1",
                    generatedAt = "2023-01-01 12:00:00"
                ),
                ItemGroupToTopicRow(
                    itemGroupKey = "itemGroup2",
                    itemGroupName = "Item Group 2",
                    topicId = "1",
                    topicName = "Topic 1",
                    company = "Company 1",
                    condition = "Condition 1",
                    therapyArea = "Therapy Area 1",
                    country = "JP",
                    subscriptionId = "s1",
                    subscriptionName = "Subscription 1",
                    subscriptionStatus = "active",
                    workflowStatus = "PUBLISHED",
                    lastModifiedAt = "2023-01-01 12:00:00",
                    lastModifiedBy = "user2",
                    generatedAt = null
                ),
                ItemGroupToTopicRow(
                    itemGroupKey = "itemGroup3",
                    itemGroupName = "Item Group 3",
                    topicId = "1",
                    topicName = "Topic 1",
                    company = "Company 1",
                    condition = "Condition 1",
                    therapyArea = "Therapy Area 1",
                    country = "JP",
                    subscriptionId = "s1",
                    subscriptionName = "Subscription 1",
                    subscriptionStatus = "active",
                    workflowStatus = "PUBLISHED",
                    lastModifiedAt = "2023-01-01 12:00:00",
                    lastModifiedBy = "user2",
                    generatedAt = null
                ),
                ItemGroupToTopicRow(
                    itemGroupKey = "itemGroup3",
                    itemGroupName = "Item Group 3",
                    topicId = "2",
                    topicName = "Topic 2",
                    company = "Company 2",
                    condition = "Condition 2",
                    therapyArea = "Therapy Area 2",
                    country = "US",
                    subscriptionId = "s2",
                    subscriptionName = "Subscription 2",
                    subscriptionStatus = "expired",
                    workflowStatus = "GENERATED",
                    lastModifiedAt = "2023-01-01 12:00:00",
                    lastModifiedBy = "user3",
                    generatedAt = null
                ),
                ItemGroupToTopicRow(
                    itemGroupKey = "itemGroup4",
                    itemGroupName = "Item Group 4",
                    topicId = "2",
                    topicName = "Topic 2",
                    company = "Company 2",
                    condition = "Condition 2",
                    therapyArea = "Therapy Area 2",
                    country = "US",
                    subscriptionId = "s2",
                    subscriptionName = "Subscription 2",
                    subscriptionStatus = "expired",
                    workflowStatus = "GENERATED",
                    lastModifiedAt = "2023-01-01 12:00:00",
                    lastModifiedBy = "user3",
                    generatedAt = null
                )
            )),
            eq(Encoders.bean(ItemGroupToTopicRow::class.java))
        )
    }

    @Test
    fun `archivePublishedItemGroups should copy published item group and delete them`() {
        // when
        publishItemGroupAdapter.archivePublishedItemGroups(listOf("v1", "v2"))

        // then
        verify(archivedItemGroupJpaRepository, times(1)).saveFromPublishedVersion(listOf("v1", "v2"))
        verify(publishedItemGroupJpaRepository, times(1)).deleteByVersionIn(listOf("v1", "v2"))
        verifyNoMoreInteractions(
            dslContext,
            publishedItemToItemGroupJpaRepository,
            publishedItemGroupJpaRepository,
            archivedItemGroupJpaRepository,
            archivedItemToItemGroupJpaRepository
        )
    }

    @Test
    fun `archivePublishedItemsToItemGroups should copy published item to item group and delete them`() {
        // when
        publishItemGroupAdapter.archivePublishedItemsToItemGroups(listOf("v1", "v2"))

        // then
        verify(archivedItemToItemGroupJpaRepository, times(1)).saveFromPublishedVersion(listOf("v1", "v2"))
        verify(publishedItemToItemGroupJpaRepository, times(1)).deleteByVersionIn(listOf("v1", "v2"))
        verifyNoMoreInteractions(
            dslContext,
            publishedItemToItemGroupJpaRepository,
            publishedItemGroupJpaRepository,
            archivedItemGroupJpaRepository,
            archivedItemToItemGroupJpaRepository
        )
    }

    @Test
    fun `finalizeArchiving should run VACUUM ANALYZE`() {
        // when
        publishItemGroupAdapter.finalizeArchiving()

        // then
        verify(dslContext, times(1)).execute("VACUUM ANALYZE published_item_group")
        verify(dslContext, times(1)).execute("VACUUM ANALYZE published_item_to_item_group")
        verifyNoMoreInteractions(
            dslContext,
            publishedItemToItemGroupJpaRepository,
            publishedItemGroupJpaRepository,
            archivedItemGroupJpaRepository,
            archivedItemToItemGroupJpaRepository
        )
    }
}