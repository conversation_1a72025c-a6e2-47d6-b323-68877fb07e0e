package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.domain.InvalidModelException
import com.prospection.domain.ModelDoesNotExistException
import com.prospection.refdata.common.consts.EntityStatus
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.integration.mapper.ItemGroupEntityMapper
import com.prospection.refdata.mock.MockDateTime
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.*

class ItemGroupJpaAdapterTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var itemGroupJpaRepository: ItemGroupJpaRepository

    @Autowired
    private lateinit var mockDateTime: MockDateTime

    @Autowired
    private lateinit var itemGroupJpaAdapter: ItemGroupJpaAdapter

    @Autowired
    private lateinit var itemGroupEntityMapper: ItemGroupEntityMapper

    private lateinit var itemGroupEntity: ItemGroupEntity

    @BeforeEach
    fun init() {
        itemGroupEntity = ItemGroupEntity(
            name = "test-name",
            businessKey = "test-name",
            rule = "test-rule",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            uuid = UUID.randomUUID().toString()
        )
        itemGroupJpaRepository.save(itemGroupEntity)
    }

    @AfterEach
    fun clear() {
        itemGroupJpaRepository.deleteAll()
    }

    @Test
    fun `should throw error if business key is duplicated when creating`() {
        val duplicateKeyItemGroup = ItemGroup(
            name = "test-name-2",
            businessKey = itemGroupEntity.businessKey,
            rule = """{"field":"rule 2"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            id = UUID.randomUUID().toString()
        )

        val exception = Assertions.assertThrows(InvalidModelException::class.java) {
            itemGroupJpaAdapter.createItemGroup(duplicateKeyItemGroup)
        }

        Assertions.assertEquals("Sorry, this business key has already been taken by a group.", exception.message)
    }

    @Test
    fun `should throw error if name is duplicated when creating`() {
        val duplicateKeyItemGroup = ItemGroup(
            name = itemGroupEntity.name.uppercase(),
            businessKey = "test-business-key-2",
            rule = """{"field":"rule 2"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            id = UUID.randomUUID().toString()
        )

        val exception = Assertions.assertThrows(InvalidModelException::class.java) {
            itemGroupJpaAdapter.createItemGroup(duplicateKeyItemGroup)
        }

        Assertions.assertEquals("Sorry, this name has already been taken by a group.", exception.message)
    }

    @Test
    fun `should throw error when update non-existing item group`() {
        val nonExistingItemGroup = ItemGroup(
            name = "test-name",
            businessKey = "test-key",
            rule = """{"field":"rule"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            id = "not-found-id"
        )

        Assertions.assertThrows(ModelDoesNotExistException::class.java) {
            itemGroupJpaAdapter.updateItemGroup(nonExistingItemGroup)
        }
    }

    @Test
    fun `should throw error if name is duplicated when updating`() {
        val itemGroupEntity2 = ItemGroupEntity(
            name = "test-name-2",
            businessKey = "test-key-2",
            rule = """{"field":"rule 2"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            uuid = UUID.randomUUID().toString()
        )
        itemGroupJpaRepository.save(itemGroupEntity2)

        val duplicateKeyItemGroup = ItemGroup(
            name = itemGroupEntity2.name.uppercase(),
            businessKey = itemGroupEntity.businessKey,
            rule = """{"field":"rule"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            id = itemGroupEntity.uuid
        )

        val exception = Assertions.assertThrows(InvalidModelException::class.java) {
            itemGroupJpaAdapter.updateItemGroup(duplicateKeyItemGroup)
        }

        Assertions.assertEquals("Sorry, this name has already been taken by a group.", exception.message)
    }

    @Test
    fun `should allow updating with the same old name`() {
        val duplicateKeyItemGroup = ItemGroup(
            name = itemGroupEntity.name,
            businessKey = itemGroupEntity.businessKey,
            rule = """{"field":"rule"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            id = itemGroupEntity.uuid
        )
        itemGroupJpaAdapter.updateItemGroup(duplicateKeyItemGroup)
    }

    @Test
    fun `should throw exception when optimistic lock fails`() {
        val itemGroup = ItemGroup(
            name = "test-name",
            businessKey = itemGroupEntity.businessKey,
            rule = """{"field":"rule"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            version = itemGroupEntity.version + 1,
            id = itemGroupEntity.uuid
        )

        val exception = Assertions.assertThrows(InvalidModelException::class.java) {
            itemGroupJpaAdapter.updateItemGroup(itemGroup)
        }

        Assertions.assertEquals(
            "This group has been updated by others. You now need to refresh the page and get the latest version to make any further changes. Before doing so, make a note of your changes - You work will be lost when refreshing the page!",
            exception.message
        )
    }

    @Test
    fun `test partial unique index on business key col`() {
        itemGroupEntity.deleted = 1
        itemGroupJpaRepository.save(itemGroupEntity)

        val itemGroup = ItemGroup(
            name = itemGroupEntity.name,
            businessKey = itemGroupEntity.businessKey,
            rule = """{"field":"rule"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            id = UUID.randomUUID().toString(),
        )

        itemGroupJpaAdapter.createItemGroup(itemGroup)
    }

    @Test
    fun `should throw error if businessKey is duplicated when un-archive item group`() {
        val deletedItemGroupEntity = ItemGroupEntity(
            name = "test-name-2",
            businessKey = itemGroupEntity.businessKey,
            rule = """{"field":"rule 2"}""",
            lastModifiedAt = mockDateTime.now(),
            lastModifiedBy = "Peter",
            uuid = UUID.randomUUID().toString(),
            deleted = EntityStatus.DELETED.value
        )
        itemGroupJpaRepository.save(deletedItemGroupEntity)

        val exception = Assertions.assertThrows(InvalidModelException::class.java) {
            itemGroupJpaAdapter.unarchiveItemGroup(deletedItemGroupEntity.uuid, deletedItemGroupEntity.lastModifiedBy, deletedItemGroupEntity.lastModifiedAt)
        }

        Assertions.assertEquals(ItemGroupJpaAdapter.DUPLICATE_BUSINESS_KEY_MESSAGE, exception.message)
    }

}