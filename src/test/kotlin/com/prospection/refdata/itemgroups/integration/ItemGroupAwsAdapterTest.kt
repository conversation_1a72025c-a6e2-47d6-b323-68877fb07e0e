package com.prospection.refdata.itemgroups.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.FileNameAdapter
import com.prospection.refdata.common.integration.S3GeneratePublicUrlAdapter
import com.prospection.refdata.common.integration.S3ImportExportHelper
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.common.integration.S3SparkImportExportHelper
import com.prospection.refdata.config.S3Path.ItemGroups
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.extractor.Extractors
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import java.net.URI
import java.net.URL
import java.nio.file.Paths
import java.time.LocalDateTime
import java.util.zip.ZipInputStream
import kotlin.io.path.name

internal class ItemGroupAwsAdapterTest : AbstractIntegrationTest() {
    companion object {
        private const val VERSION1 = "20211115T045833046Z"
        private const val VERSION2 = "20211116T045833046Z"
    }

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var s3Presigner: S3Presigner

    private lateinit var adapter: ItemGroupAwsAdapter
    private lateinit var mockImportExportHelper: ImportExportHelper<ResponseInputStream<GetObjectResponse>>
    private lateinit var mockSparkImportExportHelper: SparkImportExportHelper
    private lateinit var mockFileNameAdapter: FileNameAdapter

    @BeforeEach
    override fun setUp() {
        super.setUp()
        mockFileNameAdapter = Mockito.spy(FileNameAdapter())

        mockImportExportHelper = Mockito.spy(
            S3ImportExportHelper(
                applicationProperties,
                amazonS3,
                spark,
                objectMapper
            )
        )
        mockSparkImportExportHelper = Mockito.spy(
            S3SparkImportExportHelper(
                applicationProperties.s3Bucket,
                amazonS3,
                spark
            )
        )
        adapter = ItemGroupAwsAdapter(
            mockImportExportHelper,
            mockSparkImportExportHelper,
            S3GeneratePublicUrlAdapter(applicationProperties, s3Presigner),
            objectMapper,
            amazonS3,
            applicationProperties,
            mockFileNameAdapter
        )
    }

    data class TestRow(
        val source_code: String,
        val source_name: String?,
        val brand_type: List<String>,
        val coding_system: String
    )

    @Test
    fun `generate change summary should use the latest published version`() {
        // mock classification
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Draft.ITEM_TO_ITEM_GROUP}/test")
                .build(),
            RequestBody.fromString("")
        )

        // mock version
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(VERSION1)}/test")
                .build(),
            RequestBody.fromString("")
        )
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(VERSION2)}/test")
                .build(),
            RequestBody.fromString("")
        )

        // previous change summary is already there
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Draft.CHANGE_SUMMARY}/change-summary-${VERSION1}.csv")
                .build(),
            RequestBody.fromString("")
        )

        // mock draft and published data
        doReturn(spark.emptyDataFrame()).`when`(mockSparkImportExportHelper).readCsv(any())

        assertNotNull(adapter.generateChangeSummary(VERSION2, LocalDateTime.now()))

        // check zip file content for newly published version
        val response = amazonS3.getObject { builder ->
            builder.bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Draft.CHANGE_SUMMARY}/change-summary.zip")
        }
        ZipInputStream(response).use { zipInputStream ->
            val firstEntry = zipInputStream.nextEntry
            assertEquals("item-groups.csv", firstEntry?.name)
            zipInputStream.close()
        }
    }

    @Test
    fun `should return non null result when generate the mapping result`() {
        doReturn("file.csv").whenever(mockImportExportHelper).findFirst(any(), eq(".csv"))
        assertNotNull(adapter.generateMappingResult(LocalDateTime.now()))
    }

    @Test
    fun `Should create a zip filename with an item group name`() {
        val now = LocalDateTime.now()
        val dataFrameAlpha = createDataFrame(
            listOf(
                TestRow("item_1", "Item 1", listOf("MM 1"), "Alpha Item")
            )
        )
        writeCsv("${ItemGroups.Preview.TEMP}/Alpha Item/Alpha Item.csv", dataFrameAlpha)

        doReturn("whatever filename_2022-06-10 8888").whenever(mockFileNameAdapter)
            .createFileNameWithDateTime(
                eq("Group 1"),
                eq(now)
            )

        val zipFileUrl = (adapter.generateZipFileAndGetUrl("Group 1", now))

        assertEquals(
            "whatever filename_2022-06-10 8888.zip",
            getFileNameFromUrl(zipFileUrl)
        )

        verify(mockFileNameAdapter, times(1)).createFileNameWithDateTime(eq("Group 1"), eq(now))
    }

    @Test
    fun `Should create a excel filename with an item group name`() {
        val now = LocalDateTime.now()
        val dataFrameAlpha = createDataFrame(
            listOf(
                TestRow("item_1", "Item 1", listOf("MM 1"), "Alpha Item")
            )
        )
        writeCsv("${ItemGroups.Preview.TEMP}/Alpha Item/Alpha Item.csv", dataFrameAlpha)

        doReturn("whatever filename_2022-06-10 8888").whenever(mockFileNameAdapter)
            .createFileNameWithDateTime(
                eq("Group 1"),
                eq(now)
            )

        val excelFileUrl = (adapter.generateExcelAndGetUrl(setOf("Alpha Item"), "Group 1", now))

        assertEquals(
            "whatever filename_2022-06-10 8888.xlsx",
            getFileNameFromUrl(excelFileUrl)
        )
        verify(mockFileNameAdapter, times(1)).createFileNameWithDateTime(eq("Group 1"), eq(now))
    }

    @Test
    fun `should generate preview files for an item group`() {
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Preview.TEMP}/A/test.csv")
                .build(),
            RequestBody.fromString("")
        )

        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.Preview.TEMP}/B/test.csv")
                .build(),
            RequestBody.fromString("")
        )

        val zipFileUrl = (adapter.generateZipFileAndGetUrl("Group 1", LocalDateTime.now()))

        // check zip file content for newly published version
        val response = amazonS3.getObject { builder ->
            builder.bucket(applicationProperties.s3Bucket)
                .key("${ItemGroups.PREVIEW}/${getFileNameFromUrl(zipFileUrl)}")
        }
        ZipInputStream(response).use { zipInputStream ->
            val firstEntry = zipInputStream.nextEntry
            assertEquals("A.csv", firstEntry?.name)

            val secondEntry = zipInputStream.nextEntry
            assertEquals("B.csv", secondEntry?.name)

            zipInputStream.close()
        }
    }

    @Test
    fun `should generate excel by classification`() {
        val dataFrameAlpha = createDataFrame(
            listOf(
                TestRow("item_1", "Item 1", listOf("MM 1"), "Alpha Item")
            )
        )
        writeCsv("${ItemGroups.Preview.TEMP}/Alpha Item/Alpha Item.csv", dataFrameAlpha)


        val dataFrameBeta = createDataFrame(generateTestRows("Beta Item", 4998))
        writeCsv("${ItemGroups.Preview.TEMP}/Beta Item/Beta Item.csv", dataFrameBeta)

        val excelFileUrl =
            adapter.generateExcelAndGetUrl(setOf("Alpha Item", "Beta Item"), "Item Group Name", LocalDateTime.now())
        assertNotNull(excelFileUrl)

        assertThat(getSheetNames(excelFileUrl)).hasSize(2)
            .containsExactly("Alpha Item", "Beta Item")

        assertThat(getSheetContent(excelFileUrl, "Alpha Item")).hasSize(2)
            .extracting(Extractors.toStringMethod())
            .containsExactly(
                "brand_type,coding_system,source_code,source_name",
                "[MM 1],Alpha Item,item_1,Item 1"
            )

        assertThat(getSheetContent(excelFileUrl, "Beta Item")).hasSize(4999)
    }

    private fun getSheetNames(path: String): List<String> {
        val url = URL(path)
        val wb = XSSFWorkbook(url.openConnection().getInputStream())
        return wb.sheetIterator().asSequence().map { sheet -> sheet.sheetName }.toList()
    }

    private fun getSheetContent(path: String, sheetName: String): List<String> {
        val url = URL(path)
        val wb = XSSFWorkbook(url.openConnection().getInputStream())
        return wb.getSheet(sheetName).map { row -> row.joinToString(",") { cell -> cell.stringCellValue } }
    }

    private fun createDataFrame(rows: List<TestRow>): Dataset<Row> {
        return spark.createDataFrame(rows, TestRow::class.java)
    }

    private fun generateTestRows(itemName: String, numberOfRow: Int): List<TestRow> {
        val rows = ArrayList<TestRow>()
        for (i in 1..numberOfRow) {
            rows.add(TestRow("item_${i}", "source_name_${i}", listOf("MM_${i}"), itemName))
        }
        return rows
    }

    private fun getFileNameFromUrl(url: String?): String? {
        return url?.let { Paths.get(URI(url).path).fileName?.name }
    }
}
