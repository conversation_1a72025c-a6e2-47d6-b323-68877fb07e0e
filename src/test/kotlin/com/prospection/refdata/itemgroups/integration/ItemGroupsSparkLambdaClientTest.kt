package com.prospection.refdata.itemgroups.integration

import com.fasterxml.jackson.core.type.TypeReference
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.integration.AwsLambdaClient
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupPort
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import com.prospection.refdata.items.domain.PublishItemVersionPort
import com.prospection.refdata.items.domain.PublishedItemVersion
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

private const val TEST_FUNCTION_NAME = "testFunctionName"
private const val PUBLISHED_ITEMS_VERSION = "testVersion"

internal class ItemGroupsSparkLambdaClientTest {
    companion object {
        private val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"coding_system","value":"cs_A"}""")
        private val codingSystemToClassifications = mapOf(
            "cs_A" to listOf(
                CodingSystemToClassification(
                    codingSystem = "cs_A",
                    classification = "classification_A",
                    codingSystemColumnToExport = "source_code"
                )
            )
        )
        private val expectedPreviewResult = listOf(
            ItemGroupPreviewResult(
                "cs_A", 2, listOf("source_code"), listOf(
                    mapOf("source_code" to "code1"),
                    mapOf("source_code" to "code2")
                )
            )
        )
    }

    private val mockAwsLambdaClient: AwsLambdaClient = mock()
    private val mockItemGroupPort: ItemGroupPort = mock()
    private val mockPublishItemVersionPort: PublishItemVersionPort = mock()
    private val mockCodingSystemPort: CodingSystemPort = mock()

    private val itemGroupsSparkLambdaClient = ItemGroupSparkLambdaClient(
        TEST_FUNCTION_NAME,
        mockAwsLambdaClient,
        mockItemGroupPort,
        mockPublishItemVersionPort,
        mockCodingSystemPort,
    )

    @AfterEach
    fun tearDown() {
        reset(mockAwsLambdaClient, mockItemGroupPort, mockPublishItemVersionPort, mockCodingSystemPort)
    }

    @Test
    fun `getItemGroupPreview should call lambda with correct function and payload`() {
        // setup
        whenever(mockAwsLambdaClient.invoke(any(), any<ItemGroupPreviewRequestPayload>(), any<TypeReference<Any>>()))
            .thenReturn(expectedPreviewResult)

        // test
        val result = itemGroupsSparkLambdaClient.getItemGroupPreview(
            itemGroup,
            codingSystemToClassifications,
            PUBLISHED_ITEMS_VERSION
        )

        // verify
        assertEquals(expectedPreviewResult, result)

        val expectedPayload = ItemGroupPreviewRequestPayload(
            itemGroup,
            codingSystemToClassifications,
            PUBLISHED_ITEMS_VERSION
        )

        val functionCaptor = argumentCaptor<String>()
        val payloadCaptor = argumentCaptor<ItemGroupPreviewRequestPayload>()
        verify(mockAwsLambdaClient, times(1))
            .invoke(functionCaptor.capture(), payloadCaptor.capture(), any<TypeReference<Any>>())
        assertEquals(TEST_FUNCTION_NAME, functionCaptor.lastValue)
        assertEquals(expectedPayload, payloadCaptor.lastValue)

        verifyNoInteractions(mockItemGroupPort)
        verifyNoInteractions(mockPublishItemVersionPort)
        verifyNoInteractions(mockCodingSystemPort)
    }

    @Test
    fun `onApplicationEvent should call lambda with correct function and payload using first item group`() {
        // setup
        whenever(mockItemGroupPort.listActiveItemGroups()).thenReturn(listOf(itemGroup))
        whenever(mockPublishItemVersionPort.getLatestPublishedItemVersion())
            .thenReturn(PublishedItemVersion(PUBLISHED_ITEMS_VERSION, LocalDateTime.now(), ""))
        whenever(mockCodingSystemPort.findAllCodingSystemToClassifications())
            .thenReturn(codingSystemToClassifications)
        whenever(mockAwsLambdaClient.invoke(any(), any<ItemGroupPreviewRequestPayload>(), any<TypeReference<Any>>()))
            .thenReturn(expectedPreviewResult)

        // test
        itemGroupsSparkLambdaClient.onApplicationEvent(mock())

        // verify
        val expectedPayload = ItemGroupPreviewRequestPayload(
            itemGroup = itemGroup,
            codingSystemToClassifications = codingSystemToClassifications,
            publishedItemsVersion = PUBLISHED_ITEMS_VERSION
        )
        val functionCaptor = argumentCaptor<String>()
        val payloadCaptor = argumentCaptor<ItemGroupPreviewRequestPayload>()
        verify(mockAwsLambdaClient, times(1))
            .invoke(functionCaptor.capture(), payloadCaptor.capture(), any<TypeReference<Any>>())
        assertEquals(TEST_FUNCTION_NAME, functionCaptor.lastValue)
        assertEquals(expectedPayload, payloadCaptor.lastValue)

        verify(mockItemGroupPort, times(1)).listActiveItemGroups()
        verify(mockPublishItemVersionPort, times(1)).getLatestPublishedItemVersion()
        verify(mockCodingSystemPort, times(1)).findAllCodingSystemToClassifications()
    }

    @Test
    fun `onApplicationEvent should do nothing if there is no item group`() {
        // setup
        whenever(mockItemGroupPort.listActiveItemGroups()).thenReturn(listOf())

        // test
        itemGroupsSparkLambdaClient.onApplicationEvent(mock())

        // verify
        verify(mockItemGroupPort, times(1)).listActiveItemGroups()
        verifyNoInteractions(mockPublishItemVersionPort)
        verifyNoInteractions(mockCodingSystemPort)
        verifyNoInteractions(mockAwsLambdaClient)
    }

    @Test
    fun `onApplicationEvent should not throw if there is any exception`() {
        // setup
        whenever(mockItemGroupPort.listActiveItemGroups()).thenThrow(RuntimeException("test exception"))

        // test
        itemGroupsSparkLambdaClient.onApplicationEvent(mock())

        // verify
        verify(mockItemGroupPort, times(1)).listActiveItemGroups()
        verifyNoInteractions(mockPublishItemVersionPort)
        verifyNoInteractions(mockCodingSystemPort)
        verifyNoInteractions(mockAwsLambdaClient)
    }
}