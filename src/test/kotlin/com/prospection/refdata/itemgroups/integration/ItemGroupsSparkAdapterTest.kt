package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.featuretoggle.domain.ENABLE_ITEM_GROUP_PREVIEW_USING_LAMBDA
import com.prospection.refdata.featuretoggle.domain.FeatureTogglePort
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

private const val PUBLISHED_ITEMS_VERSION = "testVersion"

internal class ItemGroupsSparkAdapterTest {
    companion object {
        private val itemGroup = ItemGroup(name = "whatever", rule = """{"field":"coding_system","value":"cs_A"}""")
        private val codingSystemToClassifications = mapOf(
            "cs_A" to listOf(
                CodingSystemToClassification(
                    codingSystem = "cs_A",
                    classification = "classification_A",
                    codingSystemColumnToExport = "source_code"
                )
            )
        )
        private val expectedPreviewResult = listOf(
            ItemGroupPreviewResult("cs_A", 2, listOf("source_code"), listOf(
                mapOf("source_code" to "code1"),
                mapOf("source_code" to "code2")
            ))
        )
    }

    private val mockItemGroupsSparkImpl: ItemGroupsSparkImpl = mock()
    private val mockItemGroupsSparkLambdaClient: ItemGroupSparkLambdaClient = mock()
    private val mockFeatureTogglePort: FeatureTogglePort = mock()

    private val itemGroupsSparkAdapter = ItemGroupsSparkAdapter(
        mockItemGroupsSparkImpl,
        mockItemGroupsSparkLambdaClient,
        mockFeatureTogglePort,
    )

    @AfterEach
    fun tearDown() {
        reset(mockItemGroupsSparkImpl, mockItemGroupsSparkLambdaClient, mockFeatureTogglePort,)
    }

    @Test
    fun `getItemGroupPreview should call lambda with correct function and payload`() {
        // setup
        whenever(mockFeatureTogglePort.isEnable(ENABLE_ITEM_GROUP_PREVIEW_USING_LAMBDA)).thenReturn(true)
        whenever(mockItemGroupsSparkLambdaClient.getItemGroupPreview(any(), any(), any()))
            .thenReturn(expectedPreviewResult)

        // test
        val result = itemGroupsSparkAdapter.getItemGroupPreview(
            itemGroup,
            codingSystemToClassifications,
            PUBLISHED_ITEMS_VERSION
        )

        // verify
        assertEquals(expectedPreviewResult, result)

        val itemGroupCaptor = argumentCaptor<ItemGroup>()
        val codingSystemToClassificationsCaptor = argumentCaptor<Map<String, List<CodingSystemToClassification>>>()
        val publishedItemsVersionCaptor = argumentCaptor<String>()
        verify(mockItemGroupsSparkLambdaClient, times(1)).getItemGroupPreview(
            itemGroupCaptor.capture(),
            codingSystemToClassificationsCaptor.capture(),
            publishedItemsVersionCaptor.capture()
        )
        assertEquals(itemGroup, itemGroupCaptor.lastValue)
        assertEquals(codingSystemToClassifications, codingSystemToClassificationsCaptor.lastValue)
        assertEquals(PUBLISHED_ITEMS_VERSION, publishedItemsVersionCaptor.lastValue)

        verify(mockFeatureTogglePort, times(1)).isEnable(any())
        verifyNoInteractions(mockItemGroupsSparkImpl)
    }

    @Test
    fun `getItemGroupPreview should call local spark when lambda feature toggle is disabled`() {
        // setup
        whenever(mockFeatureTogglePort.isEnable(ENABLE_ITEM_GROUP_PREVIEW_USING_LAMBDA)).thenReturn(false)
        whenever(mockItemGroupsSparkImpl.getItemGroupPreview(any(), any(), any()))
            .thenReturn(expectedPreviewResult)

        // test
        val result = itemGroupsSparkAdapter.getItemGroupPreview(
            itemGroup,
            codingSystemToClassifications,
            PUBLISHED_ITEMS_VERSION
        )

        // verify
        assertEquals(expectedPreviewResult, result)

        val itemGroupCaptor = argumentCaptor<ItemGroup>()
        val codingSystemToClassificationsCaptor = argumentCaptor<Map<String, List<CodingSystemToClassification>>>()
        val publishedItemsVersionCaptor = argumentCaptor<String>()
        verify(mockItemGroupsSparkImpl, times(1)).getItemGroupPreview(
            itemGroupCaptor.capture(),
            codingSystemToClassificationsCaptor.capture(),
            publishedItemsVersionCaptor.capture()
        )
        assertEquals(itemGroup, itemGroupCaptor.lastValue)
        assertEquals(codingSystemToClassifications, codingSystemToClassificationsCaptor.lastValue)
        assertEquals(PUBLISHED_ITEMS_VERSION, publishedItemsVersionCaptor.lastValue)

        verify(mockFeatureTogglePort, times(1)).isEnable(any())
        verifyNoInteractions(mockItemGroupsSparkLambdaClient)
    }

    @Test
    fun `getItemGroupPreview should call local spark when lambda is null`() {
        // setup
        val itemGroupsSparkAdapter = ItemGroupsSparkAdapter(mockItemGroupsSparkImpl, null, mockFeatureTogglePort)
        whenever(mockFeatureTogglePort.isEnable(ENABLE_ITEM_GROUP_PREVIEW_USING_LAMBDA)).thenReturn(false)
        whenever(mockItemGroupsSparkImpl.getItemGroupPreview(any(), any(), any()))
            .thenReturn(expectedPreviewResult)

        // test
        val result = itemGroupsSparkAdapter.getItemGroupPreview(
            itemGroup,
            codingSystemToClassifications,
            PUBLISHED_ITEMS_VERSION
        )

        // verify
        assertEquals(expectedPreviewResult, result)

        val itemGroupCaptor = argumentCaptor<ItemGroup>()
        val codingSystemToClassificationsCaptor = argumentCaptor<Map<String, List<CodingSystemToClassification>>>()
        val publishedItemsVersionCaptor = argumentCaptor<String>()
        verify(mockItemGroupsSparkImpl, times(1)).getItemGroupPreview(
            itemGroupCaptor.capture(),
            codingSystemToClassificationsCaptor.capture(),
            publishedItemsVersionCaptor.capture()
        )
        assertEquals(itemGroup, itemGroupCaptor.lastValue)
        assertEquals(codingSystemToClassifications, codingSystemToClassificationsCaptor.lastValue)
        assertEquals(PUBLISHED_ITEMS_VERSION, publishedItemsVersionCaptor.lastValue)

        verifyNoInteractions(mockFeatureTogglePort)
        verifyNoInteractions(mockItemGroupsSparkLambdaClient)
    }
}