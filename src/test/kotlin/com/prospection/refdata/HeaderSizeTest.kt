package com.prospection.refdata

import com.prospection.refdata.itemgroups.application.rest.ItemGroupControllerTest
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupsService
import com.prospection.web.security.AuthTokenFilter.Companion.AUTH_HEADER
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.http.HttpStatus
import org.springframework.http.RequestEntity
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.net.URI
import java.util.*

/**
 * Test class for HeaderSizeTest.
 */
class HeaderSizeTest : AbstractControllerIntegrationTest() {
    @Autowired
    private lateinit var restTemplate: TestRestTemplate

    @MockitoBean
    private lateinit var itemGroupService: ItemGroupsService

    @Value("\${application.security.secret}")
    private lateinit var securitySecret: String

    @Test
    fun `can take increased size header than spring default`() {
        doReturn(ItemGroup("item-group-uuid", "name", "goal", "{}"))
            .whenever(itemGroupService).getItemGroup("item-group-uuid")

        assertEquals(HttpStatus.OK, queryBookmarkAndReturnHttpStatus(19))

        val response = queryBookmarkAndReturnResponse(21)
        assertEquals(HttpStatus.BAD_REQUEST.value(), response.statusCode.value())
    }

    private fun queryBookmarkAndReturnHttpStatus(headerSizeInKiloBytes: Int): HttpStatus {
        val response = queryBookmarkAndReturnResponse(headerSizeInKiloBytes)
        return HttpStatus.valueOf(response.statusCode.value())
    }

    private fun queryBookmarkAndReturnResponse(headerSizeInKiloBytes: Int): org.springframework.http.ResponseEntity<String> {
        return restTemplate.exchange(
            RequestEntity.get(URI.create("${ItemGroupControllerTest.BASE_URL}/item-group-uuid"))
                .header("test-header", "a".repeat(headerSizeInKiloBytes * 1024))
                .header(AUTH_HEADER, authToken())
                .build(),
            String::class.java
        )
    }

    private fun authToken(): String {
        val signingKey = Keys.hmacShaKeyFor(Base64.getDecoder().decode(securitySecret))
        return Jwts.builder()
            .subject("<EMAIL>")
            .claim("admin", true)
            .signWith(signingKey)
            .compact()
    }
}