package com.prospection.refdata.codingsystem.application.rest

import com.prospection.refdata.AbstractIntegrationTest
import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

internal class CodingSystemControllerTest : AbstractIntegrationTest() {

    companion object {
        private const val BASE_URL = "/api/ref-data-v2/coding-systems"
        private val classificationList = listOf("DRG Diagnosis", "DRG Dispensing", "DRG Procedure")
    }

    @Autowired
    private lateinit var codingSystemRepository: CodingSystemJpaRepository

    @Autowired
    private lateinit var mvc: MockMvc

    @Autowired
    private lateinit var om: ObjectMapper

    @Test
    fun `test list classification success`() {
        codingSystemRepository.saveAll(
            classificationList.map { CodingSystemEntity(name = it) }
        )

        val response = mvc.perform(MockMvcRequestBuilders.get(BASE_URL))
            .andExpect(MockMvcResultMatchers.status().is2xxSuccessful).andReturn().response

        val responseClassifications = om.readValue<List<String>>(
            response.contentAsByteArray,
            om.typeFactory.constructCollectionType(List::class.java, String::class.java)
        )
        assertEquals(classificationList, responseClassifications)
    }
}