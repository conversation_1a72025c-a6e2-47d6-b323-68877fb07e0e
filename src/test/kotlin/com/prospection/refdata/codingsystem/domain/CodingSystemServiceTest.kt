package com.prospection.refdata.codingsystem.domain

import com.prospection.refdata.job.domain.JobPort
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock
import org.mockito.Mockito.spy
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.kotlin.reset
import java.time.LocalDate
import java.time.Month

internal class CodingSystemServiceTest {

    private val codingSystemPort = mock(CodingSystemPort::class.java)

    private val jobPort = mock(JobPort::class.java)

    private val service = spy(CodingSystemService(codingSystemPort, jobPort))

    @AfterEach
    fun clearMock() {
        reset(jobPort, codingSystemPort)
    }

    @Test
    fun `should update etl status`() {
        val codingSystem = CodingSystem(
            name = "classification-1"
        )
        val lastPulledVersion = LocalDate.of(2022, Month.APRIL, 5)

        doReturn(codingSystem).`when`(codingSystemPort).findByName(codingSystem.name)
        doReturn(codingSystem).`when`(codingSystemPort).updateLastPulledVersion(codingSystem.name, lastPulledVersion)


        val result = service.updateEtlStatus(codingSystem.name, lastPulledVersion)
        Assertions.assertEquals(codingSystem, result)

        verify(codingSystemPort, times(1)).updateLastPulledVersion(codingSystem.name, lastPulledVersion)
    }

    @Test
    fun `should return false when checking etl job with older version`() {
        val codingSystem = CodingSystem(
            name = "classification-1",
            lastPulledVersion = LocalDate.of(2022, Month.MAY, 9)
        )
        doReturn(codingSystem).`when`(codingSystemPort).findByName(codingSystem.name)
        doReturn(false).`when`(jobPort).existsRunningJob()
        val toPullVersion = LocalDate.of(2022, Month.MAY, 8)
        val result = service.checkEtlJobPossibility(codingSystem.name, toPullVersion)
        Assertions.assertEquals(false, result)
    }

    @Test
    fun `should return false when checking etl job with currently running job`() {
        val codingSystem = CodingSystem(
            name = "classification-1",
        )
        doReturn(codingSystem).`when`(codingSystemPort).findByName(codingSystem.name)
        doReturn(true).`when`(jobPort).existsRunningJob()
        val toPullVersion = LocalDate.of(2022, Month.MAY, 8)
        val result = service.checkEtlJobPossibility(codingSystem.name, toPullVersion)
        Assertions.assertEquals(false, result)
    }

    @Test
    fun `should return true when checking etl job`() {
        val codingSystem = CodingSystem(
            name = "classification-1",
            lastPulledVersion = LocalDate.of(2022, Month.MAY, 8)
        )
        doReturn(codingSystem).`when`(codingSystemPort).findByName(codingSystem.name)
        doReturn(false).`when`(jobPort).existsRunningJob()
        val toPullVersion = LocalDate.of(2022, Month.MAY, 9)
        val result = service.checkEtlJobPossibility(codingSystem.name, toPullVersion)
        Assertions.assertEquals(true, result)
    }
}