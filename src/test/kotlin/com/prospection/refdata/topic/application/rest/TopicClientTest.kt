package com.prospection.refdata.topic.application.rest

import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestTopic
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestTopicDto
import com.prospection.refdata.topic.application.rest.dto.TopicDto
import com.prospection.refdata.topic.application.rest.dto.WorkflowDto
import com.prospection.refdata.topic.application.rest.mapper.TopicDtoMapper
import com.prospection.refdata.topic.application.rest.mapper.TopicDtoMapperImpl
import com.prospection.refdata.topic.domain.Topic
import com.prospection.refdata.topic.domain.Workflow
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito.anyString
import org.mockito.Mockito.eq
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.web.client.RestTemplate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime


@ExtendWith(SpringExtension::class)
@ContextConfiguration(classes = [TopicDtoMapperImpl::class])
class TopicClientTest {
    private lateinit var topicClient: TopicClient
    private lateinit var dashxRestTemplate: RestTemplate

    @Autowired
    private lateinit var mapper: TopicDtoMapper

    @BeforeEach
    fun setUp() {
        dashxRestTemplate = mock(RestTemplate::class.java)
        topicClient = TopicClient(dashxRestTemplate, mapper)
    }

    @Test
    fun shouldFetchTopicsByItemGroupsInChunks() {
        // given
        val itemGroups = (1..1001).map { "group_$it" }
        val respondedTopicsChunk1 = (1..1000).map { createTestTopicDto(it.toLong(), "topic$it") }
        val respondedTopicsChunk2 = listOf(createTestTopicDto(1001L, "topic1001"))

        whenever(
            dashxRestTemplate.postForObject(
                anyString(),
                any(),
                eq(Array<TopicDto>::class.java)
            )
        )
            .thenReturn(respondedTopicsChunk1.toTypedArray())
            .thenReturn(respondedTopicsChunk2.toTypedArray())

        // when
        val result = topicClient.listTopicsByItemGroups(itemGroups)

        // then
        verify(dashxRestTemplate, times(2)).postForObject(
            anyString(),
            any(),
            eq(Array<TopicDto>::class.java)
        )

        Assertions.assertEquals((1..1001).map { createTestTopic(it.toLong(), "topic$it") }, result)
    }

    @Test
    fun `should map the fields of topic and workflow using DTOs`() {
        val instant = ZonedDateTime.of(
            LocalDateTime.of(2023, 12, 22, 9, 53),
            ZoneId.of("UTC")
        )
            .toInstant()

        val testTopicDto = TopicDto(
            id = 1L,
            name = "Topic 1",
            companyId = "company1",
            conditionId = "condition1",
            conditionName = "Condition 1",
            subscriptionId = "subscription1",
            bridge = false,
            adhoc = false,
            therapyAreaName = "Therapy Area 1",
            subscriptionTherapyAreaId = "subscriptionTherapyArea1",
            standalone = true,
            relatedWorkflows = listOf(
                WorkflowDto(
                    id = 2L,
                    lastModifiedBy = "thomas",
                    lastModifiedDate = instant,
                    generatedTime = instant,
                    status = "GENERATED",
                    country = "JP",
                    relatedItemGroups = listOf("group1")
                ),
                WorkflowDto(
                    id = 3L,
                    lastModifiedBy = "thomas",
                    lastModifiedDate = instant,
                    generatedTime = null,
                    status = "GENERATED",
                    country = "JP",
                    relatedItemGroups = listOf("group1")
                )
            )
        )

        // given
        whenever(
            dashxRestTemplate.postForObject(
                anyString(),
                any(),
                eq(Array<TopicDto>::class.java)
            )
        ).thenReturn(arrayOf(testTopicDto))

        // when
        val actual = topicClient.listTopicsByItemGroups(listOf("group1"))[0]

        // then
        assertEquals(Topic(
            id = 1L,
            name = "Topic 1",
            companyId = "company1",
            conditionId = "condition1",
            conditionName = "Condition 1",
            subscriptionId = "subscription1",
            bridge = false,
            adhoc = false,
            therapyAreaName = "Therapy Area 1",
            subscriptionTherapyAreaId = "subscriptionTherapyArea1",
            standalone = true,
            relatedWorkflows = listOf(
                Workflow(
                    id = 2L,
                    lastModifiedBy = "thomas",
                    lastModifiedDate = instant,
                    generatedTime = instant,
                    status = "GENERATED",
                    country = "JP",
                    relatedItemGroups = listOf("group1")
                ),
                Workflow(
                    id = 3L,
                    lastModifiedBy = "thomas",
                    lastModifiedDate = instant,
                    generatedTime = null,
                    status = "GENERATED",
                    country = "JP",
                    relatedItemGroups = listOf("group1")
                ),
            )
        ), actual)
    }
}