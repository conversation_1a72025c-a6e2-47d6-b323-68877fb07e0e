package com.prospection.refdata

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.ResultActions
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

abstract class AbstractControllerIntegrationTest : AbstractIntegrationTest(){

    @Autowired
    private lateinit var mvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Throws(Exception::class)
    private fun performRequest(request: MockHttpServletRequestBuilder): ResultActions {
        return mvc.perform(request.secure(true))
    }

    @Throws(Exception::class)
    private fun performRequest(request: MockHttpServletRequestBuilder, body: Any?): ResultActions {
        return mvc.perform(
            request
                .secure(true)
                .content(objectMapper.writeValueAsBytes(body))
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding("UTF-8")
        )
    }

    // GET
    @Throws(Exception::class)
    protected fun performGet(url: String): ResultActions {
        return performRequest(get(url))
    }

    // POST
    @Throws(java.lang.Exception::class)
    protected fun performPost(url: String, content: Any?): ResultActions {
        return performRequest(post(url), content)
    }

    @Throws(java.lang.Exception::class)
    protected fun performPost(url: String): ResultActions {
        return performRequest(post(url))
    }

    // PUT
    @Throws(java.lang.Exception::class)
    protected fun performPut(url: String, content: Any?): ResultActions {
        return performRequest(put(url), content)
    }

    // DELETE
    @Throws(java.lang.Exception::class)
    protected fun performDelete(url: String): ResultActions {
        return performRequest(delete(url))
    }

    protected fun <T> parseResponse(response: MockHttpServletResponse, clazz: Class<T>): T {
        return objectMapper.readValue(response.contentAsString, clazz)
    }

    protected fun <T> parseResponse(response: MockHttpServletResponse, valueType: TypeReference<T> ): T {
        return objectMapper.readValue(response.contentAsString, valueType)
    }
}