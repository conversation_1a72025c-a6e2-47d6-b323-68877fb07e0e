package com.prospection.refdata.items.integration

import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.rules.domain.EnrichedAttribute
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.Rule

object ApplyRulesRuleSamples {
    val NAME_EQUALS_TO_ABC = createEnrichmentRuleWithRule(
        "1", Rule(
            rules = listOf(
                Rule(field = "source_name", value = "abc", operator = "="),
            ),
            combinator = "and"
        )
    )

    val NAME_NOT_EQUALS_TO_ABC = createEnrichmentRuleWithRule(
        "1", Rule(
            rules = listOf(
                Rule(field = "source_name", value = "abc", operator = "!="),
            ),
            combinator = "and"
        )
    )

    val NAME_CONTAINS_BC = createEnrichmentRuleWithRule(
        "1", Rule(
            rules = listOf(
                Rule(field = "source_name", value = "bc", operator = "contains"),
            ),
            combinator = "and"
        )
    )

    val NAME_NOT_CONTAINS_BC = createEnrichmentRuleWithRule(
        "1", Rule(
            rules = listOf(
                Rule(field = "source_name", value = "bc", operator = "doesNotContain"),
            ),
            combinator = "and"
        )
    )

    val NAME_STARTS_WITH_AB = createEnrichmentRuleWithRule(
        "1", Rule(
            rules = listOf(
                Rule(field = "source_name", value = "ab", operator = "beginsWith"),
            ),
            combinator = "and"
        )
    )

    val NAME_NOT_STARTS_WITH_AB = createEnrichmentRuleWithRule(
        "1", Rule(
            rules = listOf(
                Rule(field = "source_name", value = "ab", operator = "doesNotBeginWith"),
            ),
            combinator = "and"
        )
    )

    val NAME_EQUALS_TO_ABC_THEN_VALUE_2 = EnrichmentRule(
        id = "2",
        enrichedAttributeValue = EnrichedAttributeValue(
            id = "value_2",
            value = "value_2",
            enrichedAttribute = EnrichedAttribute(
                id = "attribute_1",
                name = "attribute_1"
            )
        ),
        rule = HasRule.objectMapper.writeValueAsString(
            Rule(
                rules = listOf(
                    Rule(field = "source_name", value = "abc", operator = "="),
                ),
                combinator = "and"
            )
        ),
        deleted = 0,
    )

    val NAME_EQUALS_TO_ABC_AND_CODE_EQUALS_TO_1 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            rules = listOf(
                Rule(field = "source_name", value = "abc", operator = "="),
                Rule(field = "source_code", value = "1", operator = "="),
            ),
            combinator = "and"
        )
    )

    val NAME_EQUALS_TO_ABC_OR_CODE_EQUALS_TO_1 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            rules = listOf(
                Rule(field = "source_name", value = "abc", operator = "="),
                Rule(field = "source_code", value = "1", operator = "="),
            ),
            combinator = "or"
        )
    )

    val NESTED_RULE = createEnrichmentRuleWithRule(
        "1",
        Rule(
            rules = listOf(
                Rule(field = "source_name", value = "abc", operator = "="),
                Rule(
                    combinator = "and", rules = listOf(
                        Rule(field = "source_name", value = "def", operator = "="),
                        Rule(field = "source_code", value = "2", operator = "="),
                        Rule(field = "source_icd_code", value = "5", operator = "contains"),
                    )
                ),
            ),
            combinator = "or"
        )
    )

    val NON_EXISTING_SOURCE_ATTRIBUTE = createEnrichmentRuleWithRule(
        "1",
        Rule(
            rules = listOf(
                Rule(field = "source_not_existing_column", value = "abc", operator = "="),
            ),
            combinator = "and"
        )
    )

    val SOURCE_NAME_DOES_NOT_CONTAIN_ABC_RULE = createEnrichmentRuleWithRule(
        "1",
        Rule(
            rules = listOf(
                Rule(field = "source_name", value = "abc", operator = "doesNotContain"),
            ),
            combinator = "and"
        )
    )

    val SOURCE_ICD_CODE_EQUAL_A1 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(Rule(field = "source_icd_code", value = "A1", operator = "="))
        )
    )

    val SOURCE_ICD_CODE_NOT_EQUAL_A1 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(Rule(field = "source_icd_code", value = "A1", operator = "!="))
        )
    )

    val SOURCE_ICD_CODE_CONTAINS_1 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(Rule(field = "source_icd_code", value = "1", operator = "contains"))
        )
    )

    val SOURCE_ICD_CODE_DOES_NOT_CONTAIN_1 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(Rule(field = "source_icd_code", value = "1", operator = "doesNotContain"))
        )
    )

    val SOURCE_ICD_CODE_BEGINS_WITH_C4 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(Rule(field = "source_icd_code", value = "C4", operator = "beginsWith"))
        )
    )

    val SOURCE_ICD_CODE_DOES_NOT_BEGIN_WITH_C3 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(Rule(field = "source_icd_code", value = "C3", operator = "doesNotBeginWith"))
        )
    )

    val SOURCE_ICD_CODE_BEGINS_WITH_C_AND_CONTAINS_3 = createEnrichmentRuleWithRule(
        "1",
        Rule(
            combinator = "and",
            rules = listOf(
                Rule(field = "source_icd_code", value = "C", operator = "beginsWith"),
                Rule(field = "source_icd_code", value = "3", operator = "contains"),

            )
        )
    )

    private fun createEnrichmentRuleWithRule(ruleId: String, rule: Rule): EnrichmentRule {
        return EnrichmentRule(
            id = ruleId,
            enrichedAttributeValue = EnrichedAttributeValue(
                id = "value_${ruleId}",
                value = "value_${ruleId}",
                enrichedAttribute = EnrichedAttribute(
                    id = "attribute_${ruleId}",
                    name = "attribute_${ruleId}"
                )
            ),
            rule = HasRule.objectMapper.writeValueAsString(rule),
            deleted = 0,
        )
    }
}