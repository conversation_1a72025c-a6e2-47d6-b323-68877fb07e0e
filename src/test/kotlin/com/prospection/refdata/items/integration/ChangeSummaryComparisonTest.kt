package com.prospection.refdata.items.integration

import TestSpark.spark
import com.prospection.refdata.common.integration.ChangeSummaryHelper.compare
import org.apache.spark.sql.RowFactory
import org.apache.spark.sql.types.DataTypes
import org.apache.spark.sql.types.Metadata
import org.apache.spark.sql.types.StructField
import org.apache.spark.sql.types.StructType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class ChangeSummaryComparisonTest {
    companion object {
        private val CHANGE_SUMMARY_PUBLISHED_ITEMS = spark.createDataFrame(listOf(
            ChangeSummaryRow("1", "A1", "B1", "C1", "D1"),
            ChangeSummaryRow("2", "A2", "B2", "C2", "D2"),
            ChangeSummaryRow("3", "A3", "B3", "C3", "D3"),
            ChangeSummaryRow("4", "A4", "B4", "C4", "D4"),
        ), ChangeSummaryRow::class.java)

        private val CHANGE_SUMMARY_ADDED_IN_DRAFT_ITEMS = spark.createDataFrame(listOf(
            ChangeSummaryRow("1", "A1", "B1", "C1", "D1"),
            ChangeSummaryRow("2", "A2", "B2", "C2", "D2"),
            ChangeSummaryRow("3", "A3", "B3", "C3", "D3"),
            ChangeSummaryRow("4", "A4", "B4", "C4", "D4"),
            ChangeSummaryRow("5", "A5", "B5", "C5", "D5"),
        ), ChangeSummaryRow::class.java)

        private val CHANGE_SUMMARY_DELETED_IN_DRAFT_ITEMS = spark.createDataFrame(listOf(
            ChangeSummaryRow("1", "A1", "B1", "C1", "D1"),
            ChangeSummaryRow("2", "A2", "B2", "C2", "D2"),
            ChangeSummaryRow("3", "A3", "B3", "C3", "D3"),
        ), ChangeSummaryRow::class.java)

        private val CHANGE_SUMMARY_CHANGED_IN_DRAFT_ITEMS = spark.createDataFrame(listOf(
            ChangeSummaryRow("1", "A1", "B1", "C1", "D1"),
            ChangeSummaryRow("2", "A21", "B2", "C2", "D2"),
            ChangeSummaryRow("3", "A3", "B3", "C3", "D3"),
            ChangeSummaryRow("4", "A4", "B4", "C4", "D4")
        ), ChangeSummaryRow::class.java)

        private val CHANGE_SUMMARY_WITH_MULTIPLE_EDITS_IN_DRAFT_ITEMS = spark.createDataFrame(listOf(
            ChangeSummaryRow("2", "A21", "B2", "C2", "D2"),
            ChangeSummaryRow("3", "A3", "B3", "C3", "D3"),
            ChangeSummaryRow("4", "A4", "B4", "C4", "D4"),
            ChangeSummaryRow("5", "A5", "B5", "C5", "D5"),
        ), ChangeSummaryRow::class.java)
    }

    @Test
    fun `Should generate change summary with no change`() {
        val changeSummaryDs = compare(CHANGE_SUMMARY_PUBLISHED_ITEMS, CHANGE_SUMMARY_PUBLISHED_ITEMS).diffData
            .collectAsList()

        assertThat(changeSummaryDs)
            .hasSize(0)
    }

    @Test
    fun `Should generate change summary with added items`() {
        val changeSummaryDs = compare(CHANGE_SUMMARY_ADDED_IN_DRAFT_ITEMS, CHANGE_SUMMARY_PUBLISHED_ITEMS).diffData
            .collectAsList()

        assertThat(changeSummaryDs)
            .hasSize(1)
            .first()
            .hasToString("[D5,A5,B5,C5,5,added]")
    }

    @Test
    fun `Should generate change summary with deleted items`() {
        val changeSummaryDs = compare(CHANGE_SUMMARY_DELETED_IN_DRAFT_ITEMS, CHANGE_SUMMARY_PUBLISHED_ITEMS).diffData
            .collectAsList()

        assertThat(changeSummaryDs)
            .hasSize(1)
            .first()
            .hasToString("[D4,A4,B4,C4,4,deleted]")
    }


    @Test
    fun `Should generate change summary with change items`() {
        val changeSummaryDs = compare(CHANGE_SUMMARY_CHANGED_IN_DRAFT_ITEMS, CHANGE_SUMMARY_PUBLISHED_ITEMS).diffData
            .collectAsList()

        assertThat(changeSummaryDs)
            .hasSize(2)

        assertThat(changeSummaryDs.map{ it.toString() })
            .containsExactly(
                "[D2,A21,B2,C2,2,added]",
                "[D2,A2,B2,C2,2,deleted]"
            )
    }

    @Test
    fun `Should generate change summary with removed col in draft items`() {
        val changeSummaryDs =
            compare(CHANGE_SUMMARY_WITH_MULTIPLE_EDITS_IN_DRAFT_ITEMS, CHANGE_SUMMARY_PUBLISHED_ITEMS).diffData
                .collectAsList()

        assertThat(changeSummaryDs)
            .hasSize(4)

        assertThat(changeSummaryDs.map{ it.toString() })
            .containsExactlyInAnyOrder(
                "[D2,A2,B2,C2,2,deleted]",
                "[D2,A21,B2,C2,2,added]",
                "[D5,A5,B5,C5,5,added]",
                "[D1,A1,B1,C1,1,deleted]"
            )
    }

    @Test
    fun `return the correct change summary even if the order of the columns between draft and published is different`() {
        val publishedItemGroupsSchema = StructType(
            listOf(
                StructField("item_code", DataTypes.StringType, false, Metadata.empty()),
                StructField("item_group", DataTypes.StringType, false, Metadata.empty()),
                StructField("classification", DataTypes.StringType, false, Metadata.empty()),
                StructField("item_group_name", DataTypes.StringType, false, Metadata.empty()),
            ).toTypedArray()
        )

        // Create a dataframe based on given schema instead of java bean to return columns in order
        val publishedItemGroups = spark.createDataFrame(listOf(
            RowFactory.create("1", "I1", "ClassificationA", "Item group 1"),
            RowFactory.create("2", "I2", "ClassificationA", "Item group 2"),
            RowFactory.create("3", "I3", "ClassificationA", "Item group 3"),
            RowFactory.create("4", "I4", "ClassificationA", "Item group 4"),
        ), publishedItemGroupsSchema)

        val draftItemGroupsSchema = StructType(
            listOf(
                StructField("item_code", DataTypes.StringType, false, Metadata.empty()),
                StructField("item_group", DataTypes.StringType, false, Metadata.empty()),
                StructField("item_group_name", DataTypes.StringType, false, Metadata.empty()),
                StructField("classification", DataTypes.StringType, false, Metadata.empty()),
            ).toTypedArray()
        )
        val draftItemGroups = spark.createDataFrame(listOf(
            RowFactory.create("1", "I1", "Item group 1", "ClassificationA"),
            RowFactory.create("2", "I2", "Item group 2", "ClassificationA"),
            RowFactory.create("5", "I5", "Item group 5", "ClassificationA"),
            RowFactory.create("6", "I6", "Item group 6", "ClassificationA"),
        ), draftItemGroupsSchema)

        val changeSummaryDs = compare(draftItemGroups, publishedItemGroups).diffData.collectAsList()

        assertThat(changeSummaryDs).hasSize(4)

        assertThat(changeSummaryDs.map{ it.toString() })
            .containsExactlyInAnyOrder(
                "[5,I5,Item group 5,ClassificationA,added]",
                "[6,I6,Item group 6,ClassificationA,added]",
                "[3,I3,Item group 3,ClassificationA,deleted]",
                "[4,I4,Item group 4,ClassificationA,deleted]"
            )
    }

    data class ChangeSummaryRow(val source_code: String, val source_a: String, val source_b: String, val source_c: String, val enriched_d: String)
}