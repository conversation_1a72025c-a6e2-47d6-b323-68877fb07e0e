package com.prospection.refdata.items.integration

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.MappedClassification
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.codingsystem.integration.CodingSystemToClassificationEntity
import com.prospection.refdata.codingsystem.integration.mapper.CodingSystemEntityMapper
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime

class RawItemsMetadataJpaAdapterTest : AbstractIntegrationTest() {

    companion object {
        private const val PUBLISH_VERSION = "20210101T000000.000Z"
        private const val PUBLISH_VERSION_1 = "20210201T000000"
        private const val TEST_CODING_SYSTEM = "test-coding-system"
        private const val TEST_CLASSIFICATION = "test-classification"
        private const val TEST_EXPORT_COLUMN = "test-source_code"
        private const val TEST_IMPORTER = "tester"

        private val RAW_ITEMS = listOf(
            TestRow("1", "A", "B", "c"),
            TestRow("2", "A", "B", "c"),
            TestRow("3", "A", "B", "c"),
            TestRow("4", "A", "B", "c"),
            TestRow("5", "a", "b", "C"),
        )

        private val RAW_ITEMS_WITH_MORE_ROW = listOf(
            TestRow("1", "A", "B", "c"),
            TestRow("2", "A", "B", "c"),
            TestRow("3", "A", "B", "c"),
            TestRow("4", "A", "B", "c"),
            TestRow("5", "a", "b", "C"),
            TestRow("6", "b", "c", "d"),
        )

        private val PUBLISHED_ITEMS = listOf(
            TestRow("1", "A", "B", "c"),
            TestRow("2", "A", "B", "c"),
            TestRow("3", "A", "B", "c"),
            TestRow("4", "A", "B", "c"),
            TestRow("7", "X", "Y", "Z")
        )
    }

    @Autowired
    private lateinit var rawItemsMetadataJpaRepository: RawItemsMetadataJpaRepository

    @Autowired
    private lateinit var rawItemsMetadataJpaAdapter: RawItemsMetadataJpaAdapter

    @Autowired
    private lateinit var codingSystemRepository: CodingSystemJpaRepository

    @Autowired
    private lateinit var publishedItemVersionJpaRepository: PublishedItemVersionJpaRepository

    @Autowired
    private lateinit var codingSystemEntityMapper: CodingSystemEntityMapper

    private lateinit var codingSystem: CodingSystem

    @BeforeEach
    fun init() {
        val codingSystemEntity = CodingSystemEntity(name = TEST_CODING_SYSTEM)
        val codingSystemToClassificationEntity = CodingSystemToClassificationEntity(
            classification = TEST_CLASSIFICATION,
            codingSystem = codingSystemEntity,
            codingSystemColumnToExport = TEST_EXPORT_COLUMN
        )
        codingSystemEntity.codingSystemToClassifications = listOf(codingSystemToClassificationEntity)

        codingSystem = codingSystemEntityMapper.toDomain(
            codingSystemRepository.save(codingSystemEntity)
        )

        publishedItemVersionJpaRepository.saveAll(
            listOf(
                PublishedItemVersionEntity(
                    publishedVersion = PUBLISH_VERSION,
                    publishedBy = "tester",
                    publishedAt = LocalDateTime.now()
                ),
                PublishedItemVersionEntity(
                    publishedVersion = PUBLISH_VERSION_1,
                    publishedBy = "tester",
                    publishedAt = LocalDateTime.now()
                )
            )
        )
    }

    @AfterEach
    fun clear() {
        rawItemsMetadataJpaRepository.deleteAll()
        codingSystemRepository.deleteAll()
        publishedItemVersionJpaRepository.deleteAll()
    }

    @Test
    fun `should has only one draft metadata of a classification`() {
        rawItemsMetadataJpaAdapter.saveDraftMetadata(
            rawItems = createDataFrame(RAW_ITEMS),
            publishedItems = createDataFrame(PUBLISHED_ITEMS),
            codingSystem = codingSystem,
            createdBy = TEST_IMPORTER,
            createdAt = LocalDateTime.now()
        )

        rawItemsMetadataJpaAdapter.saveDraftMetadata(
            rawItems = createDataFrame(RAW_ITEMS_WITH_MORE_ROW),
            publishedItems = createDataFrame(PUBLISHED_ITEMS),
            codingSystem = codingSystem,
            createdBy = TEST_IMPORTER,
            createdAt = LocalDateTime.now()
        )

        val rawItemsMetaData = rawItemsMetadataJpaAdapter.getAllDraftMetadata().filter {
            it.codingSystem == codingSystem
        }

        Assertions.assertEquals(1, rawItemsMetaData.size)

        val metadata = rawItemsMetaData[0]
        Assertions.assertEquals(TEST_CODING_SYSTEM, metadata.codingSystem.name)
        Assertions.assertEquals(listOf(MappedClassification(TEST_CLASSIFICATION, TEST_EXPORT_COLUMN)), metadata.codingSystem.mappedClassifications)
        Assertions.assertEquals(6, metadata.totalItem)
        Assertions.assertEquals(2, metadata.newItem)
        Assertions.assertEquals(1, metadata.deletedItem)
        Assertions.assertEquals(
            listOf("source_a", "source_b", "source_c", "source_code"),
            metadata.sourceAttributes
        )
        Assertions.assertEquals(TEST_IMPORTER, metadata.createdBy)
    }

    @Test
    fun `should generate right metadata with published version`() {
        rawItemsMetadataJpaAdapter.saveDraftMetadata(
            rawItems = createDataFrame(RAW_ITEMS_WITH_MORE_ROW),
            publishedItems = createDataFrame(PUBLISHED_ITEMS),
            codingSystem = codingSystem,
            createdBy = TEST_IMPORTER,
            createdAt = LocalDateTime.now()
        )

        val rawItemsMetaData = rawItemsMetadataJpaAdapter.getAllDraftMetadata().first()

        Assertions.assertEquals(TEST_CODING_SYSTEM, rawItemsMetaData.codingSystem.name)
        Assertions.assertEquals(listOf(MappedClassification(TEST_CLASSIFICATION, TEST_EXPORT_COLUMN)), rawItemsMetaData.codingSystem.mappedClassifications)
        Assertions.assertEquals(6, rawItemsMetaData.totalItem)
        Assertions.assertEquals(2, rawItemsMetaData.newItem)
        Assertions.assertEquals(1, rawItemsMetaData.deletedItem)
        Assertions.assertEquals(
            listOf("source_a", "source_b", "source_c", "source_code"),
            rawItemsMetaData.sourceAttributes
        )
        Assertions.assertEquals(TEST_IMPORTER, rawItemsMetaData.createdBy)
    }

    private fun createDataFrame(rows: List<TestRow>): Dataset<Row> {
        return spark.createDataFrame(rows, TestRow::class.java)
    }

    data class TestRow(val source_code: String, val source_a: String, val source_b: String, val source_c: String)

}
