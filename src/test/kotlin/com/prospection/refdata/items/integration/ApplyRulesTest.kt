package com.prospection.refdata.items.integration

import TestSpark.spark
import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.common.domain.GeneratePublicUrlPort
import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.S3ImportExportHelper
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.items.domain.ItemsSparkPort
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.doReturn
import org.mockito.kotlin.whenever


internal class ApplyRulesTest {
    private val objectMapper = Mockito.mock(ObjectMapper::class.java)
    private val importExportHelper = Mockito.mock(S3ImportExportHelper::class.java)
    private val sparkImportExportPort = Mockito.mock(SparkImportExportHelper::class.java)
    private val s3PublicizeAdapter = Mockito.mock(GeneratePublicUrlPort::class.java)
    private val itemsSparkPort = Mockito.mock(ItemsSparkPort::class.java)

    private val itemsAwsAdapter = ItemsAwsAdapter(
        importExportHelper,
        sparkImportExportPort,
        itemsSparkPort,
        s3PublicizeAdapter,
        objectMapper,
    )

    private val TestDF = createTestDf(
        TestRow("1", "abc", arrayOf("A1", "B2", "C35")),
        TestRow("2", "def", arrayOf("A2", "B2", "C45")),
    )

    @BeforeEach
    fun reset() {
        Mockito.reset(
            importExportHelper,
            sparkImportExportPort,
            itemsSparkPort,
            s3PublicizeAdapter,
            objectMapper
        )
    }

    @Test
    fun `Should populate a coding system column`() {
        val df = createTestDf(TestRow("1", "abc"))

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItemsDs = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC)
        )

        assertThat(enrichedItemsDs.columns()).contains(CODING_SYSTEM_ATTRIBUTE_NAME)
    }

    @Test
    fun `Should populate an enriched column and value by an enrichment rule`() {
        val df = createTestDf(TestRow("1", "abc"))

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC)
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(1)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Should not populate an enriched value if rule condition is not met`() {
        val df = createTestDf(TestRow("1", "def"))

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC)
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(1)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Rule condition should be case-free`() {
        val df = createTestDf(TestRow("1", "ABC"))

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC)
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(1)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Should apply 2 enrichment rules affecting the same enriched attribute and create 2 enriched elements in an array type column`() {
        val df = createTestDf(TestRow("1", "abc"))

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC,
                ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC_THEN_VALUE_2,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(1)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1", "value_2"))
    }

    @Test
    fun `Test condition for source attribute (string type) - equals to`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Test condition for source attribute (string type) - not equals to`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_NOT_EQUALS_TO_ABC,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (string type) - contains`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_CONTAINS_BC,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Test condition for source attribute (string type) - does not contain`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_NOT_CONTAINS_BC,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (string type) - starts with`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_STARTS_WITH_AB,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Test condition for source attribute (string type) - does not start with`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_NOT_STARTS_WITH_AB,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test 2 conditions with AND combinator`() {
        val df = createTestDf(
                TestRow("1", "abc"),
                TestRow("2", "abc"),
                TestRow("1", "def"),
        )

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC_AND_CODE_EQUALS_TO_1,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(3)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 2)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Test 2 conditions with OR combinator`() {
        val df = createTestDf(
            TestRow("1", "abc"),
            TestRow("2", "abc"),
            TestRow("1", "def"),
        )

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NAME_EQUALS_TO_ABC_OR_CODE_EQUALS_TO_1,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(3)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 2)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test a nested condition`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NESTED_RULE,
            )
        )
            .collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Should not throw an error even when a non-existing source attribute is specified in a rule`() {
        val df = createTestDf(TestRow("1", "abc"))

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItemsDs = itemsAwsAdapter.applyRules(
            "whatever", listOf(
                ApplyRulesRuleSamples.NON_EXISTING_SOURCE_ATTRIBUTE,
            )
        )

        val enrichedItems = enrichedItemsDs.collectAsList()

        assertThat(enrichedItemsDs.columns()).containsOnly(
            "source_code",
            "source_icd_code",
            "source_name",
            "attribute_1",
            CODING_SYSTEM_ATTRIBUTE_NAME
        )

        assertThat(enrichedItems).hasSize(1)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `should evaluate enrichment rule to get source name attribute is null value`() {
        val df = createTestDf(
                TestRow("1", "abc"),
                TestRow("2", null),
        )

        doReturn(df).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItemsDs = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_NAME_DOES_NOT_CONTAIN_ABC_RULE)
        )

        val enrichedItems = enrichedItemsDs.collectAsList()

        assertThat(enrichedItems).hasSize(2)

        assertThat(enrichedItems[0].getList<String>(enrichedItems[1].fieldIndex("attribute_1"))).isEqualTo(emptyList<String>())

        assertThat(enrichedItems[1].getAs<String>("source_code")).isEqualTo("2")
        assertThat(enrichedItems[1].getAs<String>("source_name")).isNull()
        assertThat(enrichedItems[1].getList<String>(enrichedItems[1].fieldIndex("attribute_1"))).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (array type) - equals operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_EQUAL_A1)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Test condition for source attribute (array type) - not equals operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_NOT_EQUAL_A1)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (array type) - contains operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_CONTAINS_1)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
    }

    @Test
    fun `Test condition for source attribute (array type) - doesNotContain operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_DOES_NOT_CONTAIN_1)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (array type) - beginsWith operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_BEGINS_WITH_C4)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (array type) - doesNotBeginWith operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_DOES_NOT_BEGIN_WITH_C3)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (array type) - doesNotBeginWith operator and empty value`() {
        val testDs = createTestDf(
            TestRow("1", "abc", arrayOf("C3X")),
            TestRow("3", "def", arrayOf()),
        )

        doReturn(testDs).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_DOES_NOT_BEGIN_WITH_C3)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(emptyList<String>())
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(listOf("value_1"))
    }

    @Test
    fun `Test condition for source attribute (array type) - contains and beginsWith operator`() {
        doReturn(TestDF).whenever(sparkImportExportPort).readParquet("${Items.Draft.RAW_ITEMS}/whatever/")

        val enrichedItems = itemsAwsAdapter.applyRules(
            "whatever",
            listOf(ApplyRulesRuleSamples.SOURCE_ICD_CODE_BEGINS_WITH_C_AND_CONTAINS_3)
        ).collectAsList()

        assertThat(enrichedItems).hasSize(2)
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 0)).isEqualTo(listOf("value_1"))
        assertThat(extractEnrichedValuesFromRow(enrichedItems, 1)).isEqualTo(emptyList<String>())
    }

    private fun createTestDf(vararg rows: TestRow): Dataset<Row> {
        return spark.createDataFrame(rows.toList(), TestRow::class.java)
    }

    private fun extractEnrichedValuesFromRow(ds: List<Row>, rowNumber: Int): List<String> {
        return extractEnrichedValuesFromRow(ds, rowNumber, "attribute_1")
    }

    private fun extractEnrichedValuesFromRow(ds: List<Row>, rowNumber: Int, colName: String): List<String> {
        val index = ds[rowNumber].fieldIndex(colName)
        return ds[rowNumber].getList<String>(index).toList()
    }

    data class TestRow(
        val source_code: String,
        val source_name: String?,
        val source_icd_code: Array<String> = emptyArray()
    )
}