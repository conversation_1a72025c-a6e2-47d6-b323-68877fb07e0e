package com.prospection.refdata.items.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.common.consts.CodingSystems.FDB_DRUG
import com.prospection.refdata.common.integration.S3GeneratePublicUrlAdapter
import com.prospection.refdata.common.integration.S3ImportExportHelper
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.common.integration.S3SparkImportExportHelper
import com.prospection.refdata.config.S3Path
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.config.S3Path.RAW_UPLOAD
import com.prospection.refdata.config.S3Path.SNAPSHOTS
import org.apache.spark.sql.RowFactory
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.types.ArrayType
import org.apache.spark.sql.types.DataTypes
import org.apache.spark.sql.types.Metadata
import org.apache.spark.sql.types.StructField
import org.apache.spark.sql.types.StructType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.springframework.beans.factory.annotation.Autowired
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.model.S3Exception
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import java.time.LocalDateTime
import java.util.zip.ZipInputStream

class ItemsAwsAdapterTest : AbstractIntegrationTest() {
    companion object {
        private const val TEST_CLASSIFICATION = "test-classification"
        private const val VERSION1 = "2021-11-16T04:58:33.046Z"
        private const val VERSION2 = "2021-11-15T04:58:33.046Z"
        private const val PUBLISH_VERSION = "20210101T000000.000Z"

        private val RAW_ITEMS = listOf(
            TestRow("1", "A", "B", "c"),
            TestRow("2", "A", "B", "c"),
            TestRow("3", "A", "B", "c"),
            TestRow("4", "A", "B", "c"),
            TestRow("5", "a", "b", "C"),
        )
    }

    @Autowired
    private lateinit var om: ObjectMapper

    @Autowired
    private lateinit var s3Presigner: S3Presigner

    private lateinit var itemsAwsAdapterSpy: ItemsAwsAdapter
    private lateinit var mockS3ImportExportDatasetAdapter: S3ImportExportHelper
    private lateinit var mockSparkImportExportHelper: S3SparkImportExportHelper

    @BeforeEach
    override fun setUp() {
        super.setUp()
        mockS3ImportExportDatasetAdapter = Mockito.spy(
            S3ImportExportHelper(
                applicationProperties,
                amazonS3,
                spark,
                om
            )
        )
        mockSparkImportExportHelper = Mockito.spy(
            S3SparkImportExportHelper(
                applicationProperties.s3Bucket,
                amazonS3,
                spark
            )
        )
        itemsAwsAdapterSpy = ItemsAwsAdapter(
            mockS3ImportExportDatasetAdapter,
            mockSparkImportExportHelper,
            ItemsSparkAdapter(mockSparkImportExportHelper),
            S3GeneratePublicUrlAdapter(applicationProperties, s3Presigner),
            om,
        )
    }

    @Test
    fun cleanup_temporary_folder() {
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${S3Path.RAW_UPLOAD_CSV}/")
                .build(),
            RequestBody.fromString("Test,Data")
        )

        val response = amazonS3.getObject { builder ->
            builder.bucket(applicationProperties.s3Bucket).key("${S3Path.RAW_UPLOAD_CSV}/")
        }
        assertEquals("Test,Data", String(response.readAllBytes()))

        itemsAwsAdapterSpy.cleanupTemporaryFolder()

        val exception = assertThrows(S3Exception::class.java) {
            amazonS3.getObject { builder ->
                builder.bucket(applicationProperties.s3Bucket).key("${S3Path.RAW_UPLOAD_CSV}/")
            }
        }

        assertThat(exception.message).contains("The specified key does not exist.")
    }

    @Test
    fun upload_temporary_items() {
        val csvContent = "code,name,icd_code,subjects\n\rA1,B1,C1;C2,[Java|C++]"

        itemsAwsAdapterSpy.uploadTemporaryItems(csvContent, "Test.csv")

        val content = spark.read().parquet("s3a://${applicationProperties.s3Bucket}/$RAW_UPLOAD").collectAsList()

        assertThat(content[0].getAs<String>("code")).isEqualTo("A1")
        assertThat(content[0].getAs<String>("name")).isEqualTo("B1")
        assertThat(content[0].getAs<String>("icd_code")).isEqualTo("C1;C2")
        assertThat(getAsArray(content[0], "subjects")).isEqualTo(arrayOf("Java", "C++"))
    }

    @Test
    fun write_draft_items() {
        val rawItemsSchema = StructType(
            listOf(
                StructField("code", DataTypes.StringType, false, Metadata.empty()),
                StructField("icd_code", ArrayType(DataTypes.StringType, true), false, Metadata.empty()),
                StructField("name", DataTypes.StringType, false, Metadata.empty()),
            ).toTypedArray()
        )

        val rawItemsDF = spark.createDataFrame(
            listOf(
                RowFactory.create("1", arrayOf("C1"), "I1"),
                RowFactory.create("2", arrayOf("C2", "C2.2"), "I2"),
            ), rawItemsSchema
        )

        itemsAwsAdapterSpy.writeDraftItems(FDB_DRUG, rawItemsDF)

        val dataset = spark.read().parquet("s3a://${applicationProperties.s3Bucket}/${Items.Draft.RAW_ITEMS}/$FDB_DRUG")

        assertArrayEquals(arrayOf("source_code", "source_icd_code", "source_name"), dataset.columns())
        assertEquals(DataTypes.StringType, dataset.schema().find { it.name().equals("source_code") }.get().dataType())
        assertEquals(DataTypes.StringType, dataset.schema().find { it.name().equals("source_name") }.get().dataType())
        assertEquals(
            ArrayType(DataTypes.StringType, true),
            dataset.schema().find { it.name().equals("source_icd_code") }.get().dataType()
        )
    }

    @Test
    fun get_change_summaries_should_return_null_if_no_published_version() {
        Assertions.assertNull(
            itemsAwsAdapterSpy.getChangeSummariesUrl(
                PUBLISH_VERSION,
                setOf(TEST_CLASSIFICATION),
                LocalDateTime.now()
            )
        )
    }

    @Test
    fun get_change_summaries_should_return_null_if_no_change_summaries() {
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Published.ENRICHED_ITEMS_PARQUET}/${createVersionPath(VERSION1)}/test")
                .build(),
            RequestBody.fromString("")
        )

        Assertions.assertNull(
            itemsAwsAdapterSpy.getChangeSummariesUrl(
                PUBLISH_VERSION,
                setOf(TEST_CLASSIFICATION),
                LocalDateTime.now()
            )
        )
    }

    @Test
    fun `change summary zip file should not be generated if there is no dataset difference for all classifications`() {
        // mock classification
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Draft.RAW_ITEMS}/${TEST_CLASSIFICATION}/test")
                .build(),
            RequestBody.fromString("")
        )

        // mock publish version
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Published.ENRICHED_ITEMS_PARQUET}/${createVersionPath(VERSION1)}/test")
                .build(),
            RequestBody.fromString("")
        )

        // no difference as both published and enriched items as empty dataframe
        doReturn(spark.emptyDataFrame()).`when`(mockSparkImportExportHelper).readParquet(any())

        Assertions.assertNull(
            itemsAwsAdapterSpy.getChangeSummariesUrl(
                PUBLISH_VERSION,
                setOf(TEST_CLASSIFICATION),
                LocalDateTime.now()
            )
        )
    }

    @Test
    fun `change summary zip should contain file for classification with difference for latest publisehd version`() {
        // mock classification
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Draft.RAW_ITEMS}/${TEST_CLASSIFICATION}/test")
                .build(),
            RequestBody.fromString("")
        )
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Draft.RAW_ITEMS}/no-diff-classification/test")
                .build(),
            RequestBody.fromString("")
        )

        // mock latest publish version
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Published.ENRICHED_ITEMS_PARQUET}/${createVersionPath(VERSION1)}/test")
                .build(),
            RequestBody.fromString("")
        )

        // mock previous published version and associated change summary
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Published.ENRICHED_ITEMS_PARQUET}/${createVersionPath(VERSION2)}/test")
                .build(),
            RequestBody.fromString("")
        )
        amazonS3.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Draft.CHANGE_SUMMARY}/change-summary-${VERSION2}.zip")
                .build(),
            RequestBody.fromString("")
        )

        // no difference as both published and enriched items are mocked as empty dataframe
        doReturn(spark.emptyDataFrame()).`when`(mockSparkImportExportHelper)
            .readParquet("${Items.Published.ENRICHED_ITEMS_PARQUET}/${createVersionPath(VERSION1)}/no-diff-classification")
        doReturn(spark.emptyDataFrame()).`when`(mockSparkImportExportHelper)
            .readParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/no-diff-classification/")

        // Mock changed data for test classification
        doReturn(
            spark.createDataFrame(
                listOf(
                    ChangeSummaryComparisonTest.ChangeSummaryRow("2", "A2", "B2", "C2", "D2")
                ),
                ChangeSummaryComparisonTest.ChangeSummaryRow::class.java
            )
        )
            .`when`(mockSparkImportExportHelper)
            .readParquet("${Items.Published.ENRICHED_ITEMS_PARQUET}/${TEST_CLASSIFICATION}/${createVersionPath(VERSION1)}")
        doReturn(
            spark.createDataFrame(
                listOf(
                    ChangeSummaryComparisonTest.ChangeSummaryRow("2", "A21", "B2", "C2", "D2")
                ),
                ChangeSummaryComparisonTest.ChangeSummaryRow::class.java
            )
        )
            .`when`(mockSparkImportExportHelper)
            .readParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/${TEST_CLASSIFICATION}/")


        Assertions.assertNotNull(
            itemsAwsAdapterSpy.getChangeSummariesUrl(
                VERSION1,
                setOf(TEST_CLASSIFICATION),
                LocalDateTime.now()
            )
        )

        // check zip file content
        ZipInputStream(
            amazonS3.getObject { builder ->
                builder.bucket(applicationProperties.s3Bucket)
                    .key("${Items.Draft.CHANGE_SUMMARY}/change-summary.zip")
            }.readAllBytes().inputStream()
        ).use { zipInputStream ->
            val firstEntry = zipInputStream.nextEntry
            assertEquals("${TEST_CLASSIFICATION}.csv", firstEntry?.name)
        }
    }

    @Test
    fun `should has publish items folder when publishing items successfully`() {
        val publishedItemsPrefix =
            "${Items.Published.ENRICHED_ITEMS_PARQUET}/${createVersionPath(PUBLISH_VERSION)}/${TEST_CLASSIFICATION}"

        // Import raw items to enriched items folder
        exportToParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/${TEST_CLASSIFICATION}", RAW_ITEMS)

        // Publish items
        itemsAwsAdapterSpy.publishItems(PUBLISH_VERSION, setOf(TEST_CLASSIFICATION))

        // Check publish items folder
        val response = amazonS3.listObjectsV2(
            ListObjectsV2Request.builder()
                .bucket(applicationProperties.s3Bucket)
                .prefix(publishedItemsPrefix)
                .build()
        )
        assertEquals(publishedItemsPrefix, response.prefix())
    }

    @Test
    fun `should return raw upload file`() {
        val mockDs = spark.createDataFrame(listOf(RAW_ITEMS[0]), TestRow::class.java)
        mockDs.write().option("header", "true").parquet("s3a://${applicationProperties.s3Bucket}/${RAW_UPLOAD}")

        val resultDs = itemsAwsAdapterSpy.getRawUpload()
        assertEquals(mockDs.collectAsList(), resultDs.collectAsList())
    }

    @Test
    fun `should write snapshot file with correct version`() {
        val mockDs = spark.createDataFrame(RAW_ITEMS, TestRow::class.java)
        itemsAwsAdapterSpy.writeSnapshot(mockDs, VERSION1, TEST_CLASSIFICATION)

        val snapshotPath =
            "s3a://${applicationProperties.s3Bucket}/${SNAPSHOTS}/${TEST_CLASSIFICATION}/timestamp=${VERSION1}"
        val resultDs = spark.read().option("header", "true").parquet(snapshotPath)
        assertEquals((mockDs).collectAsList(), resultDs.collectAsList())
    }

    private inline fun <reified T> exportToParquet(path: String, rows: List<T>) {
        val ds = spark.createDataFrame(rows, T::class.java)

        ds.write().mode(SaveMode.Overwrite)
            .parquet("s3a://${applicationProperties.s3Bucket}/${path}")
    }

    data class TestRow(val source_code: String, val source_a: String, val source_b: String, val source_c: String)
}