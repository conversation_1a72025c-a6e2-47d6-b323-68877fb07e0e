package com.prospection.refdata.items.application.rest

import com.prospection.refdata.AbstractControllerIntegrationTest
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.codingsystem.integration.CodingSystemToClassificationEntity
import com.prospection.refdata.items.application.rest.dto.ItemsCSVUploadDto
import com.prospection.refdata.items.integration.PublishedItemVersionEntity
import com.prospection.refdata.items.integration.PublishedItemVersionJpaRepository
import com.prospection.refdata.items.integration.RawItemsMetadataEntity
import com.prospection.refdata.items.integration.RawItemsMetadataJpaRepository
import org.hamcrest.Matchers.containsString
import org.hamcrest.Matchers.hasSize
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class ItemsControllerTest : AbstractControllerIntegrationTest() {

    companion object {
        private const val TEST_CLASSIFICATION = "DRG Diagnosis"
        private const val BASE_URL = "/api/ref-data-v2"
        private const val ITEM_IMPORT_URL = BASE_URL + "/classifications/${TEST_CLASSIFICATION}/items/import"
    }

    @Autowired
    private lateinit var rawItemsMetadataJpaRepository: RawItemsMetadataJpaRepository

    @Autowired
    private lateinit var codingSystemJpaRepository: CodingSystemJpaRepository

    @Autowired
    private lateinit var publishedItemVersionJpaRepository: PublishedItemVersionJpaRepository

    @Test
    fun `should response error when csv string is empty`() {
        val body = ItemsCSVUploadDto(csv = "", totalChunk = 1, currentChunk = 1)
        performPut(ITEM_IMPORT_URL, body)
            .andExpect(status().is4xxClientError)
    }

    @Test
    fun `should response error when the current chunk is greater than the total chunk `() {
        val body = ItemsCSVUploadDto(csv = "test-csv", totalChunk = 1, currentChunk = 2)
        performPut(ITEM_IMPORT_URL, body)
            .andExpect(status().is4xxClientError)
    }

    @Test
    fun `should response metadata when call get items metadata `() {
        val now: LocalDateTime = LocalDateTime.now()
        val codingSystemName = "Coding System"
        val classification = "Classification"
        val exportCode = "source_code"

        val codingSystemEntity = CodingSystemEntity(name = codingSystemName)
        val codingSystemToClassificationEntity = CodingSystemToClassificationEntity(
            classification = classification,
            codingSystem = codingSystemEntity,
            codingSystemColumnToExport = exportCode
        )
        codingSystemEntity.codingSystemToClassifications = listOf(codingSystemToClassificationEntity)
        val codingSystem = codingSystemJpaRepository.save(codingSystemEntity)

        rawItemsMetadataJpaRepository.save(
            RawItemsMetadataEntity(
                codingSystem = codingSystem,
                sourceAttributes = emptyList(),
                newItem = 5,
                totalItem = 10,
                deletedItem = 0,
                createdBy = "admin",
                createdAt = now
            )
        )

        performGet("$BASE_URL/items/metadata")
            .andExpect(status().isOk)
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.[0].newItem", `is`(5)))
            .andExpect(jsonPath("$.[0].totalItem", `is`(10)))
            .andExpect(jsonPath("$.[0].sourceAttributes", hasSize<String>(0)))
            .andExpect(jsonPath("$.[0].codingSystem", `is`(codingSystemName)))
            .andExpect(jsonPath("$.[0].mappedClassifications[0].classification", `is`(classification)))
            .andExpect(jsonPath("$.[0].mappedClassifications[0].codingSystemColumnToExport", `is`(exportCode)))
            .andExpect(jsonPath("$.[0].createdAt", containsString(now.format(DateTimeFormatter.ISO_DATE_TIME))))
    }

    @Test
    fun `should response last published version when call get items version latest `() {

        publishedItemVersionJpaRepository.saveAll(
            listOf(
                PublishedItemVersionEntity(
                    publishedVersion = "20221014T080229Z",
                    publishedBy = "trung.mai",
                    publishedAt = LocalDateTime.parse("2022-10-14T08:02:29"),
                ),
                PublishedItemVersionEntity(
                    publishedVersion = "20221013T080229Z",
                    publishedBy = "trung.mai",
                    publishedAt = LocalDateTime.parse("2022-10-13T08:02:29"),
                )
            )
        )

        performGet("$BASE_URL/items/version/latest")
            .andExpect(status().isOk)
            .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
            .andExpect(jsonPath("$.id", `is`("20221014T080229Z")))
            .andExpect(jsonPath("$.publishedBy", `is`("trung.mai")))
            .andExpect(jsonPath("$.publishedDate", `is`("2022-10-14T08:02:29Z")))

    }
}
