package com.prospection.refdata.items.domain

import TestSpark.spark
import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.domain.MappedClassification
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.common.domain.S3ItemsExporterForExternalToolPort
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.common.integration.GenerateDataUpdateReportHelper
import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.ItemGroupPort
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.rules.domain.EnrichedAttribute
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.EnrichmentRulePort
import com.prospection.refdata.rules.domain.EnrichmentRuleService
import com.prospection.refdata.rules.domain.PublishEnrichmentRulePort
import com.prospection.refdata.rules.domain.Rule
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

class ItemsServiceTest {
    companion object {
        private val PREVIEWED_ENRICHED_ATTRIBUTE_VALUE = EnrichedAttributeValue(
            value = "enriched_value",
            id = "enriched_value",
            enrichedAttribute = EnrichedAttribute(
                name = "enriched_attribute",
                id = "enriched_attribute"
            )
        )
    }

    private val itemsPort = mock(ItemsPort::class.java)
    private val enrichmentRuleService = mock(EnrichmentRuleService::class.java)
    private val enrichmentRulePort = mock(EnrichmentRulePort::class.java)
    private val userPort = mock(UserPort::class.java)
    private val dateTimePort = mock(DateTimePort::class.java)
    private val publishEnrichmentRulePort = mock(PublishEnrichmentRulePort::class.java)
    private val codingSystemPort = mock(CodingSystemPort::class.java)
    private val jobPort = mock(JobPort::class.java)
    private val publishItemVersionPort = mock(PublishItemVersionPort::class.java)
    private val rawItemsMetadataPort = mock(RawItemsMetadataPort::class.java)
    private val enrichedItemsMetadataPort = mock(EnrichedItemsMetadataPort::class.java)
    private val itemGroupPort = mock(ItemGroupPort::class.java)
    private val applyItemGroupRulePort = mock(ApplyItemGroupRulePort::class.java)
    private val generateDataUpdateReportHelper = mock(GenerateDataUpdateReportHelper::class.java)
    private val s3ItemsExporterForExternalToolPort = mock(S3ItemsExporterForExternalToolPort::class.java)

    private val service = spy(
        ItemsService(
            itemsPort,
            enrichmentRuleService,
            enrichmentRulePort,
            userPort,
            dateTimePort,
            publishEnrichmentRulePort,
            codingSystemPort,
            jobPort,
            publishItemVersionPort,
            rawItemsMetadataPort,
            enrichedItemsMetadataPort,
            itemGroupPort,
            applyItemGroupRulePort,
            generateDataUpdateReportHelper,
            s3ItemsExporterForExternalToolPort
        )
    )

    private lateinit var publishedItemVersion: PublishedItemVersion

    @AfterEach
    fun clear() {
        reset(itemsPort)
        reset(enrichmentRuleService)
        reset(enrichmentRulePort)
        reset(userPort)
        reset(dateTimePort)
        reset(publishEnrichmentRulePort)
        reset(codingSystemPort)
        reset(jobPort)
        reset(publishItemVersionPort)
        reset(rawItemsMetadataPort)
        reset(s3ItemsExporterForExternalToolPort)

        reset(service)
    }

    @BeforeEach
    fun setup() {
        doReturn("thomas").whenever(userPort).getCurrentUserId()
        publishedItemVersion = PublishedItemVersion(
            publishedVersion = "test-version",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )
        doReturn(publishedItemVersion).whenever(publishItemVersionPort).getLatestPublishedItemVersion()
    }

    @Test
    fun `should call cleanupTemporaryFolder when import the first chunk`() {
        val codingSystem = "test-coding-system"
        val itemsImport = ItemsImport(
            csv = "test-csv",
            totalChunk = 2,
            currentChunk = 1,
            classification = codingSystem
        )
        service.import(itemsImport)
        verify(itemsPort).cleanupTemporaryFolder()
        verify(itemsPort).uploadTemporaryItems(eq(itemsImport.csv), any())
    }

    @Test
    fun `should call writeDraftItems when import the last chunk`() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()

        val codingSystem = CodingSystem(name = "test-classification")
        doReturn(codingSystem).whenever(codingSystemPort).findByName(any())

        val mockRawUpload: Dataset<Row> = mock()
        doReturn(mockRawUpload).whenever(itemsPort).getRawUpload()

        val itemsImport = ItemsImport(
            csv = "test-csv",
            totalChunk = 2,
            currentChunk = 2,
            classification = codingSystem.name
        )
        service.import(itemsImport)
        verify(itemsPort, times(0)).cleanupTemporaryFolder()
        verify(itemsPort).uploadTemporaryItems(eq(itemsImport.csv), any())

        val mockVersion = "20221001"
        service.processAfterImport(itemsImport.classification, "thomas", mockVersion, true)
        verify(itemsPort).getRawUpload()
        verify(itemsPort).writeSnapshot(mockRawUpload, mockVersion, itemsImport.classification)
        verify(s3ItemsExporterForExternalToolPort).storeExternal(itemsImport.classification, mockRawUpload)
        verify(itemsPort).writeDraftItems(eq(itemsImport.classification), eq(mockRawUpload))
        verify(rawItemsMetadataPort).saveDraftMetadata(eq(null), eq(null), any(), eq("thomas"), any())
    }

    @Test
    fun `should not call writeSnapshot when processAfterImport where storeSnapshot is false`() {
        val mockRawUpload: Dataset<Row> = mock()
        doReturn(mockRawUpload).whenever(itemsPort).getRawUpload()

        service.processAfterImport("coding-system", "peterN", "version", false)
        verify(itemsPort, times(1)).getRawUpload()
        verify(itemsPort, times(0)).writeSnapshot(any(), any(), any())
        verify(s3ItemsExporterForExternalToolPort).storeExternal("coding-system", mockRawUpload)
    }

    @Test
    fun `Should try to re-enrich items while generating change summary`() {
        doReturn(setOf("a", "b")).whenever(codingSystemPort).findAll()

        service.generateChangeSummary("thomas")

        verify(service, times(1)).enrichItemsIfObsolete("a", "thomas")
        verify(service, times(1)).enrichItemsIfObsolete("b", "thomas")
    }

    @Test
    fun `Should try to re-enrich items while downloading all items`() {
        doReturn(null)
            .whenever(itemsPort).getEnrichedItemsDownloadUrl(any(), any())

        service.getEnrichedItemsDownloadUrl("a", "thomas")

        verify(service, times(1)).enrichItemsIfObsolete("a", "thomas")
    }

    @Test
    fun `Should try to re-enrich items while publishing`() {
        doReturn(setOf("a", "b")).whenever(codingSystemPort).findAll()

        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        doReturn("whatever").whenever(dateTimePort).toPublishedVersionString(any())

        service.publish("thomas")

        verify(service, times(1)).enrichItemsIfObsolete("a", "thomas")
        verify(service, times(1)).enrichItemsIfObsolete("b", "thomas")
    }

    @Test
    fun `Should persist the published item version while publishing`() {
        val now: LocalDateTime = LocalDateTime.now()
        val publishedVersion = "test-version"
        val userId = "thomas"

        doReturn(now).whenever(dateTimePort).now()
        doReturn(publishedVersion)
            .whenever(dateTimePort)
            .toPublishedVersionString(eq(now))

        service.publish(userId)

        verify(publishItemVersionPort, times(1)).savePublishedVersion(
            eq(
                PublishedItemVersion(
                    publishedVersion = publishedVersion,
                    publishedAt = now,
                    publishedBy = userId
                )
            )
        )
    }

    @Test
    fun `should write downloadable enriched items when download enriched items of a classification`() {
        doReturn(null).whenever(itemsPort).getEnrichedItemsDownloadUrl(any(), any())

        service.getEnrichedItemsDownloadUrl("a", "tester")

        verify(itemsPort, times(1)).writeDownloadableEnrichedItems("a")
    }

    @Test
    fun `should apply and evaluate the enrichment rule if it contains matching coding system`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        val enrichmentRuleCodingSystemA = EnrichmentRule(
            enrichedAttributeValue = PREVIEWED_ENRICHED_ATTRIBUTE_VALUE,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = codingSystemA, operator = "="),
                        Rule(field = "source_name", value = "a", operator = "=")
                    ),
                    combinator = "and"
                )
            )
        )
        val enrichmentRuleCodingSystemB = EnrichmentRule(
            enrichedAttributeValue = PREVIEWED_ENRICHED_ATTRIBUTE_VALUE,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = codingSystemB, operator = "="),
                        Rule(field = "source_name", value = "b", operator = "=")
                    ),
                    combinator = "and"
                )
            ),
            deleted = 0,
        )
        doReturn(listOf(enrichmentRuleCodingSystemA, enrichmentRuleCodingSystemB)).whenever(enrichmentRuleService)
            .list()
        doReturn(true).whenever(itemsPort).doesRawItemsExist(codingSystemA)
        doReturn(spark.emptyDataFrame()).whenever(itemsPort).applyRules(any(), any())
        doReturn(CodingSystem(name = codingSystemA)).whenever(codingSystemPort).findByName(any())
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        service.enrichItems(codingSystemA, "tester")
        verify(itemsPort).applyRules(eq(codingSystemA), eq(listOf(enrichmentRuleCodingSystemA)))
    }

    @Test
    fun `should apply and evaluate the enrichment rule if no coding system is specified`() {
        val codingSystemA = "A"
        val enrichmentRuleCodingSystemA = EnrichmentRule(
            enrichedAttributeValue = PREVIEWED_ENRICHED_ATTRIBUTE_VALUE,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "source_name", value = "Other Source Name", operator = "=")
                    ),
                    combinator = "and"
                )
            )
        )
        doReturn(listOf(enrichmentRuleCodingSystemA)).whenever(enrichmentRuleService)
            .list()
        doReturn(true).whenever(itemsPort).doesRawItemsExist(codingSystemA)
        doReturn(spark.emptyDataFrame()).whenever(itemsPort).applyRules(any(), any())
        doReturn(CodingSystem(name = codingSystemA)).whenever(codingSystemPort).findByName(any())
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        service.enrichItems(codingSystemA, "tester")
        verify(itemsPort).applyRules(eq(codingSystemA), eq(listOf(enrichmentRuleCodingSystemA)))
    }

    @Test
    fun `should not apply and evaluate the enrichment rule if different coding system is specified`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        val enrichmentRule = EnrichmentRule(
            enrichedAttributeValue = PREVIEWED_ENRICHED_ATTRIBUTE_VALUE,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = codingSystemB, operator = "="),
                        Rule(field = "source_name", value = "Other Source Name", operator = "=")
                    ),
                    combinator = "and"
                )
            )
        )
        doReturn(listOf(enrichmentRule)).whenever(enrichmentRuleService)
            .list()
        doReturn(true).whenever(itemsPort).doesRawItemsExist(codingSystemA)
        doReturn(spark.emptyDataFrame()).whenever(itemsPort).applyRules(any(), any())
        doReturn(CodingSystem(name = codingSystemA)).whenever(codingSystemPort).findByName(any())
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()

        service.enrichItems(codingSystemA, "tester")

        verify(itemsPort).applyRules(eq(codingSystemA), eq(listOf()))
    }

    @Test
    fun `should apply enrichment rule if it is top level OR that specifies coding System and can resolve to any coding System`() {
        val codingSystemA = "A"
        val codingSystemB = "B"
        val enrichmentRule = EnrichmentRule(
            enrichedAttributeValue = PREVIEWED_ENRICHED_ATTRIBUTE_VALUE,
            rule = HasRule.objectMapper.writeValueAsString(
                Rule(
                    rules = listOf(
                        Rule(field = "coding_system", value = codingSystemA, operator = "="),
                        Rule(field = "source_name", value = "Other Source Name", operator = "=")
                    ),
                    combinator = "or"
                )
            )
        )

        doReturn(listOf(enrichmentRule)).whenever(enrichmentRuleService)
            .list()
        doReturn(true).whenever(itemsPort).doesRawItemsExist(codingSystemA)
        doReturn(true).whenever(itemsPort).doesRawItemsExist(codingSystemB)
        doReturn(spark.emptyDataFrame()).whenever(itemsPort).applyRules(any(), any())
        doReturn(CodingSystem(name = codingSystemA)).whenever(codingSystemPort).findByName(eq(codingSystemA))
        doReturn(CodingSystem(name = codingSystemB)).whenever(codingSystemPort).findByName(eq(codingSystemB))
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()

        // Coding System A has a rule that references it so it should apply
        service.enrichItems(codingSystemA, "tester")
        verify(itemsPort).applyRules(eq(codingSystemA), eq(listOf(enrichmentRule)))

        // Coding System B does not have a rule that references it however the TOP level OR that contains a Rule
        // that does not specify a coding system means it can potentially apply to any coding system
        service.enrichItems(codingSystemB, "tester")
        verify(itemsPort).applyRules(eq(codingSystemB), eq(listOf(enrichmentRule)))
    }

    @Test
    fun `should return all draft metadata when get get raw items metadata`() {
        val now: LocalDateTime = LocalDateTime.now()
        val rawItemsMetadata: List<RawItemsMetadata> = listOf(
            RawItemsMetadata(
                codingSystem = CodingSystem(
                    name = "Coding System name",
                    mappedClassifications = listOf(
                        MappedClassification(
                            "Classification name",
                            "export_code_name"
                        )
                    ),
                    country = "AU"
                ),
                sourceAttributes = emptyList(),
                totalItem = 10,
                newItem = 5,
                deletedItem = 0,
                createdBy = "admin",
                createdAt = now
            )
        )

        doReturn(rawItemsMetadata).whenever(rawItemsMetadataPort).getAllDraftMetadata()

        val items = service.getRawItemsMetadata()
        assertEquals(rawItemsMetadata, items)

        verify(rawItemsMetadataPort, times(1)).getAllDraftMetadata()
    }

    @Test
    fun `should return latest published version when get latest published items version`() {

        val latestVersion = PublishedItemVersion(
            publishedVersion = "20221014T080229Z",
            publishedBy = "trung.mai",
            publishedAt = LocalDateTime.parse("2022-10-14T08:02:29")
        )

        doReturn(latestVersion).whenever(publishItemVersionPort).getLatestPublishedItemVersion()

        val items = service.getLatestPublishedItemsVersion()
        assertEquals(latestVersion, items)

        verify(publishItemVersionPort, times(1)).getLatestPublishedItemVersion()
    }

    @Test
    fun `should throw exception when get latest published items version if items have never been published `() {

        doReturn(null).whenever(publishItemVersionPort).getLatestPublishedItemVersion()

        val exception = assertThrows(RuntimeException::class.java) {
            service.getLatestPublishedItemsVersion()
        }

        assertThat(exception.message).contains("Items have never been published")

        verify(publishItemVersionPort, times(1)).getLatestPublishedItemVersion()
    }

    @Test
    fun `throw an exception if coding system does not have enough two latest snapshot versions to compare`() {
        val codingSystem = "PBS Drug"
        doReturn(null).whenever(itemsPort).getSecondLatestSnapshotRawItems(eq(codingSystem))

        Assertions.assertThatThrownBy { service.generateDataUpdateReport(codingSystem) }
            .isInstanceOf(RuntimeException::class.java)
            .hasMessageContaining("$codingSystem does not have enough two latest snapshot versions to compare")
    }
}
