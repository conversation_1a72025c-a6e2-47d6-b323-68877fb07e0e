package com.prospection.refdata.items.domain

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.codingsystem.integration.CodingSystemToClassificationEntity
import com.prospection.refdata.codingsystem.integration.mapper.CodingSystemEntityMapper
import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.HasRule
import com.prospection.refdata.common.domain.S3ItemsExporterForExternalToolPort
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.common.integration.GenerateDataUpdateReportHelper
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.common.integration.datasetrow.ItemGroupToTopicAndSubscriptionRow
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.config.S3Path.SNAPSHOTS
import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.ItemGroupPort
import com.prospection.refdata.itemgroups.integration.ItemGroupEntity
import com.prospection.refdata.itemgroups.integration.ItemGroupJpaRepository
import com.prospection.refdata.items.integration.PublishedItemVersionEntity
import com.prospection.refdata.items.integration.PublishedItemVersionJpaRepository
import com.prospection.refdata.items.integration.RawItemsMetadataEntity
import com.prospection.refdata.items.integration.RawItemsMetadataJpaRepository
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.job.domain.JobStatus
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.EnrichmentRulePort
import com.prospection.refdata.rules.domain.EnrichmentRuleService
import com.prospection.refdata.rules.domain.PublishEnrichmentRulePort
import com.prospection.refdata.rules.domain.Rule
import com.prospection.refdata.rules.integration.EnrichedAttributeEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeJpaRepository
import com.prospection.refdata.rules.integration.EnrichedAttributeValueEntity
import com.prospection.refdata.rules.integration.EnrichedAttributeValueEntityMapper
import com.prospection.refdata.rules.integration.EnrichedAttributeValueJpaRepository
import com.prospection.refdata.rules.integration.EnrichmentRuleJpaRepository
import org.apache.spark.sql.Column
import org.apache.spark.sql.Encoders
import org.apache.spark.sql.Row
import org.apache.spark.sql.RowFactory
import org.apache.spark.sql.types.ArrayType
import org.apache.spark.sql.types.DataTypes.StringType
import org.apache.spark.sql.types.Metadata
import org.apache.spark.sql.types.StructField
import org.apache.spark.sql.types.StructType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.reset
import org.mockito.Mockito.spy
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.transaction.annotation.Transactional
import org.testcontainers.shaded.org.yaml.snakeyaml.util.UriEncoder
import java.time.LocalDateTime
import java.util.*

@Transactional
internal class ItemsServiceIntegrationTest : AbstractIntegrationTest() {
    companion object {
        private const val CODING_SYSTEM = "Test Coding System"
        private const val CLASSIFICATION = "Test Classification"
        private const val ENRICHED_ATTRIBUTE_VALUE = "enriched attribute value"
        private const val ITEM_GROUP_KEY = "item_group_key"
        private val ITEM_GROUPS_TO_TOPICS_AND_SUBSCRIPTIONS = listOf(
            ItemGroupToTopicAndSubscriptionRow(
                item_group = "test_item_group",
                topic_name = "test_topic_name",
                workflow_status = "GENERATED",
                condition = "test_condition",
                therapy_area = "test_therapy_area",
                subscription_id = "test_sub_id",
                subscription_name = "test_sub_name",
                subscription_record_id = "test_sub_record_id",
                topic_id = 1L
            )
        )
        private lateinit var enrichmentRule: EnrichmentRule
    }

    @Autowired
    private lateinit var enrichedAttributeValueEntityMapper: EnrichedAttributeValueEntityMapper

    @Autowired
    private lateinit var enrichmentRuleService: EnrichmentRuleService

    @Autowired
    private lateinit var enrichedAttributeJpaRepository: EnrichedAttributeJpaRepository

    @Autowired
    private lateinit var enrichedAttributeValueJpaRepository: EnrichedAttributeValueJpaRepository

    @Autowired
    private lateinit var enrichmentRuleJpaRepository: EnrichmentRuleJpaRepository

    @Autowired
    private lateinit var rawItemsMetadataJpaRepository: RawItemsMetadataJpaRepository

    @Autowired
    private lateinit var userPort: UserPort

    @Autowired
    private lateinit var itemsPort: ItemsPort

    @Autowired
    private lateinit var itemGroupPort: ItemGroupPort

    @Autowired
    private lateinit var applyItemGroupRulePort: ApplyItemGroupRulePort

    @MockitoSpyBean
    private lateinit var rawItemsMetadataPort: RawItemsMetadataPort

    @MockitoSpyBean
    private lateinit var enrichedItemsMetadataPort: EnrichedItemsMetadataPort

    @Autowired
    private lateinit var codingSystemRepository: CodingSystemJpaRepository

    @Autowired
    private lateinit var codingSystemEntityMapper: CodingSystemEntityMapper

    private val dateTimePort: DateTimePort = mock()

    @Autowired
    private lateinit var codingSystemPort: CodingSystemPort

    private val enrichmentRulePort: EnrichmentRulePort = mock()

    private val publishEnrichmentRulePort: PublishEnrichmentRulePort = mock()

    @Autowired
    private lateinit var jobPort: JobPort

    @Autowired
    private lateinit var publishItemVersionPort: PublishItemVersionPort

    @Autowired
    private lateinit var publishedItemVersionJpaRepository: PublishedItemVersionJpaRepository

    @Autowired
    private lateinit var itemGroupJpaRepository: ItemGroupJpaRepository

    @Autowired
    private lateinit var generateDataUpdateReportHelper: GenerateDataUpdateReportHelper

    private lateinit var spyGenerateDataUpdateReportHelper: GenerateDataUpdateReportHelper

    @Autowired
    private lateinit var s3ItemsExporterForExternalToolPort: S3ItemsExporterForExternalToolPort

    private lateinit var itemsService: ItemsService

    private lateinit var codingSystem: CodingSystem

    private lateinit var codingSystemEntity: CodingSystemEntity

    private lateinit var enrichedAttributeEntity: EnrichedAttributeEntity

    @BeforeEach
    fun init() {
        enrichedAttributeEntity = EnrichedAttributeEntity(
            uuid = "attr_uuid1",
            name = "therapy_area",
        ).let { enrichedAttributeJpaRepository.save(it) }

        val enrichedAttributeValueEntity = EnrichedAttributeValueEntity(
            uuid = "value_uuid1",
            value = "MM",
            enrichedAttribute = enrichedAttributeEntity,
        ).let { enrichedAttributeValueJpaRepository.save(it) }

        enrichmentRule = enrichmentRuleService.create(
            EnrichmentRule(
                enrichedAttributeValue = enrichedAttributeValueEntityMapper.toDomain(enrichedAttributeValueEntity),
                lastModifiedBy = "thomas",
                lastModifiedAt = LocalDateTime.now(),
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = listOf(
                            Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = CODING_SYSTEM, operator = "="),
                            Rule(field = "source_name", value = "abc", operator = "="),
                        ),
                        combinator = "and"
                    ),
                ),
            )
        )

        codingSystemEntity = CodingSystemEntity(name = CODING_SYSTEM, country = "AU,US")
        val codingSystemToClassificationEntity = CodingSystemToClassificationEntity(
            classification = CLASSIFICATION,
            codingSystem = codingSystemEntity,
            codingSystemColumnToExport = "source_code",
        )

        codingSystemEntity.codingSystemToClassifications = listOf(codingSystemToClassificationEntity)

        codingSystem = codingSystemEntityMapper.toDomain(
            codingSystemRepository.save(codingSystemEntity)
        )

        val draftItems = listOf(
            RowFactory.create("1", "abc"),
            RowFactory.create("2", "def"),
        )

        val itemSchema = StructType(
            listOf(
                StructField("source_code", StringType, false, Metadata.empty()),
                StructField("source_name", StringType, false, Metadata.empty()),
            ).toTypedArray()
        )

        val draftItemsDataFrame = spark.createDataFrame(draftItems, itemSchema)
        writeParquet("${Items.Draft.RAW_ITEMS}/$CODING_SYSTEM", draftItemsDataFrame)

        spyGenerateDataUpdateReportHelper = spy(generateDataUpdateReportHelper)

        doReturn(
            spark.createDataset(
                ITEM_GROUPS_TO_TOPICS_AND_SUBSCRIPTIONS, Encoders.bean(
                    ItemGroupToTopicAndSubscriptionRow::class.java
                )
            )
        )
            .`when`(spyGenerateDataUpdateReportHelper).enrichItemGroupsByTopicsAndSubscriptions(any())

        itemsService = spy(
            ItemsService(
                itemsPort,
                enrichmentRuleService,
                enrichmentRulePort,
                userPort,
                dateTimePort,
                publishEnrichmentRulePort,
                codingSystemPort,
                jobPort,
                publishItemVersionPort,
                rawItemsMetadataPort,
                enrichedItemsMetadataPort,
                itemGroupPort,
                applyItemGroupRulePort,
                spyGenerateDataUpdateReportHelper,
                s3ItemsExporterForExternalToolPort
            )
        )
    }

    @AfterEach
    fun clear() {
        reset(dateTimePort)
        reset(enrichmentRulePort)
        reset(publishEnrichmentRulePort)
    }

    @Test
    fun `Should generate the fields of the metadata of enriched items`() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(1).whenever(enrichmentRulePort).getLatestEnrichmentRuleRevisionId()

        itemsService.enrichItems(CODING_SYSTEM, "thomas")

        val metadata = enrichedItemsMetadataPort.getDraftMetadata(codingSystem)

        assertThat(metadata).isNotNull
        metadata!!.let {
            assertThat(it.createdBy).contains("thomas")
            assertThat(it.createdAt).isEqualTo(now)
            assertThat(it.latestEnrichmentRuleRevisionId).isEqualTo(1L)
            assertThat(it.sourceAttributes).containsExactly("source_code", "source_name")
            assertThat(it.enrichedAttributes).containsExactly("therapy_area")
        }
    }

    @Test
    fun `Should enriched item with some column array type base on coding system`() {
        val codingSystemName = "Coding System With Array Column"
        val draftItems = listOf(
            RowFactory.create("A1", "C1", arrayOf("B1", "B1.1")),
            RowFactory.create("A2", "C2", arrayOf("B2"))
        )

        val itemSchema = StructType(
            listOf(
                StructField("source_code", StringType, false, Metadata.empty()),
                StructField("source_name", StringType, false, Metadata.empty()),
                StructField("source_icd_code", ArrayType(StringType, false), false, Metadata.empty()),
            ).toTypedArray()
        )

        writeParquet("${Items.Draft.RAW_ITEMS}/$codingSystemName", spark.createDataFrame(draftItems, itemSchema))

        codingSystemEntityMapper.toDomain(
            codingSystemRepository.save(CodingSystemEntity(name = codingSystemName))
        )

        val enrichedAttributeValueEntity = EnrichedAttributeValueEntity(
            uuid = UUID.randomUUID().toString(),
            value = "Test Value",
            enrichedAttribute = enrichedAttributeEntity,
        ).let { enrichedAttributeValueJpaRepository.save(it) }

        createEnrichmentRule(
            enrichedAttributeValueEntityMapper.toDomain(enrichedAttributeValueEntity),
            listOf(
                Rule(field = CODING_SYSTEM_ATTRIBUTE_NAME, value = codingSystemName, operator = "="),
                Rule(field = "source_code", value = "A1", operator = "="),
                Rule(field = "source_icd_code", value = "B1", operator = "contains"),
            )
        )

        mockCommon()

        itemsService.enrichItems(codingSystemName, "test_user")

        val draftEnrichedItems = readParquet("${Items.Draft.ENRICHED_ITEMS_PARQUET}/${codingSystemName}")
            .collectAsList()
            .sortedBy { it.getAs<String>("source_code") }

        assertThat(draftEnrichedItems).hasSize(2)

        assertThat(draftEnrichedItems[0].getAs<String>("source_code")).isEqualTo("A1")
        assertThat(draftEnrichedItems[0].getAs<String>("source_name")).isEqualTo("C1")
        assertThat(getAsArray(draftEnrichedItems[0], "source_icd_code")).isEqualTo(arrayOf("B1", "B1.1"))
        assertThat(draftEnrichedItems[0].getAs<String>("coding_system")).isEqualTo(codingSystemName)
        assertThat(getAsArray(draftEnrichedItems[0], "therapy_area")).isEqualTo(arrayOf("Test Value"))

        assertThat(getAsArray(draftEnrichedItems[1], "source_icd_code")).isEqualTo(arrayOf("B2"))
        assertThat(getAsArray(draftEnrichedItems[1], "therapy_area")).isEmpty()
    }

    @Test
    fun `Should re-generate the metadata of enriched items if enrichment rule revision is incremented`() {
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        doReturn(1, 2).whenever(enrichmentRulePort).getLatestEnrichmentRuleRevisionId()

        itemsService.enrichItemsIfObsolete(CODING_SYSTEM, "thomas")

        assertThat(enrichedItemsMetadataPort.getDraftMetadata(codingSystem)!!.latestEnrichmentRuleRevisionId).isEqualTo(
            1
        )

        itemsService.enrichItemsIfObsolete(CODING_SYSTEM, "thomas")

        assertThat(enrichedItemsMetadataPort.getDraftMetadata(codingSystem)!!.latestEnrichmentRuleRevisionId).isEqualTo(
            2
        )
    }

    @Test
    fun `Should not re-generate the metadata of enriched items if enrichment rule revision is the same`() {
        doReturn(LocalDateTime.now()).whenever(dateTimePort).now()
        doReturn(1).whenever(enrichmentRulePort).getLatestEnrichmentRuleRevisionId()

        itemsService.enrichItemsIfObsolete(CODING_SYSTEM, "thomas")
        val firstMetadata = enrichedItemsMetadataPort.getDraftMetadata(codingSystem)

        itemsService.enrichItemsIfObsolete(CODING_SYSTEM, "thomas")
        val secondMetadata = enrichedItemsMetadataPort.getDraftMetadata(codingSystem)

        // second call shouldn't trigger enrichItems
        verify(itemsService, times(1)).enrichItems(CODING_SYSTEM, "thomas")
        assertEquals(firstMetadata, secondMetadata)
    }

    @Test
    fun `Should return list coding system`() {
        val rawItemsMetadataEntity = rawItemsMetadataJpaRepository.save(
            RawItemsMetadataEntity(
                totalItem = 20,
                newItem = 10,
                deletedItem = 0,
                codingSystem = codingSystemEntity,
                sourceAttributes = listOf("source_code", "source_jcode"),
                createdBy = "trung.mai",
                createdAt = LocalDateTime.now()
            )
        )

        val metadata = itemsService.getRawItemsMetadata().first()

        assertEquals(rawItemsMetadataEntity.createdAt, metadata.createdAt)
        assertEquals(rawItemsMetadataEntity.createdBy, metadata.createdBy)
        assertEquals(rawItemsMetadataEntity.totalItem, metadata.totalItem)
        assertEquals(rawItemsMetadataEntity.newItem, metadata.newItem)
        assertEquals(rawItemsMetadataEntity.sourceAttributes, metadata.sourceAttributes)
        assertEquals(
            rawItemsMetadataEntity.publishedItemVersion?.publishedVersion,
            metadata.publishedItemVersion?.publishedVersion
        )
        assertEquals(rawItemsMetadataEntity.codingSystem.name, metadata.codingSystem.name)
        assertEquals(rawItemsMetadataEntity.codingSystem.country, metadata.codingSystem.country)
    }

    @Test
    fun `Should run a job if importing items`() {
        val testClassification = "test classification"

        val job = itemsService.queueProcessAfterImport(testClassification)

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Importing $testClassification", JobStatus.RUNNING)
    }

    @Test
    fun `Should run a job if publishing items`() {
        val job = itemsService.queuePublishItemsAndRules()

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Publishing items", JobStatus.RUNNING)
    }

    @Test
    fun `Should run a job if downloading enriched items`() {
        val testClassification = "test classification 1"

        val job = itemsService.queueDownloadingEnrichedItems(testClassification)

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Generating the enriched items of $testClassification", JobStatus.RUNNING)
    }

    @Test
    fun `Should run a job if downloading a change summary`() {
        val job = itemsService.queueDownloadingChangeSummary()

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Generating the change summary of items", JobStatus.RUNNING)
    }

    @Test
    fun `should run a job if generate data update report`() {
        val job = itemsService.queueGenerateDataUpdateReport(CODING_SYSTEM)

        assertThat(job)
            .extracting("name", "status")
            .containsExactly("Generating data update report of $CODING_SYSTEM", JobStatus.RUNNING)
    }

    @Test
    fun `Should increment the published item group version after publishing item groups`() {
        val yesterday = LocalDateTime.now().minusDays(1)
        doReturn(yesterday).whenever(dateTimePort).now()
        doReturn("version-yesterday").whenever(dateTimePort).toPublishedVersionString(yesterday)

        itemsService.publish("whoever")

        assertEquals("version-yesterday", publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion)

        val today = LocalDateTime.now()
        doReturn(today).whenever(dateTimePort).now()
        doReturn("version-today").whenever(dateTimePort).toPublishedVersionString(today)

        itemsService.publish("whoever")

        assertEquals("version-today", publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion)
    }

    @Test
    fun `Should insert the published item version with the correct values`() {
        val testVersion = "test-version"
        val testUser = "test user"

        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(testVersion).whenever(dateTimePort).toPublishedVersionString(now)

        itemsService.publish(testUser)

        assertThat(publishedItemVersionJpaRepository.findAll())
            .hasSize(1)
            .first()
            .extracting("publishedVersion", "publishedBy", "publishedAt")
            .containsExactly(testVersion, testUser, now)
    }

    @Test
    fun `should call method to save publish metadata`() {
        val testVersion = "test-version"
        val testUser = "test user"

        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(testVersion).whenever(dateTimePort).toPublishedVersionString(now)

        insertTestDraftRawItemsMetadata()
        insertTestDraftEnrichedItemsMetadata()

        itemsService.publish(testUser)

        val publishedItemVersion = PublishedItemVersion(
            publishedBy = testUser,
            publishedVersion = testVersion,
            publishedAt = now,
        )

        verify(rawItemsMetadataPort).savePublishMetadata(eq(publishedItemVersion), eq(testUser), eq(now))
        verify(enrichedItemsMetadataPort).savePublishMetadata(eq(publishedItemVersion), eq(testUser), eq(now))
    }

    @Test
    fun `return data update report with new items only and empty item group when new draft items have not published`() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        val version = "20221108"
        doReturn(version).whenever(dateTimePort).toPublishedVersionString(now)

        val draftItems = listOf(
            RowFactory.create("A1", "A2", null),
            RowFactory.create("B1", "B3", null), // changed item with existing code and name attribute changed
            RowFactory.create("C1", "C2", arrayOf("C3")), // new code
            RowFactory.create("A1", "A3", null), // added item with existing code and a new name attribute value
            RowFactory.create("E1", "E2", null)
        )
        mockSnapshotsData(draftItems)
        mockDraftItems(draftItems)

        val latestPublishedItems = listOf(
            RowFactory.create("A1", "A2", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE)),
            RowFactory.create("B1", "B2", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE)),
            RowFactory.create("E1", "E2", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE)),
        )
        mockLatestPublishedItems(latestPublishedItems)

        mockItemGroups()

        doReturn(spark.emptyDataset(Encoders.bean(ItemGroupToTopicAndSubscriptionRow::class.java)))
            .`when`(spyGenerateDataUpdateReportHelper).enrichItemGroupsByTopicsAndSubscriptions(any())

        val excelPath =
            "${Items.Draft.DATA_UPDATE_REPORT}/${CODING_SYSTEM}/DataUpdateReport-${CODING_SYSTEM}-${version}.xlsx"

        val resultUrl = itemsService.generateDataUpdateReport(CODING_SYSTEM)

        // Assert 1st tab
        val publishedDataUpdateReport = readExcel(excelPath, "New Items")
            .collectAsList()
            .sortedBy { it.getAs<String>("source_code") }

        assertThat(publishedDataUpdateReport).hasSize(1)

        assertThat(publishedDataUpdateReport[0].getAs<String>("source_code")).isEqualTo("C1")
        assertThat(publishedDataUpdateReport[0].getAs<String>("source_name")).isEqualTo("C2")
        assertThat(publishedDataUpdateReport[0].getAs<String>("source_indication_condition")).isEqualTo("[C3]")
        assertThat(publishedDataUpdateReport[0].getAs<String>("enriched_attribute")).isNull()
        assertThat(publishedDataUpdateReport[0].getAs<String>("status")).isEqualTo("New")
        assertThat(publishedDataUpdateReport[0].getAs<String>("item_group")).isEqualTo("[]")

        // Assert url
        assertThat(resultUrl).contains(UriEncoder.encode(excelPath))
    }

    @Test
    fun `return data update report with new items only and item group when new draft items have published`() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        val version = "20221108"
        doReturn(version).whenever(dateTimePort).toPublishedVersionString(now)

        val draftItems = listOf(
            RowFactory.create("A1", "A2", null),
            RowFactory.create("B1", "B3", null), // changed item with existing code and name attribute changed
            RowFactory.create("C1", "C2", null), // new code
            RowFactory.create("A1", "A3", null), // added item with existing code and a new name attribute value
            RowFactory.create("E1", "E2", null)
        )
        mockSnapshotsData(draftItems)
        mockDraftItems(draftItems)

        val latestPublishedItems = listOf(
            RowFactory.create("A1", "A2", null, arrayOf("")),
            RowFactory.create("B1", "B3", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE)), // changed item with existing code and name attribute changed
            RowFactory.create("C1", "C2", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE)), // new code
            RowFactory.create("A1", "A3", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE)), // added item with existing code and a new name attribute value
            RowFactory.create("E1", "E2", null, arrayOf(ENRICHED_ATTRIBUTE_VALUE))
        )
        mockLatestPublishedItems(latestPublishedItems)

        mockItemGroups()

        val resultUrl = itemsService.generateDataUpdateReport(CODING_SYSTEM)

        val excelPath =
            "${Items.Draft.DATA_UPDATE_REPORT}/${CODING_SYSTEM}/DataUpdateReport-${CODING_SYSTEM}-${version}.xlsx"

        // Assert 1st tab
        val publishedDataUpdateReport = readExcel(excelPath, "New Items")
            .collectAsList()
            .sortedBy { it.getAs<String>("source_code") }

        assertThat(publishedDataUpdateReport).hasSize(1)

        assertThat(publishedDataUpdateReport[0].getAs<String>("source_code")).isEqualTo("C1")
        assertThat(publishedDataUpdateReport[0].getAs<String>("source_name")).isEqualTo("C2")
        assertThat(publishedDataUpdateReport[0].getAs<String>("source_indication_condition")).isEqualTo("[]")
        assertThat(publishedDataUpdateReport[0].getAs<String>("enriched_attribute")).isEqualTo("[$ENRICHED_ATTRIBUTE_VALUE]")
        assertThat(publishedDataUpdateReport[0].getAs<String>("status")).isEqualTo("New")
        assertThat(publishedDataUpdateReport[0].getAs<String>("item_group")).isEqualTo("[$ITEM_GROUP_KEY]")

        // Assert 2nd tab
        val itemGroupsTopicAndSubscriptions = readExcel(excelPath, "Topics & Subscriptions")
            .withColumn("topic_id", Column("topic_id").cast("bigint"))
            .`as`(Encoders.bean(ItemGroupToTopicAndSubscriptionRow::class.java))
            .collectAsList()

        assertThat(itemGroupsTopicAndSubscriptions).containsExactlyInAnyOrderElementsOf(
            ITEM_GROUPS_TO_TOPICS_AND_SUBSCRIPTIONS
        )

        // Assert url
        assertThat(resultUrl).contains(UriEncoder.encode(excelPath))
    }

    private fun mockDraftItems(draftItems: List<Row>) {
        val itemsSchema = StructType(
            listOf(
                StructField("source_code", StringType, false, Metadata.empty()),
                StructField("source_name", StringType, false, Metadata.empty()),
                StructField("source_indication_condition", ArrayType(StringType, true), true, Metadata.empty())
            ).toTypedArray()
        )
        val currentDraftItemsDs = spark.createDataFrame(draftItems, itemsSchema)
        writeParquet("${Items.Draft.RAW_ITEMS}/$CODING_SYSTEM", currentDraftItemsDs)
    }

    private fun mockSnapshotsData(draftItems: List<Row>) {
        val snapshotItemSchema = StructType(
            listOf(
                StructField("code", StringType, false, Metadata.empty()),
                StructField("name", StringType, false, Metadata.empty()),
                StructField("indication_condition", ArrayType(StringType, true), true, Metadata.empty())
            ).toTypedArray()
        )
        val previousDraftSnapshotItemsDs = spark.createDataFrame(
            listOf(
                RowFactory.create("A1", "A2", null),
                RowFactory.create("B1", "B2", null),
                RowFactory.create("E1", "E2", null)
            ),
            snapshotItemSchema
        )
        writeParquet("$SNAPSHOTS/$CODING_SYSTEM/timestamp=202209", previousDraftSnapshotItemsDs)

        val currentDraftSnapshotItemsDs = spark.createDataFrame(draftItems, snapshotItemSchema)
        writeParquet("$SNAPSHOTS/$CODING_SYSTEM/timestamp=202210", currentDraftSnapshotItemsDs)
    }

    private fun mockLatestPublishedItems(latestPublishedItems: List<Row>) {
        val lastedPublishedItemVersion = PublishedItemVersionEntity(
            publishedVersion = "20220901000000Z",
            publishedBy = "tester",
            publishedAt = LocalDateTime.now()
        )
        publishedItemVersionJpaRepository.save(lastedPublishedItemVersion)

        val publishedItemsSchema = StructType(
            listOf(
                StructField("source_code", StringType, false, Metadata.empty()),
                StructField("source_name", StringType, false, Metadata.empty()),
                StructField("source_indication_condition", ArrayType(StringType, true), true, Metadata.empty()),
                StructField("enriched_attribute", ArrayType(StringType, false), false, Metadata.empty())
            ).toTypedArray()
        )
        val latestPublishedItemsDs = spark.createDataFrame(latestPublishedItems, publishedItemsSchema)

        writeParquet(
            "${Items.Published.ENRICHED_ITEMS_PARQUET}/${CODING_SYSTEM}/${
                createVersionPath(
                    lastedPublishedItemVersion.publishedVersion
                )
            }", latestPublishedItemsDs
        )
    }

    private fun mockItemGroups() {
        val itemGroupEntity = ItemGroupEntity(
            name = "test-name",
            businessKey = ITEM_GROUP_KEY,
            rule = """{"field":"enriched_attribute", "value":"enriched attribute value", "operator":"="}""",
            lastModifiedAt = LocalDateTime.now(),
            lastModifiedBy = "tester",
            uuid = UUID.randomUUID().toString()
        )

        itemGroupJpaRepository.save(itemGroupEntity)
    }

    private fun mockCommon() {
        val now = LocalDateTime.now()
        doReturn(now).whenever(dateTimePort).now()
        doReturn(1).whenever(enrichmentRulePort).getLatestEnrichmentRuleRevisionId()
    }

    private fun createEnrichmentRule(
        enrichedAttributeValue: EnrichedAttributeValue,
        rules: List<Rule>
    ): EnrichmentRule {
        return enrichmentRuleService.create(
            EnrichmentRule(
                enrichedAttributeValue = enrichedAttributeValue,
                lastModifiedBy = "test-user",
                lastModifiedAt = LocalDateTime.now(),
                rule = HasRule.objectMapper.writeValueAsString(
                    Rule(
                        rules = rules,
                        combinator = "and"
                    ),
                ),
            )
        )
    }

    private fun insertTestDraftRawItemsMetadata() {
        rawItemsMetadataPort.saveDraftMetadata(
            rawItems = spark.createDataFrame(
                listOf(
                    TestRawItemsMetadataRow("1", "A", "B", "c"),
                    TestRawItemsMetadataRow("2", "A", "B", "c"),
                    TestRawItemsMetadataRow("3", "A", "B", "c"),
                    TestRawItemsMetadataRow("4", "A", "B", "c"),
                    TestRawItemsMetadataRow("5", "a", "b", "C"),
                ),
                TestRawItemsMetadataRow::class.java
            ),
            publishedItems = spark.createDataFrame(
                listOf(
                    TestRawItemsMetadataRow("1", "A", "B", "c"),
                    TestRawItemsMetadataRow("2", "A", "B", "c"),
                    TestRawItemsMetadataRow("3", "A", "B", "c"),
                    TestRawItemsMetadataRow("4", "A", "B", "c"),
                ),
                TestRawItemsMetadataRow::class.java
            ),
            codingSystem = codingSystem,
            createdBy = "tester",
            createdAt = LocalDateTime.now()
        )
    }

    private fun insertTestDraftEnrichedItemsMetadata() {
        enrichedItemsMetadataPort.saveDraftMetadata(
            EnrichedItemsMetadata(
                codingSystem = codingSystem,
                createdBy = "tester",
                createdAt = LocalDateTime.now(),
                latestEnrichmentRuleRevisionId = 0,
                sourceAttributes = listOf(),
                enrichedAttributes = listOf()
            )
        )
    }

    data class TestRawItemsMetadataRow(
        val source_code: String,
        val source_a: String,
        val source_b: String,
        val source_c: String
    )

    data class TestRow(val source_code: String, val source_name: String)
}
