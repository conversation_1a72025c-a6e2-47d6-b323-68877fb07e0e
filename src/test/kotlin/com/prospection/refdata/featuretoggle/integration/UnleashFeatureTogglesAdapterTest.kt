package com.prospection.refdata.featuretoggle.integration

import io.getunleash.Unleash
import io.getunleash.UnleashContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.KArgumentCaptor
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder

private const val TEST_USERNAME: String = "a_user"
private const val TEST_FEATURE: String = "abc"

internal class UnleashFeatureTogglesAdapterTest {
    private val unleash: Unleash = mock()
    private val unleashFeatureTogglesService: UnleashFeatureTogglesAdapter = UnleashFeatureTogglesAdapter(unleash)

    private lateinit var contextArgumentCaptor: KArgumentCaptor<UnleashContext>

    @BeforeEach
    fun setUp() {
        // Setup
        reset(unleash)
        contextArgumentCaptor = argumentCaptor<UnleashContext>()
    }

    @AfterEach
    fun tearDown() {
        SecurityContextHolder.clearContext()
    }

    @Test
    fun isEnableShouldReturnTrue() {
        // Setup
        whenever(unleash.isEnabled(eq(TEST_FEATURE), any<UnleashContext>())).thenReturn(true)

        // Test & verify
        assertThat(unleashFeatureTogglesService.isEnable(TEST_USERNAME, TEST_FEATURE)).isTrue()

        verify(unleash, times(1)).isEnabled(any<String>(), contextArgumentCaptor.capture())
        assertThat(contextArgumentCaptor.lastValue.userId).containsSame(TEST_USERNAME)
    }

    @Test
    fun isEnableShouldReturnFalseIfUnleashReturnFalse() {
        // Setup
        whenever(unleash.isEnabled(eq(TEST_FEATURE), any<UnleashContext>())).thenReturn(false)

        // Test & verify
        assertThat(unleashFeatureTogglesService.isEnable(TEST_USERNAME, TEST_FEATURE)).isFalse()

        verify(unleash, times(1)).isEnabled(any<String>(), contextArgumentCaptor.capture())
        assertThat(contextArgumentCaptor.lastValue.userId).containsSame(TEST_USERNAME)
    }

    @Test
    fun isEnableShouldReturnFalseIfUnleashThrow() {
        // Setup
        whenever(unleash.isEnabled(eq(TEST_FEATURE), any<UnleashContext>())).thenThrow(RuntimeException())

        // Test & verify
        assertThat(unleashFeatureTogglesService.isEnable(TEST_USERNAME, TEST_FEATURE)).isFalse()

        verify(unleash, times(1)).isEnabled(any<String>(), contextArgumentCaptor.capture())
        assertThat(contextArgumentCaptor.lastValue.userId).containsSame(TEST_USERNAME)
    }

    @Test
    fun isEnableShouldUseAuthenticationNameAsUser() {
        // Setup
        val authentication = mock<Authentication>()
        SecurityContextHolder.getContext().authentication = authentication
        whenever(authentication.name).thenReturn(TEST_USERNAME)
        whenever(unleash.isEnabled(eq(TEST_FEATURE), any<UnleashContext>())).thenReturn(true)

        // Test & verify
        assertThat(unleashFeatureTogglesService.isEnable(TEST_FEATURE)).isTrue()

        verify(unleash, times(1)).isEnabled(any<String>(), contextArgumentCaptor.capture())
        assertThat(contextArgumentCaptor.lastValue.userId).containsSame(TEST_USERNAME)
    }

    @Test
    fun isEnableShouldUseAnonymousAsUserWhenAuthenticationIsNull() {
        // Setup
        whenever(unleash.isEnabled(eq("Anonymous"), any<UnleashContext>())).thenReturn(true)

        // Test & verify
        assertThat(unleashFeatureTogglesService.isEnable(TEST_FEATURE)).isFalse()

        verify(unleash, times(1)).isEnabled(any<String>(), contextArgumentCaptor.capture())
        assertThat(contextArgumentCaptor.lastValue.userId).containsSame("Anonymous")
    }

}