package com.prospection.refdata.common.integration

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.lambda.model.InvokeRequest
import software.amazon.awssdk.services.lambda.model.InvokeResponse

private const val TEST_FUNCTION_NAME = "testFunctionName"

internal class AwsLambdaClientTest {

    private val mockLambdaClient: LambdaClient = mock()
    private val mockObjectMapper: ObjectMapper = mock()

    private val awsLambdaClient = AwsLambdaClient(mockLambdaClient, mockObjectMapper)

    @AfterEach
    fun tearDown() {
        reset(mockLambdaClient, mockObjectMapper)
    }

    @Test
    fun `invoke should call lambda with correct function name and payload`() {
        // setup
        val payload = TestClass("A", 1)
        val payloadString = "payload_string"
        whenever(mockObjectMapper.writeValueAsString(any<TestClass>())).thenReturn(payloadString)
        
        val mockInvokeResponse: InvokeResponse = mock()
        whenever(mockLambdaClient.invoke(any<InvokeRequest>())).thenReturn(mockInvokeResponse)
        whenever(mockInvokeResponse.statusCode()).thenReturn(200)
        
        val mockSdkBytes: SdkBytes = mock()
        whenever(mockInvokeResponse.payload()).thenReturn(mockSdkBytes)
        val resultJsonString = "json_string"
        whenever(mockSdkBytes.asUtf8String()).thenReturn(resultJsonString)
        
        val expectedResult = listOf(
            TestClass("A", 1),
            TestClass("B", 2)
        )
        whenever(mockObjectMapper.readValue(eq(resultJsonString), any<TypeReference<Any>>()))
            .thenReturn(expectedResult)

        // test
        val result = awsLambdaClient.invoke(TEST_FUNCTION_NAME, payload, object : TypeReference<List<TestClass>>() {})

        // verify
        assertEquals(expectedResult, result)

        val payloadCaptor = argumentCaptor<TestClass>()
        verify(mockObjectMapper, times(1)).writeValueAsString(payloadCaptor.capture())
        assertEquals(payload, payloadCaptor.lastValue)

        val invokeCaptor = argumentCaptor<InvokeRequest>()
        verify(mockLambdaClient, times(1)).invoke(invokeCaptor.capture())
        assertEquals(TEST_FUNCTION_NAME, invokeCaptor.lastValue.functionName())
        assertEquals(SdkBytes.fromUtf8String(payloadString).asByteArray().toList(), invokeCaptor.lastValue.payload().asByteArray().toList())
    }

    @Test
    fun `invoke should throw if status code is not 200`() {
        // setup
        val payload = TestClass("A", 1)
        val payloadString = "payload_string"
        whenever(mockObjectMapper.writeValueAsString(any<TestClass>())).thenReturn(payloadString)
        
        val mockInvokeResponse: InvokeResponse = mock()
        whenever(mockLambdaClient.invoke(any<InvokeRequest>())).thenReturn(mockInvokeResponse)
        whenever(mockInvokeResponse.statusCode()).thenReturn(201)

        // test & verify
        assertThrows<RuntimeException> {
            awsLambdaClient.invoke(TEST_FUNCTION_NAME, payload, object : TypeReference<List<TestClass>>() {})
        }

        val payloadCaptor = argumentCaptor<TestClass>()
        verify(mockObjectMapper, times(1)).writeValueAsString(payloadCaptor.capture())
        assertEquals(payload, payloadCaptor.lastValue)

        val invokeCaptor = argumentCaptor<InvokeRequest>()
        verify(mockLambdaClient, times(1)).invoke(invokeCaptor.capture())
        assertEquals(TEST_FUNCTION_NAME, invokeCaptor.lastValue.functionName())
        assertEquals(SdkBytes.fromUtf8String(payloadString).asByteArray().toList(), invokeCaptor.lastValue.payload().asByteArray().toList())
    }

    @Test
    fun `invoke should throw if function error is not null`() {
        // setup
        val payload = TestClass("A", 1)
        val payloadString = "payload_string"
        whenever(mockObjectMapper.writeValueAsString(any<TestClass>())).thenReturn(payloadString)
        
        val mockInvokeResponse: InvokeResponse = mock()
        whenever(mockLambdaClient.invoke(any<InvokeRequest>())).thenReturn(mockInvokeResponse)
        whenever(mockInvokeResponse.statusCode()).thenReturn(200)
        whenever(mockInvokeResponse.functionError()).thenReturn("Some error")
        
        // test & verify
        assertThrows<RuntimeException> {
            awsLambdaClient.invoke(TEST_FUNCTION_NAME, payload, object : TypeReference<List<TestClass>>() {})
        }
    }

    private data class TestClass(val name: String, val age: Int)
}