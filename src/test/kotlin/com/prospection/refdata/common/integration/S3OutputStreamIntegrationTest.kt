package com.prospection.refdata.common.integration

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.config.S3Path
import org.apache.commons.lang3.RandomStringUtils
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import java.nio.charset.Charset

internal class S3OutputStreamIntegrationTest : AbstractIntegrationTest() {
    companion object {
        private const val PATH_FILE_TEST = "${S3Path.Items.Draft.RAW_ITEMS}/whatever/test.txt"
        protected const val BUFFER_SIZE = 10000000
    }
    private lateinit var s3OutputStream : S3OutputStream


    @BeforeEach
    override fun setUp() {
        super.setUp()
        // Initialize S3 bucket
        amazonS3.createBucket { builder ->
            builder.bucket(applicationProperties.s3Bucket)
        }

        s3OutputStream = Mockito.spy(
            S3OutputStream(
                amazonS3,
                applicationProperties.s3Bucket,
                PATH_FILE_TEST
            )
        )
    }

    @Test
    fun `Should write data greater than buffer size`() {

        val content = RandomStringUtils.randomAlphabetic(BUFFER_SIZE + 10)
        s3OutputStream.write(content.toByteArray(Charset.defaultCharset()))
        s3OutputStream.close()

        val response = amazonS3.getObject { builder ->
            builder.bucket(applicationProperties.s3Bucket)
                   .key(PATH_FILE_TEST)
        }
        val responseBytes = response.readAllBytes()
        assertEquals(BUFFER_SIZE + 10, responseBytes.size )
        assertEquals(content, String(responseBytes))
    }
}