package com.prospection.refdata.common.integration

import TestSpark.spark
import com.prospection.refdata.common.consts.CodingSystems
import com.prospection.refdata.common.integration.GenerateDataUpdateReportHelper.Companion.ADDITIONAL_AUDIT_COLUMNS
import com.prospection.refdata.common.integration.GenerateDataUpdateReportHelper.Companion.SUBSCRIBED_TOPIC_IDS
import com.prospection.refdata.common.integration.GenerateDataUpdateReportHelper.Companion.USED_CONDITIONS_COLUMN
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestTopic
import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestWorkflow
import com.prospection.refdata.common.integration.datasetrow.ItemGroupToTopicAndSubscriptionRow
import com.prospection.refdata.itemgroups.integration.ItemGroupExportColumns
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import com.prospection.refdata.subscription.domain.CompanySubscription
import com.prospection.refdata.subscription.domain.CompanySubscriptionPort
import com.prospection.refdata.topic.domain.TopicPort
import org.apache.spark.sql.Encoders
import org.apache.spark.sql.RowFactory
import org.apache.spark.sql.types.ArrayType
import org.apache.spark.sql.types.DataTypes
import org.apache.spark.sql.types.Metadata
import org.apache.spark.sql.types.StructField
import org.apache.spark.sql.types.StructType
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.extractor.Extractors
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.doReturn


class GenerateDataUpdateReportHelperTest {

    companion object {
        private const val ITEM_GROUP_1 = "test-item-group1"
        private const val ITEM_GROUP_2 = "test-item-group2"
        private const val ITEM_GROUP_3 = "test-item-group3"
        private val ITEM_GROUPS = listOf(ITEM_GROUP_1, ITEM_GROUP_2, ITEM_GROUP_3)

        private val PBS_DRUG_ITEM_WITH_ITEM_GROUP_SCHEMA = StructType(
            listOf(
                StructField("source_code", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_name", DataTypes.StringType, true, Metadata.empty()),
                StructField("source_indication_id", DataTypes.StringType, true, Metadata.empty()),
                StructField("source_restriction_flag", DataTypes.StringType, true, Metadata.empty()),
                StructField("status", DataTypes.StringType, true, Metadata.empty()),
                StructField("coding_system", DataTypes.StringType, true, Metadata.empty()),
                StructField("item_group", ArrayType(DataTypes.StringType, false), false, Metadata.empty())
            ).toTypedArray()
        )

        private val PBS_AUTHORITY_ITEM_WITH_ITEM_GROUP_SCHEMA = StructType(
            listOf(
                StructField("source_code", DataTypes.StringType, false, Metadata.empty()),
                StructField("source_name", DataTypes.StringType, true, Metadata.empty()),
                StructField("status", DataTypes.StringType, true, Metadata.empty()),
                StructField("coding_system", DataTypes.StringType, true, Metadata.empty()),
                StructField("item_group", ArrayType(DataTypes.StringType, false), false, Metadata.empty())
            ).toTypedArray()
        )

        private val ITEM_GROUP_TO_TOPIC_AND_SUBSCRIPTION_SCHEMA = StructType(
            listOf(
                StructField("item_group", DataTypes.StringType, false, Metadata.empty()),
                StructField("topic_id", DataTypes.LongType, true, Metadata.empty()),
                StructField("topic_name", DataTypes.StringType, true, Metadata.empty()),
                StructField("workflow_status", DataTypes.StringType, true, Metadata.empty()),
                StructField("condition", DataTypes.StringType, true, Metadata.empty()),
                StructField("therapy_area", DataTypes.StringType, true, Metadata.empty()),
                StructField("subscription_id", DataTypes.StringType, true, Metadata.empty()),
                StructField("subscription_name", DataTypes.StringType, true, Metadata.empty()),
                StructField("subscription_record_id", DataTypes.StringType, true, Metadata.empty())
            ).toTypedArray()
        )

        private val PBS_DRUG_ITEM_WITH_ITEM_GROUP_ROWS = listOf(
            RowFactory.create("1", "A", "B", "c", "New", CodingSystems.PBS_DRUG, ITEM_GROUPS),
            RowFactory.create("2", "A", "B", "c", "New", CodingSystems.PBS_DRUG, listOf(ITEM_GROUP_1)),
            RowFactory.create(
                "3",
                "A",
                "B",
                "c",
                "New",
                CodingSystems.PBS_DRUG,
                listOf(ITEM_GROUP_1, ITEM_GROUP_2)
            ),
            RowFactory.create(
                "4",
                "A",
                "B",
                "c",
                "New",
                CodingSystems.PBS_DRUG,
                listOf("fake_item_group_1")
            ),
            RowFactory.create(
                "5",
                "a",
                "b",
                "C",
                "New",
                CodingSystems.PBS_DRUG,
                listOf(ITEM_GROUP_1, "fake_item_group_1")
            ),
        )

        private val PBS_AUTHORITY_ITEM_WITH_ITEM_GROUP_ROWS = listOf(
            RowFactory.create("1", "A", "New", CodingSystems.PBS_DRUG, ITEM_GROUPS),
            RowFactory.create("2", "A", "New", CodingSystems.PBS_DRUG, listOf(ITEM_GROUP_1)),
            RowFactory.create(
                "3",
                "A",
                "New",
                CodingSystems.PBS_DRUG,
                listOf(ITEM_GROUP_1, ITEM_GROUP_2)
            ),
            RowFactory.create(
                "4",
                "A",
                "New",
                CodingSystems.PBS_DRUG,
                listOf("fake_item_group_1")
            ),
            RowFactory.create(
                "5",
                "a",
                "New",
                CodingSystems.PBS_DRUG,
                listOf(ITEM_GROUP_1, "fake_item_group_1")
            ),
        )

        private val ITEM_GROUP_TO_TOPIC_AND_SUBSCRIPTION_ROWS = listOf(
            RowFactory.create(
                ITEM_GROUP_1,
                1L,
                "B",
                "GENERATED",
                "c1",
                "ta1",
                "sub_id_1",
                "sub_name_1",
                "sub_record_id_1"
            ),
            RowFactory.create(
                ITEM_GROUP_2,
                1L,
                "B",
                "GENERATED",
                "c2",
                "ta2",
                "sub_id_2",
                "sub_name_2",
                "sub_record_id_1"
            ),
            RowFactory.create(
                ITEM_GROUP_3,
                1L,
                "B",
                "GENERATED",
                "c3",
                "ta3",
                "sub_id_3",
                "sub_name_3",
                "sub_record_id_1"
            ),
            RowFactory.create(
                "item_group_4",
                1L,
                "B",
                "GENERATED",
                "c4",
                "ta4",
                null,
                null,
                null
            ),
            RowFactory.create(
                "item_group_5",
                1L,
                "b",
                "GENERATED",
                "c5",
                "ta5",
                null,
                null,
                null
            ),
        )
    }

    private val topicPort = mock(TopicPort::class.java)

    private val companySubscriptionPort = mock(CompanySubscriptionPort::class.java)

    private val helper = GenerateDataUpdateReportHelper(topicPort, companySubscriptionPort, spark)

    @Test
    fun shouldReturnEmptyResult() {
        val inputDs = spark.emptyDataFrame()
            .withColumn(ItemGroupExportColumns.ITEM_GROUP_CODE.columnName, ScalaSparkItemsFunctions.emptyArray())
        assertThat(helper.enrichItemGroupsByTopicsAndSubscriptions(inputDs).collectAsList()).isEmpty()
    }

    @Test
    fun shouldEnrichItemGroupsWithTopicsAndSubscriptions() {
        val itemGroup1 = "test-item-group1"
        val itemGroup2 = "test-item-group2"
        val itemGroup3 = "test-item-group3"
        val itemGroups = listOf(itemGroup1, itemGroup2, itemGroup3)
        val subscriptionRecordId = "subscription-record-id"

        val workflowInfo1 = createTestWorkflow(
            id = 1L,
            status = "GENERATED",
            relatedItemGroups = listOf(itemGroup1, itemGroup2),
        )

        val workflowInfo2 = createTestWorkflow(
            id = 2L,
            status = "PUBLISHED",
            relatedItemGroups = listOf(itemGroup1, itemGroup2),
        )


        val topic1 = createTestTopic(
            id = 1L,
            relatedWorkflows = listOf(workflowInfo1),
            subscriptionId = subscriptionRecordId,
            name = "topic1",
            conditionName = "condition-1",
            therapyAreaName = "therapy-area-1"
        )

        val topic2 = createTestTopic(
            id = 2L,
            relatedWorkflows = listOf(workflowInfo2),
            name = "topic2",
        )

        // mock topic client
        doReturn(listOf(topic1, topic2)).`when`(topicPort).listTopicsByItemGroups(any())

        val subscription = CompanySubscription(
            id = "subscription-id",
            name = "subscription-name",
            recordId = subscriptionRecordId
        )

        // mock subscription client
        doReturn(listOf(subscription)).`when`(companySubscriptionPort)
            .listSubscriptionsByRecordIds(listOf(subscriptionRecordId))

        // mock ds
        val inputDs = spark.createDataFrame(listOf(TestRow(itemGroups), TestRow(emptyList())), TestRow::class.java)

        val result = helper.enrichItemGroupsByTopicsAndSubscriptions(inputDs).collectAsList()

        val expectedResult = listOf(
            ItemGroupToTopicAndSubscriptionRow(
                item_group = itemGroup1,
                topic_id = topic1.id,
                topic_name = topic1.name,
                workflow_status = "GENERATED",
                condition = topic1.conditionName,
                therapy_area = topic1.therapyAreaName,
                subscription_id = subscription.id,
                subscription_name = subscription.name,
                subscription_record_id = subscription.recordId
            ),
            ItemGroupToTopicAndSubscriptionRow(
                item_group = itemGroup2,
                topic_id = topic1.id,
                topic_name = topic1.name,
                workflow_status = "GENERATED",
                condition = topic1.conditionName,
                therapy_area = topic1.therapyAreaName,
                subscription_id = subscription.id,
                subscription_name = subscription.name,
                subscription_record_id = subscription.recordId
            ),
            ItemGroupToTopicAndSubscriptionRow(
                item_group = itemGroup1,
                topic_id = topic2.id,
                topic_name = topic2.name,
                workflow_status = "PUBLISHED"
            ),
            ItemGroupToTopicAndSubscriptionRow(
                item_group = itemGroup2,
                topic_id = topic2.id,
                topic_name = topic2.name,
                workflow_status = "PUBLISHED"
            ),
            ItemGroupToTopicAndSubscriptionRow(
                item_group = itemGroup3
            )
        )

        val itemGroupsCaptor = argumentCaptor<List<String>>()
        verify(topicPort).listTopicsByItemGroups(itemGroupsCaptor.capture())
        assertThat(itemGroupsCaptor.firstValue).containsExactlyInAnyOrderElementsOf(itemGroups)
        verify(companySubscriptionPort).listSubscriptionsByRecordIds(listOf(subscriptionRecordId))
        assertThat(result).containsExactlyInAnyOrderElementsOf(expectedResult)
    }

    @Test
    fun `should add additional columns and remove redundant columns for PBS Drug`() {
        // given
        val itemsWithItemGroupsDs =
            spark.createDataFrame(PBS_DRUG_ITEM_WITH_ITEM_GROUP_ROWS, PBS_DRUG_ITEM_WITH_ITEM_GROUP_SCHEMA)

        val itemGroupToTopicAndSubscriptionRowDs =
            spark.createDataFrame(
                ITEM_GROUP_TO_TOPIC_AND_SUBSCRIPTION_ROWS,
                ITEM_GROUP_TO_TOPIC_AND_SUBSCRIPTION_SCHEMA
            )
                .`as`(Encoders.bean(ItemGroupToTopicAndSubscriptionRow::class.java))

        // when
        val result = helper.enrichItemsWithConditions(
            itemsWithItemGroupsDs,
            itemGroupToTopicAndSubscriptionRowDs,
            CodingSystems.PBS_DRUG
        ).collectAsList()

        // then
        val expectedHeader =
            listOf(
                "source_code",
                "source_name",
                "item_group"
            ) + USED_CONDITIONS_COLUMN + SUBSCRIBED_TOPIC_IDS + ADDITIONAL_AUDIT_COLUMNS

        assertThat(result[0].schema().fieldNames().toList()).isEqualTo(expectedHeader)

        val expectedResult = listOf(
            "[1,A,ArraySeq(test-item-group1, test-item-group2, test-item-group3),ArraySeq(c1, c2, c3),ArraySeq(1),,,,,,,,,,]",
            "[2,A,ArraySeq(test-item-group1),ArraySeq(c1),ArraySeq(1),,,,,,,,,,]",
            "[3,A,ArraySeq(test-item-group1, test-item-group2),ArraySeq(c1, c2),ArraySeq(1),,,,,,,,,,]",
            "[4,A,ArraySeq(fake_item_group_1),ArraySeq(),ArraySeq(),,,,,,,,,,]",
            "[5,a,ArraySeq(test-item-group1, fake_item_group_1),ArraySeq(c1),ArraySeq(1),,,,,,,,,,]"
        )

        assertThat(result)
            .extracting(Extractors.toStringMethod())
            .containsExactlyElementsOf(expectedResult)
    }

    @Test
    fun `should not remove columns for other coding systems`() {
        // given
        val itemsWithItemGroupsDs =
            spark.createDataFrame(PBS_AUTHORITY_ITEM_WITH_ITEM_GROUP_ROWS, PBS_AUTHORITY_ITEM_WITH_ITEM_GROUP_SCHEMA)

        val itemGroupToTopicAndSubscriptionRowDs =
            spark.createDataFrame(
                ITEM_GROUP_TO_TOPIC_AND_SUBSCRIPTION_ROWS,
                ITEM_GROUP_TO_TOPIC_AND_SUBSCRIPTION_SCHEMA
            )
                .`as`(Encoders.bean(ItemGroupToTopicAndSubscriptionRow::class.java))

        // when
        val result = helper.enrichItemsWithConditions(
            itemsWithItemGroupsDs,
            itemGroupToTopicAndSubscriptionRowDs,
            CodingSystems.PBS_AUTHORITY
        ).collectAsList()

        // then
        val expectedHeader =
            setOf(
                "source_code",
                "source_name",
                "item_group",
                "status",
                "coding_system"
            ) + USED_CONDITIONS_COLUMN + SUBSCRIBED_TOPIC_IDS + ADDITIONAL_AUDIT_COLUMNS

        assertThat(result[0].schema().fieldNames().toSet()).isEqualTo(expectedHeader)

        val expectedResult = listOf(
            "[1,A,New,PBS Drug,ArraySeq(test-item-group1, test-item-group2, test-item-group3),ArraySeq(c1, c2, c3),ArraySeq(1),,,,,,,,,,]",
            "[2,A,New,PBS Drug,ArraySeq(test-item-group1),ArraySeq(c1),ArraySeq(1),,,,,,,,,,]",
            "[3,A,New,PBS Drug,ArraySeq(test-item-group1, test-item-group2),ArraySeq(c1, c2),ArraySeq(1),,,,,,,,,,]",
            "[4,A,New,PBS Drug,ArraySeq(fake_item_group_1),ArraySeq(),ArraySeq(),,,,,,,,,,]",
            "[5,a,New,PBS Drug,ArraySeq(test-item-group1, fake_item_group_1),ArraySeq(c1),ArraySeq(1),,,,,,,,,,]"
        )

        assertThat(result)
            .extracting(Extractors.toStringMethod())
            .containsExactlyElementsOf(expectedResult)
    }

    data class TestRow(val item_group: List<String>)
}