package com.prospection.refdata.common.integration

import com.prospection.refdata.AbstractIntegrationTest
import org.apache.spark.sql.Encoders
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class S3SparkImportExportHelperTest : AbstractIntegrationTest() {
    private lateinit var adapter: S3SparkImportExportHelper

    @BeforeEach
    override fun setUp() {
        super.setUp()
        adapter = S3SparkImportExportHelper(applicationProperties.s3Bucket, amazonS3, spark)
    }

    @Test
    fun `should encode a list of objects as Spark's dataset and export as parquet`() {
        val testObjects = listOf(
            TestClass(1, "test1"),
            TestClass(2, "test2")
        )

        adapter.writeParquet("test", testObjects, Encoders.bean(TestClass::class.java))

        adapter.readParquet("test")?.collectAsList()?.run {
            assertEquals(2, size)

            assertEquals(1, get(0).getAs("id"))
            assertEquals("test1", get(0).getAs("name"))
            assertEquals(2, get(1).getAs("id"))
            assertEquals("test2", get(1).getAs("name"))
        }
    }
}

// Note that fields are var, not val so Encoder can inter the types
// Also, this class should be defined outside test method so Spark can generate code to encode this class successfully
data class TestClass(var id: Int, var name: String)