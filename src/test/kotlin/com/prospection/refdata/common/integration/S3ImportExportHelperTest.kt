package com.prospection.refdata.common.integration

import com.prospection.refdata.AbstractIntegrationTest
import com.fasterxml.jackson.databind.ObjectMapper
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class S3ImportExportHelperTest : AbstractIntegrationTest() {
    private lateinit var adapter: S3ImportExportHelper

    @BeforeEach
    override fun setUp() {
        super.setUp()
        adapter = S3ImportExportHelper(applicationProperties, amazonS3, spark, ObjectMapper())
    }

    @Test
    fun `should return destination s3 path by source s3 path and object key`() {
        assertEquals(
            "items/published/raw-items/CPT HCPCS Procedure/version=20231121T004018Z/part-00000-00159238-a8cc-4981-8de1-5a8ca3946199-c000.snappy.parquet",
            S3PathUtils.createDestinationKey(
                "items/draft/raw-items/CPT HCPCS Procedure/part-00000-00159238-a8cc-4981-8de1-5a8ca3946199-c000.snappy.parquet",
                "items/draft/raw-items/CPT HCPCS Procedure",
                "items/published/raw-items/CPT HCPCS Procedure/version=20231121T004018Z"
            )
        )
    }
}