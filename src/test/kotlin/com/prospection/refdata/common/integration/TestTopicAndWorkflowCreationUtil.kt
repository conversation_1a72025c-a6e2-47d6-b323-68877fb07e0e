package com.prospection.refdata.common.integration

import com.prospection.refdata.topic.application.rest.dto.TopicDto
import com.prospection.refdata.topic.domain.Topic
import com.prospection.refdata.topic.domain.Workflow
import java.time.Instant

class TestTopicAndWorkflowCreationUtil {
    companion object {
        fun createTestWorkflow(
            id: Long,
            status: String,
            relatedItemGroups: List<String>
        ): Workflow {
            return Workflow(
                id = id,
                status = status,
                relatedItemGroups = relatedItemGroups,
                lastModifiedBy = "whatever",
                lastModifiedDate = Instant.now()
            )
        }

        fun createTestTopic(
            id: Long,
            name: String,
            relatedWorkflows: List<Workflow> = listOf(),
            subscriptionId: String? = null,
            conditionName: String? = null,
            therapyAreaName: String? = null,
            companyId: String = "whatever"
        ): Topic {
            return Topic(
                id = id,
                relatedWorkflows = relatedWorkflows,
                subscriptionId = subscriptionId,
                name = name,
                conditionName = conditionName,
                therapyAreaName = therapyAreaName,
                adhoc = false,
                bridge = false,
                companyId = companyId,
                standalone = true
            )
        }

        fun createTestTopicDto(
            id: Long,
            name: String,
        ): TopicDto {
            return TopicDto(
                id = id,
                companyId = "whatever",
                relatedWorkflows = emptyList(),
                name = name,
                adhoc = false,
                bridge = false,
                standalone = true
            )
        }
    }
}