package com.prospection.refdata.common.integration

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.config.S3ItemsForExternalToolProperties
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class S3ItemsExporterForExternalToolAdapterIntegrationTest : AbstractIntegrationTest() {

    @Autowired
    private lateinit var codingSystemPort : CodingSystemPort
    @Autowired
    private lateinit var s3ItemsForExternalToolProperties : S3ItemsForExternalToolProperties
    @Autowired
    private lateinit var codingSystemJpaRepository: CodingSystemJpaRepository

    private lateinit var adapter: S3ItemsExporterForExternalToolAdapter


    companion object {
        private val CODING_SYSTEM = "Test Coding System"

        private val RAW_ITEMS = listOf(
            TestRow("1", "A"),
            TestRow("2", "A"),
        )
    }
    @BeforeEach
    override fun setUp() {
        super.setUp()
        amazonS3.createBucket { builder ->
            builder.bucket(applicationProperties.s3Bucket)
        }

        val codingSystemEntity = CodingSystemEntity(name = CODING_SYSTEM, country = "AU,US,JP")
        codingSystemJpaRepository.save(codingSystemEntity)

        adapter = S3ItemsExporterForExternalToolAdapter(
            codingSystemPort,
            s3ItemsForExternalToolProperties,
        )
    }

    @Test
    fun `Should get store external path`() {
        val rawDataset = spark.createDataFrame(RAW_ITEMS, TestRow::class.java)

        adapter.storeExternal(CODING_SYSTEM, rawDataset)

        "AU,US,JP".split(",").map {
                "${s3ItemsForExternalToolProperties.countryPaths[it.lowercase()]}/${CODING_SYSTEM}"
            }.forEach { path ->

            val externalDs = spark.read().parquet(path)

            assertEquals((rawDataset).collectAsList(), externalDs.collectAsList())
        }
    }

    data class TestRow(val source_code: String, val source_a: String)
}