package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.common.consts.CodingSystems.JMDC_DRUG
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.jmdc.row.JmdcDrugRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class JmdcDrugEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private val DATE_VERSION = "20230303"
        private val OUTPUT_ROWS = listOf(
            JmdcDrugRow(
                "100000041358",
                "Calonal",
                "Calonal Fine Granules 20%",
                "N02BE01",
                "N02B-",
                "1141007C1075",
                "mg",
                "200.000000",
                "1",
                "Acetaminophen",
                "Oral Use",
                "Fine Granule",
                "Powder(Oral Use)",
            ),
            JmdcDrugRow(
                "100000084687",
                "Fluticasone [SANWA]",
                "Fluticasone Nasal Solution 50ug [SANWA] 56sprays",
                "R01AD08",
                "R01A1",
                "1329707Q3230",
                null,
                null,
                "1",
                "Fluticasone Propionate",
                "External Use",
                "Nasal Solution",
                "Ophthalmologic/Otorhinolaryngologic Use",
            )
        )
    }

    override fun getCodingSystemName(): String {
        return JMDC_DRUG
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(JMDC_DRUG to "etl/jmdc"), DATE_VERSION)

        EtlJobExecutor(JmdcDrugEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }


    private fun assertDataset(path: String) {
        val ds = sparkReadParquet(path)
        assert(ds.count() == 2L)
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.brand_name(), foundItem.getAs("brand_name"))
            assertEquals(it.description(), foundItem.getAs("description"))
            assertEquals(it.atc_code(), foundItem.getAs("atc_code"))
            assertEquals(it.atc_ephmra(), foundItem.getAs("atc_ephmra"))
            assertEquals(it.nhi_code(), foundItem.getAs("nhi_code"))
            assertEquals(it.strength_unit(), foundItem.getAs("strength_unit"))
            assertEquals(it.strength_number(), foundItem.getAs("strength_number"))
            assertEquals(it.generic_flag(), foundItem.getAs("generic_flag"))
            assertEquals(it.drug_name(), foundItem.getAs("drug_name"))
            assertEquals(it.drug_usage(), foundItem.getAs("drug_usage"))
            assertEquals(it.dose_form_sml(), foundItem.getAs("dose_form_sml"))
            assertEquals(it.dose_form_med(), foundItem.getAs("dose_form_med"))
        }
    }
}