package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.common.consts.CodingSystems.JMDC_DIAGNOSIS
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.jmdc.row.JmdcDiagnosisRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class JmdcDiagnosisEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private val DATE_VERSION = "20230303"
        private val OUTPUT_ROWS = listOf(
            JmdcDiagnosisRow("8834451", "knee bruise", "S800"),
            JmdcDiagnosisRow("2392027", "facial skin neoplasm", "D485")
        )
    }

    override fun getCodingSystemName(): String {
        return JMDC_DIAGNOSIS
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(JMDC_DIAGNOSIS to "etl/jmdc"), DATE_VERSION)

        EtlJobExecutor(JmdcDiagnosisEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }


    private fun assertDataset(path: String) {
        val ds = sparkReadParquet(path)
        assert(ds.count() == 2L)
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.diagnosis_name(), foundItem.getAs("diagnosis_name"))
            assertEquals(it.icd10_code(), foundItem.getAs("icd10_code"))
        }
    }
}