package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.common.consts.CodingSystems.JMDC_PROCEDURE
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.jmdc.row.JmdcProcedureRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class JmdcProcedureEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private val DATE_VERSION = "20230303"
        private val OUTPUT_ROWS = listOf(
            JmdcProcedureRow(
                "160017110",
                "direct bilirubin (DBIL)",
                "D007",
                "test",
                "laboratory tests fee",
                "biochemical test (1)",
                "201804"
            ),
            JmdcProcedureRow(
                "120003470",
                "additional fee for anti-malignant tumor drug management <prescription fee>",
                "F400",
                "administration",
                "prescription fee",
                "prescription fee",
                "201404"
            ),
        )
    }

    override fun getCodingSystemName(): String {
        return JMDC_PROCEDURE
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(JMDC_PROCEDURE to "etl/jmdc"), DATE_VERSION)

        EtlJobExecutor(JmdcProcedureEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }

    private fun assertDataset(path: String) {
        val ds = sparkReadParquet(path)
        assert(ds.count() == 2L)
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.description(), foundItem.getAs("description"))
            assertEquals(it.kubuncode(), foundItem.getAs("kubuncode"))
            assertEquals(it.procedure_cat_med(), foundItem.getAs("procedure_cat_med"))
            assertEquals(it.procedure_cat_sml(), foundItem.getAs("procedure_cat_sml"))
            assertEquals(it.procedure_cat_subclass(), foundItem.getAs("procedure_cat_subclass"))
            assertEquals(it.procedure_version(), foundItem.getAs("procedure_version"))
        }
    }
}