package com.prospection.refdata.etl.jmdc

import com.prospection.refdata.common.consts.CodingSystems.JMDC_MATERIAL
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.jmdc.row.JmdcMaterialRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class JmdcMaterialEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private val DATE_VERSION = "20230303"
        private val OUTPUT_ROWS = listOf(
            JmdcMaterialRow(
                "700710000",
                "ZE019",
                "Film for image recording (half cut) (356 * 432 mm)",
                "Film",
                "Specified material for diagnostic imaging",
                "200804",
            ),
            JmdcMaterialRow(
                "737280000",
                "Z127",
                "Artificial cardiopulmonary circuit (individual function product/safety valve)",
                "Artificial cardiopulmonary circuit",
                "Cardiac/vascular material",
                "201204",
            )
        )
    }

    override fun getCodingSystemName(): String {
        return JMDC_MATERIAL
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(JMDC_MATERIAL to "etl/jmdc"), DATE_VERSION)

        EtlJobExecutor(JmdcMaterialEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }

    private fun assertDataset(path: String) {
        val ds = sparkReadParquet(path)
        assert(ds.count() == 2L)
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.material_code(), foundItem.getAs("material_code"))
            assertEquals(it.material_name(), foundItem.getAs("material_name"))
            assertEquals(it.material_cat_med(), foundItem.getAs("material_cat_med"))
            assertEquals(it.material_cat_large(), foundItem.getAs("material_cat_large"))
            assertEquals(it.material_version(), foundItem.getAs("material_version"))
        }
    }
}