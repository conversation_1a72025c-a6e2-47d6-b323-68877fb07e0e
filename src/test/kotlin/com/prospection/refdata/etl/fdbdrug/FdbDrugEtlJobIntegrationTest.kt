package com.prospection.refdata.etl.fdbdrug

import com.prospection.refdata.common.consts.CodingSystems.FDB_DRUG
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.fdbdrug.rows.FdbDrugOutputRow
import org.apache.spark.sql.Column
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class FdbDrugEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private const val DATE_VERSION = "20220925"
        private val OUTPUT_ROWS = listOf(
            FdbDrugOutputRow(
                "00002010102",
                "AMMONIUM CHLORIDE 500 MG ENS",
                "AMMONIUM CHLORIDE",
                "ammonium chloride",
                arrayOf("ammonium chloride"),
                arrayOf("500.0"),
                arrayOf("mg"),
                "100.0",
                "TABLET, DELAYED RELEASE (ENTERIC COATED)",
                "BOTTLE",
                "ORAL",
                "ELI LILLY & CO.",
                arrayOf("J3260"),
                arrayOf("Tobramycin sulfate inj, 80 MG"),
                "R1C",
                "INORGANIC SALT DIURETICS",
                arrayOf("00002536", "00003096"),
                arrayOf("Diuretic - Inorganic Salt", "Urinary Acidifier - Others"),
                "41190",
                "DIURETICS, OTHER",
                arrayOf("40040000"),
                arrayOf("ACIDIFYING AGENTS"),
                "G04BA01",
                // TODO: Check why the order was changed after upgrading to new spark version
                arrayOf("chronic heart failure", "ventricular rate control in atrial fibrillation"),
                arrayOf("paroxysmal supraventricular tachycardia"),
                arrayOf("I47.1", "428.0", "427.0"),
                "20220413",
                arrayOf("20171005"),
            ),

            FdbDrugOutputRow(
                "00002050101",
                "NEBCIN PED 10 MG/ML VIAL",
                "NEBCIN",
                "tobramycin sulfate",
                arrayOf("tobramycin sulfate"),
                arrayOf("10.0"),
                arrayOf("mg"),
                "2.0",
                "VIAL (ML)",
                "VIAL",
                "INJECTION",
                "ELI LILLY & CO.",
                arrayOf("J3260"),
                arrayOf("Tobramycin sulfate inj, 80 MG"),
                "W1F",
                "AMINOGLYCOSIDE ANTIBIOTICS",
                arrayOf("00000034"),
                arrayOf("Aminoglycoside Antibiotic"),
                "15170",
                "AMINOGLYCOSIDES",
                arrayOf("08120200"),
                arrayOf("AMINOGLYCOSIDE ANTIBIOTICS"),
                "J01GB01",
                arrayOf("synergy for nosocomial pneumonia due to Pseudomonas aeruginosa"),
                arrayOf(),
                arrayOf(),
                null,
                arrayOf(),
            ),
            FdbDrugOutputRow(
                "99999099213",
                "EUA PATIENT ASSESSMENT",
                "EUA PATIENT ASSESSMENT",
                "EUA patient assessment",
                arrayOf("EUA patient assessment"),
                arrayOf("0.0"),
                arrayOf(""),
                "1.0",
                "MISCELLANEOUS",
                "BOX",
                "MISCELLANEOUS",
                "NCPDP EMERGENCY",
                arrayOf("J3260"),
                arrayOf("Tobramycin sulfate inj, 80 MG"),
                "X6C",
                "DIAGNOSTIC TEST DEVICES, SUPPLIES, AND SERVICES",
                arrayOf("00001207", "00005904"),
                arrayOf("Medical Supplies and DME - Miscellaneous Other", "Medical Supply, FDB Superset"),
                "40400",
                "DIAGNOSTICS OTHERS",
                arrayOf("94000000"),
                arrayOf("DEVICES"),
                null,
                arrayOf("drowsy", "fatigue"),
                arrayOf(),
                arrayOf("R53.83", "780.79"),
                "00000000",
                arrayOf("20171005"),
            )
        )
    }

    override fun getCodingSystemName(): String {
        return FDB_DRUG
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(FDB_DRUG to "etl/fdb-drug/raw"), DATE_VERSION)

        EtlJobExecutor(FdbDrugEtlJob(spark, etlJobParams)).execute()

        assertRawItems(etlJobParams.outputPath())
        assertNewSnapshot("${etlJobParams.snapshotPath()}/timestamp=$DATE_VERSION")
    }

    private fun assertRawItems(rawItemsPath: String) {
        val rawItemsDs = sparkReadParquet(rawItemsPath)
        assertDataset(rawItemsDs)

        assert(!rawItemsDs.columns().contains("date_first_listed"))
        assert(!rawItemsDs.columns().contains("date_last_listed"))
    }

    private fun assertNewSnapshot(newSnapshotPath: String) {
        val snapShotDs = sparkReadParquet(newSnapshotPath)
        assertDataset(snapShotDs)
    }

    private fun assertDataset(ds: Dataset<Row>) {
        assert(ds.count() == 3L)
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))

            assertEquals(it.label_name(), foundItem.getAs("label_name"))
            assertEquals(it.brand_name(), foundItem.getAs("brand_name"))
            assertEquals(it.drug_name(), foundItem.getAs("drug_name"))
            assertArrayEquals(it.ingredient_name(), getAsArray(foundItem, "ingredient_name"))
            assertArrayEquals(it.strength_number(), getAsArray(foundItem, "strength_number"))
            assertArrayEquals(
                it.strength_unit_description_abbreviation(),
                getAsArray(foundItem, "strength_unit_description_abbreviation")
            )
            assertEquals(it.package_size(), foundItem.getAs("package_size"))
            assertEquals(it.dose_form(), foundItem.getAs("dose_form"))
            assertEquals(it.package_description(), foundItem.getAs("package_description"))
            assertEquals(it.route_of_administration(), foundItem.getAs("route_of_administration"))
            assertEquals(it.labeller_name(), foundItem.getAs("labeller_name"))
            assertArrayEquals(it.jcode(), getAsArray(foundItem, "jcode"))
            assertArrayEquals(it.jcode_desc(), getAsArray(foundItem, "jcode_desc"))
            assertEquals(it.hic3_classification(), foundItem.getAs("hic3_classification"))
            assertEquals(it.hic3_description(), foundItem.getAs("hic3_description"))
            assertArrayEquals(it.etc_classification(), getAsArray(foundItem, "etc_classification"))
            assertArrayEquals(it.etc_description(), getAsArray(foundItem, "etc_description"))
            assertEquals(it.usc_classification(), foundItem.getAs("usc_classification"))
            assertEquals(it.usc_description(), foundItem.getAs("usc_description"))
            assertArrayEquals(it.ahfs_classification(), getAsArray(foundItem, "ahfs_classification"))
            assertArrayEquals(it.ahfs_description(), getAsArray(foundItem, "ahfs_description"))
            assertEquals(it.atc_code(), foundItem.getAs("atc_code"))
            assertArrayEquals(it.on_label_indication(), getAsArray(foundItem, "on_label_indication"))
            assertArrayEquals(it.off_label_indication(), getAsArray(foundItem, "off_label_indication"))
            assertArrayEquals(it.icd_code(), getAsArray(foundItem, "icd_code"))
            assertEquals(it.obsolete_date(), foundItem.getAs("obsolete_date"))
            assertArrayEquals(it.previous_version_delete_date(), getAsArray(foundItem, "previous_version_delete_date"))
        }
    }
}