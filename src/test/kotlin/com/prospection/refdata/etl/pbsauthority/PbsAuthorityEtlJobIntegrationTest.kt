package com.prospection.refdata.etl.pbsauthority

import com.prospection.refdata.common.consts.CodingSystems.PBS_AUTHORITY
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.common.job.EtlJobParams
import com.prospection.refdata.etl.pbsauthority.rows.PbsAuthorityOutputRow
import org.apache.spark.sql.Column
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class PbsAuthorityEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private const val DATE_VERSION = "20220925"
        private val OUTPUT_ROWS = listOf(
            PbsAuthorityOutputRow("1004", "Acute lactose intolerance in infants up to the age of 12 months."),
            PbsAuthorityOutputRow("1003", "Acute lactose intolerance in children over 1 year of age"),
            PbsAuthorityOutputRow("1002", "Acute bacterial enterocolitis"),
            PbsAuthorityOutputRow("1000", "Abetalipoproteinaemia"),
            PbsAuthorityOutputRow("1001", "Acromegaly"),
        )
    }

    override fun getCodingSystemName(): String {
       return PBS_AUTHORITY
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(
            mapOf(PBS_AUTHORITY to "etl/pbs-authority/raw/PBS_10PRCNT_QUARTERLY_REPORT_CAVEATS_25SEP2022.xlsx"),
            DATE_VERSION
        )

        EtlJobExecutor(PbsAuthorityEtlJob(spark, etlJobParams)).execute()

        assertData(etlJobParams)
    }

    @Test
    fun `should run correctly with xls extension but content is xml`() {
        val etlJobParams = getEtlJobParams(
            mapOf(PBS_AUTHORITY to "etl/pbs-authority/raw/PBS_10PRCNT_QUARTERLY_REPORT_CAVEATS_25SEP2022.xls"),
            DATE_VERSION
        )

        EtlJobExecutor(PbsAuthorityEtlJob(spark, etlJobParams)).execute()

        assertData(etlJobParams)
    }

    private fun assertData(etlJobParams: EtlJobParams) {
        val monthVersion = DATE_VERSION.substring(0, 6)
        assertHistoricalData(etlJobParams.warehousePath(), monthVersion)
        assertNewSnapshot("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
        assertRawItems(etlJobParams.outputPath())
    }

    private fun assertRawItems(rawItemsPath: String) {
        val rawItemsDs = sparkReadParquet(rawItemsPath)
        assertDataset(rawItemsDs)

        assert(!rawItemsDs.columns().contains("date_first_listed"))
        assert(!rawItemsDs.columns().contains("date_last_listed"))
    }

    private fun assertNewSnapshot(newSnapshotPath: String) {
        val snapShotDs = sparkReadParquet(newSnapshotPath)
        assertDataset(snapShotDs)
    }

    private fun assertHistoricalData(warehousePathPrefix: String, monthVersion: String) {
        val dataCategories = listOf(
            Pair("restrictions", sparkOptions()),
        )
        dataCategories.forEach {
            val path = "${warehousePathPrefix}/${it.first}/month=${monthVersion}"
            val df = sparkReadCsv(path)
            assert(df.count() == 5L)

            assertTrue(
                listOf(
                    "[1004,Acute lactose intolerance in infants up to the age of 12 months.]",
                    "[1003,Acute lactose intolerance in children over 1 year of age]",
                    "[1002,Acute bacterial enterocolitis]",
                    "[1000,Abetalipoproteinaemia]",
                    "[1001,Acromegaly]"
                ).containsAll(getDataAsString(df))
            )
        }
    }

    private fun assertDataset(ds: Dataset<Row>) {
        Assertions.assertEquals(5, ds.count())
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            Assertions.assertEquals(it.code(), foundItem.getAs("code"))
            Assertions.assertEquals(it.description(), foundItem.getAs("description"))
        }
    }

    private fun getDataAsString(data: Dataset<Row>): List<String> {
        val result = mutableListOf<String>()
        val iter: Iterator<Row> = data.toLocalIterator()
        while (iter.hasNext()) {
            result.add(iter.next().toString())
        }

        return result
    }
}