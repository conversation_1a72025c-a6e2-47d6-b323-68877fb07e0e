package com.prospection.refdata.etl.mdvitem

import com.prospection.refdata.common.consts.CodingSystems.MDV_HIA_ITEM
import com.prospection.refdata.common.consts.CodingSystems.MDV_ITEM
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.mdvitem.rows.OutputRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class MdvItemEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private const val DATE_VERSION = "20220601"
        private val OUTPUT_ROWS = listOf(
            OutputRow(
                null,
                null,
                null,
                "113000510",
                null,
                "Specific drug treatment management fee 1 (fourth month and after)",
                null,
                null,
                "B0012",
                "特定薬剤治療管理料１（第４月目以降）",
            ),
            OutputRow(
                null,
                null,
                null,
                "114011070",
                null,
                "Injection needle for infusion device add-on fee (others)",
                null,
                null,
                "C1532",
                "注入器用注射針加算（その他）",
            ),
            OutputRow(
                "A10M1",
                "ナテグリニド",
                "Nateglinide",
                "610432027",
                "スターシス錠９０ｍｇ",
                "Starsis Tablets 90mg",
                "Oral medication",
                "Non - Generic",
                null,
                null,
            ),
            OutputRow(
                "A10L0",
                "ボグリボース",
                "Voglibose",
                "610406391",
                "ベイスン錠０．３　０．３ｍｇ",
                "Basen Tablets 0.3 0.3mg",
                "Oral medication",
                "Non - Generic",
                null,
                null,
            ),
        )
    }

    override fun getCodingSystemName(): String {
        return MDV_ITEM
    }

    @Test
    fun `should run correctly to merge both MDV Item and MDV HIA Item`() {
        val etlJobParams = getEtlJobParams(
            mapOf(MDV_ITEM to "etl/mdv-item/raw", MDV_HIA_ITEM to "etl/mdv-hia-item/raw"),
            DATE_VERSION
        )

        EtlJobExecutor(MdvItemEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }

    private fun assertDataset(path: String) {
        val ds = sparkReadParquet(path)
        assertEquals(4, ds.count())
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.atc_ephmra(), foundItem.getAs("atc_ephmra"))
            assertEquals(it.drug_name_jp(), foundItem.getAs("drug_name_jp"))
            assertEquals(it.drug_name(), foundItem.getAs("drug_name"))
            assertEquals(it.description_jp(), foundItem.getAs("description_jp"))
            assertEquals(it.description(), foundItem.getAs("description"))
            assertEquals(it.drug_usage(), foundItem.getAs("drug_usage"))
            assertEquals(it.generic_flag(), foundItem.getAs("generic_flag"))
            assertEquals(it.kubuncode(), foundItem.getAs("kubuncode"))
            assertEquals(it.procedure_name(), foundItem.getAs("procedure_name"))
        }
    }
}