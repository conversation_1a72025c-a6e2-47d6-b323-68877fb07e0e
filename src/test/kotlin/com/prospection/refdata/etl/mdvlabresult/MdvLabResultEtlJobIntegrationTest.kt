package com.prospection.refdata.etl.mdvlabresult

import com.prospection.refdata.common.consts.CodingSystems.MDV_LAB_RESULT
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.common.StandardColumns
import com.prospection.refdata.etl.mdvlabresult.rows.OutputRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class MdvLabResultEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private const val DATE_VERSION = "20220601"
        private val OUTPUT_ROWS = listOf(
            OutputRow(
                "4B035000000000000",
                "FT4",
                "Free thyroxine",
                "ng/dL",
            )
        )
    }

    override fun getCodingSystemName(): String {
        return MDV_LAB_RESULT
    }

    @Test
    fun `should run Mdv Lab Result ETL job correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(MDV_LAB_RESULT to "etl/mdv-lab-result/raw"), DATE_VERSION)

        EtlJobExecutor(MdvLabResultEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }

    private fun assertDataset(rawItemsPath: String) {
        val rawItemsDs = sparkReadParquet(rawItemsPath)

        assertEquals(1, rawItemsDs.count())

        OUTPUT_ROWS.forEach {
            val foundItem = rawItemsDs.where(Column("code").equalTo(it.code())).first()
            assertEquals(it.code(), foundItem.getAs(StandardColumns.Code()))
            assertEquals(it.lab_test_name_jp(), foundItem.getAs("lab_test_name_jp"))
            assertEquals(it.lab_test_name(), foundItem.getAs("lab_test_name"))
            assertEquals(it.unit(), foundItem.getAs("unit"))
        }
    }

}