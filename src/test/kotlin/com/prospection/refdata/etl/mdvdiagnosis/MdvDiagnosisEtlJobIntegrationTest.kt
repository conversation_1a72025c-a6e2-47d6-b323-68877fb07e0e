package com.prospection.refdata.etl.mdvdiagnosis

import com.prospection.refdata.common.consts.CodingSystems.MDV_DIAGNOSIS
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.mdvdiagnosis.rows.OutputRow
import org.apache.spark.sql.Column
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class MdvDiagnosisEtlJobIntegrationTest : AbstractEtlIntegrationTest() {
    companion object {
        private const val DATE_VERSION = "20220601"
        private val OUTPUT_ROWS = listOf(
            OutputRow(
                "0703002",
                "B169",
                "Ｂ型肝炎",
                "hepatitis B"
            ),
            OutputRow(
                "8845189",
                "E550",
                "未熟児くる病",
                "rickets of prematurity"
            )
        )
    }

    override fun getCodingSystemName(): String {
        return MDV_DIAGNOSIS
    }

    @Test
    fun `should run correctly to merge both MDV Disease and MDV HIA Disease`() {
        val etlJobParams = getEtlJobParams(
            mapOf("MDV Disease" to "etl/mdv-disease", "MDV HIA Disease" to "etl/mdv-hia-disease"),
            DATE_VERSION
        )

        EtlJobExecutor(MdvDiagnosisEtlJob(spark, etlJobParams)).execute()

        assertDataset(etlJobParams.outputPath())
        assertDataset("${etlJobParams.snapshotPath()}/timestamp=${DATE_VERSION}")
    }

    private fun assertDataset(path: String) {
        val ds = sparkReadParquet(path)
        assertEquals(2, ds.count())
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.icd10_code(), foundItem.getAs("icd10_code"))
            assertEquals(it.diagnosis_name_jp(), foundItem.getAs("diagnosis_name_jp"))
            assertEquals(it.diagnosis_name(), foundItem.getAs("diagnosis_name"))
        }
    }
}