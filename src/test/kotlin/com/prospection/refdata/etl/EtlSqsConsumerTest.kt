package com.prospection.refdata.etl

import com.prospection.refdata.common.consts.CodingSystems
import com.prospection.refdata.config.ApplicationProperties
import com.prospection.refdata.job.domain.Job
import com.prospection.refdata.job.domain.JobPort
import org.apache.spark.sql.SparkSession
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock
import org.mockito.Mockito.reset
import org.mockito.Mockito.times
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDateTime

internal class EtlSqsConsumerTest {
    companion object {
        private const val TEST_CLASSIFICATION_NAME = "test-classification"
    }

    private val jobPort: JobPort = mock(JobPort::class.java)

    private val sparkSession = mock(SparkSession::class.java)

    private val applicationProperties = mock(ApplicationProperties::class.java)

    private val etlJobWrapper = mock(EtlJobWrapper::class.java)

    private val etlSqsConsumer = EtlSqsConsumer(
        jobPort,
        sparkSession,
        applicationProperties,
        etlJobWrapper,
    )

    @BeforeEach
    fun setup() {
        reset(jobPort)
    }

    @Test
    fun `Should throw an exception if there's a running job`() {
        doReturn(true).`when`(jobPort).existsRunningJob()
        doReturn(
            Job(
                id = "",
                name = "",
                createdAt = LocalDateTime.now(),
                createdBy = "",
                lastModifiedAt = LocalDateTime.now()
            )
        ).whenever(jobPort).getLatestJob()

        val exception = assertThrows(RuntimeException::class.java) {
            etlSqsConsumer.receiveMessage(EtlMessage(TEST_CLASSIFICATION_NAME))
        }

        assertThat(exception.message).contains("A new message couldn't be processed due to an existing job")
    }

    @Test
    fun `Should throw an exception when there is no classification in the message`() {
        doReturn(false).`when`(jobPort).existsRunningJob()

        val exception = assertThrows(RuntimeException::class.java) {
            etlSqsConsumer.receiveMessage(EtlMessage())
        }

        assertThat(exception.message).contains("No classification found in sqs message")
    }

    @Test
    fun `Should throw an exception when there is no version in the message`() {
        doReturn(false).`when`(jobPort).existsRunningJob()

        val exception = assertThrows(RuntimeException::class.java) {
            etlSqsConsumer.receiveMessage(EtlMessage(classification = TEST_CLASSIFICATION_NAME))
        }

        assertThat(exception.message).contains("No version found in sqs message")
    }

    @Test
    fun `Should start a job when there is etl job for classification`() {
        doReturn(false).`when`(jobPort).existsRunningJob()
        doReturn(Job(
            id = "",
            name = "",
            createdAt = LocalDateTime.now(),
            createdBy = "",
            lastModifiedAt = LocalDateTime.now()
        )).`when`(jobPort).startJob(any(), any(), any())

        val version = "20220601"

        assertDoesNotThrow {
            etlSqsConsumer.receiveMessage(EtlMessage(
                classification = CodingSystems.PBS_DRUG,
                version = version,
                paths = mapOf(Pair(CodingSystems.PBS_DRUG, EtlInputPath("bucket", "path"))),
                ))
        }

        verify(jobPort, times(1)).existsRunningJob()
        verify(jobPort, times(1)).startJob(
            eq("etl job for ${CodingSystems.PBS_DRUG} with version: $version"),
            eq("etl-sqs-consumer"),
            any()
        )
    }
}