package com.prospection.refdata.etl.icd10

import com.prospection.refdata.common.consts.CodingSystems.ICD_DIAGNOSIS
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.icd10.rows.IcdOutputRow
import org.apache.spark.sql.Column
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class IcdDiagnosisEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private val DATE_VERSION = "20230303"
        private val OUTPUT_ROWS = listOf(
            IcdOutputRow(
                "0010",
                "Cholera due to vibrio cholerae ä é è",
                "Cholera d/t vib cholerae",
                "ICD-9-CM Dx",
                "DIG001",
                "Intestinal infection",
                arrayOf("DIG001", "INF003", "NVS001"),
                arrayOf("Bacterial infections", "Intestinal infection", "Meningitis"),
            ),
            IcdOutputRow(
                "A00",
                "Cholera",
                "Cholera",
                "ICD-10-CM",
                null,
                null,
                arrayOf(),
                arrayOf(),
            ),
            IcdOutputRow(
                "A000",
                "Cholera due to Vibrio cholerae 01, biovar cholerae",
                "Cholera due to Vibrio cholerae 01, biovar cholerae",
                "ICD-10-CM",
                "DIG001",
                "Intestinal infection",
                arrayOf("DIG001", "INF003"),
                arrayOf("Intestinal infection", "Bacterial infections"),
            ),
            IcdOutputRow(
                "A001",
                "Cholera due to Vibrio cholerae 01, biovar eltor",
                "Cholera due to Vibrio cholerae 01, biovar eltor",
                "ICD-10-CM",
                "DIG001",
                "Intestinal infection",
                arrayOf("DIG001", "INF003"),
                arrayOf("Intestinal infection", "Bacterial infections"),
            ),
            IcdOutputRow(
                "A009",
                "Cholera, unspecified",
                "Cholera, unspecified",
                "ICD-10-CM",
                "DIG001",
                "Intestinal infection",
                arrayOf("DIG001", "INF003"),
                arrayOf("Intestinal infection", "Bacterial infections"),
            ),
            IcdOutputRow(
                "A01",
                "Typhoid and paratyphoid fevers",
                "Typhoid and paratyphoid fevers",
                "ICD-10-CM",
                null,
                null,
                arrayOf(),
                arrayOf(),
            ),
            IcdOutputRow(
                "A010",
                "Typhoid fever",
                "Typhoid fever",
                "ICD-10-CM",
                null,
                null,
                arrayOf(),
                arrayOf(),
            ),
            IcdOutputRow(
                "A0100",
                "Typhoid fever, unspecified",
                "Typhoid fever, unspecified",
                "ICD-10-CM",
                "DIG001",
                "Intestinal infection",
                arrayOf("DIG001", "INF003"),
                arrayOf("Intestinal infection", "Bacterial infections"),
            ),
            IcdOutputRow(
                "A0101",
                "Typhoid meningitis",
                "Typhoid meningitis",
                "ICD-10-CM",
                "INF003",
                "Bacterial infections",
                arrayOf("INF003", "NVS001"),
                arrayOf("Bacterial infections", "Meningitis"),
            ),
            IcdOutputRow(
                "A0102",
                "Typhoid fever with heart involvement",
                "Typhoid fever with heart involvement",
                "ICD-10-CM",
                "INF003",
                "Bacterial infections",
                arrayOf("INF003"),
                arrayOf("Bacterial infections"),
            ),
            IcdOutputRow(
                "A0103",
                "Typhoid pneumonia",
                "Typhoid pneumonia",
                "ICD-10-CM",
                "INF003",
                "Bacterial infections",
                arrayOf("INF003", "RSP002"),
                arrayOf("Bacterial infections", "Pneumonia (except that caused by tuberculosis)"),
            )
        )
    }


    override fun getCodingSystemName(): String {
        return ICD_DIAGNOSIS
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(ICD_DIAGNOSIS to "etl/icd-diagnosis"), DATE_VERSION)

        EtlJobExecutor(IcdDiagnosisEtlJob(spark, etlJobParams)).execute()

        assertRawItems(etlJobParams.outputPath())
        assertNewSnapshot("${etlJobParams.snapshotPath()}/timestamp=$DATE_VERSION")
    }

    private fun assertRawItems(rawItemsPath: String) {
        val rawItemsDs = sparkReadParquet(rawItemsPath)
        assertDataset(rawItemsDs)
    }

    private fun assertNewSnapshot(newSnapshotPath: String) {
        val snapShotDs = sparkReadParquet(newSnapshotPath)
        assertDataset(snapShotDs)
    }

    private fun assertDataset(ds: Dataset<Row>) {
        assert(ds.count() == 11L)
        OUTPUT_ROWS.forEach {
            val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.description(), foundItem.getAs("description"))
            assertEquals(it.short_description(), foundItem.getAs("short_description"))
            assertEquals(it.icd_type(), foundItem.getAs("icd_type"))
            assertEquals(it.default_ccsr_code(), foundItem.getAs("default_ccsr_code"))
            assertEquals(it.default_ccsr_description(), foundItem.getAs("default_ccsr_description"))
            assertArrayEquals(it.all_ccsr_codes(), getAsArray(foundItem, "all_ccsr_codes"))
            assertArrayEquals(it.all_ccsr_descriptions(), getAsArray(foundItem, "all_ccsr_descriptions"))
        }
    }

}