package com.prospection.refdata.etl.icd10

import com.prospection.refdata.common.consts.CodingSystems.ICD_PROCEDURE
import com.prospection.refdata.etl.AbstractEtlIntegrationTest
import com.prospection.refdata.etl.EtlJobExecutor
import com.prospection.refdata.etl.icd10.rows.IcdOutputRow
import org.apache.spark.sql.Column
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.junit.jupiter.api.Assertions.assertArrayEquals
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class IcdProcedureEtlJobIntegrationTest : AbstractEtlIntegrationTest() {

    companion object {
        private val DATE_VERSION = "20230303"
        private val OUTPUT_ROWS = listOf(
            IcdOutputRow(
                "0016070",
                "Bypass Cerebral Ventricle to Nasopharynx with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Nasophar with Autol Sub, Open Approach",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016071",
                "Bypass Cerebral Ventricle to Mastoid Sinus with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Mastoid Sinus w Autol Sub, Open",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016072",
                "Bypass Cerebral Ventricle to Atrium with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Atrium with Autol Sub, Open Approach",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016073",
                "Bypass Cerebral Ventricle to Blood Vessel with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Blood Vess w Autol Sub, Open",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016074",
                "Bypass Cerebral Ventricle to Pleural Cavity with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Pleural Cav w Autol Sub, Open",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016075",
                "Bypass Cerebral Ventricle to Intestine with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Intestine with Autol Sub, Open Approach",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016076",
                "Bypass Cerebral Ventricle to Peritoneal Cavity with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Periton Cav w Autol Sub, Open",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016077",
                "Bypass Cerebral Ventricle to Urinary Tract with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Urinary Tract w Autol Sub, Open",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0016078",
                "Bypass Cerebral Ventricle to Bone Marrow with Autologous Tissue Substitute, Open Approach",
                "Bypass Cereb Vent to Bone Mar with Autol Sub, Open Approach",
                "ICD-10-PCS",
                "CNS010",
                "Cerebrospinal fluid shunt procedures",
                arrayOf("CNS010"),
                arrayOf("Cerebrospinal fluid shunt procedures"),
            ),
            IcdOutputRow(
                "0049",
                "Supersaturated oxygen therapy",
                "SuperSat O2 therapy",
                "ICD-9-CM Px",
                "ESA011",
            "Hyperbaric oxygen therapy",
                arrayOf("ESA011"),
                arrayOf("Hyperbaric oxygen therapy")
            ),
            IcdOutputRow(
                "HZ99ZZZ",
                "Pharmacotherapy for Substance Abuse Treatment, Other Replacement Medication",
                "Pharmacotherapy for Substance Abuse, Oth Replace Med",
                "ICD-10-PCS",
                "SUD002",
                "Pharmacotherapy for substance use",
                arrayOf("SUD002"),
                arrayOf("Pharmacotherapy for substance use"),
            )
        )
    }

    override fun getCodingSystemName(): String {
        return ICD_PROCEDURE
    }

    @Test
    fun `should run correctly`() {
        val etlJobParams = getEtlJobParams(mapOf(ICD_PROCEDURE to "etl/icd-procedure"), DATE_VERSION)

        EtlJobExecutor(IcdProcedureEtlJob(spark, etlJobParams)).execute()

        assertRawItems(etlJobParams.outputPath())
        assertNewSnapshot("${etlJobParams.snapshotPath()}/timestamp=$DATE_VERSION")
    }

    private fun assertRawItems(rawItemsPath: String) {
        val rawItemsDs = sparkReadParquet(rawItemsPath)
        assertDataset(rawItemsDs)
    }

    private fun assertNewSnapshot(newSnapshotPath: String) {
        val snapShotDs = sparkReadParquet(newSnapshotPath)
        assertDataset(snapShotDs)
    }

    private fun assertDataset(ds: Dataset<Row>) {
        assert(ds.count() == 11L)
        OUTPUT_ROWS.forEach {
             val foundItem = ds.where(Column("code").equalTo(it.code())).first()

            assertEquals(it.code(), foundItem.getAs("code"))
            assertEquals(it.description(), foundItem.getAs("description"))
            assertEquals(it.short_description(), foundItem.getAs("short_description"))
            assertEquals(it.icd_type(), foundItem.getAs("icd_type"))
            assertEquals(it.default_ccsr_code(), foundItem.getAs("default_ccsr_code"))
            assertEquals(it.default_ccsr_description(), foundItem.getAs("default_ccsr_description"))
            assertArrayEquals(it.all_ccsr_codes(), getAsArray(foundItem, "all_ccsr_codes"))
            assertArrayEquals(it.all_ccsr_descriptions(), getAsArray(foundItem, "all_ccsr_descriptions"))
        }
    }
}