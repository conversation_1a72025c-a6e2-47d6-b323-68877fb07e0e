package com.prospection.refdata.etl

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.config.S3Path
import com.prospection.refdata.etl.common.SparkOptions
import com.prospection.refdata.etl.common.job.EtlJobParams
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.jetbrains.kotlinx.spark.api.asScalaMap
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired

abstract class AbstractEtlIntegrationTest : AbstractIntegrationTest() {

    abstract fun getCodingSystemName(): String

    @Autowired
    private lateinit var codingSystemJpaRepository: CodingSystemJpaRepository

    @BeforeEach
    fun init() {
        initialiseCodingSystem(getCodingSystemName())
    }

    protected fun initialiseCodingSystem(codingSystemName: String) {
        val codingSystemEntity = CodingSystemEntity(name = codingSystemName, country = "AU,US,JP")
        codingSystemJpaRepository.save(codingSystemEntity)
    }

    protected fun sparkOptions(delimiter: String = ",") = SparkOptions(
        true,
        delimiter,
        "\"",
        "\"",
        "UTF-8"
    )

    protected fun sparkReadCsv(path: String): Dataset<Row> {
        return spark.read().options(sparkOptions().toMap()).csv(path)
    }

    protected fun sparkReadParquet(path: String): Dataset<Row> {
        return spark.read().options(sparkOptions().toMap()).parquet(path)!!
    }

    protected fun getEtlJobParams(resourcePaths: Map<String, String>, dateVersion: String): EtlJobParams {
        val codingSystem = getCodingSystemName()
        val snapshotPathPrefix = "s3a://${applicationProperties.s3Bucket}/${S3Path.SNAPSHOTS}/${codingSystem}"
        val warehousePathPrefix = "s3a://${applicationProperties.s3Bucket}/${S3Path.WAREHOUSE}/${codingSystem}"
        val uploadPath = "s3a://${applicationProperties.s3Bucket}/${S3Path.RAW_UPLOAD}"
        val dataPaths = resourcePaths.map { (codingSystem, path) ->
            codingSystem  to javaClass.classLoader.getResource(path)?.path
         }.toMap()

        return EtlJobParams(
            dateVersion,
            dataPaths.asScalaMap(),
            uploadPath,
            warehousePathPrefix,
            snapshotPathPrefix,
        )
    }
}