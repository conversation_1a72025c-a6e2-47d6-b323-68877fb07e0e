package com.prospection.refdata.etl

import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.CodingSystemService
import com.prospection.refdata.common.consts.CodingSystems
import com.prospection.refdata.common.domain.GluePort
import com.prospection.refdata.common.integration.GlueAdapter
import com.prospection.refdata.job.notification.JobStatusSNSNotifier
import com.prospection.refdata.items.domain.ItemsService
import com.prospection.refdata.items.domain.RawItemsMetadata
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.function.Executable
import org.junit.jupiter.api.Assertions.assertThrows
import java.time.format.DateTimeParseException
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.doThrow
import org.mockito.kotlin.times
import org.mockito.kotlin.never
import java.time.LocalDate
import java.time.LocalDateTime

class EtlJobWrapperTest {

    companion object {
        private const val CLASSIFICATION = "classification"
        private const val VERSION = "20220701"
        private const val JOB_NAME = "PBS Item"
        private const val EXPECTED_RESULT = "success"
        private val VERSION_DATE = LocalDate.of(2022, 7, 1)

        private lateinit var mockNotifier: JobStatusSNSNotifier

        @JvmStatic
        @BeforeAll
        fun setupAll() {
            // Create a mock of JobStatusSNSNotifier
            mockNotifier = mock(JobStatusSNSNotifier::class.java)

            // Use reflection to set the static instance field
            val instanceField = JobStatusSNSNotifier::class.java.getDeclaredField("instance")
            instanceField.isAccessible = true
            instanceField.set(null, mockNotifier)
        }

        @JvmStatic
        @AfterAll
        fun teardownAll() {
            // Reset the instance field to null
            val instanceField = JobStatusSNSNotifier::class.java.getDeclaredField("instance")
            instanceField.isAccessible = true
            instanceField.set(null, null)
        }
    }

    private val etlJobExecutor = mock(EtlJobExecutor::class.java)

    private val itemsService = mock(ItemsService::class.java)

    private val codingSystemService = mock(CodingSystemService::class.java)

    private val gluePort = mock(GluePort::class.java)

    private val etlJobWrapper = EtlJobWrapper(itemsService, codingSystemService, gluePort)

    @BeforeEach
    fun init() {
        doReturn(EXPECTED_RESULT).`when`(etlJobExecutor).execute()
        doReturn(JOB_NAME).`when`(etlJobExecutor).jobName

        // Reset the mock before each test
        org.mockito.Mockito.reset(mockNotifier)
    }

    private fun setupMetadataMocks() {
        // Mock the getRawItemsMetadata method for normal tests
        val mockCodingSystem = mock(CodingSystem::class.java)
        doReturn(CLASSIFICATION).`when`(mockCodingSystem).name

        val mockRawItemsMetadata = RawItemsMetadata(
            codingSystem = mockCodingSystem,
            sourceAttributes = listOf("code", "name", "description"),
            totalItem = 200L,
            newItem = 100L,
            deletedItem = 0L,
            createdBy = "test-user",
            createdAt = LocalDateTime.now()
        )

        // For normal tests, return the mock metadata
        doReturn(mockRawItemsMetadata).`when`(itemsService).getRawItemsMetadata(CLASSIFICATION)
        doReturn(null).`when`(itemsService).getRawItemsMetadata(CodingSystems.MDV_ITEM)
    }

    @Test
    fun `should run correctly and include metadata in notification`() {
        setupMetadataMocks()

        val result = etlJobWrapper.run(
            etlJobExecutor,
            CLASSIFICATION,
            VERSION,
        )

        assertEquals(EXPECTED_RESULT, result)
        verify(itemsService, times(1)).processAfterImport(CLASSIFICATION, JOB_NAME, VERSION, false)
        verify(itemsService, times(1)).getRawItemsMetadata(CLASSIFICATION)
        verify(codingSystemService, times(1)).updateEtlStatus(CLASSIFICATION, VERSION_DATE)
        verify(gluePort, times(1)).startCrawler()

        // Verify that the notify method is called
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should run correctly with version format yyyy-MM-dd`() {
        val result = etlJobWrapper.run(
            etlJobExecutor,
            CLASSIFICATION,
            "2022-07-01",
        )

        assertEquals(EXPECTED_RESULT, result)
        verify(itemsService, times(1)).processAfterImport(CLASSIFICATION, JOB_NAME, "2022-07-01", false)
        verify(codingSystemService, times(1)).updateEtlStatus(CLASSIFICATION, VERSION_DATE)
        verify(gluePort, times(1)).startCrawler()

        // Verify that the notify method is called
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should import MDV Item and fallback to getRawItemsCount when metadata is not available`() {
        setupMetadataMocks()

        val result = etlJobWrapper.run(
            etlJobExecutor,
            CodingSystems.MDV_ITEM,
            VERSION,
        )

        assertEquals(EXPECTED_RESULT, result)
        verify(itemsService, times(1)).processAfterImport(CodingSystems.MDV_ITEM, JOB_NAME, VERSION, false)
        verify(itemsService, times(1)).getRawItemsMetadata(CodingSystems.MDV_ITEM)
        verify(codingSystemService, times(1)).updateEtlStatus(CodingSystems.MDV_ITEM, VERSION_DATE)
        verify(gluePort, times(1)).startCrawler()

        // Verify that the notify method is called
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should handle exception when etlJob execute throws exception`() {
        // Arrange
        val exception = RuntimeException("Test exception")
        doThrow(exception).`when`(etlJobExecutor).execute()

        // Act & Assert
        val thrownException = assertThrows(
            RuntimeException::class.java,
            {
                etlJobWrapper.run(
                    etlJobExecutor,
                    CLASSIFICATION,
                    VERSION,
                )
            }
        )

        // Verify the exception is the one we threw
        assertEquals("Test exception", thrownException.message)

        // Verify method calls
        verify(itemsService, never()).processAfterImport(any(), any(), any(), any())
        verify(codingSystemService, never()).updateEtlStatus(any(), any())
        verify(gluePort, never()).startCrawler()

        // Verify that the notify method is called even when an exception is thrown
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should handle exception when itemService processAfterImport throws exception`() {
        // Arrange
        val exception = RuntimeException("Item service exception")
        doThrow(exception).`when`(itemsService).processAfterImport(any(), any(), any(), any())

        // Act & Assert
        val thrownException = assertThrows(
            RuntimeException::class.java,
            {
                etlJobWrapper.run(
                    etlJobExecutor,
                    CLASSIFICATION,
                    VERSION,
                )
            }
        )

        // Verify the exception is the one we threw
        assertEquals("Item service exception", thrownException.message)

        // Verify method calls
        verify(etlJobExecutor, times(1)).execute()
        verify(itemsService, times(1)).processAfterImport(CLASSIFICATION, JOB_NAME, VERSION, false)
        verify(codingSystemService, never()).updateEtlStatus(any(), any())
        verify(gluePort, never()).startCrawler()

        // Verify that the notify method is called even when an exception is thrown
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should handle exception when codingSystemService updateEtlStatus throws exception`() {
        // Arrange
        val exception = RuntimeException("Coding system service exception")
        doThrow(exception).`when`(codingSystemService).updateEtlStatus(any(), any())

        // Act & Assert
        val thrownException = assertThrows(
            RuntimeException::class.java,
            {
                etlJobWrapper.run(
                    etlJobExecutor,
                    CLASSIFICATION,
                    VERSION,
                )
            }
        )

        // Verify the exception is the one we threw
        assertEquals("Coding system service exception", thrownException.message)

        // Verify method calls
        verify(etlJobExecutor, times(1)).execute()
        verify(itemsService, times(1)).processAfterImport(CLASSIFICATION, JOB_NAME, VERSION, false)
        verify(codingSystemService, times(1)).updateEtlStatus(CLASSIFICATION, VERSION_DATE)
        verify(gluePort, never()).startCrawler()

        // Verify that the notify method is called even when an exception is thrown
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should handle exception when gluePort startCrawler throws exception`() {
        // Arrange
        val exception = RuntimeException("Glue port exception")
        doThrow(exception).`when`(gluePort).startCrawler()

        // Act & Assert
        val thrownException = assertThrows(
            RuntimeException::class.java,
            {
                etlJobWrapper.run(
                    etlJobExecutor,
                    CLASSIFICATION,
                    VERSION,
                )
            }
        )

        // Verify the exception is the one we threw
        assertEquals("Glue port exception", thrownException.message)

        // Verify method calls
        verify(etlJobExecutor, times(1)).execute()
        verify(itemsService, times(1)).processAfterImport(CLASSIFICATION, JOB_NAME, VERSION, false)
        verify(codingSystemService, times(1)).updateEtlStatus(CLASSIFICATION, VERSION_DATE)
        verify(gluePort, times(1)).startCrawler()

        // Verify that the notify method is called even when an exception is thrown
        verify(mockNotifier, times(1)).notify(any())
    }

    @Test
    fun `should handle invalid date format exception`() {
        // Act & Assert
        val exception = assertThrows(
            DateTimeParseException::class.java,
            {
                etlJobWrapper.run(
                    etlJobExecutor,
                    CLASSIFICATION,
                    "invalid-date-format",
                )
            }
        )

        // Verify that the exception message contains information about the invalid date format
        assertTrue(exception.message?.contains("invalid-date-format") ?: false)

        // Verify that the notify method is called even when an exception is thrown
        verify(mockNotifier, times(1)).notify(any())
    }
}
