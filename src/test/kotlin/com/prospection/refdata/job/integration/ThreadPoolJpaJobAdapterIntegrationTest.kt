package com.prospection.refdata.job.integration

import com.prospection.refdata.AbstractIntegrationTest
import com.prospection.refdata.job.domain.JobStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.LocalDateTime

internal class ThreadPoolJpaJobAdapterIntegrationTest : AbstractIntegrationTest() {
    @Autowired
    private lateinit var jobJpaRepository: JobJpaRepository

    @Autowired
    protected lateinit var adapter: ThreadPoolJpaJobAdapter

    @Test
    fun `Should return the latest job by comparing createdAt`() {
        val earlier = LocalDateTime.of(2000, 1, 1, 1, 0, 0)
        val later = LocalDateTime.of(2022, 1, 1, 1, 0, 0)

        jobJpaRepository.saveAll(
            listOf(
                JobEntity(
                    uuid = "uuid1",
                    name = "name1",
                    status = JobStatus.RUNNING,
                    createdAt = earlier,
                    lastModifiedAt = later,
                    createdBy = "thomas",
                ),
                JobEntity(
                    uuid = "uuid2",
                    name = "name2",
                    status = JobStatus.RUNNING,
                    createdAt = later,
                    lastModifiedAt = earlier,
                    createdBy = "thomas",
                ),
            )
        )

        val latestJob = adapter.getLatestJob()

        assertThat(latestJob)
            .extracting("id")
            .isEqualTo("uuid2")
    }
}