package com.prospection.refdata.job.integration

import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.GenerateIdPort
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.job.domain.Job
import com.prospection.refdata.job.domain.JobStatus
import com.prospection.refdata.job.domain.JobWrappedWithEvents
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.kotlinx.spark.api.SparkSession
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.only
import org.mockito.kotlin.reset
import org.mockito.kotlin.spy
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import java.time.LocalDateTime
import java.util.concurrent.Callable

internal class ThreadPoolJpaJobAdapterUnitTest {
    private val threadPoolTaskExecutor: ThreadPoolTaskExecutor = mock()
    private val dateTimePort: DateTimePort = mock()
    private val userPort: UserPort = mock()
    private val jobJpaRepository: JobJpaRepository = mock()
    private val jobEntityMapper: JobEntityMapper = mock()
    private val generateIdPort: GenerateIdPort = mock()
    private val spark: SparkSession = mock()

    private val adapter: ThreadPoolJpaJobAdapter = spy(
        ThreadPoolJpaJobAdapter(
            threadPoolTaskExecutor,
            dateTimePort,
            userPort,
            jobJpaRepository,
            jobEntityMapper,
            generateIdPort,
            spark
        )
    )

    private val testDateTime = LocalDateTime.of(2022, 1, 1, 0, 0, 0)
    private val testUser = "thomas"
    private val testId = "test-id"

    @BeforeEach
    fun setup() {
        reset(threadPoolTaskExecutor)
        reset(dateTimePort)
        reset(userPort)
        reset(jobJpaRepository)
        reset(jobEntityMapper)
        reset(generateIdPort)

        reset(adapter)

        doReturn(testDateTime).whenever(dateTimePort).now()
        doReturn(testUser).whenever(userPort).getCurrentUserId()
        doReturn(testId).whenever(generateIdPort).generate()
    }

    @Test
    fun `Should trigger a job in onStart`() {
        adapter.startJob("whatever") { null }

        verify(adapter, times(1)).onJobStart(
            eq(
                Job(
                    id = testId,
                    name = "whatever",
                    status = JobStatus.RUNNING,
                    createdAt = testDateTime,
                    createdBy = testUser,
                    lastModifiedAt = testDateTime
                )
            )
        )
    }

    @Test
    fun `Should submit a job to the executor`() {
        adapter.startJob("whatever") { null }

        verify(threadPoolTaskExecutor, only()).submit(any<Callable<JobWrappedWithEvents>>())
    }

    @Test
    fun `Should tag running jobs to stopped`() {
        val recent = LocalDateTime.now()
        doReturn(recent).whenever(dateTimePort).now()

        val longTimeAgo = LocalDateTime.of(2000, 1, 1, 1, 0, 0)

        doReturn(
            listOf(
                JobEntity(
                    uuid = "uuid",
                    name = "name",
                    status = JobStatus.RUNNING,
                    createdAt = longTimeAgo,
                    lastModifiedAt = longTimeAgo,
                    createdBy = "thomas",
                )
            )
        ).whenever(jobJpaRepository).findAllByStatus(eq(JobStatus.RUNNING))

        doAnswer<List<JobEntity>> { it.getArgument(0) }.whenever(jobJpaRepository)
            .saveAll(any<List<JobEntity>>())

        val updatedJobs = adapter.tagRunningJobsStoppedWhenApplicationStarts()

        assertThat(updatedJobs)
            .hasSize(1)
            .first()
            .extracting("lastModifiedAt")
            .isEqualTo(recent)

        assertThat(updatedJobs)
            .first()
            .extracting("status")
            .isEqualTo(JobStatus.STOPPED_BY_SERVER_REBOOT)
    }
}