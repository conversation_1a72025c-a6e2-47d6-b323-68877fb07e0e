package com.prospection.refdata.job.domain

import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.only
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import java.time.LocalDateTime

internal class JobWrappedWithEventsTest {
    private val job = Job(
        id = "test-id",
        name = "test name",
        status = JobStatus.RUNNING,
        createdAt = LocalDateTime.of(2022, 1, 1, 0, 0, 0),
        lastModifiedAt = LocalDateTime.of(2022, 1, 1, 0, 0, 0),
        createdBy = "thomas"
    )

    @Test
    fun `Should trigger job function`() {
        val mockJobFunction = mock<() -> String?>()

        val jobWrappedWithEvents = JobWrappedWithEvents(
            job = job,
            jobFunction = mockJobFunction,
            onStart = {},
            onSuccess = {},
            onFailure = {},
            onFinally = {},
        )

        jobWrappedWithEvents.call()

        verify(mockJobFunction, only())()
    }

    @Test
    fun `Should trigger onStart`() {
        val mockOnStart = mock<() -> Unit>()

        val jobWrappedWithEvents = JobWrappedWithEvents(
            job = job,
            jobFunction = { null },
            onStart = mockOnStart,
            onSuccess = {},
            onFailure = {},
            onFinally = {},
        )

        jobWrappedWithEvents.call()

        verify(mockOnStart, only())()
    }

    @Test
    fun `Should trigger onSuccess, onFinally if job function is successful`() {
        val mockOnSuccessFunction = mock<(String?) -> Unit>()
        val mockOnFinallyFunction = mock<() -> Unit>()

        val jobWrappedWithEvents = JobWrappedWithEvents(
            job = job,
            jobFunction = { "valid result" },
            onStart = {},
            onSuccess = mockOnSuccessFunction,
            onFailure = {},
            onFinally = mockOnFinallyFunction,
        )

        jobWrappedWithEvents.call()

        verify(mockOnSuccessFunction, only())(eq("valid result"))
        verify(mockOnFinallyFunction, times(1))()
    }

    @Test
    fun `Should trigger onFailure, onFinally if job function is failed`() {
        val mockOnFailureFunction = mock<(Exception) -> Unit>()
        val mockOnFinallyFunction = mock<() -> Unit>()
        val exception = RuntimeException("test error")
        val jobWrappedWithEvents = JobWrappedWithEvents(
            job = job,
            jobFunction = { throw exception },
            onStart = {},
            onSuccess = {},
            onFailure = mockOnFailureFunction,
            onFinally = mockOnFinallyFunction,
        )

        jobWrappedWithEvents.call()

        verify(mockOnFailureFunction, only())(eq(exception))
        verify(mockOnFinallyFunction, times(1))()
    }
}