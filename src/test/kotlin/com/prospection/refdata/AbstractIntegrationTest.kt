package com.prospection.refdata

import com.prospection.refdata.config.ApplicationProperties
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import jakarta.transaction.Transactional
import org.apache.spark.SparkConf
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.SaveMode
import org.apache.spark.sql.SparkSession
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Order
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.context.ActiveProfiles
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.containers.localstack.LocalStackContainer
import org.testcontainers.utility.DockerImageName
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.glue.GlueClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import software.amazon.awssdk.services.s3.model.DeleteBucketRequest
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.SqsClient

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Transactional
@WithMockUser(roles = ["ADMIN"], value = "<EMAIL>")
@AutoConfigureMockMvc(addFilters = false)
@Import(AbstractIntegrationTest.TestConfig::class)
abstract class AbstractIntegrationTest {
    companion object {
        @JvmStatic
        protected val postgresContainer: PostgreSQLContainer<*> = PostgreSQLContainer("postgres:15.5")

        @JvmStatic
        protected val localstackContainer: LocalStackContainer =
            LocalStackContainer(DockerImageName.parse("localstack/localstack:3.8.1"))
                .withServices(LocalStackContainer.Service.S3)
                .withServices(LocalStackContainer.Service.SQS)

        init {
            // Disable AWS profile file loading to avoid version conflicts
            System.setProperty("aws.configFile", "")
            System.setProperty("aws.sharedCredentialsFile", "")

            // Set AWS region globally to avoid region resolution issues
            System.setProperty("aws.region", "ap-southeast-2")

            // Set AWS credentials globally to avoid credential resolution issues
            System.setProperty("aws.accessKeyId", localstackContainer.accessKey)
            System.setProperty("aws.secretAccessKey", localstackContainer.secretKey)

            localstackContainer.start()
        }
    }

    @TestConfiguration
    class TestConfig {
        @Bean
        @Primary
        fun getS3Client(): S3Client {
            val credentials = AwsBasicCredentials.create("test", "test")

            return S3Client.builder()
                .endpointOverride(localstackContainer.endpoint)
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.of(localstackContainer.region))
                .forcePathStyle(true)
                .build()
        }

        @Bean
        @Primary
        fun sparkSession(): SparkSession {
            // Create the Spark configuration with specific S3A settings for LocalStack
            val sparkConf = SparkConf()
                .setAppName("RefDataServiceTests")
                .setMaster("local[*]")
                // Configure S3A FileSystem with path style access for LocalStack
                .set("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")
                .set("spark.hadoop.fs.s3a.endpoint", localstackContainer.endpoint.toString())
                .set("spark.ui.enabled", "false")
                .set("spark.hadoop.fs.s3a.access.key", localstackContainer.accessKey)
                .set("spark.hadoop.fs.s3a.secret.key", localstackContainer.secretKey)
                .set("spark.hadoop.fs.s3a.path.style.access", "true")
//                .set("spark.hadoop.fs.s3a.connection.ssl.enabled", "false")
                // Fix for 0-byte objects with AWS SDK v2
//                .set("spark.hadoop.fs.s3a.multipart.size", "5242880") // 5MB
//                .set("spark.hadoop.fs.s3a.multipart.threshold", "5242880") // 5MB
                // Disable committers that might cause issues
                .set("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", "2")
                .set("spark.hadoop.fs.s3a.committer.name", "directory")
                // Set required AWS region
                .set("spark.hadoop.fs.s3a.endpoint.region", localstackContainer.region)
                // Specify AWS SDK v2 explicitly
                .set("spark.hadoop.fs.s3a.aws.sdk.version", "2")
                // Add for directory handling
//                .set("spark.hadoop.fs.s3a.directory.marker.retention", "keep")
//                .set("spark.hadoop.fs.s3a.create.directory", "true") // Ensure directories are created
//                .set("spark.hadoop.fs.s3a.connection.timeout", "5000") // Increase timeout for LocalStack
//                .set("spark.hadoop.fs.s3a.attempts.maximum", "3") // Retry failed operations

            // Create and return the SparkSession
            return SparkSession.builder()
                .config(sparkConf)
                .getOrCreate()
        }

        @Bean
        @Primary
        fun getSqsClient(): SqsClient {
            val credentials = AwsBasicCredentials.create("test", "test")
            return SqsClient.builder()
                .endpointOverride(localstackContainer.endpoint)
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.of(localstackContainer.region))
                .build()
        }

        @Bean
        @Primary
        fun getSqsAsyncClient(): SqsAsyncClient {
            val credentials = AwsBasicCredentials.create("test", "test")
            return SqsAsyncClient.builder()
                .endpointOverride(localstackContainer.endpoint)
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.of(localstackContainer.region))
                .build()
        }

        @Bean
        @Primary
        fun getGlueClient(): GlueClient {
            // Use Mockito to create a mock GlueClient to avoid AWS SDK version conflicts
            // Tests that need Glue functionality should mock the GlueAdapter instead
            return Mockito.mock(GlueClient::class.java)
        }

        @Bean
        @Primary
        fun getS3Presigner(): S3Presigner {
            val credentials = AwsBasicCredentials.create("test", "test")
            return S3Presigner.builder()
                .endpointOverride(localstackContainer.endpoint)
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.of(localstackContainer.region))
                .build()
        }
    }

    @Autowired
    protected lateinit var amazonS3: S3Client

    @Autowired
    protected lateinit var amazonSQS: SqsClient

    @Autowired
    protected lateinit var glueClient: GlueClient

    @Autowired
    protected lateinit var spark: SparkSession

    @Autowired
    protected lateinit var applicationProperties: ApplicationProperties

    @BeforeEach
    @Order(1)
    fun setUp() {
        val createBucketRequest = CreateBucketRequest.builder()
            .bucket(applicationProperties.s3Bucket)
            .build()
        amazonS3.createBucket(createBucketRequest)

//        // Create parent directories
//        val parentPath = "items/draft/raw-items/Test Coding System/"
//        val putObjectRequest = PutObjectRequest.builder()
//            .bucket(applicationProperties.s3Bucket)
//            .key(parentPath) // Create directory marker
//            .build()
//        amazonS3.putObject(putObjectRequest, RequestBody.empty())
    }

    @AfterEach
    @Order(Integer.MAX_VALUE)
    fun clearS3Bucket() {
        // List and delete all objects in the bucket
        val listRequest = ListObjectsV2Request.builder()
            .bucket(applicationProperties.s3Bucket)
            .build()

        amazonS3.listObjectsV2(listRequest).contents().forEach { s3Object ->
            val deleteRequest = DeleteObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key(s3Object.key())
                .build()
            amazonS3.deleteObject(deleteRequest)
        }

        // Delete the bucket
        val deleteBucketRequest = DeleteBucketRequest.builder()
            .bucket(applicationProperties.s3Bucket)
            .build()
        amazonS3.deleteBucket(deleteBucketRequest)
    }

    @AfterEach
    @Order(Integer.MAX_VALUE - 1)
    fun clearSparkTables() {
        spark.catalog().clearCache()
    }

//    protected fun createEtlQueue() {
//        try {
//            amazonSQS.createQueue { it.queueName("pd-au-local-etl-queue.fifo") }
//        } catch (e: Exception) {
//            println("Info: SQS queue creation skipped (likely already exists): ${e.message}")
//        }
//    }
//
//    protected fun removeEtlQueue() {
//        try {
//            amazonSQS.deleteQueue {
//                it.queueUrl(
//                    localstackContainer.getEndpointOverride(LocalStackContainer.Service.SQS)
//                        .toString() + "/000000000000/pd-au-local-etl-queue.fifo"
//                )
//            }
//        } catch (e: Exception) {
//            println("Info: SQS queue removal skipped: ${e.message}")
//        }
//    }

    protected fun readParquet(path: String): Dataset<Row> {
        return spark.read().option("header", "true")
            .parquet("s3a://${applicationProperties.s3Bucket}/${path}")
    }

    protected fun readExcel(path: String, sheetName: String): Dataset<Row> {
        return spark.read()
            .format("com.crealytics.spark.excel")
            .option("header", "true")
            .option("inferSchema", "true")
            .option("treatEmptyValuesAsNulls", "true")
            .option("dataAddress", "'${sheetName}'!A1")
            .load("s3a://${applicationProperties.s3Bucket}/${path}")
    }

    protected fun writeCsv(path: String, ds: Dataset<Row>) {
        ScalaSparkItemsFunctions.joinAllArrayColumns(ds)
            .coalesce(1)
            .write()
            .option("header", "true")
            .option("escape", "\"")
            .mode(SaveMode.Overwrite)
            .csv("s3a://${applicationProperties.s3Bucket}/${path}")
    }

    protected fun writeParquet(path: String, ds: Dataset<Row>) {
        ds
            .coalesce(1)
            .write()
            .mode(SaveMode.Overwrite)
            .parquet("s3a://${applicationProperties.s3Bucket}/${path}")
    }

    protected fun getAsArray(row: Row, fieldName: String): Array<String> {
        return row.getList<String>(row.fieldIndex(fieldName)).toTypedArray()
    }
}