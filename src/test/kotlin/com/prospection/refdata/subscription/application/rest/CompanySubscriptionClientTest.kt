package com.prospection.refdata.topic.application.rest

import com.prospection.refdata.common.integration.TestTopicAndWorkflowCreationUtil.Companion.createTestTopic
import com.prospection.refdata.subscription.application.rest.CompanySubscriptionClient
import com.prospection.refdata.subscription.application.rest.dto.CompanySubscriptionDto
import com.prospection.refdata.subscription.application.rest.mapper.CompanySubscriptionDtoMapper
import com.prospection.refdata.subscription.application.rest.mapper.CompanySubscriptionDtoMapperImpl
import com.prospection.refdata.subscription.domain.CompanySubscription
import com.prospection.refdata.topic.domain.EnrichedTopic
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentMatchers.matches
import org.mockito.Mockito.eq
import org.mockito.Mockito.mock
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpEntity
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.web.client.RestTemplate


@ExtendWith(SpringExtension::class)
@ContextConfiguration(classes = [CompanySubscriptionDtoMapperImpl::class])
class CompanySubscriptionClientTest {
    private lateinit var client: CompanySubscriptionClient
    private lateinit var dashxRestTemplate: RestTemplate

    @Autowired
    private lateinit var mapper: CompanySubscriptionDtoMapper

    companion object {
        val testSubscriptionIds = listOf("sub_record_id_1", "sub_record_id_2")
        val testSubscriptions = listOf(
            CompanySubscriptionDto(id = "s1", status = "active", name = "Sub 1", recordId = "sub_record_id_1"),
            CompanySubscriptionDto(id = "s2", status = "expired", name = "Sub 2", recordId = "sub_record_id_2"),
        )
    }

    @BeforeEach
    fun setUp() {
        dashxRestTemplate = mock(RestTemplate::class.java)
        client = CompanySubscriptionClient(dashxRestTemplate, mapper)
    }

    @Test
    fun `should list company subscriptions by subscription IDs`() {
        // given
        whenever(
            dashxRestTemplate.postForObject(
                matches("/api/company-subscriptions"),
                eq(HttpEntity(listOf("sub_record_id_1", "sub_record_id_2"))),
                eq(Array<CompanySubscriptionDto>::class.java)
            )
        ).thenReturn(testSubscriptions.toTypedArray())

        // when
        val result = client.listSubscriptionsByRecordIds(testSubscriptionIds)

        // then
        Assertions.assertEquals(
            listOf(
                CompanySubscription(id = "s1", recordId = "sub_record_id_1", status = "active", name = "Sub 1"),
                CompanySubscription(id = "s2", recordId = "sub_record_id_2", status = "expired", name = "Sub 2")
            ), result
        )
    }

    @Test
    fun `should enrich topics with subscription information`() {
        // given
        val topics = listOf(
            createTestTopic(id = 1, name = "Topic 1", relatedWorkflows = listOf(), subscriptionId = "sub_record_id_1", companyId = "c1"),
            createTestTopic(id = 2, name = "Topic 2", relatedWorkflows = listOf(), subscriptionId = "sub_record_id_2", companyId = "c2"),
            createTestTopic(id = 3, name = "Topic 3", relatedWorkflows = listOf(), companyId = "c2"),
        )

        whenever(
            dashxRestTemplate.postForObject(
                matches("/api/company-subscriptions"),
                eq(HttpEntity(listOf("sub_record_id_1", "sub_record_id_2"))),
                eq(Array<CompanySubscriptionDto>::class.java)
            )
        ).thenReturn(testSubscriptions.toTypedArray())

        whenever(
            dashxRestTemplate.postForObject(
                matches("/api/companies/names"),
                eq(HttpEntity(setOf("c1", "c2"))),
                eq(Map::class.java)
            )
        ).thenReturn(
            mapOf(
                "c1" to "Company 1",
                "c2" to "Company 2"
            )
        )

        // when
        val result = client.enrichTopicsWithCompanyAndSubscription(topics)

        // then
        Assertions.assertEquals(
            listOf(
                EnrichedTopic(
                    topic = createTestTopic(id = 1, name = "Topic 1", relatedWorkflows = listOf(), subscriptionId = "sub_record_id_1", companyId = "c1"),
                    companyName = "Company 1",
                    subscriptionName = "Sub 1",
                    subscriptionStatus = "active"
                ),
                EnrichedTopic(
                    topic = createTestTopic(id = 2, name = "Topic 2", relatedWorkflows = listOf(), subscriptionId = "sub_record_id_2", companyId = "c2"),
                    companyName = "Company 2",
                    subscriptionName = "Sub 2",
                    subscriptionStatus = "expired"
                ),
                EnrichedTopic(
                    topic = createTestTopic(id = 3, name = "Topic 3", relatedWorkflows = listOf(), companyId = "c2"),
                    companyName = "Company 2",
                    subscriptionName = null,
                    subscriptionStatus = null
                )
            ), result
        )
    }

    @Test
    fun `If company name doesn't exist, an exception will be thrown (company name isn't nullable)`() { //
        // given
        val topics = listOf(
            createTestTopic(id = 1, name = "Topic 1", relatedWorkflows = listOf(), companyId = "c1"),
        )

        whenever(
            dashxRestTemplate.postForObject(
                matches("/api/companies/names"),
                eq(HttpEntity(setOf("c1", "c2"))),
                eq(Map::class.java)
            )
        ).thenReturn(
            emptyMap<String, String>()
        )

        // when & then
        assertThrows<NullPointerException> {
            client.enrichTopicsWithCompanyAndSubscription(topics)
        }
    }
}