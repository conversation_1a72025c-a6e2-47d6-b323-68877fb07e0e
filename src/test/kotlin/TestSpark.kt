import org.apache.spark.sql.SparkSession

object TestSpark {
    val spark: SparkSession = SparkSession.builder()
        .appName("Test Spark App")
        .master("local[*]") // Use all available cores for better performance
        .config("spark.sql.shuffle.partitions", 2) // Reduce partitions for small test datasets
        .config("spark.default.parallelism", 2)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.shuffle.compress", "false")
        .config("spark.broadcast.compress", "false")
        .config("spark.ui.enabled", "false")
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer") // Faster serialization
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true") // Optimize partition count
        .getOrCreate()
        .also { it.sparkContext().setLogLevel("WARN") }
}