spring:
  datasource:
    driverClassName: org.h2.Driver
    jdbcUrl: jdbc:h2:mem:test;MODE=PostgreSQL

liquibase:
  contexts: test

server:
  max-http-request-header-size: 20KB # should match with src/main/resources/application.yml for testing. See HeaderSizeTest.java.

application:
  name: test-pd-ref-data-service
  amazon:
    region:
    s3Bucket:
    sns:
      jobNotificationTopic: test
  integration:
    dashxServiceUrl: http://localhost:8080
    customerServiceUrl: http://localhost:11080
    featureToggleService:
      enabled: false
      url: http://localhost:4242
      apiToken: default:development.unleash-insecure-api-token
  job:
    archiveUnusedData:
      cutOffMonths: 3
      chunkSize: 2

tracing:
  # TODO: PF-126 we are going to remove xray completely when upgrade pd-starter-* to the latest version
  enabled: false
