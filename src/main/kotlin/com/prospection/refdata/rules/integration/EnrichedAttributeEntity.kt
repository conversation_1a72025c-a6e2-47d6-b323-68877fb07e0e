package com.prospection.refdata.rules.integration

import org.hibernate.envers.Audited
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import jakarta.persistence.UniqueConstraint

@Audited
@Entity(name = "EnrichedAttribute")
@Table(
    name = "enriched_attribute",
    uniqueConstraints = [UniqueConstraint(columnNames = ["name"])]
)
class EnrichedAttributeEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    var uuid: String,
    var name: String
)