package com.prospection.refdata.rules.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.stereotype.Repository

@Repository
interface EnrichedAttributeValueJpaRepository :
    JpaRepository<EnrichedAttributeValueEntity, Long>,
    JpaSpecificationExecutor<EnrichedAttributeValueEntity>,
    RevisionRepository<EnrichedAttributeValueEntity, Long, Int> {

    @Query("select e from EnrichedAttributeValue e where e.enrichedAttribute.uuid = :attributeUuid order by e.value asc")
    fun findByAttributeUuid(attributeUuid: String): List<EnrichedAttributeValueEntity>

    fun getByUuid(uuid: String): EnrichedAttributeValueEntity
}