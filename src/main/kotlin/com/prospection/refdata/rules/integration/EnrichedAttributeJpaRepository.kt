package com.prospection.refdata.rules.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.stereotype.Repository

@Repository
interface EnrichedAttributeJpaRepository : JpaRepository<EnrichedAttributeEntity, Long>, JpaSpecificationExecutor<EnrichedAttributeEntity>,
    RevisionRepository<EnrichedAttributeEntity, Long, Int> {
    @Query("select e from EnrichedAttribute e order by e.name asc")
    fun list(): List<EnrichedAttributeEntity>

    fun getByUuid(uuid: String): EnrichedAttributeEntity
}