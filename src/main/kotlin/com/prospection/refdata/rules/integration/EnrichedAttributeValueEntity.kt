package com.prospection.refdata.rules.integration

import org.hibernate.envers.Audited
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import jakarta.persistence.UniqueConstraint

@Audited
@Entity(name = "EnrichedAttributeValue")
@Table(
    name = "enriched_attribute_value",
    uniqueConstraints = [UniqueConstraint(columnNames = ["enriched_attribute_id", "value"])]
)
class EnrichedAttributeValueEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    var uuid: String,
    var value: String,
    @ManyToOne(optional = false) var enrichedAttribute: EnrichedAttributeEntity,
)