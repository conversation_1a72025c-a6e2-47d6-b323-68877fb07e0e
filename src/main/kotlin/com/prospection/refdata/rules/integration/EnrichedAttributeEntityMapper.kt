package com.prospection.refdata.rules.integration

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.rules.domain.EnrichedAttribute
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface EnrichedAttributeEntityMapper : DomainMapper<EnrichedAttribute, EnrichedAttributeEntity>,
    EntityMapper<EnrichedAttributeEntity, EnrichedAttribute> {
    @Mappings(
        value = [
            Mapping(source = "uuid", target = "id"),
        ]
    )
    override fun toDomain(other: EnrichedAttributeEntity): EnrichedAttribute

    @Mappings(
        value = [
            Mapping(source = "id", target = "uuid"),
            Mapping(target = "id", ignore = true),
        ]
    )
    override fun toEntity(other: EnrichedAttribute): EnrichedAttributeEntity
}