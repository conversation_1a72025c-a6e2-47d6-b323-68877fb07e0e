package com.prospection.refdata.rules.integration

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy


@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = [EnrichedAttributeEntityMapper::class]
)
interface EnrichedAttributeValueEntityMapper : DomainMapper<EnrichedAttributeValue, EnrichedAttributeValueEntity>,
    EntityMapper<EnrichedAttributeValueEntity, EnrichedAttributeValue> {
    @Mappings(
        value = [
            Mapping(source = "uuid", target = "id"),
        ]
    )
    override fun toDomain(other: EnrichedAttributeValueEntity): EnrichedAttributeValue

    @Mappings(
        value = [
            Mapping(source = "id", target = "uuid"),
            Mapping(target = "id", ignore = true),
        ]
    )
    override fun toEntity(other: EnrichedAttributeValue): EnrichedAttributeValueEntity
}