package com.prospection.refdata.rules.integration

import com.prospection.domain.InvalidModelException
import com.prospection.domain.ModelDoesNotExistException
import com.prospection.refdata.common.domain.exceptions.ConcurrentUpdateException
import com.prospection.refdata.rules.domain.AttributeRepository
import com.prospection.refdata.rules.domain.EnrichedAttribute
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.EnrichmentRulePort
import com.prospection.refdata.rules.domain.EnrichmentRuleService
import org.hibernate.envers.AuditReader
import org.hibernate.envers.query.AuditEntity
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.orm.ObjectOptimisticLockingFailureException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Component
@Transactional(readOnly = true)
class EnrichmentRuleJpaAdapter(
    @Autowired private val enrichmentRuleJpaRepository: EnrichmentRuleJpaRepository,
    @Autowired private val enrichedAttributeJpaRepository: EnrichedAttributeJpaRepository,
    @Autowired private val enrichedAttributeValueJpaRepository: EnrichedAttributeValueJpaRepository,
    @Autowired private val enrichmentRuleMapper: EnrichmentRuleEntityMapper,
    @Autowired private val enrichedAttributeEntityMapper: EnrichedAttributeEntityMapper,
    @Autowired private val enrichedAttributeValueEntityMapper: EnrichedAttributeValueEntityMapper,
    @Autowired private val auditReader: AuditReader,
) : EnrichmentRulePort, AttributeRepository {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(EnrichmentRuleJpaAdapter::class.java)
        const val DUPLICATE_RULE_ERROR_MESSAGE =
            "Duplicate rule found. Check if there's an existing rule with the same enriched attribute/value you're configuring."
    }

    override fun listEnrichmentRules(): List<EnrichmentRule> {
        return enrichmentRuleJpaRepository.findAll().map { enrichmentRuleMapper.toDomain(it) }
    }

    override fun listActiveEnrichmentRules(): List<EnrichmentRule> {
        return enrichmentRuleJpaRepository.listActiveEnrichmentRules().map { enrichmentRuleMapper.toDomain(it) }
    }

    @Transactional
    override fun createEnrichmentRule(enrichmentRule: EnrichmentRule): EnrichmentRule {
        try {
            val entity = enrichmentRuleMapper.toEntity(enrichmentRule)

            entity.enrichedAttributeValue =
                enrichedAttributeValueJpaRepository.getByUuid(enrichmentRule.enrichedAttributeValue.id)

            return enrichmentRuleMapper.toDomain(enrichmentRuleJpaRepository.save(entity))
        } catch (ex: DataIntegrityViolationException) {
            logger.error(DUPLICATE_RULE_ERROR_MESSAGE, ex)
            throw InvalidModelException(DUPLICATE_RULE_ERROR_MESSAGE)
        }
    }

    @Transactional
    override fun updateEnrichmentRule(enrichmentRule: EnrichmentRule): EnrichmentRule {
        val uuidToUpdate = enrichmentRule.id
            ?: throw InvalidModelException("uuid should exist to update existing an enrichment rule")

        val existingEntity = enrichmentRuleJpaRepository.findByUuid(uuidToUpdate) ?: throw ModelDoesNotExistException()

        val entityToUpdate = enrichmentRuleMapper.toEntity(enrichmentRule)

        entityToUpdate.id = existingEntity.id
        entityToUpdate.enrichedAttributeValue =
            enrichedAttributeValueJpaRepository.getByUuid(enrichmentRule.enrichedAttributeValue.id)

        try {
            return enrichmentRuleJpaRepository.saveAndFlush(entityToUpdate).let {
                enrichmentRuleMapper.toDomain(it)
            }
        } catch (ex: ObjectOptimisticLockingFailureException) {
            EnrichmentRuleService.logger.error("Optimistic lock failed while updating rule: {}", uuidToUpdate, ex)
            throw ConcurrentUpdateException(ex)
        }
    }

    override fun findEnrichmentRuleByUuid(enrichmentRuleUuid: String): EnrichmentRule? {
        return enrichmentRuleJpaRepository.findByUuid(enrichmentRuleUuid)?.let {
            enrichmentRuleMapper.toDomain(it)
        }
    }

    override fun listEnrichedAttributes(): List<EnrichedAttribute> {
        return enrichedAttributeJpaRepository.list().map { enrichedAttributeEntityMapper.toDomain(it) }
    }

    override fun listEnrichedAttributeValues(): List<EnrichedAttributeValue> {
        return enrichedAttributeValueJpaRepository.findAll().map { enrichedAttributeValueEntityMapper.toDomain(it) }
    }

    override fun findEnrichedAttributeValuesByAttributeUuid(attributeUuid: String): List<EnrichedAttributeValue> {
        return enrichedAttributeValueJpaRepository.findByAttributeUuid(attributeUuid)
            .map { enrichedAttributeValueEntityMapper.toDomain(it) }
    }


    @Transactional
    override fun deleteEnrichmentRuleByUuid(
        uuid: String,
        lastModifiedBy: String,
        lastModifiedAt: LocalDateTime
    ): EnrichmentRule {
        val existingEntity = enrichmentRuleJpaRepository.findByUuid(uuid) ?: throw ModelDoesNotExistException()
        existingEntity.deleted = 1
        existingEntity.lastModifiedBy = lastModifiedBy
        existingEntity.lastModifiedAt = lastModifiedAt
        return enrichmentRuleJpaRepository.saveAndFlush(existingEntity).let {
            enrichmentRuleMapper.toDomain(it)
        }
    }

    override fun getLatestEnrichmentRuleRevisionId(): Int {
        return auditReader.createQuery()
            .forRevisionsOfEntity(EnrichmentRuleEntity::class.java, true)
            .addProjection(AuditEntity.revisionNumber().max())
            .singleResult as Int? ?: 0
    }

    @Transactional
    override fun unarchiveEnrichmentRuleByUuid(
        uuid: String,
        lastModifiedBy: String,
        lastModifiedAt: LocalDateTime
    ): EnrichmentRule {
        val existingEntity = enrichmentRuleJpaRepository.findByUuid(uuid) ?: throw ModelDoesNotExistException()
        if (enrichmentRuleJpaRepository.existsByEnrichedAttributeValueAndDeleted(existingEntity.enrichedAttributeValue, 0)) {
            throw InvalidModelException(DUPLICATE_RULE_ERROR_MESSAGE)
        }
        existingEntity.deleted = 0
        existingEntity.lastModifiedBy = lastModifiedBy
        existingEntity.lastModifiedAt = lastModifiedAt
        return enrichmentRuleJpaRepository.save(existingEntity).let {
            enrichmentRuleMapper.toDomain(it)
        }
    }
}
