package com.prospection.refdata.rules.integration

import org.hibernate.envers.Audited
import org.hibernate.envers.NotAudited
import java.time.LocalDateTime
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import jakarta.persistence.UniqueConstraint
import jakarta.persistence.Version

@Audited
@Entity(name = "EnrichmentRule")
@Table(
    name = "enrichment_rule",
    uniqueConstraints = [UniqueConstraint(columnNames = ["enriched_attribute_value_id"], name = "enrichment_rule_enriched_attribute_value_id_uk")]
)
class EnrichmentRuleEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    var uuid: String,
    @NotAudited @Version
    var version: Long = 0,
    var deleted: Long = 0,
    var lastModifiedAt: LocalDateTime,
    var lastModifiedBy: String,
    var goal: String? = null,
    @ManyToOne(optional = false) var enrichedAttributeValue: EnrichedAttributeValueEntity,
    var rule: String
)
