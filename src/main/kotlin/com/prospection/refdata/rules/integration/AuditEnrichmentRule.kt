package com.prospection.refdata.rules.integration

/**
 * EnrichmentRule has circular dependencies on rule property which cannot use to serialize.
 * So we need to create this class for auditing purpose only.
 */
data class AuditEnrichmentRule(
    val id: String? = null,
    val enrichedAttributeValue: String,
    val rule: String,
    val goal: String? = null,
    var version: Int = 0,
    val lastModifiedBy: String? = null,
    val lastModifiedAt: String? = null,
    val deleted: Int = 0,
)

