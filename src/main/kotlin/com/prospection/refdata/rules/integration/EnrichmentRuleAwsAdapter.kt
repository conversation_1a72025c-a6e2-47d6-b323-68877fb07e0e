package com.prospection.refdata.rules.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.config.ApplicationProperties
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.PublishEnrichmentRulePort
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.core.sync.RequestBody

@Component
class EnrichmentRuleAwsAdapter(
    @Autowired private val om: ObjectMapper,
    @Autowired private val s3Client: S3Client,
    @Autowired private var applicationProperties: ApplicationProperties,
) : PublishEnrichmentRulePort {
    override fun publishEnrichmentRules(enrichmentRules: List<EnrichmentRule>, version: String) {
        val auditEnrichmentRules = enrichmentRules.map {
            AuditEnrichmentRule(
                id = it.id,
                enrichedAttributeValue = om.writeValueAsString(it.enrichedAttributeValue),
                rule = it.rule,
                goal = it.goal,
                version = it.version,
                lastModifiedBy = it.lastModifiedBy,
                lastModifiedAt = it.lastModifiedAt?.toString(),
                deleted = it.deleted
            )
        }

        s3Client.putObject(
            PutObjectRequest.builder()
                .bucket(applicationProperties.s3Bucket)
                .key("${Items.Published.ARCHIVE_ENRICHMENT_RULE}/${createVersionPath(version)}/enrichment-rules.json")
                .build(),
            RequestBody.fromString(om.writeValueAsString(auditEnrichmentRules))
        )
    }

}