package com.prospection.refdata.rules.integration

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.rules.domain.EnrichmentRule
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy


@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = [EnrichedAttributeEntityMapper::class, EnrichedAttributeValueEntityMapper::class]
)
interface EnrichmentRuleEntityMapper : DomainMapper<EnrichmentRule, EnrichmentRuleEntity>,
    EntityMapper<EnrichmentRuleEntity, EnrichmentRule> {
    @Mappings(
        value = [
            Mapping(source = "uuid", target = "id")
        ]
    )
    override fun toDomain(other: EnrichmentRuleEntity): EnrichmentRule

    @Mappings(
        value = [
            Mapping(source = "id", target = "uuid"),
            Mapping(target = "id", ignore = true),
            Mapping(target = "deleted", ignore = true)
        ]
    )
    override fun toEntity(other: EnrichmentRule): EnrichmentRuleEntity
}
