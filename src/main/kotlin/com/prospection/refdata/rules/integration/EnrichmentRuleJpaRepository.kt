package com.prospection.refdata.rules.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.stereotype.Repository

@Repository
interface EnrichmentRuleJpaRepository : JpaRepository<EnrichmentRuleEntity, Long>,
    JpaSpecificationExecutor<EnrichmentRuleEntity>,
    RevisionRepository<EnrichmentRuleEntity, Long, Int> {

    @Query("select e from EnrichmentRule e where e.deleted = 0")
    fun listActiveEnrichmentRules(): List<EnrichmentRuleEntity>

    fun findByUuid(uuid: String): EnrichmentRuleEntity?

    fun existsByEnrichedAttributeValueAndDeleted(enrichedAttributeValue: EnrichedAttributeValueEntity, deleted: Long): <PERSON><PERSON><PERSON>
}