package com.prospection.refdata.rules.domain

import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.consts.SourceAttribute.CODE_COLUMN_NAME
import com.prospection.refdata.common.consts.SourceAttribute.CODING_SYSTEM_ATTRIBUTE_NAME
import com.prospection.refdata.common.consts.SourceAttribute.PREFIX_SOURCE_ATTRIBUTE
import com.prospection.refdata.common.integration.ApplyRuleHelper
import com.prospection.refdata.etl.common.StandardColumns
import com.prospection.refdata.items.RuleBuilderOperators
import com.prospection.refdata.items.domain.EnrichedItemsMetadata
import com.prospection.refdata.items.domain.EnrichedItemsMetadataPort
import com.prospection.refdata.items.domain.HasClassificationAndSourceAttributes
import com.prospection.refdata.items.domain.RawItemsMetadataPort
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class AttributeService(
    @Autowired private val attributeRepository: AttributeRepository,
    @Autowired private val codingSystemPort: CodingSystemPort,
    @Autowired private val rawItemsMetadataPort: RawItemsMetadataPort,
    @Autowired private val enrichedItemsMetadataPort: EnrichedItemsMetadataPort,
) {
    companion object {
        val HIERARCHICAL_SOURCE_ATTRIBUTES = listOf(
            "${PREFIX_SOURCE_ATTRIBUTE}${StandardColumns.AtcCode()}",
            "${PREFIX_SOURCE_ATTRIBUTE}${StandardColumns.AtcEphmra()}",
            "${PREFIX_SOURCE_ATTRIBUTE}icd10_code",
            "${PREFIX_SOURCE_ATTRIBUTE}${StandardColumns.KubunCode()}",
        )
    }

    fun listEnrichmentRuleAttributes(): List<AttributeType> {
        val allRawItemsMetadata = rawItemsMetadataPort.getAllDraftMetadata()
        return listOf(
            AttributeType(
                type = Type.CODING_SYSTEM,
                attributes = listOf(
                    Attribute(
                        name = CODING_SYSTEM_ATTRIBUTE_NAME,
                        values = codingSystemPort.findAll().toList(),
                        operators = getOperatorOfEnrichmentRule(Type.CODING_SYSTEM)
                    )
                )
            ),
            AttributeType(
                type = Type.SOURCE_ATTRIBUTE,
                attributes = getSourceAttributesWithClassifications(allRawItemsMetadata).map {
                    it.copy(operators = getOperatorOfEnrichmentRule(Type.SOURCE_ATTRIBUTE))
                }
            )
        )
    }

    fun listItemGroupRuleAttributes(): List<AttributeType> {
        val allEnrichedItemsMetadata = enrichedItemsMetadataPort.getAllDraftMetadata()

        return listOf(
            AttributeType(
                type = Type.CODING_SYSTEM,
                attributes = listOf(
                    Attribute(
                        name = CODING_SYSTEM_ATTRIBUTE_NAME,
                        values = codingSystemPort.findAll().toList(),
                        operators = getOperatorOfItemGroup(Type.CODING_SYSTEM)
                    )
                )
            ),
            createEnrichedAttributeType(allEnrichedItemsMetadata),
            AttributeType(
                type = Type.SOURCE_ATTRIBUTE,
                attributes = addAdditionalRuleBuilderOperators(
                    getSourceAttributesWithClassifications(allEnrichedItemsMetadata)
                )
            )
        )
    }

    fun listEnrichedAttributes(): List<EnrichedAttribute> {
        return attributeRepository.listEnrichedAttributes()
    }

    fun findEnrichedAttributeValuesByAttributeUuid(attributeUuid: String): List<EnrichedAttributeValue> {
        return attributeRepository.findEnrichedAttributeValuesByAttributeUuid(attributeUuid)
    }

    private fun createEnrichedAttributeType(enrichedItemsMetadata: List<EnrichedItemsMetadata>): AttributeType {
        val enrichedAttributeValuesByAttributeName = attributeRepository.listEnrichedAttributeValues()
            .groupBy { ApplyRuleHelper.escapeAsSparkColumnName(it.enrichedAttribute.name) }

        val enrichedAttributes = enrichedItemsMetadata
            .flatMap { it.enrichedAttributes }.distinct().sorted()

        return AttributeType(
            type = Type.ENRICHED_ATTRIBUTE,
            attributes = enrichedAttributes.map {
                Attribute(
                    name = it,
                    values = enrichedAttributeValuesByAttributeName[it]
                        .orEmpty()
                        .map { v -> v.value }
                        .sorted(),
                    operators = getOperatorOfItemGroup(Type.ENRICHED_ATTRIBUTE)
                )
            }
        )
    }

    private fun getSourceAttributesWithClassifications(classificationAndAttributesList: List<HasClassificationAndSourceAttributes>): List<Attribute> {
        val classificationsGroupedBySourceAttribute = classificationAndAttributesList
            .flatMap { it.sourceAttributes.map { attribute -> Pair(attribute, it.codingSystem.name) } }
            .groupBy({ it.first }, { it.second })

        val totalClassificationCount = classificationsGroupedBySourceAttribute[CODE_COLUMN_NAME]?.size ?: 0

        return classificationsGroupedBySourceAttribute
            .mapValues { it.value.distinct().sorted() }
            .entries
            .sortedBy { it.key }
            .map {
                Attribute(
                    name = it.key,
                    hasAllCodingSystems = totalClassificationCount ==
                        it.value.size,
                    codingSystems = it.value,
                    values = emptyList()
                )
            }
    }

    private fun addAdditionalRuleBuilderOperators(sourceAttributes: List<Attribute>): List<Attribute> {
        return sourceAttributes.map {
            it.copy(
                operators = getOperatorOfItemGroup(Type.SOURCE_ATTRIBUTE)
                    + getAdditionalOperatorBySourceAttribute(it.name)
            )
        }
    }

    private fun getAdditionalOperatorBySourceAttribute(sourceAttribute: String): List<String> {
        val operators = mutableListOf<String>()

        if (HIERARCHICAL_SOURCE_ATTRIBUTES.contains(sourceAttribute)) {
            operators.add(RuleBuilderOperators.BeginsWith())
            operators.add(RuleBuilderOperators.DoesNotBeginWith())
        }

        return operators
    }

    private fun getOperatorOfItemGroup(type: Type): List<String> {
        return if (Type.CODING_SYSTEM == type) {
            listOf(
                RuleBuilderOperators.EqualsTo(),
            )
        } else {
            listOf(
                RuleBuilderOperators.EqualsTo(),
                RuleBuilderOperators.IsNotEqualTo()
            )
        }
    }

    private fun getOperatorOfEnrichmentRule(type: Type): List<String> {
        return if (Type.CODING_SYSTEM == type) {
            listOf(
                RuleBuilderOperators.EqualsTo(),
            )
        } else {
            listOf(
                RuleBuilderOperators.EqualsTo(),
                RuleBuilderOperators.IsNotEqualTo(),
                RuleBuilderOperators.Contains(),
                RuleBuilderOperators.DoesNotContain(),
                RuleBuilderOperators.BeginsWith(),
                RuleBuilderOperators.DoesNotBeginWith()
            )
        }
    }

}