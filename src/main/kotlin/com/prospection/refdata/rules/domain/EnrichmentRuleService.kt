package com.prospection.refdata.rules.domain

import com.prospection.domain.InvalidModelException
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.FileNamePort
import com.prospection.refdata.common.domain.GenerateIdPort
import com.prospection.refdata.common.domain.GeneratePublicUrlPort
import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.config.S3Path.Items
import com.prospection.refdata.items.domain.ItemsPort
import com.prospection.refdata.job.domain.Job
import com.prospection.refdata.job.domain.JobPort
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

@Service
class EnrichmentRuleService(
    @Autowired private val enrichmentRulePort: EnrichmentRulePort,
    @Autowired private val dateTime: DateTimePort,
    @Autowired private val userPort: UserPort,
    @Autowired private val itemsPort: ItemsPort,
    @Autowired private val codingSystemPort: CodingSystemPort,
    @Autowired private val jobPort: JobPort,
    @Autowired private val generateIdPort: GenerateIdPort,
    @Autowired private val importExportHelper: ImportExportHelper<ResponseInputStream<GetObjectResponse>>,
    @Autowired private val generatePublicUrlPort: GeneratePublicUrlPort,
    @Autowired private val fileNamePort: FileNamePort,
) {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(EnrichmentRuleService::class.java)
    }

    fun list(shouldDisplayArchived: Boolean = false): List<EnrichmentRule> {
        return if (shouldDisplayArchived) enrichmentRulePort.listEnrichmentRules() else enrichmentRulePort.listActiveEnrichmentRules()
    }

    fun create(enrichmentRule: EnrichmentRule): EnrichmentRule {
        try {
            val enrichmentRuleWithNewUuid = enrichmentRule.copy(
                id = generateIdPort.generate(),
                lastModifiedBy = userPort.getCurrentUserId(),
                lastModifiedAt = dateTime.now(),
            )
            return enrichmentRulePort.createEnrichmentRule(enrichmentRuleWithNewUuid)
        } catch (ex: Exception) {
            logger.error("An exception occurred while creating rule: {}", enrichmentRule.rule, ex)
            throw InvalidModelException(ex)
        }
    }

    fun findByUuid(enrichmentRuleUuid: String): EnrichmentRule? {
        return enrichmentRulePort.findEnrichmentRuleByUuid(enrichmentRuleUuid)
    }

    fun update(enrichmentRuleUuid: String, enrichmentRule: EnrichmentRule): EnrichmentRule {
        val existingEnrichmentRule = enrichmentRulePort.findEnrichmentRuleByUuid(enrichmentRuleUuid)!!
        if (existingEnrichmentRule.rule.equals(enrichmentRule.rule)
            && existingEnrichmentRule.enrichedAttributeValue.id.equals(enrichmentRule.enrichedAttributeValue.id)
            && (Objects.equals(existingEnrichmentRule.goal, enrichmentRule.goal)
            )
        ) {
            return existingEnrichmentRule
        }

       return enrichmentRulePort.updateEnrichmentRule(
            enrichmentRule.copy(
                id = enrichmentRuleUuid,
                lastModifiedBy = userPort.getCurrentUserId(),
                lastModifiedAt = dateTime.now(),
            )
        )
    }
    fun delete(enrichmentRuleUuid: String): EnrichmentRule {
        return enrichmentRulePort.deleteEnrichmentRuleByUuid(
            enrichmentRuleUuid,
            userPort.getCurrentUserId(),
            dateTime.now()
        )
    }

    fun queuePreviewFileDownload(enrichmentRule: EnrichmentRule): Job {
        return jobPort.startJob("Generating the preview file of enrichment rule - ${enrichmentRule.toSimpleName()}") {
            getPreviewFileUrl(enrichmentRule)
        }
    }

    fun getPreviewFileUrl(enrichmentRule: EnrichmentRule): String? {
        val codingSystems = codingSystemPort.findAll()
            .filter { enrichmentRule.deserialisedRule.canResolveToCodingSystem(it) }

        val codingSystemAndOutputPathPairs = codingSystems.flatMap { codingSystem ->
            if (itemsPort.doesRawItemsExist(codingSystem)) {
                val enrichedItems = itemsPort.applyRules(codingSystem, listOf(enrichmentRule))

                val filteredEnrichedItems = itemsPort.filterMatchedRowsOnly(enrichedItems, enrichmentRule)

                if (filteredEnrichedItems.isEmpty) {
                    emptyList()
                } else {
                    val outputPath = itemsPort.writePreview(codingSystem, filteredEnrichedItems)
                    listOf(Pair(codingSystem, outputPath))
                }
            } else {
                emptyList()
            }
        }

        if (codingSystemAndOutputPathPairs.isEmpty()) {
            throw RuntimeException("There were no items matching the given condition")
        }

        val outputPathToFileNameMap = codingSystemAndOutputPathPairs.map {
            Pair(it.second, "${it.first}.csv")
        }.associate { it }

        val filename = generateEnrichmentRuleFileName(enrichmentRule, dateTime.now())
        val zipPath = importExportHelper.writeZip("${Items.PREVIEW}/${filename}.zip", outputPathToFileNameMap, null)
        return generatePublicUrlPort.generatePublicUrl(
            zipPath,
            Date.from(dateTime.now().plusHours(1).atZone(ZoneId.systemDefault()).toInstant())
        )
    }

    fun unarchive(enrichmentRuleUuid: String): EnrichmentRule {
        return enrichmentRulePort.unarchiveEnrichmentRuleByUuid(
            enrichmentRuleUuid,
            userPort.getCurrentUserId(),
            dateTime.now()
        )
    }

    private fun generateEnrichmentRuleFileName(enrichmentRule: EnrichmentRule, dateTime: LocalDateTime): String {
        val attributeName = enrichmentRule.enrichedAttributeValue.enrichedAttribute.name
        val attributeValue = enrichmentRule.enrichedAttributeValue.value

        return fileNamePort.createFileNameWithDateTime("$attributeName $attributeValue", dateTime)
    }
}
