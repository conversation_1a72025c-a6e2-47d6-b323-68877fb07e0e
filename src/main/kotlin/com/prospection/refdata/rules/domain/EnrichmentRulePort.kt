package com.prospection.refdata.rules.domain

import java.time.LocalDateTime

interface EnrichmentRulePort {
    fun listEnrichmentRules(): List<EnrichmentRule>

    fun listActiveEnrichmentRules(): List<EnrichmentRule>

    fun createEnrichmentRule(enrichmentRule: EnrichmentRule): EnrichmentRule

    fun updateEnrichmentRule(enrichmentRule: EnrichmentRule): EnrichmentRule

    fun findEnrichmentRuleByUuid(enrichmentRuleUuid: String): EnrichmentRule?

    fun deleteEnrichmentRuleByUuid(uuid: String, lastModifiedBy: String, lastModifiedAt: LocalDateTime): EnrichmentRule

    fun getLatestEnrichmentRuleRevisionId(): Int

    fun unarchiveEnrichmentRuleByUuid(uuid: String, lastModifiedBy: String, lastModifiedAt: LocalDateTime): EnrichmentRule
}