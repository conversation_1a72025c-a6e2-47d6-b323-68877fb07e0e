package com.prospection.refdata.rules.application.rest.dto

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.rules.domain.Attribute
import com.prospection.refdata.rules.domain.AttributeType
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR,
    uses = [AttributeDtoMapper::class]
)
interface AttributeTypeDtoMapper : DtoMapper<AttributeTypeDto, AttributeType>,
    DomainMapper<AttributeType, AttributeTypeDto>

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR,
)
interface AttributeDtoMapper : DtoMapper<AttributeDto, Attribute>,
    DomainMapper<Attribute, AttributeDto>