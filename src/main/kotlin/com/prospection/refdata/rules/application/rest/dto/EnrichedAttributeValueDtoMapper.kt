package com.prospection.refdata.rules.application.rest.dto

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.rules.domain.EnrichedAttributeValue
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.ERROR)
interface EnrichedAttributeValueDtoMapper : DtoMapper<EnrichedAttributeValueDto, EnrichedAttributeValue>,
    DomainMapper<EnrichedAttributeValue, EnrichedAttributeValueDto>
