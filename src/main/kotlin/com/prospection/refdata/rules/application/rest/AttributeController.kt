package com.prospection.refdata.rules.application.rest

import com.prospection.refdata.rules.application.rest.dto.AttributeTypeDto
import com.prospection.refdata.rules.application.rest.dto.AttributeTypeDtoMapper
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeDto
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeDtoMapper
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeValueDto
import com.prospection.refdata.rules.application.rest.dto.EnrichedAttributeValueDtoMapper
import com.prospection.refdata.rules.domain.AttributeService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/ref-data-v2/")
@Tag(name = "reference data, enriched, attribute", description = "APIs for enriched attributes")
class AttributeController(
    @Autowired private val service: AttributeService,
    @Autowired private val attributeMapper: EnrichedAttributeDtoMapper,
    @Autowired private val attributeValueMapper: EnrichedAttributeValueDtoMapper,
    @Autowired private val attributeTypeDtoMapper: AttributeTypeDtoMapper,
) {
    @Operation(summary = "Lists attributes including source attributes, enriched attributes, and classifications")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("attribute-types", produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun listAttributeTypes(): List<AttributeTypeDto> {
        return service.listEnrichmentRuleAttributes().map { attributeTypeDtoMapper.toDto(it) }
    }

    @Operation(summary = "Lists item group rule attributes including source attributes, enriched attributes, and classifications")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("item-group-rule-attributes", produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun listItemGroupRuleAttributes(): List<AttributeTypeDto> {
        return service.listItemGroupRuleAttributes().map { attributeTypeDtoMapper.toDto(it) }
    }

    @Operation(summary = "Lists enriched attributes")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("enriched-attributes", produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun listEnrichedAttributes(): List<EnrichedAttributeDto> {
        return service.listEnrichedAttributes().map { attributeMapper.toDto(it) }
    }

    @Operation(summary = "Lists enriched attribute values")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("enriched-attributes/{attributeUuid}/values", produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun findEnrichedAttributeValuesByAttributeUuid(@PathVariable attributeUuid: String): List<EnrichedAttributeValueDto> {
        return service.findEnrichedAttributeValuesByAttributeUuid(attributeUuid).map { attributeValueMapper.toDto(it) }
    }
}
