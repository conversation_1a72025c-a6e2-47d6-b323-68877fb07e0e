package com.prospection.refdata.rules.application.rest.dto

import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema
data class EnrichmentRuleDto(
    val id: String? = null,
    @field:NotNull val enrichedAttributeValue: EnrichedAttributeValueDto,
    @field:NotNull @field:NotBlank val rule: String,
    val goal: String? = null,
    val version: Int? = null,
    val lastModifiedBy: String? = null,
    var lastModifiedAt: LocalDateTime? = null,
    val deleted: Int? = 0
)
