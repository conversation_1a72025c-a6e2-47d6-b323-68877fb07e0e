package com.prospection.refdata.rules.application.rest.dto

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.rules.domain.EnrichmentRule
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.ERROR,
    uses = [EnrichedAttributeDtoMapper::class, EnrichedAttributeValueDto::class]
)
interface EnrichmentRuleDtoMapper : DtoMapper<EnrichmentRuleDto, EnrichmentRule>,
    DomainMapper<EnrichmentRule, EnrichmentRuleDto>
