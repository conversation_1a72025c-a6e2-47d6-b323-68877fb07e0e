package com.prospection.refdata.rules.application.rest.dto

import com.fasterxml.jackson.annotation.JsonInclude
import com.prospection.refdata.rules.domain.Type
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AttributeTypeDto(
    @field:NotNull val type: Type,
    @field:NotNull val attributes: List<AttributeDto>
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AttributeDto(
    @field:NotNull @field:NotBlank val name: String,
    @field:NotNull val values: List<String>,

    val hasAllCodingSystems: Boolean?,
    val codingSystems: List<String>?,
    val operators: List<String>?,
)
