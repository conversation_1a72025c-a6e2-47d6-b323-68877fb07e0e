package com.prospection.refdata.rules.application.rest.dto

import com.fasterxml.jackson.annotation.JsonInclude
import io.swagger.v3.oas.annotations.media.Schema
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema
data class EnrichedAttributeDto(
    val id: String,
    @field:NotNull @field:NotBlank val name: String,
)
