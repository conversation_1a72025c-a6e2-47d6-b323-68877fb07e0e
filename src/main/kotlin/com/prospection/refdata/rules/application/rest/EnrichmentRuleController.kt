package com.prospection.refdata.rules.application.rest

import com.prospection.domain.ModelDoesNotExistException
import com.prospection.refdata.job.application.rest.dto.JobDto
import com.prospection.refdata.job.application.rest.dto.JobDtoMapper
import com.prospection.refdata.rules.application.rest.dto.EnrichmentRuleDto
import com.prospection.refdata.rules.application.rest.dto.EnrichmentRuleDtoMapper
import com.prospection.refdata.rules.domain.EnrichmentRuleService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import jakarta.validation.Valid

@RestController
@RequestMapping("/api/ref-data-v2/enrichment-rules")
@Tag(name = "reference data, enrichment rule", description = "APIs to CRUD & export rules")
class EnrichmentRuleController(
    @Autowired private val service: EnrichmentRuleService,
    @Autowired private val mapper: EnrichmentRuleDtoMapper,
    @Autowired private val jobDtoMapper: JobDtoMapper,
) {
    @Operation(summary = "Lists rules")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseBody
    fun listEnrichmentRules(@RequestParam shouldDisplayArchived: Boolean): List<EnrichmentRuleDto> {
        return service.list(shouldDisplayArchived).map { mapper.toDto(it) }
    }

    @Operation(summary = "Creates an enrichment rule")
    @ApiResponses(value = [ApiResponse(responseCode = "201", description = "Created")])
    @PostMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
    @ResponseStatus(HttpStatus.CREATED)
    @ResponseBody
    fun createEnrichmentRule(@Valid @RequestBody dto: EnrichmentRuleDto): EnrichmentRuleDto {
        return mapper.toDto(service.create(mapper.toDomain(dto)))
    }

    @Operation(summary = "Describes an enrichment rule given an id")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping(path = ["/{id}"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getEnrichmentRule(@PathVariable id: String): EnrichmentRuleDto {
        return service.findByUuid(id)?.let {
            mapper.toDto(it)
        } ?: run { throw ModelDoesNotExistException() }
    }

    @Operation(summary = "Updates an enrichment rule given an id")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PutMapping(path = ["/{id}"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun updateEnrichmentRule(@PathVariable id: String, @Valid @RequestBody dto: EnrichmentRuleDto): EnrichmentRuleDto {
        return mapper.toDto(service.update(id, mapper.toDomain(dto)))
    }

    @Operation(summary = "Delete an enrichment rule given an id")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @DeleteMapping(path = ["/{id}"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun deleteEnrichmentRule(@PathVariable id: String): EnrichmentRuleDto {
        return mapper.toDto(service.delete(id))
    }

    @Operation(summary = "Get a file download link for rule preview")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping(path = ["/preview"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun queuePreviewFileDownload(@Valid @RequestBody dto: EnrichmentRuleDto): JobDto {
        return jobDtoMapper.toDto(service.queuePreviewFileDownload(mapper.toDomain(dto)))
    }

    @Operation(summary = "Unarchive an enrichment rule given an id")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping(path = ["/unarchive/{id}"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun unarchiveEnrichmentRule(@PathVariable id: String): EnrichmentRuleDto {
        return mapper.toDto(service.unarchive(id))
    }
}
