package com.prospection.refdata.etl

import com.prospection.refdata.codingsystem.domain.CodingSystemService
import com.prospection.refdata.common.domain.GluePort
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.items.domain.ItemsService
import com.prospection.refdata.job.domain.JobStatus as DomainJobStatus
import com.prospection.refdata.job.notification.JobStatus
import com.prospection.refdata.job.notification.JobStatusSNSNotifier
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder

@Component
class EtlJobWrapper(
    private val itemService: ItemsService,
    private val codingSystemService: CodingSystemService,
    private val gluePort: GluePort,
) {
    private val logger by lazyLogger()

    fun run(etlJob: EtlJobExecutor, classification: String, version: String): String? {
        val startTime = Instant.now()
        var endTime: Instant
        var result: String? = null
        var status = DomainJobStatus.FAILED
        val details = mutableMapOf<String, String>()

        try {
            result = etlJob.execute()

            val dateTimeFormatterBuilder = DateTimeFormatterBuilder()
                .appendOptional(DateTimeFormatter.BASIC_ISO_DATE)
                .appendOptional(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

            val versionDate = LocalDate.parse(version, dateTimeFormatterBuilder.toFormatter())

            // All snapshots will be handled inside the etlJob so storeSnapshot here is set to false
            itemService.processAfterImport(classification, etlJob.jobName, version, false)

            codingSystemService.updateEtlStatus(classification, versionDate)

            gluePort.startCrawler()

            status = DomainJobStatus.SUCCESSFUL
            details["Service"] = "Reference Data Service"
            details["Classification"] = classification
            details["Version"] = version

            // Get metadata of newly inserted items
            val metadata = itemService.getRawItemsMetadata(classification)
            if (metadata != null) {
                details["New Items"] = metadata.newItem.toString()
                details["Total Items"] = metadata.totalItem.toString()
                details["Deleted Items"] = metadata.deletedItem.toString()
                details["Source Attributes"] = metadata.sourceAttributes.joinToString(", ")
                logger.info("ETL job completed successfully: ${etlJob.jobName} with ${metadata.newItem} new items inserted")
            } else {
                logger.info("ETL job completed successfully: ${etlJob.jobName} without metadata")
            }
        } catch (e: Exception) {
            logger.error("ETL job failed: ${etlJob.jobName}", e)
            details["error"] = e.message ?: "Unknown error"
            throw e
        } finally {
            endTime = Instant.now()
            val durationSeconds = Duration.between(startTime, endTime).seconds.toInt()

            details["classification"] = classification
            details["version"] = version
            val jobStatus = JobStatus(
                jobName = etlJob.jobName,
                status = status,
                startedAt = startTime,
                finishedAt = endTime,
                durationSeconds = durationSeconds,
                details = details
            )

            JobStatusSNSNotifier.notifyStatus(jobStatus)
        }

        return result
    }
}
