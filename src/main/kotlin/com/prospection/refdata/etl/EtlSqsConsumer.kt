package com.prospection.refdata.etl

import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.config.ApplicationProperties
import com.prospection.refdata.config.S3Path
import com.prospection.refdata.etl.common.job.EtlJobParams
import com.prospection.refdata.job.domain.JobPort
import io.awspring.cloud.sqs.annotation.SqsListener
import org.apache.spark.sql.SparkSession
import org.jetbrains.kotlinx.spark.api.asScalaMap
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

@Component
@Profile("!test")
class EtlSqsConsumer(
    private val jobPort: JobPort,
    private val sparkSession: SparkSession,
    private val applicationProperties: ApplicationProperties,
    private val etlJobWrapper: EtlJobWrapper,
) {
    private val logger by lazyLogger()

    @SqsListener(value = ["\${application.amazon.etlQueueUrl}"])
    fun receiveMessage(message: EtlMessage) {
        logger.info("Received a message - $message")

        ensureNoJobRunning()

        if (message.classification === null) {
            throw RuntimeException("No classification found in sqs message")
        }

        if (message.version === null) {
            throw RuntimeException("No version found in sqs message")
        }

        val etlJobParams = getEtlJobParams(message)

        val etlJobExecutor: EtlJobExecutor = EtlJobFactory(message.classification, sparkSession, etlJobParams).etlJobExecutor

        jobPort.startJob("etl job for ${message.classification} with version: ${message.version}", "etl-sqs-consumer") {
            logger.info("A job has been triggered")

            etlJobWrapper.run(
                etlJobExecutor,
                message.classification,
                message.version,
            )
        }
    }

    private fun ensureNoJobRunning() {
        if (jobPort.existsRunningJob()) {
            val existingJob = jobPort.getLatestJob()
            throw RuntimeException("A new message couldn't be processed due to an existing job $existingJob")
        }
    }

    private fun getEtlJobParams(etlMessage: EtlMessage): EtlJobParams {
        val inputPaths = etlMessage.paths!!.map { (key, value) ->
            key to "s3a://${value.bucket}/${value.path}"
        }.toMap().asScalaMap()

        val s3Bucket = applicationProperties.s3Bucket
        return EtlJobParams(
            etlMessage.version,
            inputPaths,
            "s3a://${s3Bucket}/${S3Path.RAW_UPLOAD}",
            "s3a://${s3Bucket}/${S3Path.WAREHOUSE}/${etlMessage.classification}",
            "s3a://${s3Bucket}/${S3Path.SNAPSHOTS}/${etlMessage.classification}",
        )
    }
}