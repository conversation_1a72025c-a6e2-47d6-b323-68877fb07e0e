package com.prospection.refdata.codingsystem.domain

import com.prospection.refdata.job.domain.JobPort
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class CodingSystemService(
    @Autowired private var codingSystemPort: CodingSystemPort,
    @Autowired private var jobPort: JobPort
) {

    fun findAll(): Set<String> {
        return codingSystemPort.findAll()
    }

    fun checkEtlJobPossibility(codingSystemName: String, toPullVersion: LocalDate): Boolean {
        return codingSystemPort.findByName(codingSystemName).let {
            (it.lastPulledVersion == null || toPullVersion.isAfter(it.lastPulledVersion))
        } && !jobPort.existsRunningJob()
    }

    fun updateEtlStatus(codingSystemName: String, lastPulledVersion: LocalDate): CodingSystem {
        return codingSystemPort.updateLastPulledVersion(codingSystemName, lastPulledVersion)
    }
}