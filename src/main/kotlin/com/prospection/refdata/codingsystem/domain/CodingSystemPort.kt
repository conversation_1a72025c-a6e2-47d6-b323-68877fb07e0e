package com.prospection.refdata.codingsystem.domain

import java.time.LocalDate

interface CodingSystemPort {
    fun findAll(): Set<String>
    fun findByName(name: String): CodingSystem
    fun findByNames(names: List<String>): List<CodingSystem>
    fun findClassificationsByCodingSystem(name: String): Set<String>
    fun findCodingSystemsByClassification(classification: String): List<String>
    fun updateLastPulledVersion(codingSystemName: String, lastPulledVersion: LocalDate): CodingSystem
    fun findAllCodingSystemToClassifications(): Map<String, List<CodingSystemToClassification>>
}