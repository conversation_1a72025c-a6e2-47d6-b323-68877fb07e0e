package com.prospection.refdata.codingsystem.application.rest

import com.prospection.refdata.codingsystem.domain.CodingSystemService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate

@RestController
@RequestMapping("/api/ref-data-v2/coding-systems")
@Tag(name = "reference data, coding system", description = "APIs to CRUD & export coding systems")
@ResponseBody
class CodingSystemController(
    @Autowired private val service: CodingSystemService,
) {

    @Operation(summary = "Lists coding systems")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
    fun listClassifications(): Set<String> {
        return service.findAll()
    }

    @Operation(summary = "Check pulling job running possibility")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping(value = ["check-etl-job-possibility"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun checkEtlJobPossibility(@RequestParam(name = "classification", required = true) classification: String,
                               @RequestParam(name = "pullVersion", required = true) pullVersion: String
    ): Boolean {
        return service.checkEtlJobPossibility(classification, LocalDate.parse(pullVersion))
    }

}