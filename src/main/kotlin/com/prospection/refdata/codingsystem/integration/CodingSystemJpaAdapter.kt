package com.prospection.refdata.codingsystem.integration

import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.codingsystem.integration.mapper.CodingSystemEntityMapper
import com.prospection.refdata.codingsystem.integration.mapper.CodingSystemToClassificationEntityMapper
import org.springframework.stereotype.Component
import java.time.LocalDate


@Component
class CodingSystemJpaAdapter(
    private val codingSystemRepository: CodingSystemJpaRepository,
    private val codingSystemEntityMapper: CodingSystemEntityMapper,
    private val codingSystemToClassificationEntityMapper: CodingSystemToClassificationEntityMapper,
) : CodingSystemPort {
    override fun findAll(): Set<String> {
        return codingSystemRepository.findAll()
            .map { it.name }
            .toSortedSet()
    }

    override fun findByName(name: String): CodingSystem {
        return codingSystemRepository.findByName(name).let {
            codingSystemEntityMapper.toDomain(it)
        }
    }

    override fun findByNames(names: List<String>): List<CodingSystem> {
        return codingSystemRepository.findByNameIn(names).let {
            codingSystemEntityMapper.toDomain(it)
        }
    }

    override fun findClassificationsByCodingSystem(name: String): Set<String> {
        val codingSystemEntity = codingSystemRepository.findByName(name)

        if (codingSystemEntity.codingSystemToClassifications.isEmpty()) {
            throw RuntimeException("There were no classification mapped to this coding system - $name")
        }

        return codingSystemEntity.codingSystemToClassifications
            .map { it.classification }
            .toSortedSet()
    }

    override fun findAllCodingSystemToClassifications(): Map<String, List<CodingSystemToClassification>> {
        val codingSystemEntities = codingSystemRepository.findAll()

        val codingSystemWithEmptyClassification = codingSystemEntities
            .filter { it.codingSystemToClassifications.isEmpty() }
            .map { it.name }

        if (codingSystemWithEmptyClassification.isNotEmpty()) {
            throw RuntimeException("There were no classification mapped to this coding system - $codingSystemWithEmptyClassification")
        }

        return codingSystemEntities.associate { it.name to codingSystemToClassificationEntityMapper.toDomain(it.codingSystemToClassifications) }
    }

    override fun findCodingSystemsByClassification(classification: String): List<String> {
        return codingSystemRepository.findAll()
            .filter { codingSystem -> codingSystem.codingSystemToClassifications.any { it.classification == classification } }
            .map { it.name }
    }

    override fun updateLastPulledVersion(codingSystemName: String, lastPulledVersion: LocalDate): CodingSystem {
        val codingSystemEntity = codingSystemRepository.findByName(codingSystemName)
        codingSystemEntity.lastPulledVersion = lastPulledVersion

        return codingSystemEntityMapper.toDomain(
            codingSystemRepository.save(codingSystemEntity)
        )
    }
}