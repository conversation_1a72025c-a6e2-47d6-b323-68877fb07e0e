package com.prospection.refdata.codingsystem.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.codingsystem.integration.CodingSystemToClassificationEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface CodingSystemToClassificationEntityMapper :
    DomainMapper<CodingSystemToClassification, CodingSystemToClassificationEntity> {
    @Mappings(
        value = [
            Mapping(source = "codingSystem.name", target = "codingSystem"),
        ]
    )
    override fun toDomain(other: CodingSystemToClassificationEntity): CodingSystemToClassification
}