package com.prospection.refdata.codingsystem.integration

import org.hibernate.envers.Audited
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Audited
@Entity(name = "CodingSystemToClassification")
@Table(name = "coding_system_to_classification")
class CodingSystemToClassificationEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @ManyToOne(optional = false)
    @JoinColumn(name = "coding_system_id")
    var codingSystem: CodingSystemEntity,

    @NotNull
    var classification: String,

    @NotNull
    var codingSystemColumnToExport: String,
)
