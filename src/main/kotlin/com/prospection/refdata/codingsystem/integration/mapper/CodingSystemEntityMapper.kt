package com.prospection.refdata.codingsystem.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.domain.MappedClassification
import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
abstract class CodingSystemEntityMapper :
    DomainMapper<CodingSystem, CodingSystemEntity> {

    override fun toDomain(other: CodingSystemEntity): CodingSystem {
        return CodingSystem(
            id = other.id?.toString(),
            name = other.name,
            country = other.country,
            lastPulledVersion = other.lastPulledVersion,
            mappedClassifications = other.codingSystemToClassifications.map { e ->
                MappedClassification(
                    e.classification,
                    e.codingSystemColumnToExport
                )
            }
        )
    }
}