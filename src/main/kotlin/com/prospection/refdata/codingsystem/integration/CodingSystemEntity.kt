package com.prospection.refdata.codingsystem.integration

import org.hibernate.envers.Audited
import java.time.LocalDate
import jakarta.persistence.CascadeType
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToMany
import jakarta.persistence.Table
import jakarta.persistence.UniqueConstraint
import jakarta.validation.constraints.NotNull

@Audited
@Entity(name = "CodingSystem")
@Table(
    name = "coding_system",
    uniqueConstraints = [UniqueConstraint(columnNames = ["name"], name = "coding_system_name_uk")]
)
class CodingSystemEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @NotNull
    var name: String,

    var lastPulledVersion: LocalDate? = null,

    //Can put multiple country with COMMA: AU,US,JP
    var country: String? = null,

    @OneToMany(cascade = [CascadeType.ALL], fetch = FetchType.EAGER, targetEntity =  CodingSystemToClassificationEntity::class)
    @JoinColumn(name = "coding_system_id")
    var codingSystemToClassifications: List<CodingSystemToClassificationEntity> = emptyList(),
)
