package com.prospection.refdata.codingsystem.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.stereotype.Repository

@Repository
interface CodingSystemJpaRepository : JpaRepository<CodingSystemEntity, Long>,
    JpaSpecificationExecutor<CodingSystemEntity>,
    RevisionRepository<CodingSystemEntity, Long, Int> {

    fun findByName(name: String): CodingSystemEntity

    fun findByNameIn(names: List<String>): List<CodingSystemEntity>

}