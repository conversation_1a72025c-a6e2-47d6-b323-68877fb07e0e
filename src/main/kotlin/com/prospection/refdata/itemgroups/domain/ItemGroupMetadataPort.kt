package com.prospection.refdata.itemgroups.domain

import java.time.LocalDateTime

interface ItemGroupMetadataPort {
    /**
     * return null if draft metadata doesn't exist
     */
    fun getDraftMetadata(): ItemGroupsMetadata?

    fun saveDraftMetadata(metadata: ItemGroupsMetadata)

    fun savePublishMetadata(
        publishedItemGroupVersion: PublishedItemGroupVersion,
        createdBy: String,
        createdAt: LocalDateTime
    )
}