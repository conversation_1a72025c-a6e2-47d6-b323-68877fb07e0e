package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.rules.domain.AttributeRepository
import com.prospection.refdata.rules.domain.EnrichmentRulePort
import com.prospection.refdata.rules.domain.Type
import org.springframework.stereotype.Service

@Service
class ConditionBasedItemGroupService(
    private val enrichmentRulePort: EnrichmentRulePort,
    private val codingSystemPort: CodingSystemPort,
    private val attributeRepository: AttributeRepository,
) {
    companion object {
        const val GROUPING_ENRICHED_ATTRIBUTE = "Grouping"
    }

    fun listClassifications(): Set<String> {
        return enrichmentRulePort.listActiveEnrichmentRules()
            .filter { it.enrichedAttributeValue.enrichedAttribute.name == GROUPING_ENRICHED_ATTRIBUTE }
            .flatMap { it.deserialisedRule.resolvableFieldsOfType(it.deserialisedRule, Type.CODING_SYSTEM) }
            .flatMap { codingSystemPort.findClassificationsByCodingSystem(it) }
            .toSet()
    }

    fun listConditionGroupings(): Set<String> {
        return attributeRepository.listEnrichedAttributes()
            .filter { it.name == GROUPING_ENRICHED_ATTRIBUTE }
            .flatMap { attributeRepository.findEnrichedAttributeValuesByAttributeUuid(it.id) }
            .map { it.value }
            .toSet()
    }

}