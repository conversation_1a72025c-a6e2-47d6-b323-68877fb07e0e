package com.prospection.refdata.itemgroups.domain

import java.time.LocalDateTime

interface ItemGroupPort {
    fun listItemGroups(): List<ItemGroup>

    fun listActiveItemGroups(): List<ItemGroup>

    fun getItemGroup(id: String): ItemGroup

    fun createItemGroup(itemGroup: ItemGroup): ItemGroup

    fun updateItemGroup(itemGroup: ItemGroup): ItemGroup

    fun getLatestItemGroupRevisionId(): Int

    fun unarchiveItemGroup(uuid: String, lastModifiedBy: String, lastModifiedAt: LocalDateTime): ItemGroup

}