package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.topic.domain.EnrichedTopic
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface PublishItemGroupPort {
    fun publishItemsToItemGroups(version: String): List<PublishedItemToItemGroup>

    fun savePublishedItemToItemGroups(
        publishedVersion: String,
        publishedItemToItemGroups: List<PublishedItemToItemGroup>
    )

    fun savePublishedItemGroups(publishedVersion: String, publishedItemGroups: Set<PublishedItemGroup>)

    fun exportItemGroupsForAudit(itemGroups: List<ItemGroup>, version: String)

    fun findPublishedItemToItemGroups(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>,
    ): List<PublishedItemToItemGroup>

    fun findPublishedItemGroups(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>,
    ): List<PublishedItemGroup>

    fun findPublishedItemGroups(
        publishedItemGroupVersion: String,
        nameOrBusinessKey: String,
        pageable: Pageable,
    ): Page<PublishedItemGroup>

    fun findAllItemGroups(publishedItemGroupVersion: String): List<PublishedItemGroup>

    fun publishItemGroupsToTopics(
        version: String,
        enrichedTopics: Collection<EnrichedTopic>,
        itemGroupNameByKey: Map<String, String>
    )

    fun archivePublishedItemGroups(publishedItemGroupVersions: List<String>)

    fun archivePublishedItemsToItemGroups(publishedItemGroupVersions: Collection<String>)

    fun finalizeArchiving()
}