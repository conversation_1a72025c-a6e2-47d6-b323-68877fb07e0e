package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.itemgroups.application.rest.dto.DuplicateItemsByItemGroupsDto
import com.prospection.refdata.itemgroups.application.rest.dto.DuplicatedItemDto
import com.prospection.refdata.itemgroups.application.rest.dto.DuplicatedItemGroupDto
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class PublishedItemGroupsService(
    private val publishItemGroupPort: PublishItemGroupPort,
    private val publishItemGroupVersionPort: PublishItemGroupVersionPort,
    private val exportItemsToItemGroupsPort: ExportItemsToItemGroupsPort,
) {
    fun findAllPublishedVersions(): List<PublishedItemGroupVersion> {
        return publishItemGroupVersionPort.findAllPublishedVersions()
            .sortedByDescending { it.publishedAt }
    }

    fun getLatestPublishedVersion(): PublishedItemGroupVersion {
        return publishItemGroupVersionPort.getLatestPublishedVersion()
    }

    fun findPublishedItemToItemGroups(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>,
    ): List<PublishedItemToItemGroup> {
        return publishItemGroupPort.findPublishedItemToItemGroups(
            publishedItemGroupVersion,
            itemGroupBusinessKeys,
        )
    }

    fun findItemGroupsByBusinessKeys(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>
    ): List<PublishedItemGroup> {
        return publishItemGroupPort.findPublishedItemGroups(
            publishedItemGroupVersion,
            itemGroupBusinessKeys,
        )
    }

    fun findItemGroupsByNameOrBusinessKey(
        publishedItemGroupVersion: String,
        nameOrBusinessKey: String,
        pageable: Pageable,
    ): Page<PublishedItemGroup> {
        return publishItemGroupPort.findPublishedItemGroups(
            publishedItemGroupVersion, nameOrBusinessKey, pageable
        )
    }

    fun findClassificationsByBusinessKeys(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>
    ): List<String> {
        val itemToItemGroups = publishItemGroupPort.findPublishedItemToItemGroups(
            publishedItemGroupVersion,
            itemGroupBusinessKeys,
        )

        return itemToItemGroups
            .map {
                it.classification
            }
            .distinct()
            .sorted()
    }

    fun findAllItemGroups(publishedItemGroupVersion: String): List<PublishedItemGroup> {
        return publishItemGroupPort.findAllItemGroups(publishedItemGroupVersion)
    }

    fun getPublishedItemToItemGroupsFilePath(publishedItemGroupVersion: String): String {
        return exportItemsToItemGroupsPort.getPublishedItemToItemGroupsFilePath(publishedItemGroupVersion)
    }

    fun findDuplicatedItems(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>
    ): List<DuplicateItemsByItemGroupsDto> {
        data class DuplicateGroupsByItem(
            val duplicateItem: DuplicatedItemDto,
            val duplicateGroups: List<DuplicatedItemGroupDto>
        )

        return publishItemGroupPort
            .findPublishedItemToItemGroups(publishedItemGroupVersion, itemGroupBusinessKeys)
            .groupBy {
                DuplicatedItemDto(
                    classification = it.classification,
                    code = it.itemCode
                )
            }
            .filter { it.value.size > 1 } // exists in more than 2 groups
            .map {
                DuplicateGroupsByItem(
                    DuplicatedItemDto(
                        classification = it.key.classification,
                        code = it.key.code,
                    ),
                    it.value.map { itemToItemGroup ->
                        DuplicatedItemGroupDto(
                            name = itemToItemGroup.itemGroupName,
                            itemGroupId = itemToItemGroup.itemGroupBusinessKey,
                        )
                    }
                )
            }
            .groupBy { it.duplicateGroups }
            .map { entry ->
                DuplicateItemsByItemGroupsDto(
                    duplicateItemGroups = entry.key.sortedBy { it.name },
                    duplicateItems = entry.value
                        .map { intermediateDuplicatedInfo -> intermediateDuplicatedInfo.duplicateItem }
                        .sortedBy { it.code }
                )
            }
    }

    @Transactional(rollbackFor = [Exception::class, Error::class])
    fun archiveUnusedHistoricalData(publishedVersions: List<String>) {
        publishItemGroupVersionPort.archivePublishedVersions(publishedVersions)
        publishItemGroupPort.archivePublishedItemGroups(publishedVersions)
        publishItemGroupPort.archivePublishedItemsToItemGroups(publishedVersions)
    }

    fun finaliseArchiving() {
        publishItemGroupPort.finalizeArchiving()
    }
}