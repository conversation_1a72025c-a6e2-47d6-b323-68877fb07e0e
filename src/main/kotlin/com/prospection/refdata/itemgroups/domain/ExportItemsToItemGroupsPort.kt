package com.prospection.refdata.itemgroups.domain

import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import java.time.LocalDateTime

interface ExportItemsToItemGroupsPort {
    fun writeToDraftItemGroups(itemsToItemGroups: Dataset<Row>)

    fun writeToPreviewItemGroupTemp(itemsToItemGroup: Dataset<Row>, classification: String)

    fun generateZipFileAndGetUrl(itemGroupName: String, now: LocalDateTime): String

    fun generateMappingResult(now: LocalDateTime): String

    fun getPublishedItemToItemGroupsFilePath(publishedItemGroupVersion: String): String

    fun cleanPreviewItemGroupTemp()

    fun generateExcelAndGetUrl(classifications: Set<String>, itemGroupName: String, now: LocalDateTime): String
}