package com.prospection.refdata.itemgroups.domain

interface PublishItemGroupVersionPort {
    fun savePublishedVersion(publishedItemGroupVersion: PublishedItemGroupVersion)

    fun findAllPublishedVersions(): List<PublishedItemGroupVersion>

    fun getLatestPublishedVersion(): PublishedItemGroupVersion

    fun getLatestPublishedVersionString(): String

    fun getPublishedVersionByVersion(publishedVersion: String): PublishedItemGroupVersion

    fun archivePublishedVersions(publishedVersions: Collection<String>)
}