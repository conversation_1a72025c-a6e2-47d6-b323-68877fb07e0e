package com.prospection.refdata.itemgroups.domain

import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.consts.CodingSystems
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.Summarizable
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.itemgroups.application.rest.dto.CustomGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.CustomGroupsByPublishedItemVersionDto
import com.prospection.refdata.itemgroups.application.rest.dto.PreviewTopicDto
import com.prospection.refdata.items.domain.ItemsPort
import com.prospection.refdata.items.domain.PublishItemVersionPort
import com.prospection.refdata.job.domain.Job
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.subscription.domain.CompanySubscriptionPort
import com.prospection.refdata.topic.domain.TopicPort
import org.apache.commons.lang3.time.StopWatch
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Encoders
import org.apache.spark.sql.Row
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class ItemGroupsService(
    private val userPort: UserPort,
    private val itemGroupPort: ItemGroupPort,
    private val dateTimePort: DateTimePort,
    private val publishItemGroupPort: PublishItemGroupPort,
    private val codingSystemPort: CodingSystemPort,
    private val itemsPort: ItemsPort,
    private val itemGroupsSparkPort: ItemGroupsSparkPort,
    private val applyItemGroupRulePort: ApplyItemGroupRulePort,
    private val itemGroupMetadataPort: ItemGroupMetadataPort,
    private val exportItemsToItemGroupsPort: ExportItemsToItemGroupsPort,
    private val changeSummaryItemGroupPort: ChangeSummaryItemGroupPort,
    private val jobPort: JobPort,
    private val publishItemGroupVersionPort: PublishItemGroupVersionPort,
    private val publishItemVersionPort: PublishItemVersionPort,
    private val topicPort: TopicPort,
    private val companySubscriptionPort: CompanySubscriptionPort
) : Summarizable {
    companion object {
        private val logger = LoggerFactory.getLogger(ItemGroupsService::class.java)
        private const val MAX_ITEM_IN_EXCEL = 5000
        private const val MAX_CUSTOM_GROUPS = 100
    }

    fun createCustomGroups(customGroupCondition: CustomGroupCondition): CustomGroupsByPublishedItemVersionDto? {
        val latestPublishedItemsVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
            ?: throw RuntimeException("Items have never been published")

        val codingSystem = codingSystemPort.findCodingSystemsByClassification(customGroupCondition.classification)
            // TODO: Because one classification can be mapped to multiple coding systems. Ex: Forian Dispensing -> FDB Drug, NDC Drug
            // But for now FDB Drug is the only coding system with ICD. That's why we have a hardcode to filter out it here. It will be refactored later.
            .first { it == CodingSystems.FDB_DRUG }

        return itemsPort.getPublishedItems(codingSystem, latestPublishedItemsVersion)
            ?.let { publishedItems ->
                val customGroupEncoders = Encoders.bean(CustomGroupDto::class.java)
                val customGroups =
                    applyItemGroupRulePort.getCustomGroupsBasedOnCondition(publishedItems, customGroupCondition)
                        .`as`(customGroupEncoders)
                        .limit(MAX_CUSTOM_GROUPS)
                        .collectAsList()

                return CustomGroupsByPublishedItemVersionDto(
                    publishedItemsVersion = latestPublishedItemsVersion,
                    customGroups = customGroups
                )
            }
    }

    fun listItemGroups(shouldDisplayArchived: Boolean = false): List<ItemGroup> {
        return if (shouldDisplayArchived) itemGroupPort.listItemGroups() else itemGroupPort.listActiveItemGroups()
    }

    fun getItemGroup(id: String): ItemGroup {
        return itemGroupPort.getItemGroup(id)
    }

    fun createItemGroup(itemGroup: ItemGroup): ItemGroup {
        val savedItemGroup = itemGroup.copy(
            id = UUID.randomUUID().toString(),
            businessKey = generateBusinessKey(itemGroup.name),
            lastModifiedBy = userPort.getCurrentUserId(),
            lastModifiedAt = dateTimePort.now(),
        )

        return itemGroupPort.createItemGroup(savedItemGroup)
    }

    fun updateItemGroup(itemGroup: ItemGroup): ItemGroup {
        val existingItemGroup = itemGroupPort.getItemGroup(itemGroup.id!!)

        if (existingItemGroup.name == itemGroup.name
            && existingItemGroup.rule == itemGroup.rule
            && Objects.equals(existingItemGroup.goal, itemGroup.goal)
        ) {
            return existingItemGroup
        }

        //Only save when having something change
        val savedItemGroup = itemGroup.copy(
            lastModifiedBy = userPort.getCurrentUserId(),
            lastModifiedAt = dateTimePort.now(),
        )

        return itemGroupPort.updateItemGroup(savedItemGroup)
    }

    fun deleteItemGroup(id: String): ItemGroup {
        val itemGroupToDelete = itemGroupPort.getItemGroup(id)
        itemGroupToDelete.lastModifiedAt = dateTimePort.now()
        itemGroupToDelete.lastModifiedBy = userPort.getCurrentUserId()
        itemGroupToDelete.deleted = 1
        return itemGroupPort.updateItemGroup(itemGroupToDelete)
    }

    // Generate business key based on item group name
    private fun generateBusinessKey(itemGroupName: String): String {
        return itemGroupName.replace("\\s+".toRegex(), "_").lowercase()
    }

    fun applyItemGroupsToItems(userId: String) {
        val itemsToItemGroups = getItemsToItemGroupsWithoutSourceAttributeColumns(itemGroupPort.listActiveItemGroups())

        exportItemsToItemGroupsPort.writeToDraftItemGroups(itemsToItemGroups)

        itemGroupMetadataPort.saveDraftMetadata(
            ItemGroupsMetadata(
                createdBy = userId,
                createdAt = dateTimePort.now(),
                itemGroupRevisionId = itemGroupPort.getLatestItemGroupRevisionId(),
                publishedItemVersion = publishItemVersionPort.getLatestPublishedItemVersion()
                    ?: throw RuntimeException("Items have never been published")
            )
        )
    }

    fun applyItemGroupsIfObsolete(userId: String) {
        val metadata = itemGroupMetadataPort.getDraftMetadata()
        logger.info("Checking if has to be applied item groups again")

        if (metadata != null) {
            val latestItemGroupRevisionId = itemGroupPort.getLatestItemGroupRevisionId()
            val latestPublishedItemVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
                ?: throw RuntimeException("Items have never been published")

            if (metadata.itemGroupRevisionId < latestItemGroupRevisionId) {
                logger.info("Applying item groups because it's obsolete. Current item group ver: $latestItemGroupRevisionId, metadata ver: ${metadata.itemGroupRevisionId}")
                applyItemGroupsToItems(userId)
            } else if (metadata.publishedItemVersion.publishedVersion < latestPublishedItemVersion) {
                logger.info("Applying item groups because it's obsolete. Current published items ver: $latestPublishedItemVersion, metadata ver: ${metadata.publishedItemVersion.publishedVersion}")
                applyItemGroupsToItems(userId)
            } else {
                logger.info("Applying item groups not required because the current draft is up-to-date")
            }
        } else {
            logger.info("Applying item groups again because metadata doesn't exist somehow")
            applyItemGroupsToItems(userId)
        }
    }

    @Transactional
    fun publish(userId: String, comment: String) {
        applyItemGroupsIfObsolete(userId)

        val now = dateTimePort.now()
        val version = dateTimePort.toPublishedVersionString(now)

        try {
            val publishedItemsToItemGroups = publishItemGroupPort.publishItemsToItemGroups(version)

            val stopWatch = StopWatch.create()
            stopWatch.start()
            publishItemGroupPort.savePublishedItemToItemGroups(version, publishedItemsToItemGroups)
            logger.info("Persisting item to item groups took $stopWatch")

            publishItemGroupPort.savePublishedItemGroups(version, publishedItemsToItemGroups.map {
                PublishedItemGroup(
                    businessKey = it.itemGroupBusinessKey,
                    name = it.itemGroupName,
                )
            }.toSet())

            // Get rules from DB
            val itemGroups = itemGroupPort.listItemGroups()

            publishItemGroupPort.exportItemGroupsForAudit(itemGroups, version)

            publishItemGroupVersionPort.savePublishedVersion(
                PublishedItemGroupVersion(
                    publishedVersion = version,
                    publishedBy = userId,
                    publishedAt = now,
                    comment = comment,
                )
            )

            publishItemGroupsToTopics(publishedItemsToItemGroups, version)

            val publishedItemGroupVersion = publishItemGroupVersionPort.getPublishedVersionByVersion(version)

            itemGroupMetadataPort.savePublishMetadata(publishedItemGroupVersion, userId, now)
        } catch (ex: Exception) {
            logger.error("An exception occurred while publishing items and item groups of version: $version")
            throw ex
        }
    }

    fun publishItemGroupsToTopics(
        itemsToItemGroups: List<PublishedItemToItemGroup>,
        version: String
    ) {
        logger.info("Publishing item groups to topics started")
        val exportedItemGroupKeys = itemsToItemGroups.map { it.itemGroupBusinessKey }.toSet()

        val topics = topicPort.listTopicsByItemGroups(exportedItemGroupKeys)
            .let { companySubscriptionPort.enrichTopicsWithCompanyAndSubscription(it) }
        logger.info("Fetched topics for published item groups")

        val itemGroupNameByKey = itemsToItemGroups
            .associateBy { it.itemGroupBusinessKey }
            .mapValues { it.value.itemGroupName }

        publishItemGroupPort.publishItemGroupsToTopics(version, topics, itemGroupNameByKey)
        logger.info("Publishing item groups to topics completed")
    }

    fun queueDownloadingChangeSummary(): Job {
        val userId = userPort.getCurrentUserId()

        return jobPort.startJob("Generating the change summary of item groups") {
            val changeSummaryUrl = generateChangeSummary(userId)
                ?: throw RuntimeException("No change summary available because there’s no difference between draft and published items")

            changeSummaryUrl
        }
    }

    override fun generateChangeSummary(userId: String): String? {
        applyItemGroupsIfObsolete(userId)

        val latestItemGroupVersion = publishItemGroupVersionPort.getLatestPublishedVersionString()

        return changeSummaryItemGroupPort.generateChangeSummary(latestItemGroupVersion, dateTimePort.now())
    }

    fun queuePublishingItemsAndItemGroups(comment: String): Job {
        val userId = userPort.getCurrentUserId()
        return jobPort.startJob("Publishing item groups") {
            publish(userId, comment)
            null
        }
    }

    fun getItemsToItemGroupsWithoutSourceAttributeColumns(itemGroups: List<ItemGroup>): Dataset<Row> {
        val latestPublishedItemsVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
            ?: throw RuntimeException("Items have never been published")

        val codingSystemToClassifications = codingSystemPort.findAllCodingSystemToClassifications()

        return codingSystemToClassifications.mapNotNull { (codingSystem, codingSystemToClassifications) ->
            val itemGroupsForCodingSystem = itemGroups.filter {
                it.deserialisedRule.canResolveToCodingSystem(codingSystem)
            }

            val publishedItems = itemsPort.getPublishedItems(codingSystem, latestPublishedItemsVersion)

            if (publishedItems != null && itemGroupsForCodingSystem.isNotEmpty()) {
                applyItemGroupRulePort.getItemsToItemsGroupsWithoutSourceAttributeColumns(
                    publishedItems,
                    itemGroupsForCodingSystem,
                    codingSystemToClassifications
                )
            } else {
                null
            }
        }.reduce { acc, cur ->
            acc.unionAll(cur)
        }
    }

    fun exportPreviewFileAndCountByClassification(itemGroup: ItemGroup): Map<String, Long> {
        // Clean temp folder before generating new one
        exportItemsToItemGroupsPort.cleanPreviewItemGroupTemp()

        val itemsToItemGroupsByClassifications = getItemsToItemGroupsByClassification(itemGroup)

        if (itemsToItemGroupsByClassifications.isEmpty()) {
            throw RuntimeException("There were no items matching the given condition")
        }

        return itemsToItemGroupsByClassifications.map {
            val classification = it.key
            val dataset = it.value
            exportItemsToItemGroupsPort.writeToPreviewItemGroupTemp(dataset, classification)
            classification to dataset.count()
        }.toMap()
    }

    fun queuePreviewFileDownload(itemGroup: ItemGroup): Job {
        return jobPort.startJob("Generating the preview file of item group - ${itemGroup.name}") {
            val itemCountsByClassification = exportPreviewFileAndCountByClassification(itemGroup)
            getPreviewFileUrl(itemGroup.name, itemCountsByClassification)
        }
    }

    fun getItemGroupPreview(itemGroup: ItemGroup): List<ItemGroupPreviewResult> {
        val latestPublishedItemsVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
            ?: throw RuntimeException("Items have never been published")

        val codingSystemToClassifications = codingSystemPort.findAllCodingSystemToClassifications()
            .filter { itemGroup.deserialisedRule.canResolveToCodingSystem(it.key) }

        return itemGroupsSparkPort.getItemGroupPreview(
            itemGroup,
            codingSystemToClassifications,
            latestPublishedItemsVersion
        )
    }

    fun getItemGroupToTopics(itemGroup: ItemGroup): List<PreviewTopicDto> {
        val itemGroupKey = itemGroup.businessKey.orEmpty()
        val enrichedTopics = topicPort.listTopicsByItemGroups(listOf(itemGroupKey))
            .let { companySubscriptionPort.enrichTopicsWithCompanyAndSubscription(it) }

        return enrichedTopics.flatMap { enrichedTopic ->
            val topic = enrichedTopic.topic

            topic.relatedWorkflows.map { workflow ->
                PreviewTopicDto(
                    topicId = topic.id,
                    topicName = topic.name,
                    companyId = topic.companyId,
                    companyName = enrichedTopic.companyName ?: "Unknown",
                    workflowId = workflow.id,
                    workflowStatus = workflow.status,
                    condition = topic.conditionName,
                    therapyArea = topic.therapyAreaName,
                    subscriptionName = enrichedTopic.subscriptionName,
                    subscriptionStatus = enrichedTopic.subscriptionStatus,
                    subscriptionRecordId = topic.subscriptionId
                )
            }
        }
    }

    fun queueDownloadingMappingResult(): Job {
        val userId = userPort.getCurrentUserId()
        return jobPort.startJob("Generating the mapping result between items and item groups") {
            getMappingResultUrl(userId)
        }
    }

    fun getMappingResultUrl(userId: String): String {
        applyItemGroupsIfObsolete(userId)
        return exportItemsToItemGroupsPort.generateMappingResult(dateTimePort.now())
    }

    fun unarchiveItemGroup(id: String): ItemGroup =
        itemGroupPort.unarchiveItemGroup(id, userPort.getCurrentUserId(), dateTimePort.now())

    fun getPreviewFileUrl(itemGroupName: String, countByClassification: Map<String, Long>): String {
        val totalItem = countByClassification.values.sum()
        val now = dateTimePort.now()

        return if (totalItem >= MAX_ITEM_IN_EXCEL) {
            logger.info("Preview file in a zip file")
            exportItemsToItemGroupsPort.generateZipFileAndGetUrl(itemGroupName, now)
        } else {
            logger.info("Preview file in an excel file")
            exportItemsToItemGroupsPort.generateExcelAndGetUrl(countByClassification.keys, itemGroupName, now)
        }
    }

    private fun getItemsToItemGroupsByClassification(itemGroup: ItemGroup): Map<String, Dataset<Row>> {
        val latestPublishedItemsVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
            ?: throw RuntimeException("Items have never been published")

        val codingSystemToClassifications = codingSystemPort.findAllCodingSystemToClassifications()
            .filter { itemGroup.deserialisedRule.canResolveToCodingSystem(it.key) }

        return codingSystemToClassifications.entries.mapNotNull { (codingSystem, codingSystemToClassifications) ->

            val itemsToItemGroups = itemsPort.getPublishedItems(codingSystem, latestPublishedItemsVersion)?.let {
                applyItemGroupRulePort.getItemsToItemGroupWithSourceAttributeColumns(
                    it,
                    itemGroup,
                    codingSystemToClassifications
                )
            }

            if (itemsToItemGroups == null || itemsToItemGroups.isEmpty) {
                null
            } else {
                codingSystem to itemsToItemGroups
            }
        }.toMap()
    }
}
