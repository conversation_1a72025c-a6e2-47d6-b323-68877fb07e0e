package com.prospection.refdata.itemgroups.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.refdata.itemgroups.application.rest.dto.PublishedItemGroupVersionDto
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupVersion
import org.mapstruct.Mapper
import java.time.ZoneOffset

@Mapper(
    componentModel = "spring",
)
abstract class PublishedItemGroupVersionDtoMapper : DtoMapper<PublishedItemGroupVersionDto, PublishedItemGroupVersion> {
    override fun toDto(other: PublishedItemGroupVersion): PublishedItemGroupVersionDto {
        return PublishedItemGroupVersionDto(
            id = other.publishedVersion,
            publishedBy = other.publishedBy,
            comment = other.comment,
            publishedDate = other.publishedAt.toInstant(ZoneOffset.UTC) // See LocaleConfiguration
        )
    }
}