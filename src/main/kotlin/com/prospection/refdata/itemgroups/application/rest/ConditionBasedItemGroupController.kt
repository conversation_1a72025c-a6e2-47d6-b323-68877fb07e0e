package com.prospection.refdata.itemgroups.application.rest

import com.prospection.refdata.itemgroups.domain.ConditionBasedItemGroupService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/ref-data-v2/condition-based-item-group")
@Tag(name = "condition based item group", description = "APIs for condition based item group")
class ConditionBasedItemGroupController(
    private val conditionBasedItemGroupService: ConditionBasedItemGroupService
) {
    @Operation(summary = "Get list of classifications support condition based item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("/classification")
    fun getSupportedClassifications(): Set<String> {
        return conditionBasedItemGroupService.listClassifications()
    }

    @Operation(summary = "Get condition grouping")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("/condition-grouping")
    fun getConditionGrouping(): Set<String> {
        return conditionBasedItemGroupService.listConditionGroupings()
    }

}