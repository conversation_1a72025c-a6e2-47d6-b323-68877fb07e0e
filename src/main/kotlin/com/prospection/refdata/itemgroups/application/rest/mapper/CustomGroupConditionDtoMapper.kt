package com.prospection.refdata.itemgroups.application.rest.mapper

import com.prospection.domain.DomainMapper
import com.prospection.refdata.itemgroups.application.rest.dto.CustomGroupConditionDto
import com.prospection.refdata.itemgroups.domain.CustomGroupCondition
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface CustomGroupConditionDtoMapper: DomainMapper<CustomGroupCondition, CustomGroupConditionDto>