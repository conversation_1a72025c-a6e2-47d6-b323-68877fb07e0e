package com.prospection.refdata.itemgroups.application.job

import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupsService
import com.prospection.refdata.topic.domain.TopicPort
import org.jooq.DSLContext
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.annotation.Scheduled.CRON_DISABLED
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicInteger
import kotlin.system.measureTimeMillis

@Component
class UnusedHistoricalDataArchivingJob(
    private val topicPort: TopicPort,
    private val publishedItemGroupsService: PublishedItemGroupsService,
    private val dslContext: DSLContext,
    @Value("\${application.job.archiveUnusedData.cutOffMonths:6}")
    private val cutOffMonth: Long,
    @Value("\${application.job.archiveUnusedData.chunkSize:20}")
    private val chunkSize: Int,
) {
    companion object {
        private val logger by lazyLogger()
    }

    @Scheduled(
        cron = "\${application.job.archiveUnusedData.cron:$CRON_DISABLED}",
        zone = "\${application.job.archiveUnusedData.timezone:Australia/Sydney}"
    )
    fun scheduleStart() {
        logger.info("Triggered scheduled unused historical data archiving job...")
        start()
    }

    @Async("asyncTaskExecutor")
    fun asyncStart() {
        logger.info("Triggered async unused historical data archiving job...")
        start()
    }

    fun start() {
        acquireLock()

        val atomicProcessedChunkCounter = AtomicInteger(0)
        try {
            logger.info("Start archiving unused historical data...")
            val cutOffDateTime = LocalDateTime.now().minusMonths(cutOffMonth)
            val inUseVersions = topicPort.listInUseReferenceDataVersions()
                .toSet()

            if (inUseVersions.isEmpty()) {
                logger.warn("No in-use reference data versions found, and it seems wrong. Exiting.")
                return
            }

            val versionsToArchive = publishedItemGroupsService.findAllPublishedVersions()
                .filter { it.publishedVersion !in inUseVersions && it.publishedAt.isBefore(cutOffDateTime) }
                .sortedBy { it.publishedAt }
                .map { it.publishedVersion }

            if (versionsToArchive.isEmpty()) {
                logger.info("No unused versions older than $cutOffMonth months found. Exiting.")
                return
            }
            logger.info("There are ${versionsToArchive.size} unused versions older than $cutOffMonth months to archive: ${versionsToArchive.joinToString(", ")}.")

            versionsToArchive.chunked(chunkSize).forEachIndexed { idx, chunk ->
                logger.info("Archiving chunk ${idx + 1} with ${chunk.size} versions: ${chunk.joinToString(", ")}.")
                // each chunk will be archived in a separate transaction since it's going to be a long-running transaction
                val timeTaken = measureTimeMillis { publishedItemGroupsService.archiveUnusedHistoricalData(chunk) }
                logger.info("Archiving chunk ${idx + 1} with ${chunk.size} versions finished in $timeTaken ms.")
                atomicProcessedChunkCounter.set(idx + 1)
            }
            // TODO: implement clean up archived data older than e.g. 12 months
        } finally {
            if (atomicProcessedChunkCounter.get() > 0) {
                val timeTaken = measureTimeMillis { publishedItemGroupsService.finaliseArchiving() }
                logger.info("Finalising archiving in $timeTaken ms.")
            }
            releaseLock()
        }
    }

    private fun releaseLock() {
        if (dslContext.execute("UPDATE archive_unused_historical_data_lock SET locked = false WHERE locked = true") > 0) {
            logger.info("Successfully released archive_unused_historical_data_lock.")
        } else {
            logger.error("Lock has been released before job finished.")
        }
    }

    private fun acquireLock() {
        val acquired = dslContext.execute("UPDATE archive_unused_historical_data_lock SET locked = true WHERE locked = false") > 0
        if (!acquired) {
            throw IllegalStateException("Failed to acquire archive_unused_historical_data_lock, job stopped.")
        }
        logger.info("Successfully acquired archive_unused_historical_data_lock.")
    }
}