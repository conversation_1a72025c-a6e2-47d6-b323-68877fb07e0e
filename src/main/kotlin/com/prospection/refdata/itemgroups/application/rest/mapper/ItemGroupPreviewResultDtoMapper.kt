package com.prospection.refdata.itemgroups.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.refdata.itemgroups.application.rest.dto.ItemGroupPreviewResultDto
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface ItemGroupPreviewResultDtoMapper: DtoMapper<ItemGroupPreviewResultDto, ItemGroupPreviewResult>