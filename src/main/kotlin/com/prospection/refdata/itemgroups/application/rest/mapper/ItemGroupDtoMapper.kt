package com.prospection.refdata.itemgroups.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.itemgroups.application.rest.dto.ItemGroupDto
import com.prospection.refdata.itemgroups.domain.ItemGroup
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface ItemGroupDtoMapper: DomainMapper<ItemGroup, ItemGroupDto>,
    DtoMapper<ItemGroupDto, ItemGroup>