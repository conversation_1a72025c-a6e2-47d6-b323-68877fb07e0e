package com.prospection.refdata.itemgroups.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.itemgroups.application.rest.dto.ItemToItemGroupDto
import com.prospection.refdata.itemgroups.domain.PublishedItemToItemGroup
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface ItemToItemGroupDtoMapper : DomainMapper<PublishedItemToItemGroup, ItemToItemGroupDto>,
    DtoMapper<ItemToItemGroupDto, PublishedItemToItemGroup>