package com.prospection.refdata.itemgroups.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.itemgroups.application.rest.dto.PublishedItemGroupDto
import com.prospection.refdata.itemgroups.domain.PublishedItemGroup
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface PublishedItemGroupDtoMapper : DomainMapper<PublishedItemGroup, PublishedItemGroupDto>,
    DtoMapper<PublishedItemGroupDto, PublishedItemGroup>