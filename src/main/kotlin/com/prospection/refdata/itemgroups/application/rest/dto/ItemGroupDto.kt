package com.prospection.refdata.itemgroups.application.rest.dto

import java.time.LocalDateTime
import jakarta.validation.constraints.NotBlank

class ItemGroupDto (
    var id: String? = null,

    var businessKey: String? = null,

    @field:NotBlank
    var name: String,

    @field:NotBlank
    var rule: String,

    var goal: String? = null,

    var deleted: Long = 0,

    var lastModifiedAt: LocalDateTime? = null,

    var lastModifiedBy: String? = null,

    var version: Long = 0,
)