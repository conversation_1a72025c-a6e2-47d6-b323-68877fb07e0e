package com.prospection.refdata.itemgroups.application.rest

import com.prospection.refdata.itemgroups.application.rest.dto.CustomGroupConditionDto
import com.prospection.refdata.itemgroups.application.rest.dto.CustomGroupsByPublishedItemVersionDto
import com.prospection.refdata.itemgroups.application.rest.dto.ItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.ItemGroupPreviewResultDto
import com.prospection.refdata.itemgroups.application.rest.dto.PreviewTopicDto
import com.prospection.refdata.itemgroups.application.rest.dto.PublishItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.mapper.CustomGroupConditionDtoMapper
import com.prospection.refdata.itemgroups.application.rest.mapper.ItemGroupDtoMapper
import com.prospection.refdata.itemgroups.application.rest.mapper.ItemGroupPreviewResultDtoMapper
import com.prospection.refdata.itemgroups.domain.ItemGroupsService
import com.prospection.refdata.job.application.rest.dto.JobDto
import com.prospection.refdata.job.application.rest.dto.JobDtoMapper
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import jakarta.validation.Valid

@RestController
@RequestMapping("/api/ref-data-v2/item-groups")
@Tag(name = "reference data, item groups", description = "APIs for item groups related operations")
class ItemGroupController(
    private val itemGroupsService: ItemGroupsService,
    private val itemGroupDtoMapper: ItemGroupDtoMapper,
    private val customGroupConditionDtoMapper: CustomGroupConditionDtoMapper,
    private val itemGroupPreviewResultDtoMapper: ItemGroupPreviewResultDtoMapper,
    private val jobDtoMapper: JobDtoMapper,
) {
    @Operation(summary = "Create custom groups based on condition")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("/custom-groups")
    fun createCustomGroups(@RequestBody customGroupConditionDto: CustomGroupConditionDto): CustomGroupsByPublishedItemVersionDto? {
        return itemGroupsService.createCustomGroups(customGroupConditionDtoMapper.toDomain(customGroupConditionDto))
    }

    @Operation(summary = "List item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping
    fun listItemGroups(@RequestParam shouldDisplayArchived: Boolean): List<ItemGroupDto> {
        return itemGroupsService.listItemGroups(shouldDisplayArchived).map { itemGroupDtoMapper.toDto(it) }
    }

    @Operation(summary = "Get item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("{id}")
    fun getItemGroup(@PathVariable id: String): ItemGroupDto {
        return itemGroupsService.getItemGroup(id).let { itemGroupDtoMapper.toDto(it) }
    }

    @Operation(summary = "Create item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping
    fun createItemGroup(@Valid @RequestBody itemGroupDto: ItemGroupDto): ItemGroupDto {
        return itemGroupDtoMapper.toDto(itemGroupsService.createItemGroup(itemGroupDtoMapper.toDomain(itemGroupDto)))
    }

    @Operation(summary = "Update item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PutMapping("{id}")
    fun updateItemGroup(@Valid @RequestBody itemGroupDto: ItemGroupDto, @PathVariable id: String): ItemGroupDto {
        itemGroupDto.id = id
        return itemGroupDtoMapper.toDto(itemGroupsService.updateItemGroup(itemGroupDtoMapper.toDomain(itemGroupDto)))
    }

    @Operation(summary = "Delete item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @DeleteMapping("{id}")
    fun deleteItemGroup(@PathVariable id: String): ItemGroupDto {
        return itemGroupDtoMapper.toDto(itemGroupsService.deleteItemGroup(id))
    }

    @Operation(summary = "Unarchive item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("unarchive/{id}")
    fun unarchiveItemGroup(@PathVariable id: String): ItemGroupDto {
        return itemGroupDtoMapper.toDto(itemGroupsService.unarchiveItemGroup(id))
    }

    @Operation(summary = "Publish items and item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("publish")
    fun publishItemsAndItemGroups(@Valid @RequestBody publishItemGroupDto: PublishItemGroupDto): JobDto {
        return jobDtoMapper.toDto(itemGroupsService.queuePublishingItemsAndItemGroups(publishItemGroupDto.comment))
    }

    @Operation(summary = "Publish items and item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("change-summaries")
    fun getChangeSummaries(): JobDto {
        return jobDtoMapper.toDto(itemGroupsService.queueDownloadingChangeSummary())
    }

    @Operation(summary = "Get a file download link for item group's preview")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping(path = ["generate-entire-results"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun queuePreviewFileDownload(@Valid @RequestBody dto: ItemGroupDto): JobDto {
        return jobDtoMapper.toDto(itemGroupsService.queuePreviewFileDownload(itemGroupDtoMapper.toDomain(dto)))
    }

    @Operation(summary = "Preview item group")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping(path = ["preview"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getItemGroupPreview(@Valid @RequestBody dto: ItemGroupDto): List<ItemGroupPreviewResultDto> {
        return itemGroupPreviewResultDtoMapper.toDto(itemGroupsService.getItemGroupPreview(itemGroupDtoMapper.toDomain(dto)))
    }

    @Operation(summary = "Download the mappings between items and item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("mapping-result")
    fun queueDownloadingMappingResult(): JobDto {
        return jobDtoMapper.toDto(itemGroupsService.queueDownloadingMappingResult())
    }

    @Operation(summary = "Get item group to topics")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping(path = ["topics"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun getItemGroupToTopics(@Valid @RequestBody dto: ItemGroupDto): List<PreviewTopicDto> {
        return itemGroupsService.getItemGroupToTopics(itemGroupDtoMapper.toDomain(dto))
    }

}