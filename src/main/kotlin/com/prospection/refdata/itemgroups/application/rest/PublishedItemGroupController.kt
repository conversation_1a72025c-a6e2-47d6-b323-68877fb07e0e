package com.prospection.refdata.itemgroups.application.rest

import com.prospection.refdata.itemgroups.application.job.UnusedHistoricalDataArchivingJob
import com.prospection.refdata.itemgroups.application.rest.dto.DuplicateItemsByItemGroupsDto
import com.prospection.refdata.itemgroups.application.rest.dto.ItemToItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.PublishedItemGroupDto
import com.prospection.refdata.itemgroups.application.rest.dto.PublishedItemGroupVersionDto
import com.prospection.refdata.itemgroups.application.rest.mapper.ItemToItemGroupDtoMapper
import com.prospection.refdata.itemgroups.application.rest.mapper.PublishedItemGroupDtoMapper
import com.prospection.refdata.itemgroups.application.rest.mapper.PublishedItemGroupVersionDtoMapper
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupsService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/ref-data-v2/versions")
@Tag(name = "reference data, item groups", description = "APIs for item groups related operations")
class PublishedItemGroupController(
    private val service: PublishedItemGroupsService,
    private val publishedItemGroupVersionDtoMapper: PublishedItemGroupVersionDtoMapper,
    private val itemToItemGroupDtoMapper: ItemToItemGroupDtoMapper,
    private val publishedItemGroupDtoMapper: PublishedItemGroupDtoMapper,
    private val unusedHistoricalDataArchivingJob: UnusedHistoricalDataArchivingJob,
) {
    @Operation(summary = "Find all the published versions of item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping
    fun findAllPublishedVersions(): List<PublishedItemGroupVersionDto> {
        return service.findAllPublishedVersions().let {
            publishedItemGroupVersionDtoMapper.toDto(it)
        }
    }

    @Operation(summary = "Get the latest published version of item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("latest")
    fun getLatestPublishedVersion(): PublishedItemGroupVersionDto {
        return publishedItemGroupVersionDtoMapper.toDto(service.getLatestPublishedVersion())
    }

    @Operation(summary = "Find item groups by item group business keys")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("{publishedItemGroupVersion}/item-to-item-groups")
    fun findPublishedItemToItemGroups(
        @PathVariable publishedItemGroupVersion: String,
        @RequestBody groupKeys: Set<String>
    ): List<ItemToItemGroupDto> {
        return service.findPublishedItemToItemGroups(publishedItemGroupVersion, groupKeys).let {
            itemToItemGroupDtoMapper.toDto(it)
        }
    }

    @Operation(summary = "Find item groups by item group business keys")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("{publishedItemGroupVersion}/item-groups-by-business-keys")
    fun findItemGroupsByBusinessKeys(
        @PathVariable publishedItemGroupVersion: String,
        @RequestBody ids: Set<String>,
    ): List<PublishedItemGroupDto> {
        return service.findItemGroupsByBusinessKeys(publishedItemGroupVersion, ids).map {
            publishedItemGroupDtoMapper.toDto(it)
        }
    }

    @Operation(summary = "Find item groups by name or business key")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("{publishedItemGroupVersion}/item-groups-by-name-or-business-key")
    fun findItemGroupsByNameOrBusinessKey(
        @PathVariable publishedItemGroupVersion: String,
        nameOrBusinessKey: String,
        pageable: Pageable,
    ): Page<PublishedItemGroupDto> {
        return service.findItemGroupsByNameOrBusinessKey(publishedItemGroupVersion, nameOrBusinessKey, pageable).map {
            publishedItemGroupDtoMapper.toDto(it)
        }
    }

    @Operation(summary = "Find classifications by business keys")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("{publishedItemGroupVersion}/classifications")
    fun findClassificationsByBusinessKeys(
        @PathVariable publishedItemGroupVersion: String,
        @RequestBody businessKeys: Set<String>,
    ): List<String> {
        return service.findClassificationsByBusinessKeys(publishedItemGroupVersion, businessKeys)
    }

    @Operation(summary = "Find all item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("{publishedItemGroupVersion}/item-groups")
    fun findAllItemGroups(
        @PathVariable publishedItemGroupVersion: String,
    ): List<PublishedItemGroupDto> {
        return service.findAllItemGroups(publishedItemGroupVersion).map {
            publishedItemGroupDtoMapper.toDto(it)
        }
    }

    @Operation(summary = "Get published item to item groups file path")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("{publishedItemGroupVersion}/item-to-item-groups-file-path")
    fun getPublishedItemToItemGroupsFilePath(
        @PathVariable publishedItemGroupVersion: String,
    ): String {
        return service.getPublishedItemToItemGroupsFilePath(publishedItemGroupVersion)
    }

    @Operation(summary = "Find duplicate items by item groups")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("{publishedItemGroupVersion}/duplicated-items")
    fun findDuplicateItemsByItemGroups(
        @PathVariable publishedItemGroupVersion: String,
        @RequestBody itemGroupBusinessKeys: Set<String>
    ): List<DuplicateItemsByItemGroupsDto> {
        return service.findDuplicatedItems(publishedItemGroupVersion, itemGroupBusinessKeys)
    }

    @Operation(summary = "Archive unused historical data")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("archiveUnusedHistoricalData")
    fun archiveUnusedHistoricalData() {
        unusedHistoricalDataArchivingJob.asyncStart()
    }

}