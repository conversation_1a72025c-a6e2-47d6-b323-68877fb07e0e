package com.prospection.refdata.itemgroups.integration

import org.hibernate.envers.Audited
import org.hibernate.envers.NotAudited
import java.time.LocalDateTime
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import jakarta.persistence.Version

@Audited
@Entity(name = "ItemGroup")
@Table(
    name = "item_group"
)
class ItemGroupEntity (
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var uuid: String,

    var businessKey: String,

    var name: String,

    var rule: String,

    var goal: String? = null,

    @NotAudited
    @Version
    var version: Long = 0,

    var deleted: Long = 0,

    var lastModifiedAt: LocalDateTime,

    var lastModifiedBy: String,
)