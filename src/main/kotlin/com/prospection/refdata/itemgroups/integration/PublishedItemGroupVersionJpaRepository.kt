package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface PublishedItemGroupVersionJpaRepository : JpaRepository<PublishedItemGroupVersionEntity, String>,
    JpaSpecificationExecutor<PublishedItemGroupVersionEntity> {

    fun findByArchivedIsFalseOrderByPublishedAtDesc(): List<PublishedItemGroupVersionEntity>

    @Modifying
    @Query("UPDATE PublishedItemGroupVersion v SET v.archived = true where v.publishedVersion in :publishedVersions")
    fun archivePublishVersion(publishedVersions: Collection<String>): Int

    fun findFirstByArchivedIsFalseOrderByPublishedAtDesc(): PublishedItemGroupVersionEntity?

    fun findByArchivedIsFalseAndPublishedVersion(publishedVersion: String): PublishedItemGroupVersionEntity?
}