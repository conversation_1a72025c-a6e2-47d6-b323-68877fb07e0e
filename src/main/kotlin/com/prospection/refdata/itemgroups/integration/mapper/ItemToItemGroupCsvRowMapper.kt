package com.prospection.refdata.itemgroups.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.itemgroups.domain.PublishedItemToItemGroup
import com.prospection.refdata.itemgroups.integration.PublishedItemToItemGroupCsvRow
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface ItemToItemGroupCsvRowMapper : DomainMapper<PublishedItemToItemGroup, PublishedItemToItemGroupCsvRow>,
    EntityMapper<PublishedItemToItemGroupCsvRow, PublishedItemToItemGroup>