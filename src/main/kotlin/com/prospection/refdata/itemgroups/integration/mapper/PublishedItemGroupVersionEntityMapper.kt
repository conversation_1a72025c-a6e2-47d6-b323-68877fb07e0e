package com.prospection.refdata.itemgroups.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupVersion
import com.prospection.refdata.itemgroups.integration.PublishedItemGroupVersionEntity
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface PublishedItemGroupVersionEntityMapper : DomainMapper<PublishedItemGroupVersion, PublishedItemGroupVersionEntity>,
    EntityMapper<PublishedItemGroupVersionEntity, PublishedItemGroupVersion>