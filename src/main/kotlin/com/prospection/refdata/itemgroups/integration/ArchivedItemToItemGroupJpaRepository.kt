package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository


@Repository
interface ArchivedItemToItemGroupJpaRepository : JpaRepository<ArchivedItemToItemGroupEntity, ArchivedItemToItemGroupEntity> {

    @Modifying
    @Query(
        nativeQuery = true,
        value = "INSERT INTO archived_item_to_item_group (version, item_code, item_group_business_key, classification, item_group_name)\n" +
                "SELECT version, item_code, item_group_business_key, classification, item_group_name\n" +
                "FROM published_item_to_item_group p\n" +
                "WHERE p.version IN ?1\n"
    )
    fun saveFromPublishedVersion(publishedVersions: Collection<String>): Int

}