package com.prospection.refdata.itemgroups.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.consts.SourceAttribute.CODE_COLUMN_NAME
import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.ChangeSummaryHelper
import com.prospection.refdata.common.integration.FileNameAdapter
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.common.integration.S3GeneratePublicUrlAdapter
import com.prospection.refdata.common.integration.S3OutputStream
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.config.ApplicationProperties
import com.prospection.refdata.config.S3Path.ItemGroups
import com.prospection.refdata.itemgroups.domain.ChangeSummaryItemGroupPort
import com.prospection.refdata.itemgroups.domain.ExportItemsToItemGroupsPort
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import com.univocity.parsers.csv.CsvParser
import com.univocity.parsers.csv.CsvParserSettings
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.functions.explode
import org.apache.spark.sql.functions.lit
import org.springframework.stereotype.Component
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import java.time.LocalDateTime

@Component
class ItemGroupAwsAdapter(
    private val importExportHelper: ImportExportHelper<ResponseInputStream<GetObjectResponse>>,
    private val sparkImportExportHelper: SparkImportExportHelper,
    private val s3PublicizeAdapter: S3GeneratePublicUrlAdapter,
    private val om: ObjectMapper,
    private val s3Client: S3Client,
    private val applicationProperties: ApplicationProperties,
    private var fileNameAdapter: FileNameAdapter,
) : ExportItemsToItemGroupsPort, ChangeSummaryItemGroupPort {
    private val logger by lazyLogger()

    override fun writeToDraftItemGroups(itemsToItemGroups: Dataset<Row>) {
        sparkImportExportHelper.writeCsv(ItemGroups.Draft.ITEM_TO_ITEM_GROUP, itemsToItemGroups)
    }

    override fun writeToPreviewItemGroupTemp(itemsToItemGroup: Dataset<Row>, classification: String) {
        sparkImportExportHelper.writeCsv(
            "${ItemGroups.Preview.TEMP}/$classification",
            itemsToItemGroup
        )
    }

    override fun generateZipFileAndGetUrl(itemGroupName: String, now: LocalDateTime): String {
        val regex = "^${ItemGroups.Preview.TEMP}/(.+)/.+csv\$".toRegex()
        val pathToEntryMap = importExportHelper.findAllPaths(ItemGroups.Preview.TEMP, ".csv")
            .associateBy(
                { it },
                { "${regex.find(it)?.groupValues?.get(1)}.csv" }
            )

        val zipPath = "${ItemGroups.PREVIEW}/${generateItemGroupFileName(itemGroupName, now)}.zip"

        importExportHelper.writeZip(zipPath, pathToEntryMap, null)

        return s3PublicizeAdapter.generatePublicUrlExpirationInOneHours(zipPath, now)
    }

    override fun generateChangeSummary(latestPublishedItemGroupVersion: String, now: LocalDateTime): String? {
        val publishedDs = latestPublishedItemGroupVersion.let {
            sparkImportExportHelper.readCsv("${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(it)}")
        }
        val draftDs = sparkImportExportHelper.readCsv(ItemGroups.Draft.ITEM_TO_ITEM_GROUP)

        return run {
            val changeSummary = ChangeSummaryHelper.compare(draftDs, publishedDs)
            val metadata = om.writerWithDefaultPrettyPrinter().writeValueAsBytes(changeSummary.metaData)

            sparkImportExportHelper.writeCsv(ItemGroups.Draft.CHANGE_SUMMARY, changeSummary.diffData)
            importExportHelper.findFirst(ItemGroups.Draft.CHANGE_SUMMARY, ".csv")?.let {
                val zipPath = importExportHelper.writeZip(
                    "${ItemGroups.Draft.CHANGE_SUMMARY}/change-summary.zip",
                    mapOf(Pair(it, "item-groups.csv")), metadata
                )

                s3PublicizeAdapter.generatePublicUrlExpirationInOneHours(zipPath, now)
            }
        }
    }

    override fun generateMappingResult(now: LocalDateTime): String {
        val csvPath = importExportHelper.findFirst(ItemGroups.Draft.ITEM_TO_ITEM_GROUP, ".csv")

        return if (csvPath == null) {
            throw RuntimeException("No mapping result available to download")
        } else {
            s3PublicizeAdapter.generatePublicUrlExpirationInOneHours(csvPath, now)
        }
    }

    override fun getPublishedItemToItemGroupsFilePath(publishedItemGroupVersion: String): String {
        val path = importExportHelper.findFirst(
            "${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(publishedItemGroupVersion)}",
            ".csv"
        ) ?: throw RuntimeException("A CSV output of item to item group mappings should exist at this point")

        return "s3a://${importExportHelper.getBucketName()}/${path}"
    }

    override fun cleanPreviewItemGroupTemp() {
        importExportHelper.deleteAll("${ItemGroups.Preview.TEMP}/")
    }

    override fun generateExcelAndGetUrl(
        classifications: Set<String>,
        itemGroupName: String,
        now: LocalDateTime
    ): String {

        val excelPath = "${ItemGroups.PREVIEW}/${generateItemGroupFileName(itemGroupName, now)}.xlsx"

        generateExcelByClassifications(excelPath, classifications)

        return s3PublicizeAdapter.generatePublicUrlExpirationInOneHours(excelPath, now)
    }

    private fun generateExcelByClassifications(excelPath: String, classifications: Set<String>) {
        val s3OutputStream = S3OutputStream(s3Client, applicationProperties.s3Bucket, excelPath)
        val settings = CsvParserSettings()
        settings.isHeaderExtractionEnabled = true
        settings.maxCharsPerColumn = -1

        s3OutputStream.use { outputStream ->
            XSSFWorkbook().use { workbook ->
                logger.info("Generating a preview xlsx file")

                classifications.forEach { classification ->
                    val s3Path = importExportHelper.findFirst(
                        "${ItemGroups.Preview.TEMP}/${classification}",
                        ".csv"
                    )!!

                    logger.info("Generating a sheet for $classification")
                    val sheet = workbook.createSheet(classification)
                    val parser = CsvParser(settings)

                    logger.info("Reading the CSV file of $classification")
                    importExportHelper.readFile(s3Path) { inputStream ->
                        val parsedCsv = parser.iterate(inputStream)
                        var rowCount = 0
                        val headerRow = sheet.createRow(rowCount)

                        // Get the first row to trigger header parsing
                        val iterator = parsedCsv.iterator()
                        if (!iterator.hasNext()) {
                            logger.warn("CSV file $s3Path is empty")
                            return@readFile
                        }

                        val firstRow = iterator.next()
                        val columnNames = parser.context.parsedHeaders()

                        logger.info("Writing the excel sheet of $classification")
                        columnNames.forEachIndexed { colIndex, columnName ->
                            val cell = headerRow.createCell(colIndex)
                            cell.setCellValue(columnName)
                        }

                        // Process the first row
                        val firstExcelRow = sheet.createRow(++rowCount)
                        columnNames.forEachIndexed { colIndex, _ ->
                            val cell = firstExcelRow.createCell(colIndex)
                            cell.setCellValue(firstRow[colIndex])
                        }

                        // Process the remaining rows
                        iterator.forEach { csvRow ->
                            val excelRow = sheet.createRow(++rowCount)
                            columnNames.forEachIndexed { colIndex, _ ->
                                val cell = excelRow.createCell(colIndex)
                                cell.setCellValue(csvRow[colIndex])
                            }
                        }
                    }!!
                }
                workbook.write(outputStream)
            }
        }
    }

    private fun generateItemGroupFileName(itemGroupName: String, dateTime: LocalDateTime): String {
        return fileNameAdapter.createFileNameWithDateTime(itemGroupName.ifBlank { "Unnamed item group" }, dateTime)
    }

    private fun explodeByCodingSystemToClassification(
        codingSystemToClassifications: List<CodingSystemToClassification>,
        items: Dataset<Row>
    ): Dataset<Row> {
        return codingSystemToClassifications.map { codingSystemToClassification ->
            //Replace source_code by codingSystemColumnToExport
            fillCodeByCodingSystemColumnToExport(codingSystemToClassification.codingSystemColumnToExport, items)
                //Add classification column
                .withColumn(
                    ItemGroupExportColumns.CLASSIFICATION.columnName,
                    lit(codingSystemToClassification.classification)
                )
                .distinct()

        }.reduce { acc, dataset -> acc.unionAll(dataset) }
    }

    private fun fillCodeByCodingSystemColumnToExport(
        codingSystemColumnToExport: String,
        items: Dataset<Row>
    ): Dataset<Row> {
        return if (ScalaSparkItemsFunctions.isArrayTypeColumn(items, codingSystemColumnToExport)) {
            items.withColumn(CODE_COLUMN_NAME, explode(col(codingSystemColumnToExport)))
        } else {
            items.withColumn(CODE_COLUMN_NAME, col(codingSystemColumnToExport))
        }
    }
}