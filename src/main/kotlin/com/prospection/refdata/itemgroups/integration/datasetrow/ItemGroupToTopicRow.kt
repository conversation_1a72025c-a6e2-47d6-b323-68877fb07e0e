package com.prospection.refdata.itemgroups.integration.datasetrow

import com.prospection.refdata.topic.domain.EnrichedTopic
import com.prospection.refdata.topic.domain.Workflow
import java.time.ZoneId
import java.time.format.DateTimeFormatter

// A class to be used as row class for Dataset should only have var fields (instead of val fields)
// Otherwise, you'll encounter writeEmptySchemasUnsupportedByDataSourceError
data class ItemGroupToTopicRow(
    var itemGroupKey: String,
    var itemGroupName: String,
    var topicId: String,
    var topicName: String,
    var workflowStatus: String,
    var generatedAt: String?,
    var lastModifiedAt: String,
    var lastModifiedBy: String,
    var company: String?,
    var condition: String?,
    var therapyArea: String?,
    var country: String?,
    var subscriptionId: String?,
    var subscriptionName: String?,
    var subscriptionStatus: String?,
) {
    companion object {
        private val dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
            .withZone(ZoneId.of("Australia/Sydney"))

        fun toRow(
            itemGroupBusinessKey: String,
            itemGroupName: String,
            enrichedTopic: EnrichedTopic,
            workflow: Workflow,
        ) = ItemGroupToTopicRow(
            itemGroupKey = itemGroupBusinessKey,
            itemGroupName = itemGroupName,
            topicId = enrichedTopic.topic.id.toString(),
            topicName = enrichedTopic.topic.name,
            condition = enrichedTopic.topic.conditionName,
            therapyArea = enrichedTopic.topic.therapyAreaName,
            country = workflow.country,
            subscriptionId = enrichedTopic.topic.subscriptionId,
            workflowStatus = workflow.status,
            generatedAt = workflow.generatedTime?.let { dateTimeFormatter.format(it) },
            lastModifiedAt = dateTimeFormatter.format(workflow.lastModifiedDate),
            lastModifiedBy = workflow.lastModifiedBy,
            company = enrichedTopic.companyName,
            subscriptionName = enrichedTopic.subscriptionName,
            subscriptionStatus = enrichedTopic.subscriptionStatus,
        )
    }
}