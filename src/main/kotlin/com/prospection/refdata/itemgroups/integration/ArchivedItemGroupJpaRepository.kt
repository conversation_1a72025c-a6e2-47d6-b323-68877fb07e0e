package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository


@Repository
interface ArchivedItemGroupJpaRepository : JpaRepository<ArchivedItemGroupEntity, ArchivedItemGroupEntity> {

    @Modifying
    @Query(
        nativeQuery = true,
        value = "INSERT INTO archived_item_group (version, business_key, name)\n" +
                "SELECT version, business_key, name\n" +
                "FROM published_item_group p\n" +
                "WHERE p.version IN ?1\n"
    )
    fun saveFromPublishedVersion(publishedVersions: Collection<String>): Int

}