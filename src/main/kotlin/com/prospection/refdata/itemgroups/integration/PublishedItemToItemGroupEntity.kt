package com.prospection.refdata.itemgroups.integration

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Entity(name = "PublishedItemToItemGroup")
@Table(
    name = "published_item_to_item_group",
    indexes = [
        Index( // This is a clustered index for performance. See https://www.postgresql.org/docs/9.1/sql-cluster.html
            columnList = "version",
            name = "published_item_to_item_group_version_idx"
        ),
        Index(
            columnList = "version,itemGroupBusinessKey",
            name = "published_item_to_item_group_version_item_group_bkey_idx"
        ),
    ]
)
class PublishedItemToItemGroupEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    @NotNull var version: String,
    @NotNull var itemCode: String,
    @NotNull var itemGroupBusinessKey: String,
    @NotNull var classification: String,
    @NotNull var itemGroupName: String,
)