package com.prospection.refdata.itemgroups.integration

import java.time.LocalDateTime
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
@Entity(name = "PublishedItemGroupVersion")
@Table(
    name = "published_item_group_version",
)
class PublishedItemGroupVersionEntity(
    @Id @NotNull var publishedVersion: String,
    @NotNull var publishedBy: String,
    @NotNull var publishedAt: LocalDateTime,
    var comment: String? = null,
    var archived: Boolean = false,
)