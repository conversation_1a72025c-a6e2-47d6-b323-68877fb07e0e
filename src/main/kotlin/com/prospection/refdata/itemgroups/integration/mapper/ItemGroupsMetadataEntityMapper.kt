package com.prospection.refdata.itemgroups.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.itemgroups.domain.ItemGroupsMetadata
import com.prospection.refdata.itemgroups.integration.ItemGroupsMetadataEntity
import com.prospection.refdata.items.integration.mapper.PublishedItemVersionEntityMapper
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = [PublishedItemVersionEntityMapper::class]
)
interface ItemGroupsMetadataEntityMapper : DomainMapper<ItemGroupsMetadata, ItemGroupsMetadataEntity>,
    EntityMapper<ItemGroupsMetadataEntity, ItemGroupsMetadata>