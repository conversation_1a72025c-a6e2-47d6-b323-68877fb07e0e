package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface PublishedItemGroupJpaRepository : JpaRepository<PublishedItemGroupEntity, Long>,
    JpaSpecificationExecutor<PublishedItemGroupEntity> {
    fun findByVersionAndBusinessKeyIn(
        version: String,
        businessKeys: Set<String>
    ): List<PublishedItemGroupEntity>

    fun findByVersion(version: String): List<PublishedItemGroupEntity>

    @Modifying
    @Query("DELETE PublishedItemGroup p WHERE p.version in :versions")
    fun deleteByVersionIn(versions: Collection<String>): Int
}