package com.prospection.refdata.itemgroups.integration

/**
 * ItemGroup has circular dependencies on rule property which cannot use to serialize.
 * So we need to create this class for auditing purpose only.
 */
data class AuditItemGroup(
    val businessKey: String?,
    val name: String,
    val rule: String,
    val lastModifiedBy: String?,
    val lastModifiedAt: String,
    val deleted: Long = 0,
)

