package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface PublishedItemToItemGroupJpaRepository : JpaRepository<PublishedItemToItemGroupEntity, Long>,
    JpaSpecificationExecutor<PublishedItemToItemGroupEntity> {

    fun findByVersionAndItemGroupBusinessKeyIn(
        version: String,
        itemGroupBusinessKey: Set<String>
    ): List<PublishedItemToItemGroupEntity>

    @Modifying
    @Query("DELETE PublishedItemToItemGroup p WHERE p.version in :versions")
    fun deleteByVersionIn(versions: Collection<String>): Int
}