package com.prospection.refdata.itemgroups.integration

import com.prospection.domain.InvalidModelException
import com.prospection.domain.ModelDoesNotExistException
import com.prospection.refdata.common.consts.EntityStatus
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupMetadataPort
import com.prospection.refdata.itemgroups.domain.ItemGroupPort
import com.prospection.refdata.itemgroups.domain.ItemGroupsMetadata
import com.prospection.refdata.itemgroups.domain.PublishItemGroupVersionPort
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupVersion
import com.prospection.refdata.itemgroups.integration.mapper.ItemGroupEntityMapper
import com.prospection.refdata.itemgroups.integration.mapper.ItemGroupsMetadataEntityMapper
import com.prospection.refdata.itemgroups.integration.mapper.PublishedItemGroupVersionEntityMapper
import com.prospection.refdata.items.integration.mapper.PublishedItemVersionEntityMapper
import org.hibernate.envers.AuditReader
import org.hibernate.envers.query.AuditEntity
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.orm.ObjectOptimisticLockingFailureException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import jakarta.persistence.EntityManager

@Component
class ItemGroupJpaAdapter(
    private val itemGroupEntityMapper: ItemGroupEntityMapper,
    private val itemGroupJpaRepository: ItemGroupJpaRepository,
    private val auditReader: AuditReader,
    private val publishedItemGroupVersionEntityMapper: PublishedItemGroupVersionEntityMapper,
    private val publishedItemGroupVersionJpaRepository: PublishedItemGroupVersionJpaRepository,
    private val itemGroupsMetadataJpaRepository: ItemGroupsMetadataJpaRepository,
    private val itemGroupsMetadataEntityMapper: ItemGroupsMetadataEntityMapper,
    private val publishedItemVersionEntityMapper: PublishedItemVersionEntityMapper,
    private val entityManager: EntityManager
) : ItemGroupPort, PublishItemGroupVersionPort, ItemGroupMetadataPort {

    companion object {
        const val DUPLICATE_NAME_MESSAGE = "Sorry, this name has already been taken by a group."
        const val DUPLICATE_BUSINESS_KEY_MESSAGE = "Sorry, this business key has already been taken by a group."
        val logger: Logger = LoggerFactory.getLogger(ItemGroupJpaAdapter::class.java)
    }

    override fun listItemGroups(): List<ItemGroup> {
        return itemGroupJpaRepository.findAll().map { itemGroupEntityMapper.toDomain(it) }
    }

    override fun listActiveItemGroups(): List<ItemGroup> {
        return itemGroupJpaRepository.listActiveItemGroups().map { itemGroupEntityMapper.toDomain(it) }
    }

    override fun getItemGroup(id: String): ItemGroup {
        return itemGroupJpaRepository.findByUuid(id)?.let {
            itemGroupEntityMapper.toDomain(it)
        } ?: throw ModelDoesNotExistException()
    }

    override fun createItemGroup(itemGroup: ItemGroup): ItemGroup {
        val entity = itemGroupEntityMapper.toEntity(itemGroup)

        // check business key
        if (itemGroupJpaRepository.existsByName(entity.name)) {
            throw InvalidModelException(DUPLICATE_NAME_MESSAGE)
        }
        if (itemGroupJpaRepository.existsByBusinessKey(entity.businessKey)) {
            throw InvalidModelException(DUPLICATE_BUSINESS_KEY_MESSAGE)
        }

        // Save entity
        val savedEntity = itemGroupJpaRepository.save(entity)
        return itemGroupEntityMapper.toDomain(savedEntity)
    }

    override fun updateItemGroup(itemGroup: ItemGroup): ItemGroup {
        val entityToUpdate = itemGroupEntityMapper.toEntity(itemGroup)
        val existingEntity = itemGroupJpaRepository.findByUuid(entityToUpdate.uuid)
            ?: throw ModelDoesNotExistException()
        entityToUpdate.id = existingEntity.id
        entityToUpdate.businessKey = existingEntity.businessKey
        itemGroupJpaRepository.findByName(entityToUpdate.name)?.let {
            if (it.id != entityToUpdate.id) {
                throw InvalidModelException(DUPLICATE_NAME_MESSAGE)
            }
        }
        try {
            return itemGroupJpaRepository.save(entityToUpdate).let {
                itemGroupEntityMapper.toDomain(it)
            }
        } catch (ex: ObjectOptimisticLockingFailureException) {
            logger.error("Optimistic lock failed while updating item group: {}", entityToUpdate.id, ex)
            throw InvalidModelException("This group has been updated by others. You now need to refresh the page and get the latest version to make any further changes. Before doing so, make a note of your changes - You work will be lost when refreshing the page!")
        }
    }

    override fun getLatestItemGroupRevisionId(): Int {
        return auditReader.createQuery()
            .forRevisionsOfEntity(ItemGroupEntity::class.java, true)
            .addProjection(AuditEntity.revisionNumber().max())
            .singleResult as Int? ?: 0
    }

    override fun savePublishedVersion(publishedItemGroupVersion: PublishedItemGroupVersion) {
        publishedItemGroupVersionJpaRepository.save(
            publishedItemGroupVersionEntityMapper.toEntity(publishedItemGroupVersion)
        )
    }

    override fun findAllPublishedVersions(): List<PublishedItemGroupVersion> {
        return publishedItemGroupVersionJpaRepository.findByArchivedIsFalseOrderByPublishedAtDesc().let {
            publishedItemGroupVersionEntityMapper.toDomain(it)
        }
    }

    override fun getLatestPublishedVersion(): PublishedItemGroupVersion {
        return publishedItemGroupVersionJpaRepository.findFirstByArchivedIsFalseOrderByPublishedAtDesc().let {
            publishedItemGroupVersionEntityMapper.toDomain(
                it ?: throw RuntimeException("Item groups have never been published")
            )
        }
    }

    override fun getLatestPublishedVersionString(): String = getLatestPublishedVersion().publishedVersion

    override fun getPublishedVersionByVersion(publishedVersion: String): PublishedItemGroupVersion {
        return publishedItemGroupVersionJpaRepository.findByArchivedIsFalseAndPublishedVersion(publishedVersion).let {
            publishedItemGroupVersionEntityMapper.toDomain(
                it ?: throw RuntimeException("Item groups have never been published")
            )
        }
    }

    @Transactional
    override fun saveDraftMetadata(metadata: ItemGroupsMetadata) {
        val itemGroupMetadataEntity = itemGroupsMetadataJpaRepository.findByPublishedItemGroupVersionNull()
        itemGroupsMetadataJpaRepository.save(
            ItemGroupsMetadataEntity(
                id = itemGroupMetadataEntity?.id,
                itemGroupRevisionId = metadata.itemGroupRevisionId,
                publishedItemVersion = publishedItemVersionEntityMapper.toEntity(metadata.publishedItemVersion),
                createdBy = metadata.createdBy,
                createdAt = metadata.createdAt
            )
        )
    }

    @Transactional
    override fun savePublishMetadata(
        publishedItemGroupVersion: PublishedItemGroupVersion,
        createdBy: String,
        createdAt: LocalDateTime
    ) {
        itemGroupsMetadataJpaRepository.findByPublishedItemGroupVersionNull()
            ?.let {
                itemGroupsMetadataJpaRepository.save(
                    ItemGroupsMetadataEntity(
                        itemGroupRevisionId = it.itemGroupRevisionId,
                        publishedItemVersion = it.publishedItemVersion,
                        publishedItemGroupVersion = publishedItemGroupVersionEntityMapper.toEntity(
                            publishedItemGroupVersion
                        ),
                        createdBy = createdBy,
                        createdAt = createdAt
                    )
                )
            }
    }

    override fun archivePublishedVersions(publishedVersions: Collection<String>) {
        publishedItemGroupVersionJpaRepository.archivePublishVersion(publishedVersions)
        // since we update manually, we need to clear so that findAll does not return archived versions
        entityManager.clear()
    }

    override fun getDraftMetadata(): ItemGroupsMetadata? {
        return itemGroupsMetadataJpaRepository.findByPublishedItemGroupVersionNull()?.let {
            itemGroupsMetadataEntityMapper.toDomain(it)
        }
    }

    override fun unarchiveItemGroup(uuid: String, lastModifiedBy: String, lastModifiedAt: LocalDateTime): ItemGroup {
        val entityToUpdate = itemGroupJpaRepository.findByUuid(uuid) ?: throw ModelDoesNotExistException()
        itemGroupJpaRepository.findByBusinessKey(entityToUpdate.businessKey)?.let {
            if (it.id != entityToUpdate.id) {
                throw InvalidModelException(DUPLICATE_BUSINESS_KEY_MESSAGE)
            }
        }
        entityToUpdate.deleted = EntityStatus.ACTIVE.value
        entityToUpdate.lastModifiedAt = lastModifiedAt
        entityToUpdate.lastModifiedBy = lastModifiedBy
        return itemGroupEntityMapper.toDomain(itemGroupJpaRepository.save(entityToUpdate))
    }
}