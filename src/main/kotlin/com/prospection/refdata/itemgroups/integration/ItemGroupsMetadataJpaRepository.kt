package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface ItemGroupsMetadataJpaRepository : JpaRepository<ItemGroupsMetadataEntity, Long>,
    JpaSpecificationExecutor<ItemGroupsMetadataEntity> {

    fun findByPublishedItemGroupVersionNull(): ItemGroupsMetadataEntity?

}