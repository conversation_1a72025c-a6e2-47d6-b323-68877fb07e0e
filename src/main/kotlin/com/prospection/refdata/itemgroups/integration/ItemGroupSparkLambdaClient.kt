package com.prospection.refdata.itemgroups.integration

import com.fasterxml.jackson.core.type.TypeReference
import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.common.integration.AwsLambdaClient
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupPort
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import com.prospection.refdata.itemgroups.domain.ItemGroupsSparkPort
import com.prospection.refdata.items.domain.PublishItemVersionPort
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.ApplicationListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(value = ["application.amazon.lambda.itemGroupsSpark"])
class ItemGroupSparkLambdaClient(
    @Value("\${application.amazon.lambda.itemGroupsSpark:}")
    private val lambdaFunctionName: String,
    private val awsLambdaClient: AwsLambdaClient,
    private val itemGroupPort: ItemGroupPort,
    private val publishItemVersionPort: PublishItemVersionPort,
    private val codingSystemPort: CodingSystemPort,
): ItemGroupsSparkPort, ApplicationListener<ApplicationReadyEvent> {

    companion object {
        private val logger by lazyLogger()
    }

    override fun onApplicationEvent(event: ApplicationReadyEvent) {
        logger.info("Application is ready, warming up lambdas-spark")
        // We don't want to stop the application if there is any error
        try {
            itemGroupPort.listActiveItemGroups().firstOrNull() ?.let { itemGroup ->
                val latestPublishedItemsVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
                    ?: throw RuntimeException("Items have never been published")
                val codingSystemToClassifications = codingSystemPort.findAllCodingSystemToClassifications()
                    .filter { itemGroup.deserialisedRule.canResolveToCodingSystem(it.key) }

                getItemGroupPreview(itemGroup, codingSystemToClassifications, latestPublishedItemsVersion)
            } ?: logger.info("No item group available to warm up lambdas-spark")
        } catch (e: Exception) {
            logger.error("Error warming up lambdas-spark", e)
        }
    }

    override fun getItemGroupPreview(
        itemGroup: ItemGroup,
        codingSystemToClassifications: Map<String, List<CodingSystemToClassification>>,
        publishedItemsVersion: String
    ): List<ItemGroupPreviewResult> {
        val payload = ItemGroupPreviewRequestPayload(itemGroup, codingSystemToClassifications, publishedItemsVersion)

        return awsLambdaClient.invoke(
            lambdaFunctionName, payload,
            object : TypeReference<List<ItemGroupPreviewResult>>() {}
        )
    }
}