package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.items.integration.PublishedItemVersionEntity
import java.time.LocalDateTime
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Entity(name = "ItemGroupsMetadata")
@Table(name = "item_groups_metadata")
class ItemGroupsMetadataEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @NotNull
    var itemGroupRevisionId: Int,

    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "published_item_version")
    var publishedItemVersion: PublishedItemVersionEntity,

    @ManyToOne
    @JoinColumn(name = "published_item_group_version")
    var publishedItemGroupVersion: PublishedItemGroupVersionEntity? = null,

    @NotNull
    var createdBy: String,

    @NotNull
    var createdAt: LocalDateTime,
)