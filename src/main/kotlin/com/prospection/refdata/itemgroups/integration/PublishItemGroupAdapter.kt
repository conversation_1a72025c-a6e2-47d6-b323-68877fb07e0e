package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.common.integration.S3PathUtils.createVersionPath
import com.prospection.refdata.config.S3Path.ItemGroups
import com.prospection.refdata.config.S3Path.ItemGroups.Published.ITEM_GROUPS_TO_TOPICS
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.PublishItemGroupPort
import com.prospection.refdata.itemgroups.domain.PublishedItemGroup
import com.prospection.refdata.itemgroups.domain.PublishedItemToItemGroup
import com.prospection.refdata.itemgroups.integration.datasetrow.ItemGroupToTopicRow
import com.prospection.refdata.itemgroups.integration.mapper.ItemToItemGroupCsvRowMapper
import com.prospection.refdata.itemgroups.integration.mapper.PublishedItemGroupEntityMapper
import com.prospection.refdata.itemgroups.integration.mapper.PublishedItemToItemGroupEntityMapper
import com.prospection.refdata.topic.domain.EnrichedTopic
import com.univocity.parsers.common.processor.BeanListProcessor
import com.univocity.parsers.csv.CsvParser
import com.univocity.parsers.csv.CsvParserSettings
import org.apache.spark.sql.Encoders
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.model.GetObjectResponse

@Component
class PublishItemGroupAdapter(
    private val importExportHelper: ImportExportHelper<ResponseInputStream<GetObjectResponse>>,
    private val sparkImportExportHelper: SparkImportExportHelper,
    private val itemToItemGroupCsvRowMapper: ItemToItemGroupCsvRowMapper,
    private val dslContext: DSLContext,
    private val publishedItemToItemGroupJpaRepository: PublishedItemToItemGroupJpaRepository,
    private val publishedItemToItemGroupEntityMapper: PublishedItemToItemGroupEntityMapper,
    private val publishedItemGroupJpaRepository: PublishedItemGroupJpaRepository,
    private val publishedItemGroupEntityMapper: PublishedItemGroupEntityMapper,
    private val archivedItemGroupJpaRepository: ArchivedItemGroupJpaRepository,
    private val archivedItemToItemGroupJpaRepository: ArchivedItemToItemGroupJpaRepository,
) : PublishItemGroupPort {
    companion object {
        val logger by lazyLogger()
    }

    override fun publishItemsToItemGroups(version: String): List<PublishedItemToItemGroup> {
        importExportHelper.copyAll(
            ItemGroups.Draft.ITEM_TO_ITEM_GROUP,
            "${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(version)}"
        )

        return getItemToItemGroups(version)
    }

    override fun exportItemGroupsForAudit(itemGroups: List<ItemGroup>, version: String) {
        val auditItemGroups = itemGroups.map {
            AuditItemGroup(
                businessKey = it.businessKey,
                name = it.name,
                rule = it.rule,
                lastModifiedBy = it.lastModifiedBy,
                lastModifiedAt = it.lastModifiedAt.toString(),
                deleted = it.deleted
            )
        }

        importExportHelper.writeJson(
            // FIXME: WHY we are using ARCHIVE_ITEM_GROUP path for audit item groups?
            "${ItemGroups.Published.ARCHIVE_ITEM_GROUP}/${createVersionPath(version)}/item-groups.json",
            auditItemGroups
        )
    }

    override fun findPublishedItemToItemGroups(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>
    ): List<PublishedItemToItemGroup> {
        val start = System.currentTimeMillis()
        return publishedItemToItemGroupJpaRepository.findByVersionAndItemGroupBusinessKeyIn(
            publishedItemGroupVersion,
            itemGroupBusinessKeys
        ).let {
            val durationInMs = System.currentTimeMillis() - start
            if (durationInMs > 1000) {
                logger.warn("findPublishedItemToItemGroups took $durationInMs ms for: version = '$publishedItemGroupVersion'" +
                        " and itemGroupBusinessKeys in ${itemGroupBusinessKeys.joinToString("', '", "('", "')")}")
            }
            publishedItemToItemGroupEntityMapper.toDomain(it)
        }
    }

    override fun findPublishedItemGroups(
        publishedItemGroupVersion: String,
        itemGroupBusinessKeys: Set<String>,
    ): List<PublishedItemGroup> {
        return publishedItemGroupJpaRepository.findByVersionAndBusinessKeyIn(
            publishedItemGroupVersion,
            itemGroupBusinessKeys,
        ).let {
            publishedItemGroupEntityMapper.toDomain(it)
        }
    }

    override fun findPublishedItemGroups(
        publishedItemGroupVersion: String,
        nameOrBusinessKey: String,
        pageable: Pageable,
    ): Page<PublishedItemGroup> {
        val specification = Specification<PublishedItemGroupEntity> { root, query, builder ->
            val stringLiteral = "%${nameOrBusinessKey.lowercase()}%"

            query?.orderBy(builder.asc(root.get<String>("name")))

            builder.and(
                builder.equal(root.get<String>("version"), publishedItemGroupVersion),
                builder.or(
                    builder.like(
                        builder.lower(root.get("name")), stringLiteral
                    ),
                    builder.like(
                        builder.lower(root.get("businessKey")), stringLiteral
                    )
                )
            )
        }

        return publishedItemGroupJpaRepository.findAll(specification, pageable)
            .map { publishedItemGroupEntityMapper.toDomain(it) }
    }

    override fun findAllItemGroups(publishedItemGroupVersion: String): List<PublishedItemGroup> {
        return publishedItemGroupJpaRepository.findByVersion(publishedItemGroupVersion).map {
            publishedItemGroupEntityMapper.toDomain(it)
        }
    }

    override fun publishItemGroupsToTopics(version: String, enrichedTopics: Collection<EnrichedTopic>, itemGroupNameByKey: Map<String, String>) {
        val rowsToExport = enrichedTopics.flatMap { enrichedTopic ->
            enrichedTopic.topic.relatedWorkflows.flatMap { workflow ->
                workflow.relatedItemGroups.map { itemGroupKey ->
                    ItemGroupToTopicRow.toRow(itemGroupKey, itemGroupNameByKey.get(itemGroupKey)!!, enrichedTopic, workflow)
                }
            }
        }

        sparkImportExportHelper.writeParquet("$ITEM_GROUPS_TO_TOPICS/${createVersionPath(version)}",
            rowsToExport, Encoders.bean(ItemGroupToTopicRow::class.java))
    }

    override fun savePublishedItemToItemGroups(
        publishedVersion: String,
        publishedItemToItemGroups: List<PublishedItemToItemGroup>
    ) {
        // using jooq's batch insert instead of hibernate because it's much faster
        // inserting 10k rows took 3~5 sec with jooq while 12~18 sec with hibernate
        val batchStatement = publishedItemToItemGroups.map {
            dslContext.insertInto(DSL.table("published_item_to_item_group"))
                .columns(
                    DSL.field("version"),
                    DSL.field("item_code"),
                    DSL.field("item_group_business_key"),
                    DSL.field("classification"),
                    DSL.field("item_group_name")
                )
                .values(publishedVersion, it.itemCode, it.itemGroupBusinessKey, it.classification, it.itemGroupName)
        }

        dslContext.batch(*batchStatement.toTypedArray())
            .execute()
    }

    override fun savePublishedItemGroups(publishedVersion: String, publishedItemGroups: Set<PublishedItemGroup>) {
        val batchStatement = publishedItemGroups.map {
            dslContext.insertInto(DSL.table("published_item_group"))
                .columns(
                    DSL.field("version"),
                    DSL.field("business_key"),
                    DSL.field("name")
                )
                .values(publishedVersion, it.businessKey, it.name)
        }

        dslContext.batch(*batchStatement.toTypedArray())
            .execute()
    }

    override fun archivePublishedItemGroups(publishedItemGroupVersions: List<String>) {
        archivedItemGroupJpaRepository.saveFromPublishedVersion(publishedItemGroupVersions)
        publishedItemGroupJpaRepository.deleteByVersionIn(publishedItemGroupVersions)
    }

    override fun archivePublishedItemsToItemGroups(publishedItemGroupVersions: Collection<String>) {
        archivedItemToItemGroupJpaRepository.saveFromPublishedVersion(publishedItemGroupVersions)
        publishedItemToItemGroupJpaRepository.deleteByVersionIn(publishedItemGroupVersions)
        publishedItemGroupVersions.forEach {
            val publishVersionPath = "${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(it)}"
            val archivedVersionPath = "${ItemGroups.Archived.ITEM_TO_ITEM_GROUP}/${createVersionPath(it)}"

            importExportHelper.copyAll(publishVersionPath, archivedVersionPath)
            importExportHelper.deleteAll(publishVersionPath)
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    override fun finalizeArchiving() {
        dslContext.execute("VACUUM ANALYZE published_item_group")
        dslContext.execute("VACUUM ANALYZE published_item_to_item_group")
    }

    /**
     * Here, univocity parser is used instead of spark dataset and collectAsList to fetch the item to item group mappings.
     * It's because
     *  (1) collecting the dataset rows at once can cause out of memory error by any chance while univocity parser can read line by line
     *  (2) performance of using univocity is better than using spark when reading a single CSV
     */
    // TODO: using streaming reader to read the CSV file line by line, there are more than 500k rows in the CSV file now
    private fun getItemToItemGroups(version: String): List<PublishedItemToItemGroup> {
        val csvPath = importExportHelper.findFirst(
            "${ItemGroups.Published.ITEM_TO_ITEM_GROUP}/${createVersionPath(version)}",
            ".csv"
        )!!

        val settings = CsvParserSettings()
        settings.isHeaderExtractionEnabled = true

        val rowProcessor: BeanListProcessor<PublishedItemToItemGroupCsvRow> =
            BeanListProcessor(PublishedItemToItemGroupCsvRow::class.java)
        settings.setProcessor(rowProcessor)

        val parser = CsvParser(settings)

        val csvRows = importExportHelper.readFile(csvPath) {
            parser.parse(it)
            rowProcessor.beans
        }!!

        return csvRows.map { itemToItemGroupCsvRowMapper.toDomain(it) }
    }
}