package com.prospection.refdata.itemgroups.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.integration.ItemGroupEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface ItemGroupEntityMapper : DomainMapper<ItemGroup, ItemGroupEntity>, EntityMapper<ItemGroupEntity, ItemGroup> {
    @Mappings(
        value = [
            Mapping(source = "uuid", target = "id")
        ]
    )
    override fun toDomain(other: ItemGroupEntity): ItemGroup

    @Mappings(
        value = [
            Mapping(source = "id", target = "uuid"),
            Mapping(target = "id", ignore = true),
        ]
    )
    override fun toEntity(other: ItemGroup): ItemGroupEntity
}