package com.prospection.refdata.itemgroups.integration

import java.io.Serializable
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.Index
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Entity(name = "ArchivedItemGroup")
@Table(
    name = "archived_item_group",
    // This is a clustered index for performance. See https://www.postgresql.org/docs/9.1/sql-cluster.html
    indexes = [Index(columnList = "version", name = "archived_item_group_version_idx")]
)
// avoid having unnecessary id field and index which is quite big for big table which we don't use at all
@IdClass(ArchivedItemGroupEntity::class)
data class ArchivedItemGroupEntity(
    @Id @NotNull var version: String,
    @Id @NotNull var businessKey: String,
    @Id @NotNull var name: String,
): Serializable
