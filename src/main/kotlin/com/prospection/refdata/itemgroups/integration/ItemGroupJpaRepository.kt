package com.prospection.refdata.itemgroups.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.history.RevisionRepository
import org.springframework.lang.Nullable
import org.springframework.stereotype.Repository

@Repository
interface ItemGroupJpaRepository : JpaRepository<ItemGroupEntity, Long>,
    JpaSpecificationExecutor<ItemGroupEntity>,
    RevisionRepository<ItemGroupEntity, Long, Int> {

    @Query("select i from ItemGroup i where i.deleted = 0")
    fun listActiveItemGroups(): List<ItemGroupEntity>

    @Query("select case when (count(i) > 0) then true else false end from ItemGroup i where ?1 = i.businessKey and i.deleted = 0")
    fun existsByBusinessKey(businessKey: String): Bo<PERSON>an

    @Query("select case when (count(i) > 0) then true else false end from ItemGroup i where UPPER(?1) = UPPER(i.name) and i.deleted = 0")
    fun existsByName(name: String): <PERSON><PERSON><PERSON>

    @Nullable
    @Query("select i from ItemGroup i where ?1 = i.businessKey and i.deleted = 0")
    fun findByBusinessKey(businessKey: String): ItemGroupEntity?

    @Nullable
    @Query("select i from ItemGroup i where UPPER(?1) = UPPER(i.name) and i.deleted = 0")
    fun findByName(name: String): ItemGroupEntity?

    fun findByUuid(uuid: String): ItemGroupEntity?
}