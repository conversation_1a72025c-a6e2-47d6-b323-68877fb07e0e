package com.prospection.refdata.itemgroups.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.itemgroups.domain.PublishedItemGroup
import com.prospection.refdata.itemgroups.integration.PublishedItemGroupEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface PublishedItemGroupEntityMapper : DomainMapper<PublishedItemGroup, PublishedItemGroupEntity>,
    EntityMapper<PublishedItemGroupEntity, PublishedItemGroup> {
    @Mappings(
        value = [
            Mapping(target = "id", ignore = true),
            Mapping(target = "version", ignore = true),
        ]
    )
    override fun toEntity(other: PublishedItemGroup): PublishedItemGroupEntity
}