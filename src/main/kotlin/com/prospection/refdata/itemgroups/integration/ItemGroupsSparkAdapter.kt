package com.prospection.refdata.itemgroups.integration

import com.prospection.refdata.codingsystem.domain.CodingSystemToClassification
import com.prospection.refdata.featuretoggle.domain.ENABLE_ITEM_GROUP_PREVIEW_USING_LAMBDA
import com.prospection.refdata.featuretoggle.domain.FeatureTogglePort
import com.prospection.refdata.itemgroups.domain.ItemGroup
import com.prospection.refdata.itemgroups.domain.ItemGroupPreviewResult
import com.prospection.refdata.itemgroups.domain.ItemGroupsSparkPort
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Component

@Primary
@Component
class ItemGroupsSparkAdapter(
    private val itemGroupsSparkImpl: ItemGroupsSparkImpl,
    @Autowired(required = false)
    private val itemGroupSparkLambdaClient: ItemGroupSparkLambdaClient?,
    private val featureTogglePort: FeatureTogglePort,
): ItemGroupsSparkPort {

    override fun getItemGroupPreview(
        itemGroup: ItemGroup,
        codingSystemToClassifications: Map<String, List<CodingSystemToClassification>>,
        publishedItemsVersion: String
    ): List<ItemGroupPreviewResult> {
        return getItemGroupSpark().getItemGroupPreview(itemGroup, codingSystemToClassifications, publishedItemsVersion)
    }

    private fun getItemGroupSpark(): ItemGroupsSparkPort {
        return itemGroupSparkLambdaClient
            ?.takeIf { featureTogglePort.isEnable(ENABLE_ITEM_GROUP_PREVIEW_USING_LAMBDA) }
            ?: itemGroupsSparkImpl
    }
}