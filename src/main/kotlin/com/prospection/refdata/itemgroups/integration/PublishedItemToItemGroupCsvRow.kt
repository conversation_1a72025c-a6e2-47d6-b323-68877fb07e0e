package com.prospection.refdata.itemgroups.integration

import com.univocity.parsers.annotations.Parsed
import jakarta.validation.constraints.NotNull

data class PublishedItemToItemGroupCsvRow(
    @Parsed(field = ["item_code"]) @NotNull var itemCode: String? = null,
    @Parsed(field = ["item_group"]) @NotNull var itemGroupBusinessKey: String? = null,
    @Parsed @NotNull var classification: String? = null,
    @Parsed(field = ["item_group_name"]) @NotNull var itemGroupName: String? = null
)
