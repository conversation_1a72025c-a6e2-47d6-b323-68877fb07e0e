package com.prospection.refdata.itemgroups.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.itemgroups.domain.PublishedItemToItemGroup
import com.prospection.refdata.itemgroups.integration.PublishedItemToItemGroupEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface PublishedItemToItemGroupEntityMapper : DomainMapper<PublishedItemToItemGroup, PublishedItemToItemGroupEntity>,
    EntityMapper<PublishedItemToItemGroupEntity, PublishedItemToItemGroup> {
    @Mappings(
        value = [
            Mapping(target = "id", ignore = true),
            Mapping(target = "version", ignore = true),
        ]
    )
    override fun toEntity(other: PublishedItemToItemGroup): PublishedItemToItemGroupEntity
}