package com.prospection.refdata.itemgroups.integration

import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Entity(name = "PublishedItemGroup")
@Table(
    name = "published_item_group",
    indexes = [
        Index( // This is a clustered index for performance. See https://www.postgresql.org/docs/9.1/sql-cluster.html
            columnList = "version",
            name = "published_item_group_version_idx"
        ),
        Index(
            columnList = "businessKey, name",
            name = "published_item_group_business_key_name_idx"
        ),
    ]
)
class PublishedItemGroupEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    @NotNull var version: String,
    @NotNull var businessKey: String,
    @NotNull var name: String,
)