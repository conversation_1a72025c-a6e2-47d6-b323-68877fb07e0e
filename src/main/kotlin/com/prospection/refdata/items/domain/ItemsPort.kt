package com.prospection.refdata.items.domain

import com.prospection.refdata.rules.domain.EnrichmentRule
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import java.time.LocalDateTime

interface ItemsPort {

    fun cleanupTemporaryFolder()

    fun uploadTemporaryItems(csv: String, fileName: String)

    fun writeDraftItems(codingSystem: String, rawUpload: Dataset<Row>)

    fun getChangeSummariesUrl(latestVersion: String, codingSystems: Set<String>, now: LocalDateTime): String?

    fun getEnrichedItemsDownloadUrl(codingSystem: String, now: LocalDateTime): String?

    fun writeEnrichedItems(codingSystem: String, enrichedItems: Dataset<Row>)

    fun writeDownloadableEnrichedItems(codingSystem: String)

    fun applyRules(codingSystem: String, enrichmentRules: List<EnrichmentRule>): Dataset<Row>

    fun writePreview(codingSystem: String, enrichedItems: Dataset<Row>): String

    fun publishItems(version: String, codingSystems: Set<String>)

    fun doesRawItemsExist(codingSystem: String): Boolean

    fun filterMatchedRowsOnly(enrichedItems: Dataset<Row>, enrichmentRule: EnrichmentRule): Dataset<Row>

    fun getPublishedItems(codingSystem: String, version: String): Dataset<Row>?

    fun getRawItems(codingSystem: String): Dataset<Row>?

    fun getRawUpload(): Dataset<Row>

    fun writeSnapshot(rawUpload: Dataset<Row>, version: String, codingSystem: String)

    fun getSecondLatestSnapshotRawItems(codingSystem: String): Dataset<Row>?

    fun getDiffRawItems(currentRawItems: Dataset<Row>, secondLatestSnapshotRawItems: Dataset<Row>): Dataset<Row>

    fun getDataUpdate(publishedItems: Dataset<Row>, diffRawItems: Dataset<Row>): Dataset<Row>

    fun getDataUpdateBeforePublishingNewDraft(publishedItems: Dataset<Row>, diffRawItems: Dataset<Row>): Dataset<Row>

    fun createDataUpdateReportAndGetUrl(codingSystem: String, dataUpdateReport: Dataset<Row>, itemGroupsWithTopicsAndSubscriptions: Dataset<Row>, now: LocalDateTime, version: String): String?
}