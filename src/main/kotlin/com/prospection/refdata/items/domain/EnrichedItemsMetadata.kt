package com.prospection.refdata.items.domain

import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.itemgroups.domain.PublishedItemGroupVersion
import java.time.LocalDateTime

data class EnrichedItemsMetadata(
    override val codingSystem: CodingSystem,
    override val sourceAttributes: List<String>,
    val enrichedAttributes: List<String>,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val latestEnrichmentRuleRevisionId: Int,
    val publishedItemGroupVersion: PublishedItemGroupVersion? = null,
): HasClassificationAndSourceAttributes