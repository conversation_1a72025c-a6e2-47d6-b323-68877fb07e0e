package com.prospection.refdata.items.domain

import com.prospection.refdata.codingsystem.domain.CodingSystem
import java.time.LocalDateTime

interface EnrichedItemsMetadataPort {
    fun saveDraftMetadata(metadata: EnrichedItemsMetadata)

    fun getAllDraftMetadata(): List<EnrichedItemsMetadata>

    /**
     * return null if draft metadata doesn't exist
     */
    fun getDraftMetadata(codingSystem: CodingSystem): EnrichedItemsMetadata?

    fun savePublishMetadata(
        newPublishedItemVersion: PublishedItemVersion,
        createdBy: String,
        createdAt: LocalDateTime
    )

}