package com.prospection.refdata.items.domain

import com.prospection.refdata.codingsystem.domain.CodingSystem
import java.time.LocalDateTime

data class RawItemsMetadata(
    override val codingSystem: CodingSystem,
    override val sourceAttributes: List<String>,
    val totalItem: Long,
    val newItem: Long,
    val deletedItem: Long,
    val publishedItemVersion: PublishedItemVersion? = null,
    val createdBy: String,
    val createdAt: LocalDateTime,
): HasClassificationAndSourceAttributes
