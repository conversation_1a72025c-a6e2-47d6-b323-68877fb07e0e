package com.prospection.refdata.items.domain

import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.S3ItemsExporterForExternalToolPort
import com.prospection.refdata.common.domain.Summarizable
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.common.integration.GenerateDataUpdateReportHelper
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.common.integration.LogSupport.measureExecution
import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.ItemGroupPort
import com.prospection.refdata.job.domain.Job
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.rules.domain.EnrichmentRule
import com.prospection.refdata.rules.domain.EnrichmentRulePort
import com.prospection.refdata.rules.domain.EnrichmentRuleService
import com.prospection.refdata.rules.domain.PublishEnrichmentRulePort
import com.prospection.refdata.rules.domain.Type
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ItemsService(
    private val itemsPort: ItemsPort,
    private val enrichmentRuleService: EnrichmentRuleService,
    private val enrichmentRulePort: EnrichmentRulePort,
    private val userPort: UserPort,
    private val datetimePort: DateTimePort,
    private val publishEnrichmentRulePort: PublishEnrichmentRulePort,
    private val codingSystemPort: CodingSystemPort,
    private val jobPort: JobPort,
    private val publishItemVersionPort: PublishItemVersionPort,
    private val rawItemsMetadataPort: RawItemsMetadataPort,
    private val enrichedItemsMetadataPort: EnrichedItemsMetadataPort,
    private val itemGroupPort: ItemGroupPort,
    private val applyItemGroupRulePort: ApplyItemGroupRulePort,
    private val generateDataUpdateReportHelper: GenerateDataUpdateReportHelper,
    private val s3ItemsExporterForExternalToolPort: S3ItemsExporterForExternalToolPort,
) : Summarizable {
    companion object {
        val logger by lazyLogger()
    }

    fun import(itemsImport: ItemsImport) {
        if (itemsImport.currentChunk == 1) {
            itemsPort.cleanupTemporaryFolder()
        }

        itemsPort.uploadTemporaryItems(itemsImport.csv, "${itemsImport.classification}-${itemsImport.currentChunk}.csv")
    }

    fun queueProcessAfterImport(codingSystem: String): Job {
        val userId = userPort.getCurrentUserId()
        val version = datetimePort.toPublishedVersionString(datetimePort.now())

        return jobPort.startJob("Importing $codingSystem") {
            processAfterImport(codingSystem, userId, version, true)

            null
        }
    }

    fun enrichItems(codingSystem: String, userId: String) {
        val enrichmentRules = enrichmentRuleService.list()

        if (itemsPort.doesRawItemsExist(codingSystem)) {
            val enrichmentRulesForCodingSystem = enrichmentRules.filter {
                it.deserialisedRule.canResolveToCodingSystem(codingSystem)
            }
            enrichItems(codingSystem, userId, enrichmentRulesForCodingSystem)
        } else {
            logger.info("Could not enrich items of $codingSystem because rawItems don't exist")
        }
    }

    private fun enrichItems(codingSystem: String, userId: String, enrichmentRules: List<EnrichmentRule>) {
        val enrichedItems = itemsPort.applyRules(codingSystem, enrichmentRules)

        itemsPort.writeEnrichedItems(codingSystem, enrichedItems)

        val columnNames = enrichedItems.columns().toList() as List<String>

        enrichedItemsMetadataPort.saveDraftMetadata(
            EnrichedItemsMetadata(
                codingSystem = codingSystemPort.findByName(codingSystem),
                createdBy = userId,
                createdAt = datetimePort.now(),
                latestEnrichmentRuleRevisionId = enrichmentRulePort.getLatestEnrichmentRuleRevisionId(),
                sourceAttributes = columnNames.filter { Type.getTypeByAttributeName(it) == Type.SOURCE_ATTRIBUTE },
                enrichedAttributes = columnNames.filter { Type.getTypeByAttributeName(it) == Type.ENRICHED_ATTRIBUTE }
            )
        )
    }

    fun getRawItemsMetadata(): List<RawItemsMetadata> = rawItemsMetadataPort.getAllDraftMetadata()

    fun getRawItemsMetadata(codingSystem: String): RawItemsMetadata? {
        return rawItemsMetadataPort.getAllDraftMetadata()
            .firstOrNull { it.codingSystem.name == codingSystem }
    }

    fun queueDownloadingChangeSummary(): Job {
        val userId = userPort.getCurrentUserId()

        return jobPort.startJob("Generating the change summary of items") {
            val changeSummaryUrl = generateChangeSummary(userId)
                ?: throw RuntimeException("No change summary available because there’s no difference between draft and published items")

            changeSummaryUrl
        }
    }

    fun queueDownloadingEnrichedItems(classification: String): Job {
        val userId = userPort.getCurrentUserId()

        return jobPort.startJob("Generating the enriched items of $classification") {
            getEnrichedItemsDownloadUrl(classification, userId)
        }
    }

    fun enrichItemsIfObsolete(codingSystemName: String, userId: String) {
        val codingSystem = codingSystemPort.findByName(codingSystemName)
        val metadata = enrichedItemsMetadataPort.getDraftMetadata(codingSystem)
        logger.info("Checking if $codingSystemName has to be enriched again")

        if (metadata != null) {
            val latestEnrichmentRuleRevisionId = enrichmentRulePort.getLatestEnrichmentRuleRevisionId()

            if (metadata.latestEnrichmentRuleRevisionId < latestEnrichmentRuleRevisionId) {
                logger.info("Enriching $codingSystemName because it's obsolete. Current ver: $latestEnrichmentRuleRevisionId, metadata ver: ${metadata.latestEnrichmentRuleRevisionId}")
                enrichItems(codingSystemName, userId)
            } else {
                logger.info("Enrichment not required because $codingSystemName is up-to-date")
            }
        } else {
            // enrichItems again if metadata doesn't exist by any chance
            enrichItems(codingSystemName, userId)
        }
    }

    fun queuePublishItemsAndRules(): Job {
        val userId = userPort.getCurrentUserId()

        return jobPort.startJob("Publishing items") {
            publish(userId)

            null
        }
    }

    override fun generateChangeSummary(userId: String): String? {
        val codingSystems = codingSystemPort.findAll()

        codingSystems.forEach {
            enrichItemsIfObsolete(it, userId)
        }

        return measureExecution(logger, "Change summary generation") {
            itemsPort.getChangeSummariesUrl(
                publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
                    ?: throw RuntimeException("Items have never been published"),
                codingSystems,
                datetimePort.now()
            )
        }
    }

    fun getEnrichedItemsDownloadUrl(codingSystem: String, userId: String): String? {
        enrichItemsIfObsolete(codingSystem, userId)

        itemsPort.writeDownloadableEnrichedItems(codingSystem)

        return itemsPort.getEnrichedItemsDownloadUrl(codingSystem, datetimePort.now())
    }

    fun processAfterImport(codingSystem: String, userId: String, version: String, storeSnapshot: Boolean) {
        val rawUpload = itemsPort.getRawUpload()

        if (storeSnapshot) {
            itemsPort.writeSnapshot(rawUpload, version, codingSystem)
        }

        s3ItemsExporterForExternalToolPort.storeExternal(codingSystem, rawUpload)

        itemsPort.writeDraftItems(codingSystem, rawUpload)

        val publishedItemVersion = publishItemVersionPort.getLatestPublishedItemVersion()
        rawItemsMetadataPort.saveDraftMetadata(
            rawItems = itemsPort.getRawItems(codingSystem),
            publishedItems = publishedItemVersion?.let {
                itemsPort.getPublishedItems(codingSystem, publishedItemVersion.publishedVersion)
            },
            codingSystem = codingSystemPort.findByName(codingSystem),
            createdBy = userId,
            createdAt = datetimePort.now()
        )

        enrichItems(codingSystem, userId)
    }

    @Transactional
    fun publish(userId: String) {
        val codingSystems = codingSystemPort.findAll()

        codingSystems.forEach {
            enrichItemsIfObsolete(it, userId)
        }

        val now = datetimePort.now()
        val version = datetimePort.toPublishedVersionString(now)
        try {
            // Publish items
            itemsPort.publishItems(version, codingSystems)

            // Get rules from DB
            val enrichmentRules = enrichmentRuleService.list()

            // Publish rules
            publishEnrichmentRulePort.publishEnrichmentRules(enrichmentRules, version)

            val publishedItemVersion = PublishedItemVersion(
                publishedBy = userId,
                publishedVersion = version,
                publishedAt = now,
            )

            publishItemVersionPort.savePublishedVersion(publishedItemVersion)

            rawItemsMetadataPort.savePublishMetadata(publishedItemVersion, userId, now)

            enrichedItemsMetadataPort.savePublishMetadata(publishedItemVersion, userId, now)
        } catch (ex: Exception) {
            logger.error("An exception occurred while publishing items and rules of version: $version")
            throw ex
        }
    }

    fun getLatestPublishedItemsVersion(): PublishedItemVersion {
        return publishItemVersionPort.getLatestPublishedItemVersion()
            ?: throw RuntimeException("Items have never been published")
    }

    fun queueGenerateDataUpdateReport(codingSystem: String): Job {
        return jobPort.startJob("Generating data update report of $codingSystem") {
            generateDataUpdateReport(codingSystem)
        }
    }

    fun generateDataUpdateReport(codingSystem: String): String? {
        val secondLatestSnapshotRawItems = itemsPort.getSecondLatestSnapshotRawItems(codingSystem)
            ?: throw RuntimeException("$codingSystem does not have enough two latest snapshot versions to compare")

        return itemsPort.getRawItems(codingSystem)?.let { currentRawItems ->
            val diffRawItems = itemsPort.getDiffRawItems(currentRawItems, secondLatestSnapshotRawItems)

            val latestPublishedItemsVersion = publishItemVersionPort.getLatestPublishedItemVersion()?.publishedVersion
                ?: throw RuntimeException("Items have never been published")

            return itemsPort.getPublishedItems(codingSystem, latestPublishedItemsVersion)?.let { publishedItems ->
                val dataUpdateItems = itemsPort.getDataUpdate(publishedItems, diffRawItems)

                // Caching to avoid re-computation at multiple places (L.272, L.273)
                val updateItemsWithItemGroups = if (dataUpdateItems.isEmpty) {
                    // This will be empty when the new draft items hasn't published
                    itemsPort.getDataUpdateBeforePublishingNewDraft(publishedItems, diffRawItems)
                } else {
                    val itemGroupsForCodingSystem = itemGroupPort.listActiveItemGroups().filter {
                        it.deserialisedRule.canResolveToCodingSystem(codingSystem)
                    }

                    // Apply item groups
                    applyItemGroupRulePort.getItemsWithItemGroupsOnly(dataUpdateItems, itemGroupsForCodingSystem)
                }
                updateItemsWithItemGroups.persist()

                try {
                    val itemGroupsWithTopicsAndSubscriptions =
                        generateDataUpdateReportHelper.enrichItemGroupsByTopicsAndSubscriptions(updateItemsWithItemGroups)

                    val itemsWithConditions = generateDataUpdateReportHelper.enrichItemsWithConditions(
                        updateItemsWithItemGroups,
                        itemGroupsWithTopicsAndSubscriptions,
                        codingSystem
                    )

                    // Generate data update report URL
                    val now = datetimePort.now()
                    val version = datetimePort.toPublishedVersionString(now)
                    itemsPort.createDataUpdateReportAndGetUrl(
                        codingSystem,
                        itemsWithConditions,
                        itemGroupsWithTopicsAndSubscriptions.toDF(),
                        now,
                        version
                    )
                } finally {
                    // Clear cache once done
                    updateItemsWithItemGroups.unpersist()
                }
            }
        }
    }
}
