package com.prospection.refdata.items.domain

import com.prospection.refdata.codingsystem.domain.CodingSystem
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import java.time.LocalDateTime

interface RawItemsMetadataPort {
    fun saveDraftMetadata(
        rawItems: Dataset<Row>?,
        publishedItems: Dataset<Row>?,
        codingSystem: CodingSystem,
        createdBy: String,
        createdAt: LocalDateTime
    )

    fun getAllDraftMetadata(): List<RawItemsMetadata>

    fun savePublishMetadata(newPublishedItemVersion: PublishedItemVersion, createdBy: String, createdAt: LocalDateTime)

}