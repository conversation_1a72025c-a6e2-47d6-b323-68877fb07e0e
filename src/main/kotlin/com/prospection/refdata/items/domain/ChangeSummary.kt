package com.prospection.refdata.items.domain

import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row

data class ChangeSummary(
    val metaData: ChangeSummaryMetaData,
    val diffData: Dataset<Row>,
)

data class ChangeSummaryMetaData(
    val addedCols: Set<String> = emptySet(),
    val deletedCols: Set<String> = emptySet(),
    val addedRowCount: Long = 0,
    val deletedRowCount: Long = 0,
)