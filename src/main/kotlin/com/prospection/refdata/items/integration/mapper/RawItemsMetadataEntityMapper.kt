package com.prospection.refdata.items.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.codingsystem.integration.mapper.CodingSystemEntityMapper
import com.prospection.refdata.items.domain.RawItemsMetadata
import com.prospection.refdata.items.integration.RawItemsMetadataEntity
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = [CodingSystemEntityMapper::class, PublishedItemVersionEntityMapper::class]
)
interface RawItemsMetadataEntityMapper : DomainMapper<RawItemsMetadata, RawItemsMetadataEntity>,
    EntityMapper<RawItemsMetadataEntity, RawItemsMetadata>