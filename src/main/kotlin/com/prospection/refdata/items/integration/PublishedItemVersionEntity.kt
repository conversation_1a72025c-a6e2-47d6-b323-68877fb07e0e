package com.prospection.refdata.items.integration

import java.time.LocalDateTime
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Entity(name = "PublishedItemVersion")
@Table(
    name = "published_item_version",
)
class PublishedItemVersionEntity(
    @Id @NotNull var publishedVersion: String,
    @NotNull var publishedBy: String,
    @NotNull var publishedAt: LocalDateTime,
)