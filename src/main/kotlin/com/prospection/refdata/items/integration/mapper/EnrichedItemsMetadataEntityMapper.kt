package com.prospection.refdata.items.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.codingsystem.integration.mapper.CodingSystemEntityMapper
import com.prospection.refdata.items.domain.EnrichedItemsMetadata
import com.prospection.refdata.items.integration.EnrichedItemsMetadataEntity
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE,
    uses = [CodingSystemEntityMapper::class, PublishedItemVersionEntityMapper::class]
)
interface EnrichedItemsMetadataEntityMapper : DomainMapper<EnrichedItemsMetadata, EnrichedItemsMetadataEntity>,
    EntityMapper<EnrichedItemsMetadataEntity, EnrichedItemsMetadata>