package com.prospection.refdata.items.integration

import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface EnrichedItemsMetadataJpaRepository : JpaRepository<EnrichedItemsMetadataEntity, Long>,
    JpaSpecificationExecutor<EnrichedItemsMetadataEntity> {

    fun findByCodingSystemAndPublishedItemVersionNull(codingSystemEntity: CodingSystemEntity): EnrichedItemsMetadataEntity?

    fun findAllByPublishedItemVersionNull(): List<EnrichedItemsMetadataEntity>

}