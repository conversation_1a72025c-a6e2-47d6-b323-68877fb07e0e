package com.prospection.refdata.items.integration

import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull
import org.hibernate.annotations.Type
import java.time.LocalDateTime

@Entity(name = "EnrichedItemsMetadata")
@Table(name = "enriched_items_metadata")
class EnrichedItemsMetadataEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    @NotNull
    @OneToOne(optional = false)
    @JoinColumn(name = "coding_system_id")
    var codingSystem: CodingSystemEntity,

    @ManyToOne
    @JoinColumn(name = "published_item_version")
    var publishedItemVersion: PublishedItemVersionEntity? = null,

    @NotNull
    @Type(ListArrayType::class)
    @Column(name = "source_attributes", columnDefinition = "text[]")
    var sourceAttributes: List<String>,

    @NotNull
    @Type(ListArrayType::class)
    @Column(name = "enriched_attributes", columnDefinition = "text[]")
    var enrichedAttributes: List<String>,

    @NotNull
    var createdBy: String,

    @NotNull
    var createdAt: LocalDateTime,

    @NotNull
    var latestEnrichmentRuleRevisionId: Int,
)