package com.prospection.refdata.items.integration

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface PublishedItemVersionJpaRepository : JpaRepository<PublishedItemVersionEntity, String>,
    JpaSpecificationExecutor<PublishedItemVersionEntity> {
    fun findFirstByOrderByPublishedAtDesc(): PublishedItemVersionEntity?
}