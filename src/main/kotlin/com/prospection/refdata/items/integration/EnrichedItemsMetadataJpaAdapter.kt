package com.prospection.refdata.items.integration

import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.items.domain.EnrichedItemsMetadata
import com.prospection.refdata.items.domain.EnrichedItemsMetadataPort
import com.prospection.refdata.items.domain.PublishedItemVersion
import com.prospection.refdata.items.integration.mapper.EnrichedItemsMetadataEntityMapper
import com.prospection.refdata.items.integration.mapper.PublishedItemVersionEntityMapper
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Component
class EnrichedItemsMetadataJpaAdapter(
    private val enrichedItemsMetadataJpaRepository: EnrichedItemsMetadataJpaRepository,
    private val codingSystemJpaRepository: CodingSystemJpaRepository,
    private val enrichedItemsMetadataEntityMapper: EnrichedItemsMetadataEntityMapper,
    private val publishedItemVersionEntityMapper: PublishedItemVersionEntityMapper,
) : EnrichedItemsMetadataPort {

    @Transactional
    override fun saveDraftMetadata(metadata: EnrichedItemsMetadata) {
        val codingSystemEntity = codingSystemJpaRepository.findByName(metadata.codingSystem.name)
        val enrichedItemsMetadataEntity =
            enrichedItemsMetadataJpaRepository.findByCodingSystemAndPublishedItemVersionNull(
                codingSystemEntity
            )

        enrichedItemsMetadataJpaRepository.save(
            EnrichedItemsMetadataEntity(
                id = enrichedItemsMetadataEntity?.id,
                codingSystem = codingSystemEntity,
                sourceAttributes = metadata.sourceAttributes,
                enrichedAttributes = metadata.enrichedAttributes,
                latestEnrichmentRuleRevisionId = metadata.latestEnrichmentRuleRevisionId,
                createdBy = metadata.createdBy,
                createdAt = metadata.createdAt
            )
        )
    }

    override fun getAllDraftMetadata(): List<EnrichedItemsMetadata> {
        return enrichedItemsMetadataJpaRepository.findAllByPublishedItemVersionNull()
            .map { enrichedItemsMetadataEntityMapper.toDomain(it) }
    }

    override fun getDraftMetadata(codingSystem: CodingSystem): EnrichedItemsMetadata? {
        val codingSystemEntity = codingSystemJpaRepository.findByName(codingSystem.name)

        return enrichedItemsMetadataJpaRepository.findByCodingSystemAndPublishedItemVersionNull(
            codingSystemEntity
        )?.let {
            enrichedItemsMetadataEntityMapper.toDomain(it)
        }
    }

    @Transactional
    override fun savePublishMetadata(
        newPublishedItemVersion: PublishedItemVersion,
        createdBy: String,
        createdAt: LocalDateTime
    ) {
        val enrichedItemMetadataList =
            enrichedItemsMetadataJpaRepository.findAllByPublishedItemVersionNull()

        enrichedItemMetadataList.forEach {
            enrichedItemsMetadataJpaRepository.save(
                EnrichedItemsMetadataEntity(
                    codingSystem = it.codingSystem,
                    sourceAttributes = it.sourceAttributes,
                    enrichedAttributes = it.enrichedAttributes,
                    latestEnrichmentRuleRevisionId = it.latestEnrichmentRuleRevisionId,
                    publishedItemVersion = publishedItemVersionEntityMapper.toEntity(newPublishedItemVersion),
                    createdBy = createdBy,
                    createdAt = createdAt
                )
            )
        }
    }

}