package com.prospection.refdata.items.integration

import com.prospection.refdata.codingsystem.domain.CodingSystem
import com.prospection.refdata.codingsystem.integration.CodingSystemJpaRepository
import com.prospection.refdata.common.consts.SourceAttribute.CODE_COLUMN_NAME
import com.prospection.refdata.items.domain.PublishedItemVersion
import com.prospection.refdata.items.domain.RawItemsMetadata
import com.prospection.refdata.items.domain.RawItemsMetadataPort
import com.prospection.refdata.items.integration.mapper.PublishedItemVersionEntityMapper
import com.prospection.refdata.items.integration.mapper.RawItemsMetadataEntityMapper
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Component
class RawItemsMetadataJpaAdapter(
    private val rawItemsMetadataJpaRepository: RawItemsMetadataJpaRepository,
    private val codingSystemJpaRepository: CodingSystemJpaRepository,
    private val rawItemsMetadataEntityMapper: RawItemsMetadataEntityMapper,
    private val publishedItemVersionEntityMapper: PublishedItemVersionEntityMapper,
) : RawItemsMetadataPort {

    @Transactional
    override fun saveDraftMetadata(
        rawItems: Dataset<Row>?,
        publishedItems: Dataset<Row>?,
        codingSystem: CodingSystem,
        createdBy: String,
        createdAt: LocalDateTime
    ) {
        if (rawItems != null) {
            val totalItem = rawItems.count()
            val newItem = if (publishedItems == null) totalItem else rawItems.join(
                publishedItems,
                rawItems.col(CODE_COLUMN_NAME)
                    .equalTo(publishedItems.col(CODE_COLUMN_NAME)),
                "left_anti"
            ).count()
            val deletedItem = if (publishedItems == null) 0 else publishedItems.join(
                rawItems,
                publishedItems.col(CODE_COLUMN_NAME)
                    .equalTo(rawItems.col(CODE_COLUMN_NAME)),
                "left_anti"
            ).count()

            val codingSystemEntity = codingSystemJpaRepository.findByName(codingSystem.name)
            val rawItemsMetadataEntity =
                rawItemsMetadataJpaRepository.findByCodingSystemAndPublishedItemVersionNull(codingSystemEntity)

            rawItemsMetadataJpaRepository.save(
                RawItemsMetadataEntity(
                    id = rawItemsMetadataEntity?.id,
                    totalItem = totalItem,
                    newItem = newItem,
                    deletedItem = deletedItem,
                    codingSystem = codingSystemEntity,
                    sourceAttributes = rawItems.columns().toList(),
                    createdBy = createdBy,
                    createdAt = createdAt
                )
            )
        }
    }

    override fun getAllDraftMetadata(): List<RawItemsMetadata> {
        return rawItemsMetadataJpaRepository.findAllByPublishedItemVersionNull()
            .map { rawItemsMetadataEntityMapper.toDomain(it) }
    }

    @Transactional
    override fun savePublishMetadata(
        newPublishedItemVersion: PublishedItemVersion,
        createdBy: String,
        createdAt: LocalDateTime
    ) {
        val rawItemsMetadataList = rawItemsMetadataJpaRepository.findAllByPublishedItemVersionNull()

        rawItemsMetadataList.forEach {
            rawItemsMetadataJpaRepository.save(
                RawItemsMetadataEntity(
                    publishedItemVersion = publishedItemVersionEntityMapper.toEntity(newPublishedItemVersion),
                    totalItem = it.totalItem,
                    newItem = it.newItem,
                    deletedItem = it.deletedItem,
                    codingSystem = it.codingSystem,
                    sourceAttributes = it.sourceAttributes,
                    createdBy = createdBy,
                    createdAt = createdAt
                )
            )
        }
    }

}
