package com.prospection.refdata.items.integration.mapper

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.items.domain.PublishedItemVersion
import com.prospection.refdata.items.integration.PublishedItemVersionEntity
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
interface PublishedItemVersionEntityMapper : DomainMapper<PublishedItemVersion, PublishedItemVersionEntity>,
    EntityMapper<PublishedItemVersionEntity, PublishedItemVersion>