package com.prospection.refdata.items.integration

import com.prospection.refdata.codingsystem.integration.CodingSystemEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository

@Repository
interface RawItemsMetadataJpaRepository : JpaRepository<RawItemsMetadataEntity, Long>,
    JpaSpecificationExecutor<RawItemsMetadataEntity> {

    fun findByCodingSystemAndPublishedItemVersionNull(codingSystemEntity: CodingSystemEntity): RawItemsMetadataEntity?

    fun findAllByPublishedItemVersionNull(): List<RawItemsMetadataEntity>

}