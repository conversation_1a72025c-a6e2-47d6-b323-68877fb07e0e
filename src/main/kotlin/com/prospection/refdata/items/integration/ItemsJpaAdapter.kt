package com.prospection.refdata.items.integration

import com.prospection.refdata.items.domain.PublishItemVersionPort
import com.prospection.refdata.items.domain.PublishedItemVersion
import com.prospection.refdata.items.integration.mapper.PublishedItemVersionEntityMapper
import org.springframework.stereotype.Component

@Component
class ItemsJpaAdapter(
    private val publishedItemVersionEntityMapper: PublishedItemVersionEntityMapper,
    private val publishedItemVersionJpaRepository: PublishedItemVersionJpaRepository,
) : PublishItemVersionPort {
    override fun savePublishedVersion(publishedItemVersion: PublishedItemVersion) {
        publishedItemVersionJpaRepository.save(
            publishedItemVersionEntityMapper.toEntity(publishedItemVersion)
        )
    }

    override fun getLatestPublishedItemVersion(): PublishedItemVersion? {
        return publishedItemVersionJpaRepository.findFirstByOrderByPublishedAtDesc()?.let {
            publishedItemVersionEntityMapper.toDomain(it)
        }
    }
}