package com.prospection.refdata.items.application.rest

import com.prospection.refdata.common.application.rest.exceptions.BadRequestException
import com.prospection.refdata.items.application.rest.dto.ItemsCSVUploadDto
import com.prospection.refdata.items.application.rest.dto.PublishedItemVersionDto
import com.prospection.refdata.items.application.rest.dto.RawItemsMetaDataDto
import com.prospection.refdata.items.application.rest.mapper.PublishedItemsVersionDtoMapper
import com.prospection.refdata.items.application.rest.mapper.RawItemsMetaDataDtoMapper
import com.prospection.refdata.items.domain.ItemsImport
import com.prospection.refdata.items.domain.ItemsService
import com.prospection.refdata.job.application.rest.dto.JobDto
import com.prospection.refdata.job.application.rest.dto.JobDtoMapper
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import jakarta.validation.Valid

@RestController
@RequestMapping("/api/ref-data-v2/")
@Tag(name = "reference data, items", description = "APIs for items related operations")
class ItemsController(
    @Autowired private val itemsService: ItemsService,
    @Autowired private val rawItemsMetaDataDtoMapper: RawItemsMetaDataDtoMapper,
    @Autowired private val jobDtoMapper: JobDtoMapper,
    @Autowired private val publishedItemsVersionDtoMapper: PublishedItemsVersionDtoMapper,
) {

    @Operation(summary = "Import items with source attribute")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PutMapping("classifications/{classification}/items/import")
    fun import(@PathVariable classification: String, @Valid @RequestBody upload: ItemsCSVUploadDto) {
        if (upload.currentChunk > upload.totalChunk) {
            throw BadRequestException("Current chunk can not be greater than total chunk")
        }
        val importDto = ItemsImport(
            csv = upload.csv,
            currentChunk = upload.currentChunk,
            totalChunk = upload.totalChunk,
            classification = classification,
        )
        return itemsService.import(importDto)
    }

    @Operation(summary = "Process items after importing")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("classifications/{classification}/items/process-after-import")
    fun processAfterImport(@PathVariable classification: String): JobDto {
        return jobDtoMapper.toDto(itemsService.queueProcessAfterImport(classification))
    }

    @Operation(summary = "Get all items metadata")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("items/metadata")
    fun getRawItemsMetadata(): List<RawItemsMetaDataDto> {
        return itemsService.getRawItemsMetadata().map {
            rawItemsMetaDataDtoMapper.toDto(it)
        }
    }

    @Operation(summary = "Get change summaries download link")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("items/change-summaries")
    fun getChangeSummaries(): JobDto {
        return jobDtoMapper.toDto(itemsService.queueDownloadingChangeSummary())
    }

    @Operation(summary = "Get all enriched items download link")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("items/enriched/{classification}")
    fun getEnrichedItemsDownloadUrl(@PathVariable classification: String): JobDto {
        return jobDtoMapper.toDto(itemsService.queueDownloadingEnrichedItems(classification))
    }

    @Operation(summary = "Publish items and enrichment rules")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("items/publish")
    fun publishItemsAndRules(): JobDto {
        return jobDtoMapper.toDto(itemsService.queuePublishItemsAndRules())
    }

    @Operation(summary = "Get the latest published version of items")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("items/version/latest")
    fun getLatestPublishedItemsVersion(): PublishedItemVersionDto {
        return publishedItemsVersionDtoMapper.toDto(itemsService.getLatestPublishedItemsVersion())
    }

    @Operation(summary = "Generate data update report")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @PostMapping("items/data-update-report/{codingSystem}")
    fun generateDraftDataUpdateReport(@PathVariable codingSystem: String): JobDto {
        return jobDtoMapper.toDto(itemsService.queueGenerateDataUpdateReport(codingSystem))
    }
}
