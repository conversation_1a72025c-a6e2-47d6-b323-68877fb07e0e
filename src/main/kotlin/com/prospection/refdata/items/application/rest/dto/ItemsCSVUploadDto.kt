package com.prospection.refdata.items.application.rest.dto

import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull

data class ItemsCSVUploadDto(
        @field:NotNull @field:NotBlank val csv: String,
        @field:NotNull @field:Min(value = 1) val currentChunk: Int,
        @field:NotNull @field:Min(value = 1) val totalChunk: Int,
)