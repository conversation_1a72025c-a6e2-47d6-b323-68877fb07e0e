package com.prospection.refdata.items.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.refdata.items.application.rest.dto.RawItemsMetaDataDto
import com.prospection.refdata.items.domain.RawItemsMetadata
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface RawItemsMetaDataDtoMapper : DtoMapper<RawItemsMetaDataDto, RawItemsMetadata> {

    @Mappings(
        value = [
            Mapping(source = "codingSystem.name", target = "codingSystem"),
            Mapping(source = "publishedItemVersion.publishedVersion", target = "publishedItemVersion"),
            Mapping(source = "codingSystem.mappedClassifications", target = "mappedClassifications"),
        ]
    )
    override fun toDto(other: RawItemsMetadata): RawItemsMetaDataDto

}