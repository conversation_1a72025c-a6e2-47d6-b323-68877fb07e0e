package com.prospection.refdata.items.application.rest.mapper

import com.prospection.controller.DtoMapper
import com.prospection.refdata.items.application.rest.dto.PublishedItemVersionDto
import com.prospection.refdata.items.domain.PublishedItemVersion
import org.mapstruct.Mapper
import java.time.ZoneOffset

@Mapper(
    componentModel = "spring",
)
abstract class PublishedItemsVersionDtoMapper : DtoMapper<PublishedItemVersionDto, PublishedItemVersion> {
    override fun toDto(other: PublishedItemVersion): PublishedItemVersionDto {
        return PublishedItemVersionDto(
            id = other.publishedVersion,
            publishedBy = other.publishedBy,
            publishedDate = other.publishedAt.toInstant(ZoneOffset.UTC)
        )
    }
}