package com.prospection.refdata.items.application.rest.dto

import java.time.LocalDateTime

class RawItemsMetaDataDto(
    var publishedItemVersion: String? = null,
    var totalItem: Long,
    var newItem: Long,
    var deletedItem: Long,
    var codingSystem: String,
    var sourceAttributes: List<String>,
    var mappedClassifications: List<MappedClassificationDto> = emptyList(),
    var createdAt: LocalDateTime,
)
