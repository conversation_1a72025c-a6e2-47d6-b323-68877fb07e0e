package com.prospection.refdata.common.application.rest

import com.prospection.refdata.common.application.rest.exceptions.BadRequestException
import com.prospection.refdata.common.domain.exceptions.ConcurrentUpdateException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseStatus

@ControllerAdvice
internal class ExceptionResponseCodeAdvice {

    @ResponseStatus(HttpStatus.CONFLICT) // 409
    @ExceptionHandler(ConcurrentUpdateException::class)
    fun handleConflict() {
        // no additional processing required
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST) // 400
    @ExceptionHandler(BadRequestException::class)
    fun handleBadRequest() {
        // no additional processing required
    }
}
