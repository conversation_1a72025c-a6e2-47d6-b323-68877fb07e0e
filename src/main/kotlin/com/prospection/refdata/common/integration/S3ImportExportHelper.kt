package com.prospection.refdata.common.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.common.domain.ImportExportHelper
import com.prospection.refdata.common.integration.S3PathUtils.createDestinationKey
import com.prospection.refdata.config.ApplicationProperties
import org.apache.spark.sql.SparkSession
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CopyObjectRequest
import software.amazon.awssdk.services.s3.model.Delete
import software.amazon.awssdk.services.s3.model.DeleteObjectsRequest
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import software.amazon.awssdk.services.s3.model.HeadObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.ObjectCannedACL
import software.amazon.awssdk.services.s3.model.ObjectIdentifier
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.model.S3Exception
import software.amazon.awssdk.services.s3.model.S3Object
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@Component
class S3ImportExportHelper(
    @Autowired private val applicationProperties: ApplicationProperties,
    @Autowired private val s3Client: S3Client,
    @Autowired val spark: SparkSession,
    @Autowired val objectMapper: ObjectMapper
) : ImportExportHelper<ResponseInputStream<GetObjectResponse>> {

    override fun <T> readFile(
        path: String,
        mapper: (s: ResponseInputStream<GetObjectResponse>) -> T
    ): T? {
        return if (objectExists(path)) {
            s3Client.getObject(
                GetObjectRequest.builder().bucket(getBucketName()).key(path).build()
            ).use { stream -> mapper(stream) }
        } else {
            null
        }
    }

    private fun getOutputStream(outputPath: String): S3OutputStream {
        return S3OutputStream(s3Client, getBucketName(), outputPath)
    }

    private fun getObject(path: String): S3Object? {
        return if (objectExists(path)) {
            S3Object.builder().key(path).build()
        } else null
    }

    private fun objectExists(path: String): Boolean {
        return try {
            s3Client.headObject(HeadObjectRequest.builder().bucket(getBucketName()).key(path).build())
            true
        } catch (e: S3Exception) {
            if (e.statusCode() == 404) false else throw e
        }
    }

    override fun findFirst(path: String, endsWith: String): String? {
        val response =
            s3Client.listObjectsV2(ListObjectsV2Request.builder().bucket(getBucketName()).prefix(path).build())
        return response.contents().map { it.key() }.find { it.endsWith(endsWith) }
    }

    override fun findAllPaths(path: String, endsWith: String): List<String> {
        val response =
            s3Client.listObjectsV2(ListObjectsV2Request.builder().bucket(getBucketName()).prefix(path).build())
        return response.contents().map { it.key() }.filter { it.endsWith(endsWith) }
    }

    override fun copyAll(sourcePath: String, destPath: String) {
        var continuationToken: String? = null
        do {
            val requestBuilder = ListObjectsV2Request.builder()
                .bucket(getBucketName())
                .prefix("${sourcePath}/")
            continuationToken?.let { requestBuilder.continuationToken(it) }
            val response = s3Client.listObjectsV2(requestBuilder.build())
            response.contents().forEach {
                s3Client.copyObject(
                    CopyObjectRequest.builder()
                        .sourceBucket(getBucketName())
                        .sourceKey(it.key())
                        .destinationBucket(getBucketName())
                        .destinationKey(createDestinationKey(it.key(), sourcePath, destPath))
                        .acl(ObjectCannedACL.BUCKET_OWNER_FULL_CONTROL)
                        .build()
                )
            }
            continuationToken = response.nextContinuationToken()
        } while (continuationToken != null)
    }

    override fun deleteAll(path: String) {
        val response =
            s3Client.listObjectsV2(ListObjectsV2Request.builder().bucket(getBucketName()).prefix(path).build())
        if (response.contents().isNotEmpty()) {
            val deleteRequest = DeleteObjectsRequest.builder()
                .bucket(getBucketName())
                .delete(
                    Delete.builder()
                        .objects(response.contents().map { ObjectIdentifier.builder().key(it.key()).build() }).build()
                )
                .build()
            s3Client.deleteObjects(deleteRequest)
        }
    }

    override fun writeString(path: String, content: String) {
        s3Client.putObject(
            PutObjectRequest.builder().bucket(getBucketName()).key(path).build(),
            RequestBody.fromString(content)
        )
    }

    override fun <T> writeJson(path: String, obj: T) {
        s3Client.putObject(
            PutObjectRequest.builder().bucket(applicationProperties.s3Bucket).key(path).build(),
            RequestBody.fromString(objectMapper.writeValueAsString(obj))
        )
    }

    override fun writeZip(
        zipPath: String,
        pathToEntryMap: Map<String, String>,
        metaData: ByteArray?
    ): String {
        val buffer = ByteArray(1024)
        ZipOutputStream(getOutputStream(zipPath)).use { zipOutputStream ->
            pathToEntryMap.forEach {
                val s3Path = it.key
                val fileNameInZipFile = it.value

                zipOutputStream.putNextEntry(ZipEntry(fileNameInZipFile))
                var length: Int
                // Fetch the object's content from S3 using s3Client
                s3Client.getObject(GetObjectRequest.builder().bucket(getBucketName()).key(s3Path).build())
                    .use { s3InputStream ->
                        while (s3InputStream.read(buffer).also { bufferLength -> length = bufferLength } > 0) {
                            zipOutputStream.write(buffer, 0, length)
                        }
                    }
                zipOutputStream.closeEntry()
            }
            metaData?.let {
                zipOutputStream.putNextEntry(ZipEntry("metadata.json"))
                zipOutputStream.write(it)
                zipOutputStream.closeEntry()
            }
        }
        return zipPath
    }

    override fun getBucketName(): String {
        return applicationProperties.s3Bucket
    }

    override fun getTwoLatestKeys(path: String): List<String> {
        val delimiter = "/"
        val prefix = if (path.endsWith(delimiter)) path else "$path$delimiter"
        val response = s3Client.listObjectsV2(
            ListObjectsV2Request.builder().bucket(getBucketName()).prefix(prefix).delimiter(delimiter).build()
        )
        return response.commonPrefixes().takeLast(2).map { it.prefix() }
    }
}
