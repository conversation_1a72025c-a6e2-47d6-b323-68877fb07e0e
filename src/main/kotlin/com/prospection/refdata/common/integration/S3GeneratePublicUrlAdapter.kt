package com.prospection.refdata.common.integration

import com.prospection.refdata.common.domain.GeneratePublicUrlPort
import com.prospection.refdata.config.ApplicationProperties
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.Date
import java.time.Duration
import java.time.Instant

@Component
class S3GeneratePublicUrlAdapter(
    @Autowired private val applicationProperties: ApplicationProperties,
    @Autowired private val s3Presigner: S3Presigner
) : GeneratePublicUrlPort {
    override fun generatePublicUrl(path: String, expiration: Date): String {
        val req = GetObjectRequest.builder()
            .bucket(applicationProperties.s3Bucket)
            .key(path)
            .build()
        val presignReq = GetObjectPresignRequest.builder()
            .signatureDuration(Duration.between(Instant.now(), expiration.toInstant()))
            .getObjectRequest(req)
            .build()
        return s3Presigner.presignGetObject(presignReq).url().toString()
    }

    override fun generatePublicUrlExpirationInOneHours(path: String, fromTime: LocalDateTime): String {
        val expiration = Date.from(fromTime.plusHours(1).atZone(ZoneId.systemDefault()).toInstant())
        return generatePublicUrl(path, expiration)
    }
}