package com.prospection.refdata.common.integration

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.TimeUnit
import kotlin.reflect.full.companionObject

object LogSupport {

    /**
     * Helper function to logger using property delegates
     * @see https://stackoverflow.com/questions/34416869/idiomatic-way-of-logging-in-kotlin
     */
    fun <R : Any> R.lazyLogger(): Lazy<Logger> {
        return lazy { LoggerFactory.getLogger(unwrapCompanionClass(this.javaClass).name) }
    }

    // unwrap companion class to enclosing class given a Java Class
    fun <T : Any> unwrapCompanionClass(ofClass: Class<T>): Class<*> {
        return ofClass.enclosingClass?.takeIf {
            ofClass.enclosingClass.kotlin.companionObject?.java == ofClass
        } ?: ofClass
    }


    /**
     * Utility function to measure execution time of a code block
     */
    inline fun <T: Any?> measureExecution(logger: Logger, logMessage: String, function: () -> T): T {

        val startTime = System.nanoTime()
        return function.invoke().also {
            val elapsedTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - startTime)
            val message = "$logMessage took ${elapsedTime}ms"
            when{
                elapsedTime > 10000 -> logger.error(message)
                elapsedTime > 5000 -> logger.warn(message)
                elapsedTime > 1000 -> logger.info(message)
                else -> logger.debug(message)
            }
        }
    }
}