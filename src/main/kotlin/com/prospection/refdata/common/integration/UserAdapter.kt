package com.prospection.refdata.common.integration

import com.prospection.refdata.common.domain.UserPort
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component

@Component
class UserAdapter: UserPort {
    override fun getCurrentUserId(): String {
        return SecurityContextHolder.getContext().authentication?.principal?.toString()
            ?: throw RuntimeException("User principal not found")
    }
}