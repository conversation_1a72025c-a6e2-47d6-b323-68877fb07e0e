package com.prospection.refdata.common.integration

import com.prospection.refdata.codingsystem.domain.CodingSystemPort
import com.prospection.refdata.common.domain.S3ItemsExporterForExternalToolPort
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.config.S3ItemsForExternalToolProperties
import org.apache.spark.sql.Dataset
import org.apache.spark.sql.Row
import org.apache.spark.sql.SaveMode
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class S3ItemsExporterForExternalToolAdapter(
    @Autowired private val codingSystemPort: CodingSystemPort,
    @Autowired private val s3ItemsForExternalToolProperties: S3ItemsForExternalToolProperties
) : S3ItemsExporterForExternalToolPort {

    companion object {
        private const val COMMA = ","
    }

    private val logger by lazyLogger()

    override fun storeExternal(codingSystemName: String, rawItems: Dataset<Row>) {
        getStoreExternalPaths(codingSystemName).forEach {
            rawItems.write().mode(SaveMode.Overwrite).parquet(it)
        }
    }

    private fun getStoreExternalPaths(codingSystemName: String): Set<String> {
        val codingSystem = codingSystemPort.findByName(codingSystemName)

        val externalPaths = if (codingSystem.country != null) {
            codingSystem.country!!.split(COMMA).map {
                "${s3ItemsForExternalToolProperties.countryPaths[it.lowercase()]}/${codingSystemName}"
            }.toSet()
        } else {
            emptySet()
        }

        logger.info("Store external path $externalPaths")
        return externalPaths
    }
}
