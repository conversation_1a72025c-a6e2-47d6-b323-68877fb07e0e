package com.prospection.refdata.common.integration

import com.prospection.refdata.common.domain.GluePort
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.config.ApplicationProperties
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.glue.GlueClient
import software.amazon.awssdk.services.glue.model.CrawlerRunningException
import software.amazon.awssdk.services.glue.model.StartCrawlerRequest


@Component
class GlueAdapter(
    @Autowired private val applicationProperties: ApplicationProperties,
    @Autowired private val glueClient: GlueClient,
) : GluePort {

    private val logger by lazyLogger()

    override fun startCrawler() {
        val crawlerName = "${applicationProperties.s3Bucket}-crawler"

        val crawlerRequest = StartCrawlerRequest.builder()
            .name(crawlerName)
            .build()

        logger.info("Start crawler $crawlerName")
        try {
            glueClient.startCrawler(crawlerRequest)
        } catch (e: CrawlerRunningException) {
            logger.debug("Unable to start Glue Crawler. It is already running.")
        }
    }
}
