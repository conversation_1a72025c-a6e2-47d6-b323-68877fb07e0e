package com.prospection.refdata.common.integration.datasetrow

import com.prospection.refdata.subscription.domain.CompanySubscription

data class CompanySubscriptionRow(
    var id: String?,
    var name: String,
    var recordId: String
) {
    companion object {
        fun toRow(companySubscription: CompanySubscription): CompanySubscriptionRow {
            return CompanySubscriptionRow(
                id = companySubscription.id,
                name = companySubscription.name,
                recordId = companySubscription.recordId
            )
        }
    }
}