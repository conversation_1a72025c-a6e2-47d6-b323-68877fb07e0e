package com.prospection.refdata.common.integration

import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CompleteMultipartUploadRequest
import software.amazon.awssdk.services.s3.model.CompletedMultipartUpload
import software.amazon.awssdk.services.s3.model.CompletedPart
import software.amazon.awssdk.services.s3.model.CreateMultipartUploadRequest
import software.amazon.awssdk.services.s3.model.ObjectCannedACL
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.model.UploadPartRequest
import java.io.OutputStream

/**
 * This class acts as a customized output stream for S3 using AWS SDK v2
 */
open class S3OutputStream(
    private val s3Client: S3Client,
    private val bucket: String,
    private val path: String
) : OutputStream() {

    companion object {
        protected const val BUFFER_SIZE = 10000000 // 10MB
    }

    private var buf: ByteArray = ByteArray(BUFFER_SIZE)
    private var position: Int = 0
    private var uploadId: String? = null
    private val etags: MutableList<CompletedPart> = ArrayList()
    private var open: Boolean = true
    private var partNumber: Int = 1

    override fun write(b: ByteArray) {
        write(b, 0, b.size)
    }

    override fun write(byteArray: ByteArray, o: Int, l: Int) {
        assertOpen()
        var ofs = o
        var len = l
        var size: Int
        while (len > (buf.size - position).also { size = it }) {
            System.arraycopy(byteArray, ofs, buf, position, size)
            position += size
            flushBufferAndRewind()
            ofs += size
            len -= size
        }
        System.arraycopy(byteArray, ofs, buf, position, len)
        position += len
    }

    @Synchronized
    override fun flush() {
        assertOpen()
    }

    protected fun flushBufferAndRewind() {
        if (uploadId == null) {
            val req = CreateMultipartUploadRequest.builder()
                .bucket(bucket)
                .key(path)
                .acl(ObjectCannedACL.BUCKET_OWNER_FULL_CONTROL)
                .build()
            val resp = s3Client.createMultipartUpload(req)
            uploadId = resp.uploadId()
        }
        uploadPart()
        position = 0
    }

    protected fun uploadPart() {
        val uploadRequest = UploadPartRequest.builder()
            .bucket(bucket)
            .key(path)
            .uploadId(uploadId)
            .partNumber(partNumber)
            .contentLength(position.toLong())
            .build()
        val uploadResp = s3Client.uploadPart(uploadRequest, RequestBody.fromBytes(buf.copyOf(position)))
        etags.add(
            CompletedPart.builder()
                .partNumber(partNumber)
                .eTag(uploadResp.eTag())
                .build()
        )
        partNumber++
    }

    override fun close() {
        if (open) {
            open = false
            if (uploadId != null) {
                if (position > 0) {
                    uploadPart()
                }
                val completeRequest = CompleteMultipartUploadRequest.builder()
                    .bucket(bucket)
                    .key(path)
                    .uploadId(uploadId)
                    .multipartUpload(
                        CompletedMultipartUpload.builder()
                            .parts(etags)
                            .build()
                    )
                    .build()
                s3Client.completeMultipartUpload(completeRequest)
            } else {
                // Single PUT if not using multipart
                s3Client.putObject(
                    PutObjectRequest.builder()
                        .bucket(bucket)
                        .key(path)
                        .acl(ObjectCannedACL.BUCKET_OWNER_FULL_CONTROL)
                        .build(),
                    RequestBody.fromBytes(buf.copyOf(position))
                )
            }
        }
    }

    override fun write(b: Int) {
        assertOpen()
        if (position >= buf.size) {
            flushBufferAndRewind()
        }
        buf[position++] = b.toByte()
    }

    private fun assertOpen() {
        check(open) { "Closed" }
    }
}