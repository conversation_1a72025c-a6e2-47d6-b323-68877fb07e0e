package com.prospection.refdata.common.integration

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.lambda.LambdaClient
import software.amazon.awssdk.services.lambda.model.InvokeRequest
import software.amazon.awssdk.services.lambda.model.InvokeResponse

@Component
@ConditionalOnProperty(value = ["application.amazon.lambda.itemGroupsSpark"])
class AwsLambdaClient(private val lambdaClient: LambdaClient, private val objectMapper: ObjectMapper) {

    companion object {
        private val logger by lazyLogger()
    }

    fun <R, P> invoke(functionName: String, payload: P, typeRef: TypeReference<R> = object : TypeReference<R>() {}): R {
        val payloadString = objectMapper.writeValueAsString(payload)

        val invokeRequest = InvokeRequest.builder()
            .functionName(functionName)
            .payload(SdkBytes.fromUtf8String(payloadString))
            .build()

        val invokeResponse = lambdaClient.invoke(invokeRequest)

        throwIfError(invokeResponse, invokeRequest)

        val responsePayload = invokeResponse.payload().asUtf8String()
        return objectMapper.readValue(responsePayload, typeRef)
    }

    private fun throwIfError(
        invokeResponse: InvokeResponse,
        invokeRequest: InvokeRequest
    ) {
        if (invokeResponse.statusCode() != 200) {
            throw RuntimeException("Failed to invoke lambda function ${invokeRequest.functionName()}: statusCode = ${invokeResponse.statusCode()}")
        } else if (invokeResponse.functionError() != null) {
            val errorMessage = extractError(invokeResponse)
            throw RuntimeException("Error during executing lambda function ${invokeRequest.functionName()}: $errorMessage")
        }
    }

    private fun extractError(invokeResponse: InvokeResponse): String = try {
        val responsePayload = invokeResponse.payload().asUtf8String()
        objectMapper.readTree(responsePayload)?.get("errorMessage")?.asText()
    } catch (e: Exception) {
        logger.error("Failed to parse error message from lambda response", e)
        null
    } ?: invokeResponse.functionError()
}