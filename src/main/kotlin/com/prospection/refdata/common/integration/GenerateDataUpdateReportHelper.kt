package com.prospection.refdata.common.integration

import com.prospection.refdata.common.consts.CodingSystems
import com.prospection.refdata.common.integration.datasetrow.CompanySubscriptionRow
import com.prospection.refdata.common.integration.datasetrow.ItemGroupToTopicAndSubscriptionRow
import com.prospection.refdata.subscription.domain.CompanySubscriptionPort
import com.prospection.refdata.subscription.domain.CompanySubscription
import com.prospection.refdata.topic.domain.TopicPort
import com.prospection.refdata.topic.domain.Topic
import com.prospection.refdata.topic.domain.Workflow
import com.prospection.refdata.itemgroups.integration.ItemGroupExportColumns
import com.prospection.refdata.items.ScalaSparkItemsFunctions
import org.apache.spark.sql.Column
import org.apache.spark.sql.Dataset
import org.apache.spark.api.java.function.MapFunction
import org.apache.spark.sql.Encoders
import org.apache.spark.sql.Row
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions
import org.apache.spark.sql.functions.array_distinct
import org.apache.spark.sql.functions.collect_list
import org.jetbrains.kotlinx.spark.api.eq
import org.jetbrains.kotlinx.spark.api.map
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class GenerateDataUpdateReportHelper(
    @Autowired private val topicPort: TopicPort,
    @Autowired private val companySubscriptionPort: CompanySubscriptionPort,
    @Autowired private val spark: SparkSession,
) {
    companion object {
        const val USED_CONDITIONS_COLUMN = "Inc in Subbed Brands"
        const val SUBSCRIBED_TOPIC_IDS = "Subscribed Topic IDs"

        val ADDITIONAL_AUDIT_COLUMNS =
            listOf(
                "Updated Enrichment (Y/N)",
                "New Enrichment (Y/N)",
                "Updated Item Group (Y/N)",
                "New Item Group (Y/N)",
                "Updated workflow (Y/N)",
                "Updated in DaysSupply Master (Y/N)",
                "Implemented By",
                "Auditor's (Notes/Rationale)",
                "Checked by",
                "QAer's (Notes/Rationale)"
            )

        val UNNECESSARY_COLUMNS_FOR_PBS_DRUG =
            setOf("source_indication_id", "source_restriction_flag", "status", "coding_system")
    }

    fun enrichItemGroupsByTopicsAndSubscriptions(ds: Dataset<Row>): Dataset<ItemGroupToTopicAndSubscriptionRow> {
        val itemGroupDs = ScalaSparkItemsFunctions.explodeDs(
            ds.select(ItemGroupExportColumns.ITEM_GROUP_CODE.columnName),
            ItemGroupExportColumns.ITEM_GROUP_CODE.columnName
        ).distinct()

        val itemGroups = itemGroupDs.map(GetStringFunction(), Encoders.STRING()).collectAsList()

        if (itemGroups.isEmpty()) {
            return spark.emptyDataset(Encoders.bean(ItemGroupToTopicAndSubscriptionRow::class.java))
        }

        val topics = topicPort.listTopicsByItemGroups(itemGroups)

        val itemGroupAndTopicDs = enrichByTopics(itemGroupDs, topics)

        val subRecordIds = topics.mapNotNull { it.subscriptionId }.distinct()

        val subscriptions = companySubscriptionPort.listSubscriptionsByRecordIds(subRecordIds)

        return enrichBySubscriptions(itemGroupAndTopicDs, subscriptions)
            .`as`(Encoders.bean(ItemGroupToTopicAndSubscriptionRow::class.java))
    }

    fun enrichItemsWithConditions(
        updateItemsWithItemGroups: Dataset<Row>,
        itemGroupToTopicAndSubscriptionRowDs: Dataset<ItemGroupToTopicAndSubscriptionRow>,
        codingSystem: String
    ): Dataset<Row> {
        val filteredItemsWithItemGroups = if (codingSystem == CodingSystems.PBS_DRUG) {
            updateItemsWithItemGroups.drop(*UNNECESSARY_COLUMNS_FOR_PBS_DRUG.toTypedArray())
        } else {
            updateItemsWithItemGroups
        }

        val updateItemsWithExplodedItemGroups =
            ScalaSparkItemsFunctions.explodeDs(
                filteredItemsWithItemGroups,
                ItemGroupToTopicAndSubscriptionRow::item_group.name
            )

        val itemGroupsWithTopicsAndConditions = itemGroupToTopicAndSubscriptionRowDs
            .filter(functions.col(ItemGroupToTopicAndSubscriptionRow::subscription_record_id.name).isNotNull)
            .groupBy(ItemGroupToTopicAndSubscriptionRow::item_group.name)
            .agg(
                array_distinct(collect_list(ItemGroupToTopicAndSubscriptionRow::condition.name)).alias("conditions"),
                array_distinct(collect_list(ItemGroupToTopicAndSubscriptionRow::topic_id.name)).alias("topic_ids")
            )

        val itemsWithItemGroupConditions =
            ScalaSparkItemsFunctions.leftJoinDs(
                updateItemsWithExplodedItemGroups,
                itemGroupsWithTopicsAndConditions,
                ItemGroupToTopicAndSubscriptionRow::item_group.name
            )
                .select(
                    updateItemsWithExplodedItemGroups.col("source_code"),
                    itemGroupsWithTopicsAndConditions.col("conditions"),
                    itemGroupsWithTopicsAndConditions.col("topic_ids")
                )

        val itemsWithItemGroupConditionsGroupByCode = itemsWithItemGroupConditions
            .groupBy("source_code")
            .agg(
                array_distinct(functions.flatten(collect_list("conditions"))).alias(USED_CONDITIONS_COLUMN),
                array_distinct(functions.flatten(collect_list("topic_ids"))).alias(SUBSCRIBED_TOPIC_IDS)
            )

        return ScalaSparkItemsFunctions.leftJoinDs(
            filteredItemsWithItemGroups,
            itemsWithItemGroupConditionsGroupByCode,
            "source_code"
        )
            .select(
                *filteredItemsWithItemGroups.columns().map { functions.col(it) }.toTypedArray<Column>(),
                itemsWithItemGroupConditionsGroupByCode.col(USED_CONDITIONS_COLUMN),
                itemsWithItemGroupConditionsGroupByCode.col(SUBSCRIBED_TOPIC_IDS),
                *ADDITIONAL_AUDIT_COLUMNS.map { functions.lit("").`as`(it) }.toTypedArray<Column>()
            )
    }

    private fun enrichByTopics(itemGroupDs: Dataset<Row>, topics: List<Topic>): Dataset<Row> {
        // FIXME - Using the domain model Topic to generate spark output should be avoided
        val topicDs = ScalaSparkItemsFunctions
            .explodeDs(
                spark.createDataset(topics, Encoders.bean(Topic::class.java)).toDF(),
                Topic::relatedWorkflows.name
            )
            .select(
                Column("${Topic::relatedWorkflows.name}.${Workflow::relatedItemGroups.name}")
                    .`as`(ItemGroupToTopicAndSubscriptionRow::item_group.name),
                Column(Topic::id.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::topic_id.name),
                Column(Topic::name.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::topic_name.name),
                Column("${Topic::relatedWorkflows.name}.${Workflow::status.name}")
                    .`as`(ItemGroupToTopicAndSubscriptionRow::workflow_status.name),
                Column(Topic::conditionName.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::condition.name),
                Column(Topic::therapyAreaName.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::therapy_area.name),
                Column(Topic::subscriptionId.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::subscription_record_id.name)
            )
        val topicWithItemGroupDs =
            ScalaSparkItemsFunctions.explodeDs(topicDs, ItemGroupToTopicAndSubscriptionRow::item_group.name)
        return ScalaSparkItemsFunctions.leftJoinDs(
            itemGroupDs,
            topicWithItemGroupDs,
            ItemGroupToTopicAndSubscriptionRow::item_group.name
        )
    }

    private fun enrichBySubscriptions(ds: Dataset<Row>, subscriptions: List<CompanySubscription>): Dataset<Row> {
        val companySubscriptionRows = subscriptions.map { CompanySubscriptionRow.toRow(it) }

        val subscriptionsDs = spark
            .createDataFrame(companySubscriptionRows, CompanySubscriptionRow::class.java)
            .select(
                Column(CompanySubscriptionRow::id.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::subscription_id.name),
                Column(CompanySubscriptionRow::name.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::subscription_name.name),
                Column(CompanySubscriptionRow::recordId.name)
                    .`as`(ItemGroupToTopicAndSubscriptionRow::subscription_record_id.name)
            )
        val joinCondition = ds.col(ItemGroupToTopicAndSubscriptionRow::subscription_record_id.name)
            .eq(subscriptionsDs.col(ItemGroupToTopicAndSubscriptionRow::subscription_record_id.name))
        return ds.join(subscriptionsDs, joinCondition, "left")
            .drop(subscriptionsDs.col(ItemGroupToTopicAndSubscriptionRow::subscription_record_id.name))
    }
}

/**
 * Serializable function to extract string from Row at index 0
 */
class GetStringFunction : MapFunction<Row, String> {
    override fun call(row: Row): String = row.getString(0)
}
