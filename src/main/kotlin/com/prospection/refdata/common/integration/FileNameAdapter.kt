package com.prospection.refdata.common.integration

import com.prospection.refdata.common.domain.FileNamePort
import org.apache.commons.io.FilenameUtils
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

@Component
class FileNameAdapter : FileNamePort {
    override fun createFileNameWithDateTime(filename: String, dateTime: LocalDateTime): String {

        val fileNamePrefix = FilenameUtils.normalizeNoEndSeparator(filename) ?: ""
        val fileNameSuffix = dateTime.atZone(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HHmm"))

        return "${fileNamePrefix}_${fileNameSuffix}"
    }
}