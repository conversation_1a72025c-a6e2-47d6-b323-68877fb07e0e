package com.prospection.refdata.common.domain

interface ImportExportHelper<Obj> {
    fun getBucketName(): String
    fun <T> readFile(path: String, mapper: (s: Obj) -> T): T?

    fun findFirst(path: String, endsWith: String): String?
    fun findAllPaths(path: String, endsWith: String): List<String>

    fun copyAll(sourcePath: String, destPath: String)
    fun deleteAll(path: String)

    fun writeString(path: String, content: String)
    fun <T> writeJson(path: String, obj: T)
    fun writeZip(zipPath: String, pathToEntryMap: Map<String, String>, metaData: ByteArray?): String

    fun getTwoLatestKeys(path: String): List<String>
}