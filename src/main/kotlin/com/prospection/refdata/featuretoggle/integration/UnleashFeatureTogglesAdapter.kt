package com.prospection.refdata.featuretoggle.integration

import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.featuretoggle.domain.FeatureTogglePort
import io.getunleash.Unleash
import io.getunleash.UnleashContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component

@Component
class UnleashFeatureTogglesAdapter(private val unleash: Unleash): FeatureTogglePort {

    companion object {
        private val logger by lazyLogger()
    }

    override fun isEnable(userId: String, featureName: String): Boolean {
        val context = UnleashContext.builder()
            .userId(userId)
            // can't set company here, it used very old pd-boot-starter-web-security which doesn't map claim to user
            .build()
        try {
            return unleash.isEnabled(featureName, context)
        } catch (e: Exception) {
            logger.error("Failed to fetch feature toggles. Defaulting to empty feature toggles.", e)
            return false
        }
    }

    override fun isEnable(featureName: String): Boolean {
        return isEnable(SecurityContextHolder.getContext().authentication?.name ?: "Anonymous", featureName)
    }

}