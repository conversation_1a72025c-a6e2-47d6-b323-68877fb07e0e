package com.prospection.refdata.security

import com.prospection.web.security.AuthTokenFilter
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.authentication.AuthenticationManager
import org.springframework.security.authorization.AuthorizationDecision
import org.springframework.security.authorization.AuthorizationManager
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.builders.WebSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.Authentication
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.access.intercept.RequestAuthorizationContext
import java.util.function.Supplier

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
class SecurityConfig(
    @Value("\${application.security.secret}")
    private val authTokenSecret: String,
    private val roleValidation: RoleValidation
) {
    companion object {
        val APIS_ALLOWED_FOR_ANONYMOUS_USERS: Array<String> = arrayOf(
            "/api/ref-data-v2/coding-systems/check-etl-job-possibility"
        )

        val APIS_ALLOWED_FOR_USERS_AND_ADMINS: Array<String> = arrayOf(
            "/api/ref-data-v2/versions/*/item-groups"
        )
    }

    @Bean
    fun authenticationManager(authenticationConfiguration: AuthenticationConfiguration): AuthenticationManager =
        authenticationConfiguration.authenticationManager

    @Bean
    fun adminAuthorizationManager(): AuthorizationManager<RequestAuthorizationContext> =
        AuthorizationManager { authenticationSupplier: Supplier<Authentication>?, _: RequestAuthorizationContext? ->
            val authentication = authenticationSupplier?.get()
            AuthorizationDecision(authentication != null && roleValidation.isAdmin(authentication))
        }

    @Bean
    fun securityFilterChain(
        http: HttpSecurity,
        authenticationManager: AuthenticationManager,
        adminAuthorizationManager: AuthorizationManager<RequestAuthorizationContext>
    ): SecurityFilterChain {
        http
            .csrf { it.disable() }
            .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
            .authorizeHttpRequests {
                it
                    .requestMatchers("/").permitAll()
                    .requestMatchers("/*.js", "/*.html", "/*.css").permitAll()
                    .requestMatchers("/actuator/**").permitAll()
                    .requestMatchers("/api/auth/**").permitAll()
                    .requestMatchers("/v3/api-docs/**").permitAll()
                    .requestMatchers("/swagger-ui/**").permitAll()
                    .requestMatchers("/swagger-ui.html").permitAll()
                    .requestMatchers(*APIS_ALLOWED_FOR_ANONYMOUS_USERS).permitAll()
                    .requestMatchers(*APIS_ALLOWED_FOR_USERS_AND_ADMINS).authenticated()
                    .anyRequest().access(adminAuthorizationManager)
            }
            .addFilter(AuthTokenFilter(authenticationManager, authTokenSecret))

        return http.build()
    }
}
