package com.prospection.refdata.security

import com.prospection.refdata.security.jwt.TokenProvider
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import java.io.IOException

/**
 * intercept requests to other services and add Authorization header
 * Either use currently logged user's authorization if the processed was triggered from API.
 * Or create a system authorization if process was triggered from scheduled task.
 */
class ServiceAuthorizationInterceptor(
    private val tokenProvider: TokenProvider
) : ClientHttpRequestInterceptor {

    @Throws(IOException::class)
    override fun intercept(
        request: HttpRequest,
        body: ByteArray,
        execution: ClientHttpRequestExecution
    ): ClientHttpResponse {
        request.headers.add(HttpHeaders.AUTHORIZATION, tokenProvider.getUserTokenOrSystemIfUnavailable())
//        request.headers
//            .add(
//                HttpHeaders.COOKIE,
//                "refresh_token=eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.EwdinDfb9vvKorzUpFXtBaLA6F0qcU8dv9uajAbBVpnFVwyxi1UL4E0FuU8lkn_PgBD2tPGLoytNOomm_8SOJTovn9Fr6ThzKiZ3-jMAXyQwD3hmVGqZ_F2M7CitUxrgQ9L6SUKJovvaOTKZgXzFFjPVUZdokFh55SRVvm0enq1WKHm7PIToCC33mAciroiIaSnm7ezbgjjN-qKeErrPJ1DiCNW1rruJnFmy_kh6YaAPtoadF62RVkmfGYPBZ5HbcOMnhK-7tyHMshszf2FVpSvK8XKpzzro9DXBTitEq4Aq3yEVoTRG5sm2RjUlNsArYUFeT51Cc0WjGvF4NvfC_A.yFNd3Ryw4c092fKZ.8HVCf4XSecGsYsGXwqH2v3SaPWhC4Ycx6taKqYjfwW5ng6eGHMhZVvb1eMHGpuQbghedtIGwLxQMUAnQFZUNdMh3YVdMfuCtFff1wehge6MFDSZPeL6UsfAIcf_WzBBihyuh2tPr7u_Osyq9DznC2MRbJ_64KgqK9FVlaCmnEY2YKVcjDIA6flAs_P74Yo1wO9lCUqKRcTbYMxOy0anLyjuJRFuekErwx5a_tEvtjVtB1kQ7JckJDZZiG9TM0MuLBEpuMYSlrJSKkV-ykEt43lUibIYJPejbEy2cC7KeZH5cY51h6LH3VPXoNuiABR-69_zn3KeuGqDc44wOPRnzm5mpewHewtkIYKBQr9kyOWF1IgP0G468d_Kir2zzzEw4Zvw76VRftIrP1OuiTz72F5k9axyMHBBIEWoAuNWpCENT7VESWQlhocbUuUrPq175qzAc9tC_IW163YlxG_GGgW7KU57E6mAAExvHVMyR0_rRE9KRfmWUNyEifPEBxAWCzbz6I9WRoFH6ACUTGI0t3xSHZSA8PXFVnTWrCGPjMOaWIk1buN7OQ9b_UsLXbMmchKGHxm4VE7VtjSO--dKHjCDHqMf7m4Ffz1NCINuDXLRgtZSrODH99NPq12eHGSzU_-3dfF9UFoJ5dcqBwJLivzRNMCXt6R6Qj0SyAGeqJmsnIBiGyQX-ebaCZ-5ldMamXa9JDY9AY_aqtIDDChnb-xyv-3NNJgyeeY9wDW8MAOlzREbrr5K8wklgxrttSyix5dcH8JJ0hV06ZYSk5iKN2PSycm-fscx3vXEBgvLAx8Pp2DoVYmpcRbYnl8EvaQQ24zJ__6qfef7RDpA7Pswm7D81wcfi5LvZwTmdD0zcZuw55BlcDWEjVrtWf_8ATLv6O8SFjyiDI83OtmCvfT5YjaYlDuLiHSrcRjA7biuBLE4oGPPGpUT5LoDLG62BX7HlYW8fpZMowwIDxgzjdNL5A1CMzm_gZxGKtbWwAz9pUf_02P0PB4UHFjfw30dStiDaiUd21nKi8C-Qej84-wUEmfmTUk_2u1aEHXkKeBpVqulH3AkFCnRcQXqyXPb1DMvfDC7T9fcEiFOk9HEdtQsM7nV5CrIG3mUR49pqSMHQzJc7gbDWc1xeigxirbTOpdQ9m0XdEye8b69zJgWlzCZgo-dXi0SdZGfsDSi7YV2NgbhW3-uaRpenK1XJydVT_8mE1xwh4rjevjVFtnIJqMxkzoaY_uVYds-G5d0_EOMR2KQmSnlJFik.wZrm1TmTQZNTVjbmR-aJnA;auth_token=eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************.J3TJ0-UqZeyvUrK1grxXUC2dexZDlKeEXozsO8-LiTa3ItEzmgkBybmx6AG4xSG1RMwoJsstHQGPFn8gUEz9Eg;"
//            )
        return execution.execute(request, body)
    }
}