package com.prospection.refdata.security

import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import org.springframework.beans.factory.annotation.Value
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import java.util.*
import javax.crypto.SecretKey

@Component
class RoleValidation {
    @Value("\${application.security.secret}")
    private lateinit var authTokenSecret: String

    private val signingKey: SecretKey by lazy {
        Keys.hmacShaKeyFor(Base64.getDecoder().decode(authTokenSecret))
    }

    private val jwtParser by lazy {
        Jwts.parser().verifyWith(signingKey).build()
    }

    fun isAdmin(authentication: Authentication): Boolean {
        val authToken = authentication.credentials.toString()
        if (authToken.isBlank()) {
            return false
        }

        val claims = jwtParser.parseSignedClaims(authToken).payload
        return claims["admin"] == true
    }
}
