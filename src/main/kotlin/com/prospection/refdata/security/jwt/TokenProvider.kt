package com.prospection.refdata.security.jwt

import com.prospection.refdata.config.ApplicationProperties
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.security.Keys
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.authentication.AnonymousAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import java.security.Key
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@Component
class TokenProvider(
    @Autowired private val applicationProperties: ApplicationProperties
) {

    companion object {
        private val TOKEN_EXPIRY = Duration.of(1, ChronoUnit.DAYS)
        private const val ADMIN_KEY = "admin"
        private const val AUTHORITIES_KEY = "authorities"
    }

    // Check the key here
    private val key: Key = Keys.hmacShaKeyFor(Base64.getDecoder().decode(applicationProperties.authTokenSecret))

    fun getSystemToken(): String = Jwts.builder()
        .issuer("pd-ref-data-service")
        .subject("pd-ref-data-service")
        .claim(ADMIN_KEY, true)
        .claim(AUTHORITIES_KEY, "CUSTOMER_FULL_ACCESS")
        .expiration(Date(Instant.now().plus(TOKEN_EXPIRY).toEpochMilli()))
        .signWith(key)
        .compact()

    /**
     * Get jwt token for the currently logged in user if the processed was triggered from API.
     * Or create a system jwt token if process was triggered from scheduled task.
     * This is needed in service layers in certain scenarios. For example for service to service communication.
     */
    fun getUserTokenOrSystemIfUnavailable(): String {
        val authentication = SecurityContextHolder.getContext().authentication
        return if (authentication == null || authentication is AnonymousAuthenticationToken || !authentication.isAuthenticated) {
            getSystemToken()
        } else {
            authentication.credentials as String
        }
    }
}