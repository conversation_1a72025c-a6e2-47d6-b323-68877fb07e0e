package com.prospection.refdata.config

import com.prospection.refdata.job.JobRejectHandler
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor


@Configuration
class ThreadPoolExecutorConfig {
    @Bean
    fun threadPoolTaskExecutor(): ThreadPoolTaskExecutor {
        val executor = ThreadPoolTaskExecutor()
        executor.maxPoolSize = 1
        executor.setQueueCapacity(0)
        executor.setRejectedExecutionHandler(JobRejectHandler())
        executor.initialize()
        return executor
    }
}