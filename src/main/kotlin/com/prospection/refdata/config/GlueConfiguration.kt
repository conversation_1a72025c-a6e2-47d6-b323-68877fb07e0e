package com.prospection.refdata.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import org.springframework.context.annotation.Primary
import org.springframework.context.annotation.Profile
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.glue.GlueClient

@Configuration
@Profile("!test")
class GlueConfiguration {

    @Primary
    @Bean
    @Lazy
    fun getGlueClient(applicationProperties: ApplicationProperties): GlueClient {
        return GlueClient.builder()
            .region(Region.of(applicationProperties.awsRegion))
            .credentialsProvider(DefaultCredentialsProvider.create())
            .build()
    }
}