package com.prospection.refdata.config

import com.prospection.refdata.security.ServiceAuthorizationInterceptor
import com.prospection.refdata.security.jwt.TokenProvider
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate

@Configuration
class RestTemplateConfiguration {

    @Autowired
    private lateinit var applicationProperties: ApplicationProperties

    @Autowired
    private lateinit var tokenProvider: TokenProvider

    @Bean
    fun dashxRestTemplate() = buildRestTemplate(applicationProperties.dashxServiceUrl)

    @Bean
    fun customerRestTemplate() = buildRestTemplate(applicationProperties.customerServiceUrl)

    private fun buildRestTemplate(rootUri: String): RestTemplate = RestTemplateBuilder()
        .rootUri(rootUri)
        .additionalInterceptors(ServiceAuthorizationInterceptor(tokenProvider))
        .build()
}