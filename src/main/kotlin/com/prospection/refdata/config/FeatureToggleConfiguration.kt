package com.prospection.refdata.config

import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import io.getunleash.DefaultUnleash
import io.getunleash.FakeUnleash
import io.getunleash.Unleash
import io.getunleash.util.UnleashConfig
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class FeatureToggleConfiguration(
    @Value("\${application.integration.featureToggleService.url:}")
    private val featureToggleServiceUrl: String,
    @Value("\${application.integration.featureToggleService.apiToken:}")
    private val featureToggleServiceApiToken: String,
    @Value("\${application.integration.featureToggleService.fake.features:}")
    private val fakeUnleashFeatures: Array<String>,
    @Value("\${application.name}")
    private val applicationName: String
) {
    companion object {
        private val logger by lazyLogger()
    }

    @Bean
    @ConditionalOnProperty(name = ["application.integration.featureToggleService.enabled"], havingValue = "true")
    fun unleash(): Unleash {
        val config = UnleashConfig.builder()
            .appName("platform")
            .instanceId(applicationName)
            .unleashAPI("$featureToggleServiceUrl/api/")
            .apiKey(featureToggleServiceApiToken)
            .build()

        return DefaultUnleash(config)
    }

    @Bean
    @ConditionalOnMissingBean(Unleash::class)
    fun noUnleash(): Unleash {
        val unleash = FakeUnleash()
        logger.warn("Using FakeUnleash as service disabled in environment")
        if (fakeUnleashFeatures.isEmpty()) {
            logger.debug("Disabling all features")
            unleash.disableAll()
        } else {
            logger.debug("Enabling features for fake: {}", fakeUnleashFeatures)
            unleash.enable(*fakeUnleashFeatures)
        }
        return unleash
    }
}