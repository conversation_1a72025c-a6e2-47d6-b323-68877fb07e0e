package com.prospection.refdata.config

import com.prospection.refdata.common.SparkConfig.getDefaultSparkConfig
import com.prospection.refdata.common.SparkConfig.getCloudSparkConfig
import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import org.apache.spark.sql.SparkSession
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import org.springframework.context.annotation.Profile

@Configuration
class SparkConfiguration {
    companion object {
        val logger by lazyLogger()
    }

    @Bean
    @Lazy
    @Profile("local")
    fun getLocalSparkSession(applicationProperties: ApplicationProperties): SparkSession {
        val sparkConfig = getDefaultSparkConfig()
            .set("spark.hadoop.fs.s3a.access.key", applicationProperties.accessKey)
            .set("spark.hadoop.fs.s3a.secret.key", applicationProperties.secretKey)
            .set(
                "spark.hadoop.fs.s3a.endpoint",
                applicationProperties.s3Endpoint
            )
            .set("spark.hadoop.fs.s3a.path.style.access", "true")

        return SparkSession
            .builder()
            .appName("pd-ref-data-service-v2")
            .config(sparkConfig)
            .orCreate

    }

    @Bean
    @Lazy
    @Profile("!test & !local")
    fun getSparkSession(): SparkSession {
        logger.info("Available processors: ${Runtime.getRuntime().availableProcessors()}")
        val sparkConfig = getCloudSparkConfig()
        return SparkSession
            .builder()
            .appName("pd-ref-data-service-v2")
            .config(sparkConfig)
            .orCreate
    }

}