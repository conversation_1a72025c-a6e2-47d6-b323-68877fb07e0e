package com.prospection.refdata.config

import org.hibernate.envers.AuditReader
import org.hibernate.envers.AuditReaderFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import jakarta.persistence.EntityManagerFactory

@Configuration
class AuditConfig(
    private val entityManagerFactory: EntityManagerFactory
) {
    @Bean
    fun auditReader(): AuditReader {
        return AuditReaderFactory.get(entityManagerFactory.createEntityManager())
    }
}