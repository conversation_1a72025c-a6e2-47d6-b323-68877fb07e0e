package com.prospection.refdata.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class ApplicationProperties(
    @Value("\${application.amazon.s3Bucket}")
    val s3Bucket: String,

    @Value("\${cloud.aws.region.static}")
    val awsRegion: String,

    @Value("\${spring.liquibase.contexts:#{null}}")
    val liquibaseContexts: String? = null,

    @Value("\${cloud.aws.sqs.endpoint:#{null}}")
    val sqsEndpoint: String? = null,

    @Value("\${cloud.aws.s3.endpoint:#{null}}")
    val s3Endpoint: String? = null,

    @Value("\${cloud.aws.credentials.accessKey:#{null}}")
    val accessKey: String? = null,

    @Value("\${cloud.aws.credentials.secretKey:#{null}}")
    val secretKey: String? = null,

    @Value("\${application.integration.dashxServiceUrl}")
    val dashxServiceUrl: String,

    @Value("\${application.integration.customerServiceUrl}")
    val customerServiceUrl: String,

    @Value("\${application.security.secret}")
    val authTokenSecret: String,

    @Value("\${application.amazon.sns.jobNotificationTopic}")
    val snsTopicArn: String,
)
