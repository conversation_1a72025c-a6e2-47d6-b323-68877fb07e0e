package com.prospection.refdata.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.core.task.TaskExecutor
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor


@Profile("!test")
@Configuration
@EnableAsync
class AsyncConfiguration {

    @Bean(name = ["asyncTaskExecutor"])
    fun asyncTaskExecutor(): TaskExecutor {
        val threadPoolTaskExecutor = ThreadPoolTaskExecutor()
        threadPoolTaskExecutor.setThreadNamePrefix("Async-")
        threadPoolTaskExecutor.corePoolSize = 1
        threadPoolTaskExecutor.maxPoolSize = 1
        threadPoolTaskExecutor.setQueueCapacity(10)
        threadPoolTaskExecutor.initialize()
        return DelegatingSecurityContextAsyncTaskExecutor(threadPoolTaskExecutor)
    }
}