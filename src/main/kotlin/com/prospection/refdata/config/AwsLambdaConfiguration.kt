package com.prospection.refdata.config

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.lambda.LambdaClient

@Configuration
@ConditionalOnProperty(value = ["application.amazon.lambda.itemGroupsSpark"])
class AwsLambdaConfiguration {

    @Lazy
    @Bean
    fun awsLambda(applicationProperties: ApplicationProperties): LambdaClient {
        return LambdaClient.builder()
            .region(Region.of(applicationProperties.awsRegion))
            .build()
    }
}