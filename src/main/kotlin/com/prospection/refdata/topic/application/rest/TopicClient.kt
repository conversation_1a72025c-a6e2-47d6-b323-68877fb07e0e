package com.prospection.refdata.topic.application.rest

import com.prospection.refdata.common.integration.LogSupport.lazyLogger
import com.prospection.refdata.topic.application.rest.dto.TopicDto
import com.prospection.refdata.topic.application.rest.mapper.TopicDtoMapper
import com.prospection.refdata.topic.domain.Topic
import com.prospection.refdata.topic.domain.TopicPort
import org.springframework.http.HttpEntity
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder
import kotlin.system.measureTimeMillis


@Component
class TopicClient(
    private val dashxRestTemplate: RestTemplate,
    private val topicDtoMapper: TopicDtoMapper
) : TopicPort {

    companion object {
        private const val ITEM_GROUPS_CHUNK_SIZE = 1000
        private val logger by lazyLogger()
    }

    override fun listTopicsByItemGroups(itemGroups: Collection<String>): List<Topic> {
        if (itemGroups.isEmpty()) {
            return emptyList()
        }

        val chunkedItemGroups = itemGroups.chunked(ITEM_GROUPS_CHUNK_SIZE)

        val uri = UriComponentsBuilder.fromUriString("/api/topics").queryParam("method", "byItemGroups").build()

        val result = mutableListOf<Topic>()

        val timeTaken = measureTimeMillis {
            for (chunk in chunkedItemGroups) {
                val topics = dashxRestTemplate.postForObject(
                    uri.toUri().toString(), HttpEntity(chunk), Array<TopicDto>::class.java
                )
                    ?.toList()
                    ?.map { topicDtoMapper.toDomain(it) }
                    ?: emptyList()

                result.addAll(topics)
            }
        }

        logger.debug("Time taken to list topics by item groups: $timeTaken ms")

        return result
    }

    override fun listInUseReferenceDataVersions(): List<String> {
        val result: List<String>
        val timeTaken = measureTimeMillis {
            result = dashxRestTemplate.getForObject(
                "/api/topics/inUseReferenceDataVersions",
                Array<String>::class.java
            )
                ?.toList()
                ?: throw IllegalStateException("In use reference data versions result cannot be null")
        }
        logger.debug("Time taken to list in use reference data version: $timeTaken ms")

        return result
    }
}