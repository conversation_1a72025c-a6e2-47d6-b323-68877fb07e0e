package com.prospection.refdata.topic.application.rest.mapper

import com.prospection.domain.DomainMapper
import com.prospection.refdata.topic.application.rest.dto.TopicDto
import com.prospection.refdata.topic.domain.Topic
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface TopicDtoMapper : DomainMapper<Topic, TopicDto>