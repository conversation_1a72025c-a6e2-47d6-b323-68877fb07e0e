package com.prospection.refdata.topic.application.rest.dto

data class TopicDto(
    val id: Long,
    val name: String,
    val companyId: String,
    val conditionId: String? = null,
    val conditionName: String? = null,
    val subscriptionId: String? = null,
    val bridge: Boolean,
    val adhoc: <PERSON><PERSON>an,
    val therapyAreaName: String? = null,
    val subscriptionTherapyAreaId: String? = null,
    val standalone: Boolean,
    val relatedWorkflows: List<WorkflowDto>
)