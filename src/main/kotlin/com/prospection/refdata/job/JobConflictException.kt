package com.prospection.refdata.job

import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseStatus


class JobConflictException : RuntimeException()

@ControllerAdvice
class JobConflictExceptionHandler {
    @ResponseStatus(HttpStatus.CONFLICT, reason = "Cannot trigger a new job while a job is running")
    @ExceptionHandler(JobConflictException::class)
    fun handleConflict() {
    }
}