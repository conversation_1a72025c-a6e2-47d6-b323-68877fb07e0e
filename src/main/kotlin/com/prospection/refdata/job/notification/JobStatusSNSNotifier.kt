package com.prospection.refdata.job.notification

import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.config.ApplicationProperties
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.PublishRequest
import java.time.Duration
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

/**
 * Notifies job status via Amazon SNS.
 */
@Component
class JobStatusSNSNotifier @Autowired constructor(
    private val applicationProperties: ApplicationProperties
) {
    private val amazonSNS: SnsClient = SnsClient.create()
    private val objectMapper = ObjectMapper()

    companion object {
        private lateinit var instance: JobStatusSNSNotifier

        @JvmStatic
        fun getInstance(): JobStatusSNSNotifier {
            return instance
        }

        @JvmStatic
        fun notifyStatus(jobStatus: JobStatus) {
            getInstance().notify(jobStatus)
        }
    }

    @Autowired
    fun init(applicationContext: ApplicationContext) {
        instance = this
    }

    /**
     * Notifies the job status via Amazon SNS.
     *
     * @param jobStatus The status of the job.
     */
    fun notify(jobStatus: JobStatus) {
        val (statusEmoji, verb) = when (jobStatus.status.name) {
            "SUCCESSFUL" -> "✅" to "completed"
            "FAILED" -> "❌" to "failed"
            "RUNNING" -> "🔄" to "running"
            "STOPPED_BY_SERVER_REBOOT" -> "⚠️" to "stopped by server reboot"
            else -> "ℹ️" to "unknown status"
        }

        val formatter: DateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm").withZone(ZoneOffset.UTC)
        val description = """
            |• Started At: ${formatter.format(jobStatus.startedAt)} UTC
            |• Finished At: ${formatter.format(jobStatus.finishedAt)} UTC
            |• Duration: ${formatDuration(jobStatus.durationSeconds)}
            |
            |• Details:
            |${
            jobStatus.details?.entries
                ?.joinToString("\n") { "  - ${it.key}: ${it.value}" }
                ?: "  No details available"
        }
        """.trimMargin()

        val notification = CustomChatbotNotification(
            content = CustomChatbotNotification.Content(
                title = "$statusEmoji Job $verb: ${jobStatus.jobName}",
                description = description
            )
        )

        val publishRequest = PublishRequest.builder()
            .topicArn(getTopicArn())
            .message(objectMapper.writeValueAsString(notification))
            .build()
        amazonSNS.publish(publishRequest)
    }

    /**
     * Formats the duration in seconds to a human-readable string.
     *
     * @param seconds The duration in seconds.
     * @return A human-readable string representation of the duration.
     */
    private fun formatDuration(seconds: Int): String {
        val duration = Duration.ofSeconds(seconds.toLong())
        val hours = duration.toHours()
        val minutes = duration.minusHours(hours).toMinutes()
        val secs = duration.minusHours(hours).minusMinutes(minutes).seconds
        return listOfNotNull(
            if (hours > 0) "$hours hour${if (hours > 1) "s" else ""}" else null,
            if (minutes > 0) "$minutes minute${if (minutes > 1) "s" else ""}" else null,
            "$secs second${if (secs != 1L) "s" else ""}"
        ).joinToString(" ")
    }

    /**
     * Gets the Amazon SNS topic ARN from the application properties.
     *
     * @return The Amazon SNS topic ARN.
     */
    fun getTopicArn(): String {
        return applicationProperties.snsTopicArn
    }
}
