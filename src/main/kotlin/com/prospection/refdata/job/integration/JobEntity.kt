package com.prospection.refdata.job.integration

import com.prospection.refdata.job.domain.JobStatus
import java.time.LocalDateTime
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Index
import jakarta.persistence.Table
import jakarta.validation.constraints.NotNull

@Entity(name = "JobEntity")
@Table(
    name = "job",
    indexes = [
        Index(name = "job_status_idx", columnList = "status", unique = false),
        Index(name = "job_created_at_idx", columnList = "createdAt", unique = false),
        Index(name = "job_last_modified_at_idx", columnList = "lastModifiedAt", unique = false),
    ]
)
class JobEntity(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,
    @NotNull var uuid: String,
    @NotNull var name: String,
    @Enumerated(EnumType.STRING) @NotNull var status: JobStatus,
    @NotNull var createdBy: String,
    @NotNull var createdAt: LocalDateTime,
    @NotNull var lastModifiedAt: LocalDateTime,
    var resultFileUrl: String? = null,
    var errorMessage: String? = null,
)