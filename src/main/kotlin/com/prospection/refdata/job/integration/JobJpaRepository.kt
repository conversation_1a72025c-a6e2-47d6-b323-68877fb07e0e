package com.prospection.refdata.job.integration

import com.prospection.refdata.job.domain.JobStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor

interface JobJpaRepository : JpaRepository<JobEntity, Long>, JpaSpecificationExecutor<JobEntity> {
    fun findAllByStatus(status: JobStatus): List<JobEntity>

    fun findByUuid(uuid: String): JobEntity

    fun findFirstByOrderByCreatedAtDesc(): JobEntity?
}