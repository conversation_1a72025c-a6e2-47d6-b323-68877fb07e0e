package com.prospection.refdata.job.integration

import com.prospection.refdata.common.domain.DateTimePort
import com.prospection.refdata.common.domain.GenerateIdPort
import com.prospection.refdata.common.domain.UserPort
import com.prospection.refdata.job.domain.Job
import com.prospection.refdata.job.domain.JobPort
import com.prospection.refdata.job.domain.JobStatus
import com.prospection.refdata.job.domain.JobWrappedWithEvents
import org.jetbrains.kotlinx.spark.api.SparkSession
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Component
import kotlin.system.measureTimeMillis


@Component
class ThreadPoolJpaJobAdapter(
    @Autowired private val threadPoolTaskExecutor: ThreadPoolTaskExecutor,
    @Autowired private val dateTimePort: DateTimePort,
    @Autowired private val userPort: UserPort,
    @Autowired private val jobJpaRepository: JobJpaRepository,
    @Autowired private val jobEntityMapper: JobEntityMapper,
    @Autowired private val generateIdPort: GenerateIdPort,
    @Autowired private val spark: SparkSession,
) : JobPort {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(ThreadPoolJpaJobAdapter::class.java)
    }

    override fun startJob(jobName: String, createdBy: String?, jobFunction: () -> String?): Job {

        val job = Job(
            id = generateIdPort.generate(),
            name = jobName,
            createdAt = dateTimePort.now(),
            lastModifiedAt = dateTimePort.now(),
            createdBy = createdBy ?: userPort.getCurrentUserId(),
        )

        val jobWrappedWithEvents = JobWrappedWithEvents(
            job = job,
            jobFunction = jobFunction,
            onStart = onJobStart(job),
            onSuccess = onJobSuccess(job.id),
            onFailure = onJobFailure(job.id),
            onFinally = {
                val timeInMs = measureTimeMillis { spark.sqlContext().clearCache() }
                logger.info("Cleared Spark cache in $timeInMs ms")
            }
        )

        threadPoolTaskExecutor.submit(jobWrappedWithEvents)

        return job
    }

    override fun getLatestJob(): Job? {
        return jobJpaRepository.findFirstByOrderByCreatedAtDesc()?.let { jobEntityMapper.toDomain(it) }
    }

    override fun existsRunningJob(): Boolean = getLatestJob()?.status == JobStatus.RUNNING

    fun onJobStart(job: Job): () -> Unit {
        return {
            logger.info("Job ${job.id} just started")
            jobJpaRepository.save(jobEntityMapper.toEntity(job))
        }
    }

    fun onJobSuccess(jobUuid: String): (String?) -> Unit {
        return { resultFileUrl ->
            val job = jobJpaRepository.findByUuid(jobUuid)
            job.status = JobStatus.SUCCESSFUL
            job.resultFileUrl = resultFileUrl
            job.lastModifiedAt = dateTimePort.now()

            jobJpaRepository.save(job)

            logger.info("Job $jobUuid was successfully finished")
        }
    }

    fun onJobFailure(jobUuid: String): (Exception) -> Unit {
        return { ex ->
            val job = jobJpaRepository.findByUuid(jobUuid)
            job.status = JobStatus.FAILED
            job.errorMessage = ex.message
            job.lastModifiedAt = dateTimePort.now()

            jobJpaRepository.save(job)

            logger.error("Job $jobUuid failed with exception", ex)
        }
    }

    @EventListener(ApplicationReadyEvent::class)
    fun onApplicationReadyEvent() {
        tagRunningJobsStoppedWhenApplicationStarts()
    }

    fun tagRunningJobsStoppedWhenApplicationStarts(): List<JobEntity> {
        val allRunningJobs = jobJpaRepository.findAllByStatus(JobStatus.RUNNING)

        return jobJpaRepository.saveAll(allRunningJobs.map {
            it.status = JobStatus.STOPPED_BY_SERVER_REBOOT
            it.lastModifiedAt = dateTimePort.now()

            it
        })
    }
}