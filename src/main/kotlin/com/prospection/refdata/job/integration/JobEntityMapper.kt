package com.prospection.refdata.job.integration

import com.prospection.domain.DomainMapper
import com.prospection.persistence.EntityMapper
import com.prospection.refdata.job.domain.Job
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface JobEntityMapper : DomainMapper<Job, JobEntity>,
    EntityMapper<JobEntity, Job> {
    @Mappings(
        value = [
            Mapping(source = "uuid", target = "id"),
        ]
    )
    override fun toDomain(other: JobEntity): Job

    @Mappings(
        value = [
            Mapping(source = "id", target = "uuid"),
            Mapping(target = "id", ignore = true),
        ]
    )
    override fun toEntity(other: Job): JobEntity
}
