package com.prospection.refdata.job.domain

import java.util.concurrent.Callable

data class JobWrappedWithEvents(
    val job: Job,
    val jobFunction: (() -> String?),
    val onStart: (() -> Unit),
    val onSuccess: ((String?) -> Unit),
    val onFailure: ((e: Exception) -> Unit),
    val onFinally: (() -> Unit),
) : Callable<String> {
    override fun call(): String? {
        return try {
            onStart()

            val result = jobFunction()

            onSuccess(result)

            result
        } catch (e: Exception) {
            onFailure(e)
            null
        } finally {
            onFinally()
        }
    }
}
