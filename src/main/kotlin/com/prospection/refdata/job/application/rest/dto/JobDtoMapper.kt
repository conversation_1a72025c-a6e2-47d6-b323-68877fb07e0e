package com.prospection.refdata.job.application.rest.dto

import com.prospection.controller.DtoMapper
import com.prospection.domain.DomainMapper
import com.prospection.refdata.job.domain.Job
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface JobDtoMapper : DomainMapper<Job, JobDto>,
    DtoMapper<JobDto, Job>