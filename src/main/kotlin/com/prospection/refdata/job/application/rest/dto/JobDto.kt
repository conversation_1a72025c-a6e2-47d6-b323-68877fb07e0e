package com.prospection.refdata.job.application.rest.dto

import com.prospection.refdata.job.domain.JobStatus
import java.time.LocalDateTime

class JobDto(
    val id: String,
    val name: String,
    val status: JobStatus = JobStatus.RUNNING,
    val createdBy: String,
    val createdAt: LocalDateTime,
    val lastModifiedAt: LocalDateTime,
    var resultFileUrl: String? = null,
    var errorMessage: String? = null,
)