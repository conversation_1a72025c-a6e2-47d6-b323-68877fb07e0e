package com.prospection.refdata.job.application.rest

import com.prospection.refdata.job.application.rest.dto.JobDto
import com.prospection.refdata.job.application.rest.dto.JobDtoMapper
import com.prospection.refdata.job.domain.JobService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/ref-data-v2/job")
@Tag(name = "reference data, job", description = "APIs for job")
class JobController(
    @Autowired private val jobService: JobService,
    @Autowired private val jobDtoMapper: JobDtoMapper
) {
    @Operation(summary = "Get the latest job")
    @ApiResponses(value = [ApiResponse(responseCode = "200", description = "Success")])
    @GetMapping("latest")
    fun getLatestJob(): JobDto? {
        return jobService.getLatestJob()?.let { jobDtoMapper.toDto(it) }
    }
}
