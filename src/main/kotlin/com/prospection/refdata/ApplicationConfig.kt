package com.prospection.refdata

import com.prospection.refdata.config.ApplicationProperties
import com.zaxxer.hikari.HikariDataSource
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info
import liquibase.integration.spring.SpringLiquibase
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.data.envers.repository.support.EnversRevisionRepositoryFactoryBean
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer

@OpenAPIDefinition(
    info = Info(
        title = "Prospection Refdata API",
        version = "v1",
        description = "API documentation for Prospection Refdata Service"
    )
)
@Configuration
@ComponentScan(basePackages = ["com.prospection"])
@EnableAutoConfiguration
@EnableJpaRepositories(
    basePackages = ["com.prospection.refdata"],
    repositoryFactoryBeanClass = EnversRevisionRepositoryFactoryBean::class
)
@EntityScan(basePackages = ["com.prospection.audit", "com.prospection.refdata"])
class ApplicationConfig(
    private val applicationProperties: ApplicationProperties,
) {
    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    fun hikariDataSource(): HikariDataSource {
        return DataSourceBuilder.create()
            .type(HikariDataSource::class.java)
            .build()
    }

    @Bean
    fun liquibase(hikariDataSource: HikariDataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.dataSource = hikariDataSource
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-master.yaml"
        liquibase.contexts = applicationProperties.liquibaseContexts
        return liquibase
    }

    @Bean
    fun pageableResolverCustomizer(): PageableHandlerMethodArgumentResolverCustomizer {
        return PageableHandlerMethodArgumentResolverCustomizer { pageableResolver -> pageableResolver.setMaxPageSize(Int.MAX_VALUE) }
    }
}
