package com.prospection.refdata.subscription.application.rest.mapper

import com.prospection.domain.DomainMapper
import com.prospection.refdata.subscription.application.rest.dto.CompanySubscriptionDto
import com.prospection.refdata.subscription.domain.CompanySubscription
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
)
interface CompanySubscriptionDtoMapper : DomainMapper<CompanySubscription, CompanySubscriptionDto>