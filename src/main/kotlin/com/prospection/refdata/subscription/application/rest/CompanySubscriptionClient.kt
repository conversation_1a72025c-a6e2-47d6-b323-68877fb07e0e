package com.prospection.refdata.subscription.application.rest

import com.prospection.refdata.subscription.application.rest.dto.CompanySubscriptionDto
import com.prospection.refdata.subscription.application.rest.mapper.CompanySubscriptionDtoMapper
import com.prospection.refdata.subscription.domain.CompanySubscription
import com.prospection.refdata.subscription.domain.CompanySubscriptionPort
import com.prospection.refdata.topic.domain.EnrichedTopic
import com.prospection.refdata.topic.domain.Topic
import org.springframework.http.HttpEntity
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.util.UriComponentsBuilder

@Component
class CompanySubscriptionClient(
    private val customerRestTemplate: RestTemplate,
    private val subscriptionDtoMapper: CompanySubscriptionDtoMapper
) : CompanySubscriptionPort {
    override fun listSubscriptionsByRecordIds(subRecordIds: List<String>): List<CompanySubscription> {
        if (subRecordIds.isEmpty()) {
            return emptyList()
        }

        val uri = UriComponentsBuilder
            .fromUriString("/api/company-subscriptions")
            .queryParam("method", "byRecordIds")
            .build()

        val request = HttpEntity(subRecordIds)

        return customerRestTemplate.postForObject(
            uri.toUri().toString(),
            request,
            Array<CompanySubscriptionDto>::class.java
        )
            ?.toList()?.map { subscriptionDtoMapper.toDomain(it) }
            ?: emptyList()
    }

    override fun enrichTopicsWithCompanyAndSubscription(topics: Collection<Topic>): Collection<EnrichedTopic> {
        @Suppress("UNCHECKED_CAST")
        val companyNameById = customerRestTemplate.postForObject(
            "/api/companies/names",
            HttpEntity(topics.map { it.companyId }.toSet()),
            Map::class.java
        ) as Map<String, String>

        val subscriptionIds = topics.mapNotNull { it.subscriptionId }.distinct()

        val subscriptionsByRecordId = listSubscriptionsByRecordIds(subscriptionIds)
            .associateBy { it.recordId }

        return topics.map {
            EnrichedTopic(
                topic = it,
                companyName = companyNameById[it.companyId],
                subscriptionName = subscriptionsByRecordId[it.subscriptionId]?.name,
                subscriptionStatus = subscriptionsByRecordId[it.subscriptionId]?.status
            )
        }
    }
}