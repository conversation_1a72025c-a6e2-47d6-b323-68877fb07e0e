<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="thomas" id="create_job_table">
        <createTable tableName="job">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_job"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="name" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp with time zone">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="timestamp with time zone">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="result_file_url" type="TEXT"/>
            <column name="error_message" type="TEXT"/>
        </createTable>
        <createIndex indexName="job_status_at_idx" tableName="job">
            <column name="status"/>
        </createIndex>
        <createIndex indexName="job_created_at_idx" tableName="job">
            <column name="created_at"/>
        </createIndex>
        <createIndex indexName="job_last_modified_at_idx" tableName="job">
            <column name="last_modified_at"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>