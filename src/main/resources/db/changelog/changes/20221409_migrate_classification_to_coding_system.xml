<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20221409_migrate_to_coding_system_for_enrichment_rules_and_item_groups" author="markus">
        <sql dbms="postgresql">
            update enrichment_rule set rule = replace(rule, '"Classification"', '"coding_system"');
            update enrichment_rule set rule = replace(rule, '"PBS Item"', '"PBS Drug"');
            update enrichment_rule set rule = replace(rule, '"MDV HIA Item"', '"MDV Item"');
            update enrichment_rule set rule = replace(rule, '"DRG Dispensing"', '"NDC Drug"');
            update enrichment_rule set rule = replace(rule, '"Forian Dispensing"', '"NDC Drug"');
            update enrichment_rule set rule = replace(rule, '"MOH Dispensing"', '"MOH Drug"');
            update enrichment_rule set rule = replace(rule, '"JMDC Dispensing"', '"JMDC Drug"');

            update item_group set rule = replace(rule, '"Classification"', '"coding_system"');
            update item_group set rule = replace(rule, '"PBS Item"', '"PBS Drug"');
            update item_group set rule = replace(rule, '"MDV HIA Item"', '"MDV Item"');
            update item_group set rule = replace(rule, '"DRG Dispensing"', '"NDC Drug"');
            update item_group set rule = replace(rule, '"Forian Dispensing"', '"NDC Drug"');
            update item_group set rule = replace(rule, '"MOH Dispensing"', '"MOH Drug"');
            update item_group set rule = replace(rule, '"JMDC Dispensing"', '"JMDC Drug"');
        </sql>
    </changeSet>

    <changeSet id="20221409_migrate_to_coding_system_for_metadata_tables" author="markus">
        <sql dbms="postgresql">
            update raw_items_metadata
            set coding_system_id = (
                select coding_system_to_classification.coding_system_id
                from coding_system_to_classification
                         join (select name
                               from coding_system
                               where raw_items_metadata.classification_id = coding_system.id) as classification_name
                              on classification_name.name = coding_system_to_classification.classification
            );

            update enriched_items_metadata
            set coding_system_id = (
                select coding_system_to_classification.coding_system_id
                from coding_system_to_classification
                         join (select name
                               from coding_system
                               where enriched_items_metadata.classification_id = coding_system.id) as classification_name
                              on classification_name.name = coding_system_to_classification.classification
            );

            -- Delete duplicated coding systems in metadata tables
            delete
            from raw_items_metadata
            where id in
                  (select id
                   from (select id,
                                coding_system_id,
                                row_number() over ( partition by coding_system_id order by id ) as row_num
                         from raw_items_metadata
                         where published_item_version is null) as t
                   where t.row_num > 1);

            delete
            from enriched_items_metadata
            where id in
                  (select id
                   from (select id,
                                coding_system_id,
                                row_number() over ( partition by coding_system_id order by id ) as row_num
                         from enriched_items_metadata
                         where published_item_version is null) as t
                   where t.row_num > 1);
        </sql>
    </changeSet>

    <changeSet id="20221409_remove_classification_id_column_in_metadata_tables" author="markus">
        <sql dbms="postgresql">
            alter table raw_items_metadata
            drop column classification_id;

            alter table enriched_items_metadata
            drop column classification_id;
        </sql>
    </changeSet>

    <changeSet id="20221409_remove_classifications_in_coding_system_table" author="markus">
        <sql dbms="postgresql">
            delete from coding_system where deleted is true;
        </sql>
    </changeSet>

    <changeSet id="20221409_remove_deleted_column_in_coding_system_table" author="markus">
        <sql dbms="postgresql">
            alter table coding_system
            drop column deleted;
        </sql>
    </changeSet>

    <changeSet id="20221409_add_unique_constraint_for_coding_system_name" author="markus">
        <addUniqueConstraint columnNames="name" constraintName="coding_system_name_uk" tableName="coding_system"/>
    </changeSet>
</databaseChangeLog>