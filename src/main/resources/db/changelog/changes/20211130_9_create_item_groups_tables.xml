<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Peter N" id="20211130_9_create_item_groups_tables">
        <createTable tableName="item_group">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true"/>
            </column>
            <column name="business_key" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="rule" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createTable tableName="item_group_aud">
            <column name="id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="integer">
                <constraints referencedTableName="revision_info"
                             referencedColumnNames="id"
                             foreignKeyName="item_group_aud_revision_info_fk"
                             nullable="false"/>
            </column>
            <column name="revtype" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="business_key" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="rule" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>