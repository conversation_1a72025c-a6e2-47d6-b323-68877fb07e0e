<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">

    <changeSet id="202221_add_icd_diagnosis_and_icd_procedure_coding_system.xml" author="trungmai">

        <!--Step 1 Add new value of coding system-->
        <sql dbms="postgresql">
            INSERT INTO coding_system (name) VALUES ('ICD Diagnosis');
            INSERT INTO coding_system (name) VALUES ('ICD Procedure');
        </sql>

        <!--Step 2 Mapping data between coding system and classification-->
        <sql dbms="postgresql">
            INSERT INTO coding_system_to_classification (coding_system_id, classification)
            VALUES((SELECT cs.id FROM coding_system cs WHERE cs.name = 'ICD Diagnosis' limit 1), 'Forian Diagnosis');
            INSERT INTO coding_system_to_classification (coding_system_id, classification)
            VALUES((SELECT cs.id FROM coding_system cs WHERE cs.name = 'ICD Procedure' limit 1), 'Forian Procedure');

        </sql>

    </changeSet>

</databaseChangeLog>