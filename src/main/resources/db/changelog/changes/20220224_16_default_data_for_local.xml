<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220224_16_default_data_for_local" author="thomas">
        <sql>
            INSERT INTO item_group (business_key, name, rule, version, deleted, last_modified_by, last_modified_at, uuid, goal) VALUES ('docetaxel', 'Docetaxel', '{"id":"g-0.07085690191259686","rules":[{"id":"r-0.6628432870635239","field":"source_mp-pt","value":"docetaxel","operator":"="}],"combinator":"and","not":false}', 0, 0, '<EMAIL>', '2022-02-24 02:17:56.423215', '7b3c6fca-8dbd-4fb4-88a9-19bbac267915', null);

            INSERT INTO published_item_group_version (published_version, published_by, published_at, comment) VALUES ('20220224T021823Z', '<EMAIL>', '2022-02-24 02:18:23.109895', 'Added Docetaxel');

            INSERT INTO published_item_to_item_group (version, item_code, item_group_business_key, classification, item_group_name) VALUES ('20220224T021823Z', '10148D', 'docetaxel', 'PBS Item', 'Docetaxel');
            INSERT INTO published_item_to_item_group (version, item_code, item_group_business_key, classification, item_group_name) VALUES ('20220224T021823Z', '10158P', 'docetaxel', 'PBS Item', 'Docetaxel');

            INSERT INTO published_item_version (published_version, published_by, published_at) VALUES ('20220224T020951Z', '<EMAIL>', '2022-02-24 02:09:51.891388');

            INSERT INTO published_item_group (version, business_key, name) VALUES ('20220224T021823Z', 'docetaxel', 'Docetaxel');
        </sql>
    </changeSet>
</databaseChangeLog>