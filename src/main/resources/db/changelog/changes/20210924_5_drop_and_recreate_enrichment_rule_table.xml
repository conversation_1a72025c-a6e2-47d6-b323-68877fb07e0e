<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="thomask" id="20210924_4_cleanup_base_entity_fields_from_rule_tables">
        <dropTable tableName="enrichment_rule" />

        <createTable tableName="enrichment_rule">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="enrichment_rule_id_pk"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false" unique="true" uniqueConstraintName="enrichment_rule_uuid_idx"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_value_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rule" type="text">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <dropColumn tableName="enrichment_rule_aud" columnName="enriched_attribute_id" />

        <addForeignKeyConstraint baseTableName="enrichment_rule" baseColumnNames="enriched_attribute_value_id" constraintName="enrichment_rule_enriched_attribute_value_id_fk"
                                 referencedTableName="enriched_attribute_value"
                                 referencedColumnNames="id"/>

        <addUniqueConstraint tableName="enrichment_rule" columnNames="enriched_attribute_value_id" constraintName="enrichment_rule_enriched_attribute_value_id_uk" />
    </changeSet>
</databaseChangeLog>