<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="thomask" id="20210910_3_create_rule_table">

        <createTable tableName="enrichment_rule">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="enrichment_rule_id_pk"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false" unique="true" uniqueConstraintName="enrichment_rule_uuid_idx"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_value_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rule" type="text">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="enrichment_rule_aud">
            <column name="id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="integer">
                <constraints referencedTableName="revision_info"
                             referencedColumnNames="id"
                             foreignKeyName="enrichment_rule_aud_revision_info_fk"
                             nullable="false"/>
            </column>
            <column name="revtype" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_value_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rule" type="text">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="enriched_attribute">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="enriched_attribute_id_pk"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false" unique="true" uniqueConstraintName="enriched_attribute_uuid_idx"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="enriched_attribute_aud">
            <column name="id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="integer">
                <constraints referencedTableName="revision_info"
                             referencedColumnNames="id"
                             foreignKeyName="enriched_attribute_aud_revision_info_fk"
                             nullable="false"/>
            </column>
            <column name="revtype" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="enriched_attribute_value">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="enriched_attribute_value_id_pk"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false" unique="true" uniqueConstraintName="enriched_attribute_value_uuid_idx"/>
            </column>
            <column name="version" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createTable tableName="enriched_attribute_value_aud">
            <column name="id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="integer">
                <constraints referencedTableName="revision_info"
                             referencedColumnNames="id"
                             foreignKeyName="enriched_attribute_value_aud_revision_info_fk"
                             nullable="false"/>
            </column>
            <column name="revtype" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted_at" type="timestamp">
                <constraints nullable="true"/>
            </column>
            <column name="deleted" type="bigint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="value" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="enriched_attribute_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="enrichment_rule" baseColumnNames="enriched_attribute_id" constraintName="enrichment_rule_enriched_attribute_id_fk"
                                 referencedTableName="enriched_attribute"
                                 referencedColumnNames="id"/>

        <addForeignKeyConstraint baseTableName="enrichment_rule" baseColumnNames="enriched_attribute_value_id" constraintName="enrichment_rule_enriched_attribute_value_id_fk"
                                 referencedTableName="enriched_attribute_value"
                                 referencedColumnNames="id"/>

        <addForeignKeyConstraint baseTableName="enriched_attribute_value" baseColumnNames="enriched_attribute_id" constraintName="enriched_attribute_value_enriched_attribute_id_fk"
                                 referencedTableName="enriched_attribute"
                                 referencedColumnNames="id"/>

        <addUniqueConstraint tableName="enrichment_rule" columnNames="enriched_attribute_id, enriched_attribute_value_id"/>

        <addUniqueConstraint tableName="enriched_attribute" columnNames="name" />

        <addUniqueConstraint tableName="enriched_attribute_value" columnNames="enriched_attribute_id, value" />
    </changeSet>
</databaseChangeLog>