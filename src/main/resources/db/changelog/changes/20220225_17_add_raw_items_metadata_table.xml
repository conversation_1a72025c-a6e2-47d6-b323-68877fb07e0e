<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220225_17_add_raw_items_metadata_table" author="markus">
        <createTable tableName="raw_items_metadata">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_raw_items_metadata"/>
            </column>
            <column name="published_item_version" type="VARCHAR(255)"/>
            <column name="total_item" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="new_item" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="classification_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="source_attributes" type="TEXT[]">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="raw_items_metadata" baseColumnNames="classification_id"
                                 constraintName="raw_items_metadata_classification_id_fk"
                                 referencedTableName="classification"
                                 referencedColumnNames="id"/>

        <addForeignKeyConstraint baseTableName="raw_items_metadata" baseColumnNames="published_item_version"
                                 constraintName="raw_items_metadata_published_item_version_fk"
                                 referencedTableName="published_item_version"
                                 referencedColumnNames="published_version"/>
    </changeSet>
</databaseChangeLog>