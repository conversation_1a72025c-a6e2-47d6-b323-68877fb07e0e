<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220221_15_add_default_classifications" author="thomas">
        <insert tableName="classification">
            <column name="name" value="MDV Item"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="MDV Disease"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="MDV Lab Result"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="MDV Discharge"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="PBS Item"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="PBS Authority"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="PBS Specialty"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="MBS Item"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="JMDC Diagnosis"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="JMDC Dispensing"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="JMDC Procedure"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="JMDC Material"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="JMDC Hospitalisation"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="DRG Dispensing"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="DRG Procedure"/>
        </insert>
        <insert tableName="classification">
            <column name="name" value="DRG Diagnosis"/>
        </insert>
    </changeSet>
</databaseChangeLog>