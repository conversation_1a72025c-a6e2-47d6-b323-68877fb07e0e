<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220226_20_default_data_for_local" author="markus">
        <sql dbms="postgresql">
            INSERT INTO raw_items_metadata (published_item_version, total_item,
                                            new_item, classification_id, source_attributes, created_by, created_at)
            values (null, 10431, 10431, 5,
                    '{source_program-code,source_atc,source_atc-type,source_atc-print-option,source_code,source_originalcode,source_restriction-flag,source_has-caution,source_has-note,source_mq,source_repeats,source_manufacturer-code,source_pack-size,source_markup-band,source_fee-code,source_dangerous-drug-code,source_brand-premium,source_therapeutic-premium,source_cp2p,source_cdpmq,source_lp2p,source_ldpmq,source_mp2p,source_mdpmq,source_mrvsn,source_bioequivalence,source_brand-name,source_mp-pt,source_tpuu-or-mpp-pt}',
                    '<EMAIL>', '2022-02-24 02:09:51.891388');

            INSERT INTO enriched_items_metadata (classification_id, published_item_version, source_attributes,
                                                 enriched_attributes, created_by, created_at,
                                                 latest_enrichment_rule_revision_id)
            values (5, null,
                    '{source_program-code,source_atc,source_atc-type,source_atc-print-option,source_code,source_originalcode,source_restriction-flag,source_has-caution,source_has-note,source_mq,source_repeats,source_manufacturer-code,source_pack-size,source_markup-band,source_fee-code,source_dangerous-drug-code,source_brand-premium,source_therapeutic-premium,source_cp2p,source_cdpmq,source_lp2p,source_ldpmq,source_mp2p,source_mdpmq,source_mrvsn,source_bioequivalence,source_brand-name,source_mp-pt,source_tpuu-or-mpp-pt}',
                    '{}', '<EMAIL>', '2022-02-24 02:10:51.891388', 0);
        </sql>
    </changeSet>
</databaseChangeLog>