<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20230418_01_update_country_data_in_coding_system_table" author="trung.mai">
        <sql dbms="postgresql">
            UPDATE coding_system
            SET country = 'AU'
            WHERE name IN ('PBS Drug', 'PBS Authority', 'MOH Drug');

            UPDATE coding_system
            SET country = 'JP'
            WHERE name IN
                  ('JMDC Drug', 'JMDC Material', 'JMDC Diagnosis', 'JMDC Procedure', 'MDV Item', 'MDV Lab Result',
                   'MDV Diagnosis');

            UPDATE coding_system
            SET country = 'US'
            WHERE name IN ('ICD Diagnosis', 'ICD Procedure', 'FDB Drug', 'NDC Drug');
        </sql>
    </changeSet>
</databaseChangeLog>