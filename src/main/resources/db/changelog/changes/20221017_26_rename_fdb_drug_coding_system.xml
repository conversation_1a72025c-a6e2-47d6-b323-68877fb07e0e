<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20221017_26_rename_fdb_drug_coding_system.xml" author="jackohara">

        <!--Step 1 Rename Forian to FDB -->
        <sql dbms="postgresql">
            UPDATE coding_system
            SET name = 'FDB Drug'
            WHERE name = 'Forian Drug'
        </sql>

        <!--Step 2 Rename mapping-->
        <sql dbms="postgresql">
            UPDATE coding_system_to_classification
            SET classification = 'FDB Drug'
            WHERE coding_system_id = (SELECT id FROM coding_system WHERE name = 'FDB Drug' LIMIT 1)
        </sql>

    </changeSet>

</databaseChangeLog>