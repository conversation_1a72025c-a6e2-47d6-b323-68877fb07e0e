<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20221228_add_mdv_diagnosis_coding_system" author="markus">
        <sql dbms="postgresql">
            INSERT INTO coding_system (name)
            VALUES ('MDV Diagnosis');
        </sql>

        <sql dbms="postgresql">
            INSERT INTO coding_system_to_classification (coding_system_id, classification)
            VALUES ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Diagnosis'), 'MDV Disease'),
                   ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Diagnosis'), 'MDV HIA Disease');
        </sql>
    </changeSet>

    <changeSet id="20221228_migrate_to_mdv_diagnosis_for_enrichment_rules_and_item_groups" author="markus">
        <sql dbms="postgresql">
            UPDATE enrichment_rule
            SET rule = replace(rule, '"MDV Disease"', '"MDV Diagnosis"');
            UPDATE enrichment_rule
            SET rule = replace(rule, '"MDV HIA Disease"', '"MDV Diagnosis"');

            UPDATE item_group
            SET rule = replace(rule, '"MDV Disease"', '"MDV Diagnosis"');
            UPDATE item_group
            SET rule = replace(rule, '"MDV HIA Disease"', '"MDV Diagnosis"');
        </sql>

        <sql dbms="postgresql">
            -- Delete in mapping coding system to classification table
            DELETE
            FROM coding_system_to_classification
            WHERE coding_system_id = (SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Disease');

            DELETE
            FROM coding_system_to_classification
            WHERE coding_system_id = (SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV HIA Disease');

            -- Delete in metadata tables
            DELETE
            FROM raw_items_metadata
            WHERE coding_system_id = (SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Disease');

            DELETE
            FROM raw_items_metadata
            WHERE coding_system_id = (SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV HIA Disease');

            DELETE
            FROM enriched_items_metadata
            WHERE coding_system_id = (SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Disease');

            DELETE
            FROM enriched_items_metadata
            WHERE coding_system_id = (SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV HIA Disease');

            -- Delete in coding system table
            DELETE
            FROM coding_system
            WHERE name = 'MDV Disease';

            DELETE
            FROM coding_system
            WHERE name = 'MDV HIA Disease';
        </sql>
    </changeSet>
</databaseChangeLog>