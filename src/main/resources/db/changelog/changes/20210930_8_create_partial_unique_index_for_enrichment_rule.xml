<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="thomask" id="20210930_8_create_partial_unique_index_for_enrichment_rule">
        <dropUniqueConstraint tableName="enrichment_rule"
                              constraintName="enrichment_rule_enriched_attribute_value_id_uk"/>
        <sql dbms="postgresql">
            CREATE UNIQUE INDEX enrichment_rule_enriched_attribute_value_id_uk ON enrichment_rule (enriched_attribute_value_id) WHERE deleted = 0
        </sql>
    </changeSet>
</databaseChangeLog>