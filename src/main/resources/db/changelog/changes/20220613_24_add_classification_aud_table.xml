<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220613_24_add_classification_aud_table.xml" author="peter">
        <createTable tableName="classification_aud">
            <column name="id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="rev" type="integer">
                <constraints referencedTableName="revision_info"
                             referencedColumnNames="id"
                             foreignKeyName="enrichment_rule_aud_revision_info_fk"
                             nullable="false"/>
            </column>
            <column name="revtype" type="integer">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_pulled_version" type="timestamp"/>
        </createTable>
    </changeSet>
</databaseChangeLog>