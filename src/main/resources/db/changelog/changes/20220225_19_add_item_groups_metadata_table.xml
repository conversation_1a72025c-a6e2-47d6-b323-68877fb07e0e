<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220225_19_add_item_groups_metadata_table" author="markus">
        <createTable tableName="item_groups_metadata">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_item_groups_metadata"/>
            </column>
            <column name="item_group_revision_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="published_item_version" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="published_item_group_version" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="item_groups_metadata" baseColumnNames="published_item_version"
                                 constraintName="item_groups_metadata_published_item_version_fk"
                                 referencedTableName="published_item_version"
                                 referencedColumnNames="published_version"/>

        <addForeignKeyConstraint baseTableName="item_groups_metadata" baseColumnNames="published_item_group_version"
                                 constraintName="item_groups_metadata_published_item_group_version_fk"
                                 referencedTableName="published_item_group_version"
                                 referencedColumnNames="published_version"/>
    </changeSet>
</databaseChangeLog>