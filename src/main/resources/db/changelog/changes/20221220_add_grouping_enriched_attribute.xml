<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20221220_add_grouping_enriched_attribute" author="markus">
        <sql dbms="postgresql">
            <!-- Insert enriched attribute Grouping -->
            INSERT INTO enriched_attribute (uuid, name)
            VALUES (uuid_in(md5(random()::text || clock_timestamp()::text)::cstring), 'Grouping');

            <!-- Insert default values for enriched attribute Grouping -->
            INSERT INTO enriched_attribute_value (uuid, value, enriched_attribute_id)
            VALUES (uuid_in(md5(random()::text || clock_timestamp()::text)::cstring), 'Multiple Myeloma',
                    (SELECT id FROM enriched_attribute WHERE name = 'Grouping'));

            INSERT INTO enriched_attribute_value (uuid, value, enriched_attribute_id)
            VALUES (uuid_in(md5(random()::text || clock_timestamp()::text)::cstring), 'Prostate Cancer',
                    (SELECT id FROM enriched_attribute WHERE name = 'Grouping'));

            INSERT INTO enriched_attribute_value (uuid, value, enriched_attribute_id)
            VALUES (uuid_in(md5(random()::text || clock_timestamp()::text)::cstring), 'Pulmonary Arterial Hypertension',
                    (SELECT id FROM enriched_attribute WHERE name = 'Grouping'));

            INSERT INTO enriched_attribute_value (uuid, value, enriched_attribute_id)
            VALUES (uuid_in(md5(random()::text || clock_timestamp()::text)::cstring), 'Schizophrenia',
                    (SELECT id FROM enriched_attribute WHERE name = 'Grouping'));
        </sql>
    </changeSet>
</databaseChangeLog>