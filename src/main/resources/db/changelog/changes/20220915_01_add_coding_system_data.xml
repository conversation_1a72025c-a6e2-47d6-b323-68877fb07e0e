<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220915_01_add_coding_system_data.xml" author="trung">

        <!--Step 1 remove unique by name and set will_delete-->
        <addColumn tableName="coding_system">
            <column name="deleted" type="boolean" defaultValue="false"/>
        </addColumn>

        <sql dbms="postgresql">
            update coding_system set deleted = true;
        </sql>

        <dropUniqueConstraint tableName="coding_system" constraintName="coding_system_name_uk"/>

        <!--Step 2 Add new value of coding system-->
        <sql dbms="postgresql">
            INSERT INTO coding_system (name) VALUES ('PBS Drug');
            INSERT INTO coding_system (name) VALUES ('PBS Authority');
            INSERT INTO coding_system (name) VALUES ('MDV Item');
            INSERT INTO coding_system (name) VALUES ('MDV Lab Result');
            INSERT INTO coding_system (name) VALUES ('NDC Drug');
            INSERT INTO coding_system (name) VALUES ('MOH Drug');
            INSERT INTO coding_system (name) VALUES ('JMDC Drug');
            INSERT INTO coding_system (name) VALUES ('JMDC Material');
            INSERT INTO coding_system (name) VALUES ('JMDC Diagnosis');
            INSERT INTO coding_system (name) VALUES ('JMDC Procedure');
            <!-- MDV Disease and MDV HIA Disease will be merged to MDV Diagnosis in next phase-->
            INSERT INTO coding_system (name) VALUES ('MDV Disease');
            INSERT INTO coding_system (name) VALUES ('MDV HIA Disease');
        </sql>

        <!--Step 3 Mapping data between coding system and classification-->
        <sql dbms="postgresql">
            INSERT INTO coding_system_to_classification (coding_system_id, classification)
            VALUES
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'PBS Drug' AND (cs.deleted = false) limit 1), 'PBS Item'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'PBS Authority' AND (cs.deleted = false) limit 1), 'PBS Authority'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Item' AND (cs.deleted = false) limit 1), 'MDV Item'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Item' AND (cs.deleted = false) limit 1), 'MDV HIA Item'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Lab Result' AND (cs.deleted = false) limit 1), 'MDV Lab Result'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'NDC Drug' AND (cs.deleted = false) limit 1), 'DRG Dispensing'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'NDC Drug' AND (cs.deleted = false) limit 1), 'Forian Dispensing'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MOH Drug' AND (cs.deleted = false) limit 1), 'MOH Dispensing'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'JMDC Drug' AND (cs.deleted = false) limit 1), 'JMDC Dispensing'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'JMDC Material' AND (cs.deleted = false) limit 1), 'JMDC Material'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'JMDC Diagnosis' AND (cs.deleted = false) limit 1), 'JMDC Diagnosis'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'JMDC Procedure' AND (cs.deleted = false) limit 1), 'JMDC Procedure'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV Disease' AND (cs.deleted = false) limit 1), 'MDV Disease'),
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'MDV HIA Disease' AND (cs.deleted = false) limit 1), 'MDV HIA Disease');
        </sql>

    </changeSet>

</databaseChangeLog>