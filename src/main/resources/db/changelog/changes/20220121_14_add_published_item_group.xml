<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="20220121_14_add_published_item_group" author="thomas">
        <createIndex tableName="published_item_to_item_group" indexName="published_item_to_item_group_business_key_idx">
            <column name="item_group_business_key"/>
        </createIndex>

        <createTable tableName="published_item_group">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_published_item_group"/>
            </column>
            <column name="version" type="VARCHAR(255)"/>
            <column name="business_key" type="text"/>
            <column name="name" type="text"/>
        </createTable>

        <createIndex indexName="published_item_group_version_idx" tableName="published_item_group">
            <column name="version"/>
        </createIndex>

        <createIndex tableName="published_item_group" indexName="published_item_group_business_key_name_idx">
            <column name="business_key"/>
            <column name="name"/>
        </createIndex>

        <!-- creating clustered index for performance because we'll insert a lot of rows -->
        <sql dbms="postgresql">
            CLUSTER published_item_group USING published_item_group_version_idx;
        </sql>
    </changeSet>
</databaseChangeLog>