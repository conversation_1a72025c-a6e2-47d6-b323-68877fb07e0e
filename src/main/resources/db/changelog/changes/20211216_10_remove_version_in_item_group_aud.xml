<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Peter N" id="20211216_10_remove_version_in_item_group_aud">
        <dropColumn tableName="item_group_aud" columnName="version"/>
        <dropUniqueConstraint tableName="item_group" constraintName="item_group_business_key_key"/>
        <dropUniqueConstraint tableName="item_group" constraintName="item_group_name_key"/>
        <sql dbms="postgresql">
            CREATE UNIQUE INDEX item_group_business_key_uk ON item_group (business_key) WHERE deleted = 0
        </sql>
        <sql dbms="postgresql">
            CREATE UNIQUE INDEX item_group_name_uk ON item_group (name) WHERE deleted = 0
        </sql>
    </changeSet>
</databaseChangeLog>