<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="persist_while_publishing" author="thomas">
        <createTable tableName="published_item_to_item_group">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_published_item_to_item_group"/>
            </column>
            <column name="version" type="VARCHAR(255)"/>
            <column name="item_code" type="VARCHAR(255)"/>
            <column name="item_group_business_key" type="text"/>
            <column name="classification" type="VARCHAR(255)"/>
            <column name="item_group_name" type="text"/>
        </createTable>

        <createIndex indexName="published_item_to_item_group_version_idx" tableName="published_item_to_item_group">
            <column name="version"/>
        </createIndex>

        <!-- creating clustered index for performance because we'll insert a lot of rows -->
        <sql dbms="postgresql">
            CLUSTER published_item_to_item_group USING published_item_to_item_group_version_idx;
        </sql>

        <createTable tableName="published_item_group_version">
            <column name="published_version" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_published_item_group_version"/>
            </column>
            <column name="published_by" type="VARCHAR(255)"/>
            <column name="published_at" type="DATETIME"/>
            <column name="comment" type="text"/>
        </createTable>

        <createTable tableName="published_item_version">
            <column name="published_version" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_published_item_version"/>
            </column>
            <column name="published_by" type="VARCHAR(255)"/>
            <column name="published_at" type="DATETIME"/>
        </createTable>
    </changeSet>
</databaseChangeLog>