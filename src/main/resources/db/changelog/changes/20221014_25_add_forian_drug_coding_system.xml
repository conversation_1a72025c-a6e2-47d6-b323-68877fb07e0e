<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20221014_25_add_fdb_drug_coding_system.xml" author="jackohara">

        <!--Step 1 Add new value of coding system-->
        <sql dbms="postgresql">
            INSERT INTO coding_system (name) VALUES ('Forian Drug');
        </sql>

        <!--Step 2 Mapping data between coding system and classification-->
        <sql dbms="postgresql">
            INSERT INTO coding_system_to_classification (coding_system_id, classification)
            VALUES
                ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'Forian Drug' limit 1), 'Forian Drug')
        </sql>

    </changeSet>

</databaseChangeLog>