<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Peter N" id="20211223_11_add_uuid_to_item_group.xml">
        <addColumn tableName="item_group">
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="goal" type="text"/>
        </addColumn>

        <addColumn tableName="item_group_aud">
            <column name="uuid" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="goal" type="text"/>
        </addColumn>

        <sql dbms="postgresql">
            DROP INDEX item_group_business_key_uk
        </sql>

        <sql dbms="postgresql">
            DROP INDEX item_group_name_uk
        </sql>

        <sql dbms="postgresql">
            CREATE UNIQUE INDEX item_group_business_key_uk ON item_group (UPPER(business_key)) WHERE deleted = 0
        </sql>

        <sql dbms="postgresql">
            CREATE UNIQUE INDEX item_group_name_uk ON item_group (UPPER(name)) WHERE deleted = 0
        </sql>
    </changeSet>
</databaseChangeLog>