<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20230227_02_insert_forian_procedure_to_classification_to_coding_system_table" author="trung">
        <sql dbms="postgresql">
            INSERT INTO coding_system_to_classification (coding_system_id, classification, coding_system_column_to_export)
            VALUES ((SELECT cs.id FROM coding_system cs WHERE cs.name = 'FDB Drug') , 'Forian Procedure', 'source_jcode');
        </sql>
    </changeSet>
</databaseChangeLog>