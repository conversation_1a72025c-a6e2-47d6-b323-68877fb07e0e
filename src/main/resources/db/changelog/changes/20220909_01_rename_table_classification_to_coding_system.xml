<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220909_01_rename_table_classification_to_coding_system.xml" author="trung">
        <renameTable oldTableName="classification" newTableName="coding_system"/>
        <renameTable oldTableName="classification_aud" newTableName="coding_system_aud"/>

        <createTable tableName="coding_system_to_classification">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="coding_system_to_classification_id_pk"/>
            </column>

            <column name="coding_system_id" type="bigint">
                <constraints nullable="false"/>
            </column>

            <column name="classification" type="varchar(255)"/>
        </createTable>

        <addForeignKeyConstraint baseTableName="coding_system_to_classification"
                                 baseColumnNames="coding_system_id"
                                 constraintName="coding_system_to_classification_coding_system_id_fk"
                                 referencedTableName="coding_system"
                                 referencedColumnNames="id"/>

        <addColumn tableName="raw_items_metadata">
            <column name="coding_system_id" type="bigint"/>
        </addColumn>

        <addForeignKeyConstraint baseTableName="raw_items_metadata"
                                 baseColumnNames="coding_system_id"
                                 constraintName="raw_items_metadata_coding_system_id_fk"
                                 referencedTableName="coding_system"
                                 referencedColumnNames="id"/>

        <addColumn tableName="enriched_items_metadata">
            <column name="coding_system_id" type="bigint"/>
        </addColumn>

        <addForeignKeyConstraint baseTableName="enriched_items_metadata"
                                 baseColumnNames="coding_system_id"
                                 constraintName="enriched_items_metadata_coding_system_id_fk"
                                 referencedTableName="coding_system"
                                 referencedColumnNames="id"/>

        <sql dbms="postgresql">
            update raw_items_metadata set coding_system_id = classification_id;
            update enriched_items_metadata set coding_system_id = classification_id;
        </sql>

        <addNotNullConstraint tableName="raw_items_metadata" columnName="coding_system_id"/>
        <addNotNullConstraint tableName="enriched_items_metadata" columnName="coding_system_id"/>


        <dropNotNullConstraint tableName="raw_items_metadata" columnName="classification_id"/>
        <dropNotNullConstraint tableName="enriched_items_metadata" columnName="classification_id"/>

        <sql dbms="postgresql">
            alter index pk_classification rename to coding_system_pk;
            alter index classification_name_uk rename to coding_system_name_uk;
        </sql>
    </changeSet>


</databaseChangeLog>