<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20250424_01_add_column_archived_to_table_published_item_group_version" author="tungtran">
        <addColumn tableName="published_item_group_version">
            <column name="archived" type="boolean" defaultValue="false"/>
        </addColumn>
    </changeSet>

    <changeSet id="20250424_02_add_table_archived_item_group" author="tungtran">
        <createTable tableName="archived_item_group">
            <column name="version" type="VARCHAR(255)"/>
            <column name="business_key" type="text"/>
            <column name="name" type="text"/>
        </createTable>

        <!-- it seems unnecessary, but we will need it later to run some basic query or delete in the future -->
        <createIndex indexName="archived_item_group_version_idx" tableName="archived_item_group">
            <column name="version"/>
        </createIndex>
    </changeSet>

    <changeSet id="20250424_03_add_table_archived_item_to_item_group" author="tungtran">
        <createTable tableName="archived_item_to_item_group">
            <column name="version" type="VARCHAR(255)"/>
            <column name="item_code" type="VARCHAR(255)"/>
            <column name="item_group_business_key" type="text"/>
            <column name="classification" type="VARCHAR(255)"/>
            <column name="item_group_name" type="text"/>
        </createTable>

        <!-- it seems unnecessary, but we will need it later to run some basic query or delete in the future -->
        <createIndex indexName="archived_item_to_item_group_version_idx" tableName="archived_item_to_item_group">
            <column name="version"/>
        </createIndex>
    </changeSet>

    <changeSet id="20250424_04_add_table_archive_unused_historical_data_lock" author="tungtran">
        <createTable tableName="archive_unused_historical_data_lock">
            <column name="locked" type="boolean"/>
        </createTable>

        <!-- it seems unnecessary, but we will need it later to run some basic query or delete in the future -->
        <insert tableName="archive_unused_historical_data_lock">
            <column name="locked" value="false"/>
        </insert>
    </changeSet>

    <changeSet id="20250424_05_remove_index_published_item_to_item_group_business_key_idx" author="tungtran">
        <sql dbms="postgresql">
            DROP INDEX IF EXISTS published_item_to_item_group_business_key_idx;
        </sql>
    </changeSet>

    <changeSet id="20250424_06_add_index_published_item_to_item_group_version_item_group_bkey_idx" author="tungtran">
        <sql dbms="postgresql">
            CREATE INDEX IF NOT EXISTS published_item_to_item_group_version_item_group_bkey_idx
                on published_item_to_item_group (version, item_group_business_key);
        </sql>
    </changeSet>

</databaseChangeLog>