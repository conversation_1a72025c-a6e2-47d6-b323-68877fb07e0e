<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="20220221_14_add_classification_table" author="thomas">
        <createTable tableName="classification">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_classification"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
        </createTable>

        <addUniqueConstraint columnNames="name" constraintName="classification_name_uk" tableName="classification"/>
    </changeSet>
</databaseChangeLog>