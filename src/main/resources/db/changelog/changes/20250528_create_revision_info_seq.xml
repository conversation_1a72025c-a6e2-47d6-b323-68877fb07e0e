<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    
    <changeSet author="system" id="20250528_create_revision_info_seq">
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists sequenceName="revision_info_seq"/>
            </not>
        </preConditions>
        
        <!-- Create the revision_info_seq sequence that Hibernate Envers expects -->
        <createSequence sequenceName="revision_info_seq"
                        incrementBy="1"
                        minValue="1"
                        startValue="1"
                        cacheSize="1"/>
        
        <!-- Set the sequence to start from the current max id + 1 to avoid conflicts -->
        <sql dbms="postgresql">
            SELECT setval('revision_info_seq', COALESCE((SELECT MAX(id) FROM revision_info), 0) + 1, false);
        </sql>
    </changeSet>
</databaseChangeLog>
