databaseChangeLog:
  - include:
      file: db/changelog/changes/20201013_1_create_hibernate_sequence.xml
  - include:
      file: db/changelog/changes/20201013_2_create_revision_info_table.xml
  - include:
      file: db/changelog/changes/20201013_3_create_rule_tables.xml
  - include:
      file: db/changelog/changes/20210924_4_cleanup_base_entity_fields_from_rule_tables.xml
  - include:
      file: db/changelog/changes/20210924_5_drop_and_recreate_enrichment_rule_table.xml
  - include:
      file: db/changelog/changes/20210928_6_add_goal_to_enrichment_rule.xml
  - include:
      file: db/changelog/changes/20210930_7_add_goal_to_enrichment_rule_aud.xml
  - include:
      file: db/changelog/changes/20210930_8_create_partial_unique_index_for_enrichment_rule.xml
  - include:
      file: db/changelog/changes/20211130_9_create_item_groups_tables.xml
  - include:
      file: db/changelog/changes/20211216_10_remove_version_in_item_group_aud.xml
  - include:
      file: db/changelog/changes/20211221_11_create_job.xml
  - include:
      file: db/changelog/changes/20211223_12_add_uuid_to_item_group.xml
  - include:
      file: db/changelog/changes/20220114_13_persist_while_publishing.xml
  - include:
      file: db/changelog/changes/20220121_14_add_published_item_group.xml
  - include:
      file: db/changelog/changes/20220221_14_add_classification_table.xml
  - include:
      file: db/changelog/changes/20220221_15_add_default_classifications.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20220224_16_default_data_for_local.xml
      context: "local"
  - include:
      file: db/changelog/changes/20220225_17_add_raw_items_metadata_table.xml
  - include:
      file: db/changelog/changes/20220225_18_add_enriched_items_metadata_table.xml
  - include:
      file: db/changelog/changes/20220225_19_add_item_groups_metadata_table.xml
  - include:
      file: db/changelog/changes/20220226_20_add_default_metadata_for_local.xml
      context: "local"
  - include:
      file: db/changelog/changes/20220406_21_add_pulling_status_to_classification_table.xml
  - include:
      file: db/changelog/changes/20220506_22_migrate_classification_ndc_dispensing.xml
  - include:
      file: db/changelog/changes/20220613_24_add_classification_aud_table.xml
  - include:
      file: db/changelog/changes/20220909_01_rename_table_classification_to_coding_system.xml
  - include:
      file: db/changelog/changes/20220915_01_add_coding_system_data.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20221409_migrate_classification_to_coding_system.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20221014_25_add_forian_drug_coding_system.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20221017_26_rename_fdb_drug_coding_system.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20221219_add_icd_diagnosis_and_icd_procedure.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20221220_add_grouping_enriched_attribute.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20221228_add_mdv_diagnosis_coding_system.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20230227_01_add_export_column_to_classification_to_coding_system_table.xml
  - include:
      file: db/changelog/changes/20230227_02_update_data_for_classification_to_coding_system_table.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20230418_01_add_country_column_to_coding_system_table.xml
  - include:
      file: db/changelog/changes/20230418_02_update_country_data_in_coding_system_table.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20230418_03_add_cpt_hcpcs_procedure.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20230816_01_change_classification_for_forian_jcodes.xml
  - include:
      file: db/changelog/changes/20250114_1_add_desc_coding_systems.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20250116_1_add_desc_coding_systems_to_classification.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20250310_1_update_desc_coding_systems_to_classification.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20250424_archive_unused_historical_data.xml
  - include:
      file: db/changelog/changes/20250516_add_desc_dispensing_classification_to_desc_item_coding_system.xml
      context: "!test"
  - include:
      file: db/changelog/changes/20250523_remove_etl_status_in_coding_system_table.xml
  - include:
      file: db/changelog/changes/20250527_add_deleted_item_column_to_raw_items_metadata_table.xml
