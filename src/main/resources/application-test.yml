
tracing:
  enabled: false

spring:
  datasource:
    driverClassName: org.testcontainers.jdbc.ContainerDatabaseDriver
    jdbcUrl: jdbc:tc:postgresql:15.5:///databasename
    username:
    password:
  liquibase:
    contexts: test

application:
  name: test-pd-ref-data-service
  security:
    secret: aiu5u+Ai520LRcrJbVbTd7yhYf07AUgkUDCeREJRqb/KN47bkGiv7KypvsAQJAS3Xrmpqm79IVLfNbdutcFnbg==
  amazon:
    s3Bucket: test-bucket
    etlQueueUrl: http://localhost:4566/000000000000/pd-au-local-etl-queue.fifo
  integration:
    dashxServiceUrl: http://localhost:8080
    customerServiceUrl: http://localhost:11080
    featureToggleService:
      enabled: false
      url: http://localhost:4242
      apiToken: default:development.unleash-insecure-api-token
      # If you're not using an actual Unleash server for feature toggles (i.e. enabled is false), you can set whatever
      # features you'd like enabled by the FakeUnleash instance that is used in that case.
      # `features` is just a comma separated list
      fake:
        features: ''
  job:
    archiveUnusedData:
      cron: "-"

s3-items-for-external-tool:
  countryPaths:
    au: s3a://test-bucket/external-reference-data
    jp: s3a://test-bucket/external-reference-data
    us: s3a://test-bucket/external-reference-data

cloud:
  aws:
    rds:
      enabled: false
    elasticache:
      enabled: false
    stack:
      enabled: false
      auto: false
    sns:
      enabled: false
    sqs:
      enabled: false
      listener:
        auto-startup: false
    s3:
      enabled: false
    glue:
      enabled: false
    region:
      static: ap-southeast-2
      auto: false

