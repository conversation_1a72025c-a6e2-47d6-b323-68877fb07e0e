spring:
  datasource:
    #    dataSourceClassName: com.prospection.jdbc.PGIamAuthTokenDataSource
    #    dataSourceProperties:
    #      serverName: pd-au-uat-pd-ref-data-service-aurora.cluster-cj4nsgusyikq.ap-southeast-2.rds.amazonaws.com
    #      portNumber: 5432
    #      user: pd_au_uat_pd_ref_data_service
    #      databaseName: pd_au_uat_pd_ref_data_service
    dataSourceClassName:
    driverClassName: org.postgresql.Driver
    jdbcUrl: ****************************************
    username: refdata
    password: Prospection123
  liquibase:
    enabled: false

tracing:
  # TODO: PF-126 we are going to remove xray completely when upgrade pd-starter-* to the latest version
  enabled: false

application:
  name: local-pd-ref-data-service
  amazon:
    #    lambda:
    #      itemGroupsSpark: pd-au-uat-pd-ref-data-service-spark
    s3Bucket: pd-au-local-ref-data-v2
    etlQueueUrl: http://localhost:4566/000000000000/pd-au-local-etl-queue.fifo
  #    s3Bucket: pd-au-uat-ref-data-v2
  #    etlQueueUrl: https://sqs.ap-southeast-2.amazonaws.com/102782770593/pd-au-uat-pd-ref-data-service-etl-queue.fifo
    sns:
      jobNotificationTopic: test
  integration:
    dashxServiceUrl: https://pd-frontend-admin.pd-au-uat.prospection-internal.net
    customerServiceUrl: https://pd-frontend-admin.pd-au-uat.prospection-internal.net
    featureToggleService:
      enabled: false
      url: http://localhost:4242
      apiToken: default:development.unleash-insecure-api-token
      # If you're not using an actual Unleash server for feature toggles (i.e. enabled is false), you can set whatever
      # features you'd like enabled by the FakeUnleash instance that is used in that case.
      # `features` is just a comma separated list
      fake:
        features: ''
  job:
    archiveUnusedData:
      cron: "-"

s3-items-for-external-tool:
  countryPaths:
    au: s3a://pd-au-local-ref-data-v2/external-reference-data
    jp: s3a://pd-au-local-ref-data-v2/external-reference-data
    us: s3a://pd-au-local-ref-data-v2/external-reference-data
#s3-items-for-external-tool:
#  countryPaths:
#    au: s3a://pd-au-uat-common/external-reference-data
#    jp: s3a://pd-jp-uat-common/external-reference-data
#    us: s3a://pd-us-uat-common/external-reference-data

server:
  port: 17080

cloud:
  aws:
    rds:
      enabled: false
    elasticache:
      enabled: false
    stack:
      enabled: false
    sns:
      enabled: false
    sqs:
      endpoint: http://localhost:4566
    s3:
      endpoint: http://localhost:4566
    credentials:
      accessKey: accesskey
      secretKey: secretkey
    region:
      static: ap-southeast-2
