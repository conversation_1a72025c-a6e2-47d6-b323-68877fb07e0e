<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <springProfile name="local | private | test">
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <Pattern>
                    %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
                </Pattern>
            </encoder>
        </springProfile>
        <springProfile name="!(local | private | test)">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
        </springProfile>
    </appender>
    <property name="LOGS" value="/tmp/ref-data-service/logs" />
    <appender name="RollingFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/service.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
            </Pattern>
        </encoder>

        <!-- rollover daily or when the file reaches 10 MegaBytes -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/archived/service-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>
    <root level="info">
        <appender-ref ref="STDOUT"/>
    </root>
    <logger name="com.prospection" level="info"/>
    <logger name="org.apache.spark" level="error"/>
    <logger name="org.apache.hadoop" level="error"/>
    <logger name="org.apache.parquet" level="error"/>
    <logger name="org.apache.spark.resource" level="info"/>
    <logger name="org.apache.spark.ui.SparkUI" level="info"/>

    <springProfile name="local">
        <root>
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="RollingFile"/>
        </root>
    </springProfile>
    <springProfile name="!local">
        <root>
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
</configuration>