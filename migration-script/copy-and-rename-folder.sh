#!/bin/bash

AWS_PROFILE="pd2-dev"

SOURCE_BUCKET_NAME=$1
SOURCE_FOLDER=$2
WORD_TO_REPLACE=$3
WORD_REPLACED=$4
NEW_PATH_PREFIX=$5
FIRST_STRING_TO_REPLACE=$6
FIRST_STRING_REPLACED=$7
SECOND_STRING_TO_REPLACE=$8
SECOND_STRING_REPLACED=$9
PATH_TO_COPY=${10}

BATCH_SIZE=7
COUNT=0
TOTAL_COUNT_OBJECTS=$(aws s3 ls --recursive s3://"$SOURCE_BUCKET_NAME"/"$SOURCE_FOLDER"/ --profile $AWS_PROFILE | wc -l)
TOTAL_BATCH=$((TOTAL_COUNT_OBJECTS / BATCH_SIZE))

SOURCE_DATA_PATH="$SOURCE_BUCKET_NAME/$SOURCE_FOLDER"

if [ -n "$PATH_TO_COPY" ]; then
  LATEST_PUBLISH_VERSION=$(aws s3 ls --recursive s3://"$SOURCE_BUCKET_NAME"/"$SOURCE_FOLDER"/ --profile $AWS_PROFILE | sort | tail -n 1 | awk '{print $4}' | cut -d/ -f 3)
  SOURCE_DATA_PATH="$SOURCE_BUCKET_NAME/$SOURCE_FOLDER/$LATEST_PUBLISH_VERSION/$PATH_TO_COPY"
fi

aws s3 ls --recursive "s3://$SOURCE_DATA_PATH/" --profile $AWS_PROFILE |
  # Get path only
  awk '{ print substr($0, index($0,$4)) }' |
  while read -r old_path; do
    if [ -n "$WORD_TO_REPLACE" ]; then
      new_path=${old_path/$WORD_TO_REPLACE/"$WORD_REPLACED"}
    fi

    if [ -n "$FIRST_STRING_TO_REPLACE" ] && [ -n "$FIRST_STRING_REPLACED" ]; then
      new_path=${new_path/$FIRST_STRING_TO_REPLACE/"$FIRST_STRING_REPLACED"}
    fi

    if [ -n "$SECOND_STRING_TO_REPLACE" ] && [ -n "$SECOND_STRING_REPLACED" ]; then
      new_path=${new_path/$SECOND_STRING_TO_REPLACE/"$SECOND_STRING_REPLACED"}
    fi

    COUNT=$((COUNT + 1))

    aws s3 cp s3://"$SOURCE_BUCKET_NAME"/"$old_path" s3://"$SOURCE_BUCKET_NAME"/"$NEW_PATH_PREFIX""$new_path" --profile $AWS_PROFILE

    if ((COUNT == BATCH_SIZE)); then
      TOTAL_BATCH=$((TOTAL_BATCH - 1))
      COUNT=0
      wait
      echo "Finished batch 7 objects"
    fi

    if ((TOTAL_BATCH == 0)) && ((COUNT > 0)); then
      wait
      echo "Finished remain objects"
    fi
  done
