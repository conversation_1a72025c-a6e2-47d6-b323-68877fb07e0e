#!/bin/bash

SOURCE_BUCKET_NAME="pd-au-dev-ref-data-v2"
AWS_PROFILE="pd2-dev"

# draft/ folder
aws s3 rm s3://$SOURCE_BUCKET_NAME/draft/ --recursive --profile $AWS_PROFILE

# public/ folder
aws s3 rm s3://$SOURCE_BUCKET_NAME/public/ --recursive --profile $AWS_PROFILE

# published/ folder
aws s3 rm s3://$SOURCE_BUCKET_NAME/published/ --recursive --profile $AWS_PROFILE

# temp/ folder
aws s3 rm s3://$SOURCE_BUCKET_NAME/temp/ --recursive --profile $AWS_PROFILE

echo "Delete old folders successfully"