#!/bin/bash

SOURCE_BUCKET_NAME="pd-au-dev-ref-data-v2"
AWS_PROFILE="pd2-dev"

# backup current bucket before migrating
aws s3 cp s3://$SOURCE_BUCKET_NAME/ s3://$SOURCE_BUCKET_NAME/backup/ --recursive --profile $AWS_PROFILE

# temp/temp_items/ -> raw-upload/
aws s3 cp s3://$SOURCE_BUCKET_NAME/temp/temp_items/ s3://$SOURCE_BUCKET_NAME/raw-upload/ --recursive --profile $AWS_PROFILE &

# Start migrate items folder
# draft/raw-items/ -> items/draft/raw-items/
./copy-and-rename-folder.sh "$SOURCE_BUCKET_NAME" "draft/raw-items" "draft/raw-items/classification=" "/" "items/draft/raw-items"

# draft/enriched-items/ -> items/draft/enriched-items-parquet/
aws s3 cp s3://$SOURCE_BUCKET_NAME/draft/enriched-items/ s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-parquet/ --recursive --profile $AWS_PROFILE &

# public/change-summary/items/ -> items/draft/change-summary/
aws s3 cp s3://$SOURCE_BUCKET_NAME/public/change-summary/items/ s3://$SOURCE_BUCKET_NAME/items/draft/change-summary/ --recursive --profile $AWS_PROFILE &

# public/enriched-items/ -> items/draft/enriched-items-csv/
aws s3 cp s3://$SOURCE_BUCKET_NAME/public/enriched-items/ s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-csv/ --recursive --profile $AWS_PROFILE &

# public/preview/ -> items/preview/
# We don't need to copy temp/ folder
aws s3 cp s3://$SOURCE_BUCKET_NAME/public/preview/ s3://$SOURCE_BUCKET_NAME/items/preview/ --recursive --exclude "temp/*" --profile $AWS_PROFILE &

# published/items/ -> items/published/
# Rename sub-folder items/ -> enriched-items-parquet/ & enrichment-rules/ -> archive-enrichment-rule/
# And remove prefix version=
./copy-and-rename-folder.sh "$SOURCE_BUCKET_NAME" "published/items" "published/items/version=" "/" "items/published" "items" "enriched-items-parquet" "enrichment-rules" "archive-enrichment-rule"
# End migrate items folder

# Start migrate item-groups folder
# draft/item-groups/ -> item-groups/draft/item-to-item-group/
aws s3 cp s3://$SOURCE_BUCKET_NAME/draft/item-groups/ s3://$SOURCE_BUCKET_NAME/item-groups/draft/item-to-item-group/ --recursive --profile $AWS_PROFILE &

# public/change-summary/item-groups/ -> item-groups/draft/change-summary/
aws s3 cp s3://$SOURCE_BUCKET_NAME/public/change-summary/item-groups/ s3://$SOURCE_BUCKET_NAME/item-groups/draft/change-summary/ --recursive --profile $AWS_PROFILE &

# public/preview-item-group/ -> item-groups/preview/
# We don't need to copy temp/ folder
aws s3 cp s3://$SOURCE_BUCKET_NAME/public/preview-item-group/ s3://$SOURCE_BUCKET_NAME/item-groups/preview/ --recursive --exclude "temp/*" --profile $AWS_PROFILE &

# published/item-groups/ -> item-groups/published/
# Need to rename sub-folder items/ -> item-to-item-group/ & item-groups/ -> archive-item-group/
./copy-and-rename-folder.sh "$SOURCE_BUCKET_NAME" "published/item-groups" "published/item-groups" "" "item-groups/published" "items" "item-to-item-group" "item-groups" "archive-item-group"
# End migrate item-groups folder

wait
echo "# -- > S3 migration script done"
