#!/bin/bash

SOURCE_BUCKET_NAME="pd-au-dev-ref-data-v2"
AWS_PROFILE="pd2-dev"

echo "# -- > Start migrate classification to coding system"

# Start migrate items folder
# rename PBS Item to PBS Drug in snapshots folder
aws s3 cp "s3://$SOURCE_BUCKET_NAME/snapshots/PBS Item/" "s3://$SOURCE_BUCKET_NAME/snapshots/PBS Drug/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/snapshots/PBS Item/" --recursive --profile $AWS_PROFILE

# rename PBS Item to PBS Drug in warehouse folder
aws s3 cp "s3://$SOURCE_BUCKET_NAME/warehouse/PBS Item/" "s3://$SOURCE_BUCKET_NAME/warehouse/PBS Drug/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/warehouse/PBS Item/" --recursive --profile $AWS_PROFILE

# rename PBS Item to PBS Drug in raw items folder
aws s3 cp "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/PBS Item/" "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/PBS Drug/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/PBS Item/" --recursive --profile $AWS_PROFILE
# rename JMDC Dispensing to JMDC Drug in raw items folder
aws s3 cp "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/JMDC Dispensing/" "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/JMDC Drug/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/JMDC Dispensing/" --recursive --profile $AWS_PROFILE

# copy DRG Dispensing to NDC Drug in items/draft/raw-items/
aws s3 cp "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/DRG Dispensing/" "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/NDC Drug/" --recursive --profile $AWS_PROFILE &
# delete Forian Dispensing & DRG Dispensing in items/draft/raw-items/
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/Forian Dispensing/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/raw-items/DRG Dispensing/" --recursive --profile $AWS_PROFILE

# copy DRG Dispensing to NDC Drug in items/draft/enriched-items-parquet/
aws s3 cp "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-parquet/DRG Dispensing/" "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-parquet/NDC Drug/" --recursive --profile $AWS_PROFILE &
# delete Forian Dispensing & DRG Dispensing in items/draft/enriched-items-parquet/
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-parquet/Forian Dispensing/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-parquet/DRG Dispensing/" --recursive --profile $AWS_PROFILE

# delete DRG Dispensing & Forian Dispensing in items/draft/change-summary/
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/change-summary/DRG Dispensing/" --recursive --profile $AWS_PROFILE &
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/change-summary/Forian Dispensing/" --recursive --profile $AWS_PROFILE &

# copy DRG Dispensing to NDC Drug in items/draft/enriched-items-csv/
aws s3 cp "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-csv/DRG Dispensing/" "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-csv/NDC Drug/" --recursive --profile $AWS_PROFILE &
# delete Forian Dispensing & DRG Dispensing in items/draft/enriched-items-csv/
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-csv/Forian Dispensing/" --recursive --profile $AWS_PROFILE
aws s3 rm "s3://$SOURCE_BUCKET_NAME/items/draft/enriched-items-csv/DRG Dispensing/" --recursive --profile $AWS_PROFILE

# copy DRG Dispensing to NDC Drug for the latest publish item version
./copy-and-rename-folder.sh "$SOURCE_BUCKET_NAME" "items/published" "items/published" "" "items/published" "enriched-items-parquet/DRG Dispensing" "enriched-items-parquet/NDC Drug" "" "" "enriched-items-parquet/DRG Dispensing"

wait
echo "# -- > End migrate classification to coding system"
