#!/bin/bash

echo "🧪 Simulating CI TestContainers failure locally..."
echo "This will reproduce the exact Docker-in-Docker issue you're seeing in Buildkite"

NEXUS_USER="tuan.ngoanh"
NEXUS_PASSWORD="FHmAOA!Tzvs7EgboFge3"

# Build the Docker image first (target the build stage)
echo "📦 Building Docker build stage..."
docker build \
  --build-arg NEXUS_USER=${NEXUS_USER} \
  --build-arg NEXUS_PASSWORD="${NEXUS_PASSWORD}" \
  --target build -t pd-ref-data-service:test-builder .

# Simulate the exact CI environment with TestContainers
echo "🐳 Running tests in Docker container (simulating CI)..."
echo "This should reproduce the TestContainers Docker-in-Docker issues..."

# Test the new TestContainers configuration
echo "🧪 Testing with enhanced TestContainers configuration..."

# Detect if we're on Windows (Git Bash/WSL) or Linux
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    echo "🪟 Detected Windows environment - testing with Docker socket access"
    # On Windows, test with Docker socket to see if our fix works
    docker run -i --rm \
        -v //var/run/docker.sock:/var/run/docker.sock \
        -v "$(pwd -W | sed 's|\\|/|g')/build/test-results:/work/build/test-results" \
        -v "$(pwd -W | sed 's|\\|/|g')/common/build/test-results:/work/common/build/test-results" \
        -v "$(pwd -W | sed 's|\\|/|g')/kotlin-spark/build/test-results:/work/kotlin-spark/build/test-results" \
        -v "$(pwd -W | sed 's|\\|/|g')/scala-spark/build/test-results:/work/scala-spark/build/test-results" \
        -e TESTCONTAINERS_RYUK_DISABLED=true \
        pd-ref-data-service:test-builder \
        ./gradlew test mergeJUnitReports \
              -PnexusUser="${NEXUS_USER}" \
              -PnexusPassword="${NEXUS_PASSWORD}" \
              --info --continue -x compileKotlin -x compileTestKotlin
else
    echo "🐧 Detected Unix-like environment - testing with Docker socket"
    docker run -i --rm \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -v $(pwd)/build/test-results:/work/build/test-results \
        -v $(pwd)/common/build/test-results:/work/common/build/test-results \
        -v $(pwd)/kotlin-spark/build/test-results:/work/kotlin-spark/build/test-results \
        -v $(pwd)/scala-spark/build/test-results:/work/scala-spark/build/test-results \
        -e TESTCONTAINERS_RYUK_DISABLED=true \
        -e TESTCONTAINERS_CHECKS_DISABLE=true \
        -e DOCKER_HOST=unix:///var/run/docker.sock \
        pd-ref-data-service:test-builder \
        ./gradlew test mergeJUnitReports \
              -PnexusUser="${NEXUS_USER}" \
              -PnexusPassword="${NEXUS_PASSWORD}" \
              --continue -x compileKotlin -x compileTestKotlin
fi

echo "✅ Simulation complete. Check the output above for TestContainers errors."