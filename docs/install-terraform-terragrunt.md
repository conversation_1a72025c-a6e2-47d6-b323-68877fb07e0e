To manage versions of terraform and terragrunt, use tfenv and tgenv.
## Install tfenv:
- Clone tfenv:\
    ```git clone https://github.com/tfutils/tfenv.git ~/.tfenv```
- Add ~/.tfenv/bin to your $PATH
- Install terraform. We use terraform version 1.4.6. Check /inf/src/main.tf file to find terraform used version.\
    ```tfenv install 1.4.6```
- Use version 1.4.6:\
    ```tfenv use 1.4.6```

## Install tgenv:
- Clone tgenv\
    ```git clone https://github.com/cunymatthieu/tgenv.git ~/.tfenv```
- Add ~/.tfenv/bin to your $PATH
- Install terragrunt\
```tgenv install 0.24.0```
- Use terragrunt 0.24.0\
```tgenv use 0.24.0```

 Note: I have issue swtiching to desired versions as there was already terraform and terragrunt installed. If you already have installed terraform and terragrunt, you may have issue using tfenv and tgenv to switch terraform versions.
 To use tfenv & tgenv, uninstall existing terraform by running:\
 ```brew unlink terraform```\
 ```brew remove terraform```\
 OR \
 ```brew uninstall --ignore-dependencies terraform```


# Use terraform
Now, you can use \
 ```terragrunt plan```\
 ```terragrunt apply```\
with set versions of terraform and terragrunt
