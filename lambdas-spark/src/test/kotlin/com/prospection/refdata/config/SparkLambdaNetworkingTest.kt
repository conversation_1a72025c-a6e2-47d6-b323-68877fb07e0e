package com.prospection.refdata.config

import org.apache.spark.sql.SparkSession
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Test to verify that Spark networking configuration works correctly in AWS Lambda environment.
 * This test specifically addresses the networking issues that cause:
 * "Failed to connect to /**************:45909" errors in Lambda.
 */
class SparkLambdaNetworkingTest {

    @Test
    fun `should initialize Spark session without networking errors`() {
        // This test verifies that the Spark session can be created without
        // attempting external network connections that fail in Lambda
        assertDoesNotThrow {
            val spark = SparkConfiguration.spark
            assertTrue(spark.sparkContext().isLocal, "Spark should be running in local mode")
            assertEquals("127.0.0.1", spark.conf.get("spark.driver.bindAddress"))
            assertEquals("127.0.0.1", spark.conf.get("spark.driver.host"))
        }
    }

    @Test
    fun `should perform basic DataFrame operations without networking issues`() {
        assertDoesNotThrow {
            val spark = SparkConfiguration.spark
            import spark.implicits._
            
            // Create a simple DataFrame to trigger Spark operations
            val df = Seq(
                ("Alice", 25),
                ("Bob", 30),
                ("Charlie", 35)
            ).toDF("name", "age")
            
            // Perform operations that might trigger networking
            val result = df.filter($"age" > 25).collect()
            assertEquals(2, result.size)
            
            // Verify no external network connections were attempted
            assertTrue(spark.sparkContext().isLocal)
        }
    }

    @Test
    fun `should verify Lambda-specific Spark configuration`() {
        val spark = SparkConfiguration.spark
        val conf = spark.conf
        
        // Verify critical Lambda networking settings
        assertEquals("127.0.0.1", conf.get("spark.driver.bindAddress"))
        assertEquals("127.0.0.1", conf.get("spark.driver.host"))
        assertEquals("false", conf.get("spark.dynamicAllocation.enabled"))
        assertEquals("false", conf.get("spark.shuffle.service.enabled"))
        assertEquals("false", conf.get("spark.ui.enabled"))
        
        // Verify timeout settings are appropriate for Lambda
        assertEquals("10s", conf.get("spark.network.timeout"))
        assertEquals("10s", conf.get("spark.rpc.askTimeout"))
        assertEquals("10s", conf.get("spark.rpc.lookupTimeout"))
        
        println("✅ All Lambda-specific Spark configurations are correct")
    }
}
