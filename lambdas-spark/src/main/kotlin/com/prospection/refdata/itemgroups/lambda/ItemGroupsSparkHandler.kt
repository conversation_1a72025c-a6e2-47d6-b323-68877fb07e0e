package com.prospection.refdata.itemgroups.lambda

import com.amazonaws.services.lambda.runtime.Context
import com.amazonaws.services.lambda.runtime.RequestStreamHandler
import com.fasterxml.jackson.databind.ObjectMapper
import com.prospection.refdata.itemgroups.integration.ItemGroupPreviewRequestPayload
import com.prospection.refdata.config.ItemGroupsSparkConfiguration
import com.prospection.refdata.config.JacksonConfiguration
import com.prospection.refdata.config.SparkConfiguration
import com.prospection.refdata.itemgroups.domain.ItemGroupsSparkPort
import java.io.InputStream
import java.io.OutputStream
import kotlin.system.measureTimeMillis

/**
 * Lambda handler for processing item group preview requests via Spark.
 * 
 * Note: Although we're updating to AWS SDK v2 for most AWS services,
 * the Lambda runtime interface (RequestStreamHandler) is still from AWS SDK v1 
 * since it's a core interface that Lambda uses and isn't part of the v2 migration.
 */
class ItemGroupsSparkHandler(
    private val objectMapper: ObjectMapper = JacksonConfiguration.objectMapper,
    private val itemGroupsSparkPort: ItemGroupsSparkPort = ItemGroupsSparkConfiguration.itemGroupsSparkPort,
): RequestStreamHandler {

    override fun handleRequest(inputStream: InputStream, outputStream: OutputStream, context: Context) {
        try {
            val payload = inputStream.use { objectMapper.readValue(it, ItemGroupPreviewRequestPayload::class.java) }
            outputStream.use {
                val previewResults = itemGroupsSparkPort.getItemGroupPreview(
                    payload.itemGroup,
                    payload.codingSystemToClassifications,
                    payload.publishedItemsVersion
                )
                objectMapper.writeValue(it, previewResults)
            }
        } finally {
            val durationInMs = measureTimeMillis {
                SparkConfiguration.spark.sqlContext().clearCache()
            }
            println("Spark cache cleared in $durationInMs ms")
        }
    }
}