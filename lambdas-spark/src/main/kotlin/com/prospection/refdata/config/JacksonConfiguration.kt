package com.prospection.refdata.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.fasterxml.jackson.module.scala.DefaultScalaModule


object JacksonConfiguration {
    val objectMapper: ObjectMapper by lazy {
        ObjectMapper()
            .registerModule(JavaTimeModule())
            .registerKotlinModule()
            // disable to convert LocalDateTime to timestamps instead of array
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            // <PERSON> deals with scala objects
            .registerModule(DefaultScalaModule())
    }
}