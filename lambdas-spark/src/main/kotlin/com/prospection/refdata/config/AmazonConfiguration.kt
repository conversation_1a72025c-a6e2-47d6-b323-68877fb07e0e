package com.prospection.refdata.config

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.regions.Region


object AmazonConfiguration {
    val s3Client: S3Client by lazy {
        S3Client.builder()
            .region(
                Region.of(
                    System.getenv("AWS_REGION") ?: throw IllegalStateException("AWS_REGION environment variable is not set")
                )
            )
            .build()
//        val credentials = AwsBasicCredentials.create("accesskey", "secretkey")
//        S3Client.builder()
//            .endpointOverride(java.net.URI.create("localhost:4566"))
//            .region(Region.of("ap-southeast-2"))
//            .credentialsProvider(StaticCredentialsProvider.create(credentials))
//            .forcePathStyle(true)
//            .build()
    }
}