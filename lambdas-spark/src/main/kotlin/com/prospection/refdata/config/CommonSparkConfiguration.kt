package com.prospection.refdata.config

import com.prospection.refdata.common.domain.SparkImportExportHelper
import com.prospection.refdata.common.integration.S3SparkImportExportHelper

object CommonSparkConfiguration {
    val sparkImportExportHelper: SparkImportExportHelper by lazy {
        S3SparkImportExportHelper(
            System.getenv("S3_BUCKET") ?: throw IllegalStateException("S3_BUCKET environment variable is not set"),
            AmazonConfiguration.s3Client,
            SparkConfiguration.spark
        )
    }
}