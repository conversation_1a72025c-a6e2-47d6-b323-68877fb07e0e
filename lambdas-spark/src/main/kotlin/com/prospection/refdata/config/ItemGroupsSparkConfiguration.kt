package com.prospection.refdata.config

import com.prospection.refdata.itemgroups.domain.ApplyItemGroupRulePort
import com.prospection.refdata.itemgroups.domain.ItemGroupsSparkPort
import com.prospection.refdata.itemgroups.integration.ApplyItemGroupRuleAwsSparkAdapter
import com.prospection.refdata.itemgroups.integration.ItemGroupsSparkImpl

object ItemGroupsSparkConfiguration {
    private val applyItemGroupRulePort: ApplyItemGroupRulePort by lazy {
        ApplyItemGroupRuleAwsSparkAdapter()
    }

    val itemGroupsSparkPort: ItemGroupsSparkPort by lazy {
        ItemGroupsSparkImpl(
            ItemsSparkConfiguration.itemsSparkPort,
            applyItemGroupRulePort
        )
    }
}