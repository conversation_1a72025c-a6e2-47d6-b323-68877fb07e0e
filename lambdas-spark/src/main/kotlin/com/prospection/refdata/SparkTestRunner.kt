package com.prospection.refdata

import com.prospection.refdata.config.SparkConfiguration

object SparkTestRunner {
    @JvmStatic
    fun main(args: Array<String>) {
        println("Attempting to initialize Spark...")
        try {
            val spark = SparkConfiguration.spark
            println("Spark Session ID: ${spark.sparkContext().applicationId()}")
            println("Spark version: ${spark.version()}")
            println("Spark initialized successfully!")
            // You can add a small Spark operation here if you want, e.g.:
            // spark.sql("SELECT 1").show()
            spark.stop()
            println("Spark stopped.")
        } catch (e: Exception) {
            println("Error initializing Spark or running test operation:")
            e.printStackTrace()
        }
    }
}
