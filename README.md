# Ref Data Service
Project for reference data management in dash2

## Overview

This project is build on top of the following tech stack + versions:

- OpenJDK 11
- Kotlin 1.5.10
- Spring 2.5.0
- JUnit Jupiter 5.6.2
- Scala 2.12.10
- Spark 3.1.2
- Node and TypeScript

There are 2 submodules in this service. Please also read through the documentation below.
- [scala-spark module](./scala-spark/README.md)
- [Lambda functions module](./lambdas/README.md)

## Usage

### Testing terraform script locally

Infrastructure code has dev environment folder (`inf/${country}/env/dev`). The purpose of this is to be able to build and test Terraform scripts locally.

- First install correct versions of Terraform and Terragrunt as described [here](https://prospection.atlassian.net/wiki/spaces/BP/pages/**********/Setup+PharmDash+2+Development+Environment)

**NOTE:** If you're using `tfenv`, then before you do anything, from the CLI use `tfenv use` to ensure you're using the correct Terraform version.
**NOTE:** If you're not using `tfenv`, maybe consider it - it's handy. Otherwise, check the version of Terraform you need by looking at the `.terraform-version` file.

- In command prompt, goto: `inf/env/au/dev`

- Set `PROJECT_NAME`, `IMAGE`, `TAG_VERSION`, `ENV_NAME`, `ENV_TEMPLATE`, `AWS_ACCOUNT_ID` and `AWS_ACCOUNT_NAME`.

Example:

```shell
export PROJECT_NAME="pd-ref-data-service"
export IMAGE=************.dkr.ecr.ap-southeast-2.amazonaws.com/${PROJECT_NAME}
export TAG_VERSION=snapshot
export ENV_NAME="dev" # The template to be used for the environment
export ENV_TEMPLATE="dev" # The name of the environment which is normally used as part of the resource name in AWS
export AWS_ACCOUNT_ID=************
export AWS_ACCOUNT_NAME="pd2-dev"
```

- run ```terragrunt plan```

- run ```terragrunt apply```


Running terraform apply will create aws resource in dev environment.

### Unit Tests
Run the below make target, to run unit tests:
```
make app_local_test
```

### Spring Boot Application

1. When running for the first time, initialise the Docker dependencies using the below make target:
    ```
    make app_local_compose_up
    ```
1. Afterwards, Docker dependencies can be started using the below target:
    ```
    make app_local_compose_start
    ```
1. Once the Docker dependencies have started, the Spring Boot application can be run from the command-line using the target below:
    ```
    make app_local_run
    ```
1. Or from an IDE, run the main `Application` class and configure the below environment property:
    ```
    spring.profiles.active=local
    ```
   In IntelliJ IDEA, go to _Edit Configurations..._ `->` _Environment variables_, and paste the properties above.
1. To stop the Docker dependencies and **keep data**, run the below target:
    ```
    make app_local_compose_stop
    ```
1. To completely tear-down the Docker dependencies, run:
    ```
    make app_local_compose_down
    ```

### AWS S3
To the local environment, we have the init S3 script at folder `local/localstack/init-script`.
If you want to add pre-defined folders or files in the local bucket, you can add commands in exist init file by using AWS command line.

## Endpoints

Swagger V3

http://localhost:17080/v3/api-docs

Swagger V2

http://localhost:17080/v2/api-docs

Swagger UI

http://localhost:17080/swagger-ui/index.html

Actuator Health

http://localhost:17080/actuator/health

Actuator Liveness

http://localhost:17080/actuator/health/liveness

Actuator Readiness

http://localhost:17080/actuator/health/readiness
