SHELL := /bin/bash
.PHONY: build push pull app_test app_local_test app_local_compose_up app_local_compose_down  \
		app_local_compose_stop app_local_compose_start app_local_run inf_plan inf_apply inf_destroy git_tag checkov_check

IMAGE            ?= pd-ref-data-service
TAG_VERSION      ?= snapshot
TAG_LATEST       ?= latest
TAG_BUILDER      ?= builder
ECR_REGION       ?= ap-southeast-2
ENV_NAME         ?= int
ENV_TEMPLATE     ?= int
COUNTRY          ?= au
AWS_ACCOUNT_ID   ?= ************
AWS_ACCOUNT_NAME ?= pd2-dev
WORK_DIR         := $(BUILDKITE_BUILD_CHECKOUT_PATH)
# substitute slash with dash in branch name for docker image name, else docker build will fail
TAG_LATEST       := $(subst /,-,$(TAG_LATEST))
TAG_BUILDER      := $(subst /,-,$(TAG_BUILDER))
DATE              = `date +%Y%m%d`
GIT_TAG_VERSION  ?= v${DATE}.${TAG_VERSION}
# If no DataDog Java Agent download URL is specified, download the latest version of the Agent
DATADOG_AGENT_URL ?= https://dtdg.co/latest-java-tracer

PULLREQUEST_KEY ?= false

build:
	docker build . \
		--cache-from ${IMAGE}:${TAG_LATEST}_inf \
		--tag ${IMAGE}:${TAG_VERSION}_inf \
		--tag ${IMAGE}:${TAG_LATEST}_inf \
		--target inf
	docker build . \
		--cache-from ${IMAGE}:${TAG_BUILDER} \
		--tag ${IMAGE}:${TAG_BUILDER} \
		--build-arg NEXUS_USER=${NEXUS_USER} \
		--build-arg NEXUS_PASSWORD="${NEXUS_PASSWORD}" \
		--build-arg DATADOG_AGENT_URL=${DATADOG_AGENT_URL} \
		--target build
	docker build . \
		--cache-from ${IMAGE}:${TAG_BUILDER} \
		--cache-from ${IMAGE}:${TAG_LATEST} \
		--tag ${IMAGE}:${TAG_VERSION} \
		--tag ${IMAGE}:${TAG_LATEST} \
		--build-arg NEXUS_USER=${NEXUS_USER} \
		--build-arg NEXUS_PASSWORD="${NEXUS_PASSWORD}" \
		--build-arg DATADOG_AGENT_URL=${DATADOG_AGENT_URL} \
		--target app
	docker build . \
		--cache-from ${IMAGE}:${TAG_BUILDER} \
		--cache-from ${IMAGE}-lambdas-spark:${TAG_LATEST} \
		--tag ${IMAGE}-lambdas-spark:${TAG_VERSION} \
		--tag ${IMAGE}-lambdas-spark:${TAG_LATEST} \
		--build-arg NEXUS_USER=${NEXUS_USER} \
		--build-arg NEXUS_PASSWORD="${NEXUS_PASSWORD}" \
		--build-arg DATADOG_AGENT_URL=${DATADOG_AGENT_URL} \
		--provenance=false \
		--platform=linux/amd64 \
		--target lambdas-spark
	docker build -f lambdas/Dockerfile lambdas \
		--cache-from ${IMAGE}-lambdas:${TAG_BUILDER} \
		--cache-from ${IMAGE}-lambdas:${TAG_LATEST} \
		--tag ${IMAGE}-lambdas:${TAG_VERSION} \
		--tag ${IMAGE}-lambdas:${TAG_LATEST} \
		--target build \
		--platform=linux/amd64

push:
	docker push ${IMAGE}:${TAG_VERSION}
	docker push ${IMAGE}:${TAG_VERSION}_inf
	docker push ${IMAGE}-lambdas-spark:${TAG_VERSION}
	docker push ${IMAGE}-lambdas:${TAG_VERSION}
	docker push ${IMAGE}:${TAG_LATEST}
	docker push ${IMAGE}:${TAG_LATEST}_inf
	docker push ${IMAGE}-lambdas-spark:${TAG_LATEST}
	docker push ${IMAGE}-lambdas:${TAG_LATEST}
	docker push ${IMAGE}:${TAG_BUILDER} || true

pull:
	docker pull ${IMAGE}:${TAG_VERSION}     || true
	docker pull ${IMAGE}:${TAG_VERSION}_inf || true
	docker pull ${IMAGE}-lambdas-spark:${TAG_VERSION} || true
	docker pull ${IMAGE}-lambdas:${TAG_VERSION} || true
	docker pull ${IMAGE}:${TAG_LATEST}      || true
	docker pull ${IMAGE}:${TAG_LATEST}_inf  || true
	docker pull ${IMAGE}-lambdas-spark:${TAG_LATEST} || true
	docker pull ${IMAGE}-lambdas:${TAG_LATEST} || true
	docker pull ${IMAGE}:${TAG_BUILDER}     || true

git_tag:
	git tag ${GIT_TAG_VERSION} ${BUILDKITE_COMMIT}
	git push origin ${GIT_TAG_VERSION}

app_test_lambdas:
	docker build ./lambdas --target test

VOLUMES = -v /var/run/docker.sock:/var/run/docker.sock \
          -v ${WORK_DIR}/common/build/test-results:/work/common/build/test-results \
          -v ${WORK_DIR}/kotlin-spark/build/test-results:/work/kotlin-spark/build/test-results \
          -v ${WORK_DIR}/lambda-spark/build/test-results:/work/lambda-spark/build/test-results \
          -v ${WORK_DIR}/scala-spark/build/test-results:/work/scala-spark/build/test-results \
          -v ${WORK_DIR}/build/test-results:/work/build/test-results \

# TestContainers environment variables for Docker
TESTCONTAINERS_ENV = -e TESTCONTAINERS_RYUK_DISABLED=true \
                     -e TESTCONTAINERS_CHECKS_DISABLE=true \
                     -e DOCKER_HOST=unix:///var/run/docker.sock \

app_test:
ifeq (${PULLREQUEST_KEY} , false)
	@echo "running a normal branch"
	docker run -i --rm \
		${VOLUMES} \
		${TESTCONTAINERS_ENV} \
		${IMAGE}:${TAG_BUILDER} \
		./gradlew test mergeJUnitReports \
			-Dsonar.login=${SONAR_TOKEN} \
			-Dsonar.branch.name=${BRANCH} \
			-Dsonar.scanner.force-deprecated-java-version=true \
			-PnexusUser="${NEXUS_USER}" \
			-PnexusPassword="${NEXUS_PASSWORD}" \
			--continue -x compileKotlin -x compileTestKotlin
else
	@echo "running a pull request branch"
	docker run -i --rm \
		${VOLUMES} \
		${TESTCONTAINERS_ENV} \
		${IMAGE}:${TAG_BUILDER} \
		./gradlew test mergeJUnitReports \
			-Dsonar.login=${SONAR_TOKEN} \
			-Dsonar.pullrequest.branch=${BRANCH} \
			-Dsonar.pullrequest.key=${PULLREQUEST_KEY}\
			-Dsonar.pullrequest.base=${PULLREQUEST_BASE}\
			-Dsonar.pullrequest.provider=${PROVIDER} \
			-Dsonar.scanner.force-deprecated-java-version=true \
			-PnexusUser="${NEXUS_USER}" \
			-PnexusPassword="${NEXUS_PASSWORD}" \
			--continue -x compileKotlin -x compileTestKotlin
endif

app_local_test:
	./gradlew test

app_local_compose_up:
	docker-compose -f local/docker-compose.yml up

app_local_compose_down:
	docker-compose -f local/docker-compose.yml down -v

app_local_compose_stop:
	docker-compose -f local/docker-compose.yml stop

app_local_compose_start:
	docker-compose -f local/docker-compose.yml start

app_local_run:
	./gradlew bootRun --args="--spring.profiles.active=local"

define inf_cmd
	mkdir -p ~/builds/${BUILDKITE_AGENT_NAME}/${BUILDKITE_ORGANIZATION_SLUG}/${BUILDKITE_PIPELINE_SLUG}/inf/env/${COUNTRY}/${ENV_TEMPLATE}/tf
	docker run -i --rm \
		-v ~/builds/${BUILDKITE_AGENT_NAME}/${BUILDKITE_ORGANIZATION_SLUG}/${BUILDKITE_PIPELINE_SLUG}/inf/env/${COUNTRY}/${ENV_TEMPLATE}/tf:/tf \
		-e AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID} \
		-e AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY} \
		-e PROJECT_NAME=${PROJECT_NAME} \
		-e IMAGE=${IMAGE} \
		-e TAG_VERSION=${TAG_VERSION} \
		-e TF_VAR_lambdas_image_uri=${IMAGE}-lambdas:${TAG_VERSION} \
		-e ENV_NAME=${ENV_NAME} \
		-e ENV_TEMPLATE=${ENV_TEMPLATE} \
		-e AWS_ACCOUNT_ID=${AWS_ACCOUNT_ID} \
		-e AWS_ACCOUNT_NAME=${AWS_ACCOUNT_NAME} \
		-e DD_API_KEY \
		-e DD_APP_KEY \
		-e DD_SITE=datadoghq.com \
		${DOCKER_OPTS} \
		${IMAGE}:${TAG_VERSION}_inf \
		/bin/bash -c 'cd env/${COUNTRY}/${ENV_TEMPLATE};$(1)'
endef

inf_plan:
	$(call inf_cmd, terragrunt validate && terragrunt plan -out tf.plan && terragrunt show -json tf.plan > /tf/tf.json)

checkov_check:
	cp ./pd-checkov/checkov-config ~/builds/${BUILDKITE_AGENT_NAME}/${BUILDKITE_ORGANIZATION_SLUG}/${BUILDKITE_PIPELINE_SLUG}/inf/env/${COUNTRY}/${ENV_TEMPLATE}/tf
	docker run --tty --volume ~/builds/${BUILDKITE_AGENT_NAME}/${BUILDKITE_ORGANIZATION_SLUG}/${BUILDKITE_PIPELINE_SLUG}/inf/env/${COUNTRY}/${ENV_TEMPLATE}/tf:/tf --workdir /tf bridgecrew/checkov:${CHECKOV_VERSION} --directory /tf --config-file checkov-config

inf_apply:
	$(call inf_cmd, terragrunt apply -auto-approve)

inf_destroy:
	$(call inf_cmd, terragrunt destroy -auto-approve)

tf_format:
	terragrunt hclfmt && terraform fmt --recursive

tf_clear_cache:
	find . -type d -name ".terragrunt-cache" -prune -exec rm -rf {} \;
